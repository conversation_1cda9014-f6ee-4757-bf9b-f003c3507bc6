package com.accounting.service;

import com.accounting.model.*;
import com.itextpdf.kernel.pdf.PdfDocument;
import com.itextpdf.kernel.pdf.PdfWriter;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.element.*;
import com.itextpdf.layout.properties.*;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.kernel.font.PdfFontFactory;
import com.itextpdf.kernel.colors.ColorConstants;
import com.itextpdf.layout.borders.Border;
import javafx.stage.FileChooser;
import javafx.stage.Stage;
import javafx.scene.control.Alert;
import java.io.File;
import java.io.IOException;
import java.time.format.DateTimeFormatter;
import java.text.DecimalFormat;
import java.util.List;

/**
 * خدمة طباعة وحفظ تقارير التصنيع
 */
public class ManufacturingPrintService {
    
    private final DecimalFormat decimalFormat = new DecimalFormat("#,##0.00");
    private final DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("dd/MM/yyyy");
    
    /**
     * طباعة تقرير أمر التصنيع
     */
    public boolean printManufacturingOrder(ManufacturingOrder order, Stage parentStage) {
        try {
            // اختيار مكان الحفظ
            FileChooser fileChooser = new FileChooser();
            fileChooser.setTitle("حفظ تقرير أمر التصنيع");
            fileChooser.setInitialFileName("تقرير_أمر_التصنيع_" + order.getOrderNumber() + ".pdf");
            fileChooser.getExtensionFilters().add(
                new FileChooser.ExtensionFilter("PDF Files", "*.pdf")
            );
            
            File file = fileChooser.showSaveDialog(parentStage);
            if (file == null) {
                return false; // المستخدم ألغى العملية
            }
            
            // إنشاء PDF
            createManufacturingOrderPDF(order, file.getAbsolutePath());
            
            // رسالة نجاح
            Alert alert = new Alert(Alert.AlertType.INFORMATION);
            alert.setTitle("نجح الحفظ");
            alert.setHeaderText("تم حفظ التقرير بنجاح");
            alert.setContentText("تم حفظ التقرير في: " + file.getAbsolutePath());
            alert.showAndWait();
            
            return true;
            
        } catch (Exception e) {
            e.printStackTrace();
            Alert alert = new Alert(Alert.AlertType.ERROR);
            alert.setTitle("خطأ في الحفظ");
            alert.setHeaderText("فشل في حفظ التقرير");
            alert.setContentText("حدث خطأ أثناء حفظ التقرير: " + e.getMessage());
            alert.showAndWait();
            return false;
        }
    }
    
    /**
     * إنشاء PDF لأمر التصنيع
     */
    private void createManufacturingOrderPDF(ManufacturingOrder order, String filePath) throws IOException {
        PdfWriter writer = new PdfWriter(filePath);
        PdfDocument pdf = new PdfDocument(writer);
        Document document = new Document(pdf);
        
        try {
            // إعداد الخط (استخدام الخط الافتراضي مؤقتاً)
            PdfFont font = PdfFontFactory.createFont();
            document.setFont(font);
            
            // معلومات الشركة
            addCompanyHeader(document);
            
            // عنوان التقرير
            addReportTitle(document, "تقرير أمر التصنيع");
            
            // معلومات الأمر الأساسية
            addOrderBasicInfo(document, order);
            
            // الخدمات المختارة
            addSelectedServices(document, order);
            
            // جداول التفاصيل
            if (order.isGlassCuttingSelected() && !order.getGlassCuttingItems().isEmpty()) {
                addGlassCuttingTable(document, order.getGlassCuttingItems());
            }
            
            if (order.isFilmSelected() && !order.getFilmItems().isEmpty()) {
                addFilmTable(document, order.getFilmItems());
            }
            
            if (order.isDoubleGlassSelected() && !order.getDoubleGlassItems().isEmpty()) {
                addDoubleGlassTable(document, order.getDoubleGlassItems());
            }
            
            if (!order.getRequiredMeasurements().isEmpty()) {
                addRequiredMeasurementsTable(document, order.getRequiredMeasurements(), "المقاسات المطلوبة (أساسي)");
            }
            
            if (order.isHasIncrease() && !order.getIncreasedMeasurements().isEmpty()) {
                addRequiredMeasurementsTable(document, order.getIncreasedMeasurements(), 
                    "المقاسات مع الزيادة (" + decimalFormat.format(order.getIncreaseValue()) + " مم)");
            }
            
            // ملاحظات
            if (order.getNotes() != null && !order.getNotes().trim().isEmpty()) {
                addNotes(document, order.getNotes());
            }
            
            // تذييل التقرير
            addReportFooter(document);
            
        } finally {
            document.close();
        }
    }
    
    /**
     * إضافة رأس الشركة
     */
    private void addCompanyHeader(Document document) {
        CompanyInfo companyInfo = CompanyInfo.getInstance();
        
        Table headerTable = new Table(1);
        headerTable.setWidth(UnitValue.createPercentValue(100));
        
        Cell headerCell = new Cell();
        headerCell.setBorder(Border.NO_BORDER);
        headerCell.setTextAlignment(TextAlignment.CENTER);
        
        Paragraph companyName = new Paragraph(companyInfo.getCompanyName())
            .setFontSize(18)
            .setBold()
            .setTextAlignment(TextAlignment.CENTER);
        
        Paragraph companyActivity = new Paragraph(companyInfo.getActivity())
            .setFontSize(12)
            .setTextAlignment(TextAlignment.CENTER);
        
        Paragraph companyAddress = new Paragraph(companyInfo.getAddress())
            .setFontSize(10)
            .setTextAlignment(TextAlignment.CENTER);
        
        Paragraph companyContact = new Paragraph("هاتف: " + companyInfo.getPhone() + " | " + 
                                                "بريد إلكتروني: " + companyInfo.getEmail())
            .setFontSize(10)
            .setTextAlignment(TextAlignment.CENTER);
        
        headerCell.add(companyName);
        headerCell.add(companyActivity);
        headerCell.add(companyAddress);
        headerCell.add(companyContact);
        
        headerTable.addCell(headerCell);
        document.add(headerTable);
        
        // خط فاصل
        document.add(new Paragraph("\n"));
        document.add(new LineSeparator());
        document.add(new Paragraph("\n"));
    }
    
    /**
     * إضافة عنوان التقرير
     */
    private void addReportTitle(Document document, String title) {
        Paragraph titleParagraph = new Paragraph(title)
            .setFontSize(16)
            .setBold()
            .setTextAlignment(TextAlignment.CENTER)
            .setBackgroundColor(ColorConstants.LIGHT_GRAY)
            .setPadding(10);
        
        document.add(titleParagraph);
        document.add(new Paragraph("\n"));
    }
    
    /**
     * إضافة معلومات الأمر الأساسية
     */
    private void addOrderBasicInfo(Document document, ManufacturingOrder order) {
        Table infoTable = new Table(4);
        infoTable.setWidth(UnitValue.createPercentValue(100));
        
        // الصف الأول
        infoTable.addCell(createInfoCell("رقم أمر العمل:", order.getOrderNumber()));
        infoTable.addCell(createInfoCell("اسم العميل:", order.getCustomerName()));
        infoTable.addCell(createInfoCell("رقم الفاتورة:", order.getInvoiceNumber()));
        infoTable.addCell(createInfoCell("التاريخ:", order.getOrderDate().format(dateFormatter)));
        
        // الصف الثاني
        infoTable.addCell(createInfoCell("الحالة:", getStatusText(order.getStatus())));
        infoTable.addCell(createInfoCell("تاريخ الإنشاء:", 
            order.getCreatedDate() != null ? order.getCreatedDate().toLocalDate().format(dateFormatter) : ""));
        infoTable.addCell(createInfoCell("زيادة المقاسات:", 
            order.isHasIncrease() ? decimalFormat.format(order.getIncreaseValue()) + " مم" : "لا"));
        infoTable.addCell(createInfoCell("", "")); // خلية فارغة
        
        document.add(infoTable);
        document.add(new Paragraph("\n"));
    }
    
    /**
     * إنشاء خلية معلومات
     */
    private Cell createInfoCell(String label, String value) {
        Cell cell = new Cell();
        cell.setPadding(5);
        
        Paragraph labelPara = new Paragraph(label).setBold().setFontSize(10);
        Paragraph valuePara = new Paragraph(value != null ? value : "").setFontSize(10);
        
        cell.add(labelPara);
        cell.add(valuePara);
        
        return cell;
    }
    
    /**
     * إضافة الخدمات المختارة
     */
    private void addSelectedServices(Document document, ManufacturingOrder order) {
        Paragraph servicesTitle = new Paragraph("الخدمات المختارة:")
            .setFontSize(14)
            .setBold()
            .setBackgroundColor(ColorConstants.LIGHT_GRAY)
            .setPadding(5);
        
        document.add(servicesTitle);
        
        StringBuilder services = new StringBuilder();
        if (order.isGlassCuttingSelected()) services.append("• قص زجاج\n");
        if (order.isThermalGlassSelected()) services.append("• حراري زجاج\n");
        if (order.isFilmSelected()) services.append("• فيلم\n");
        if (order.isBulletProofFilmSelected()) services.append("• فيلم ضد الرصاص\n");
        if (order.isDoubleGlassSelected()) services.append("• دبل جلاس\n");
        if (order.isPolishSelected()) services.append("• Polish\n");
        if (order.isHoleSelected()) services.append("• Hole\n");
        if (order.isCncSelected()) services.append("• CNC\n");
        if (order.isDrawSelected()) services.append("• Draw\n");
        if (order.isOtherServicesSelected()) services.append("• خدمات أخرى\n");
        
        Paragraph servicesList = new Paragraph(services.toString())
            .setFontSize(11)
            .setPaddingLeft(10);
        
        document.add(servicesList);
        document.add(new Paragraph("\n"));
    }
    
    /**
     * إضافة جدول قص الزجاج
     */
    private void addGlassCuttingTable(Document document, List<GlassCuttingItem> items) {
        addTableTitle(document, "جدول قص الزجاج");
        
        Table table = new Table(8);
        table.setWidth(UnitValue.createPercentValue(100));
        
        // رؤوس الأعمدة
        table.addHeaderCell(createHeaderCell("ت"));
        table.addHeaderCell(createHeaderCell("الوصف"));
        table.addHeaderCell(createHeaderCell("الطول (مم)"));
        table.addHeaderCell(createHeaderCell("العرض (مم)"));
        table.addHeaderCell(createHeaderCell("المتر المربع"));
        table.addHeaderCell(createHeaderCell("العدد"));
        table.addHeaderCell(createHeaderCell("إجمالي المتر المربع"));
        table.addHeaderCell(createHeaderCell("ملاحظات"));
        
        // البيانات
        int index = 1;
        double totalSquareMeters = 0;
        int totalCount = 0;
        
        for (GlassCuttingItem item : items) {
            table.addCell(createDataCell(String.valueOf(index++)));
            table.addCell(createDataCell(item.getDescription()));
            table.addCell(createDataCell(decimalFormat.format(item.getLength())));
            table.addCell(createDataCell(decimalFormat.format(item.getWidth())));
            table.addCell(createDataCell(decimalFormat.format(item.getSquareMeters())));
            table.addCell(createDataCell(String.valueOf(item.getCount())));
            table.addCell(createDataCell(decimalFormat.format(item.getTotalSquareMeters())));
            table.addCell(createDataCell(item.getNotes() != null ? item.getNotes() : ""));
            
            totalSquareMeters += item.getTotalSquareMeters();
            totalCount += item.getCount();
        }
        
        // صف الإجماليات
        table.addCell(createTotalCell("الإجمالي"));
        table.addCell(createTotalCell(""));
        table.addCell(createTotalCell(""));
        table.addCell(createTotalCell(""));
        table.addCell(createTotalCell(""));
        table.addCell(createTotalCell(String.valueOf(totalCount)));
        table.addCell(createTotalCell(decimalFormat.format(totalSquareMeters) + " م²"));
        table.addCell(createTotalCell(""));
        
        document.add(table);
        document.add(new Paragraph("\n"));
    }
    
    /**
     * إضافة جدول الفيلم
     */
    private void addFilmTable(Document document, List<FilmItem> items) {
        addTableTitle(document, "جدول الفيلم");
        
        Table table = new Table(8);
        table.setWidth(UnitValue.createPercentValue(100));
        
        // رؤوس الأعمدة
        table.addHeaderCell(createHeaderCell("ت"));
        table.addHeaderCell(createHeaderCell("الوصف"));
        table.addHeaderCell(createHeaderCell("الطول (مم)"));
        table.addHeaderCell(createHeaderCell("العرض (مم)"));
        table.addHeaderCell(createHeaderCell("المتر المربع"));
        table.addHeaderCell(createHeaderCell("العدد"));
        table.addHeaderCell(createHeaderCell("إجمالي المتر المربع"));
        table.addHeaderCell(createHeaderCell("ملاحظات"));
        
        // البيانات
        int index = 1;
        double totalSquareMeters = 0;
        double totalLengthMeters = 0;
        int totalCount = 0;
        
        for (FilmItem item : items) {
            table.addCell(createDataCell(String.valueOf(index++)));
            table.addCell(createDataCell(item.getDescription()));
            table.addCell(createDataCell(decimalFormat.format(item.getLength())));
            table.addCell(createDataCell(decimalFormat.format(item.getWidth())));
            table.addCell(createDataCell(decimalFormat.format(item.getSquareMeters())));
            table.addCell(createDataCell(String.valueOf(item.getCount())));
            table.addCell(createDataCell(decimalFormat.format(item.getTotalSquareMeters())));
            table.addCell(createDataCell(item.getNotes() != null ? item.getNotes() : ""));
            
            totalSquareMeters += item.getTotalSquareMeters();
            totalLengthMeters += item.getTotalLengthMeters();
            totalCount += item.getCount();
        }
        
        // صف الإجماليات
        table.addCell(createTotalCell("الإجمالي"));
        table.addCell(createTotalCell(""));
        table.addCell(createTotalCell(""));
        table.addCell(createTotalCell(""));
        table.addCell(createTotalCell(""));
        table.addCell(createTotalCell(String.valueOf(totalCount)));
        table.addCell(createTotalCell(decimalFormat.format(totalSquareMeters) + " م²"));
        table.addCell(createTotalCell(""));
        
        document.add(table);
        
        // إضافة إجمالي الأطوال
        Paragraph lengthTotal = new Paragraph("إجمالي الأطوال: " + decimalFormat.format(totalLengthMeters) + " م")
            .setBold()
            .setTextAlignment(TextAlignment.RIGHT)
            .setPaddingTop(5);
        document.add(lengthTotal);
        document.add(new Paragraph("\n"));
    }
    
    /**
     * إضافة جدول دبل جلاس
     */
    private void addDoubleGlassTable(Document document, List<DoubleGlassItem> items) {
        addTableTitle(document, "جدول دبل جلاس (خامات)");
        
        Table table = new Table(6);
        table.setWidth(UnitValue.createPercentValue(100));
        
        // رؤوس الأعمدة
        table.addHeaderCell(createHeaderCell("ت"));
        table.addHeaderCell(createHeaderCell("الوصف"));
        table.addHeaderCell(createHeaderCell("إجمالي العدد"));
        table.addHeaderCell(createHeaderCell("إجمالي الكمية"));
        table.addHeaderCell(createHeaderCell("الوحدة"));
        table.addHeaderCell(createHeaderCell("ملاحظات"));
        
        // البيانات
        int index = 1;
        int totalCount = 0;
        double totalQuantity = 0;
        
        for (DoubleGlassItem item : items) {
            table.addCell(createDataCell(String.valueOf(index++)));
            table.addCell(createDataCell(item.getDescription()));
            table.addCell(createDataCell(String.valueOf(item.getTotalCount())));
            table.addCell(createDataCell(decimalFormat.format(item.getTotalQuantity())));
            table.addCell(createDataCell(item.getUnit()));
            table.addCell(createDataCell(item.getNotes() != null ? item.getNotes() : ""));
            
            totalCount += item.getTotalCount();
            totalQuantity += item.getTotalQuantity();
        }
        
        // صف الإجماليات
        table.addCell(createTotalCell("الإجمالي"));
        table.addCell(createTotalCell(""));
        table.addCell(createTotalCell(String.valueOf(totalCount)));
        table.addCell(createTotalCell(decimalFormat.format(totalQuantity)));
        table.addCell(createTotalCell(""));
        table.addCell(createTotalCell(""));
        
        document.add(table);
        document.add(new Paragraph("\n"));
    }
    
    /**
     * إضافة جدول المقاسات المطلوبة
     */
    private void addRequiredMeasurementsTable(Document document, List<RequiredMeasurement> items, String title) {
        addTableTitle(document, title);
        
        Table table = new Table(9);
        table.setWidth(UnitValue.createPercentValue(100));
        
        // رؤوس الأعمدة
        table.addHeaderCell(createHeaderCell("ت"));
        table.addHeaderCell(createHeaderCell("الوصف"));
        table.addHeaderCell(createHeaderCell("طول (مم)"));
        table.addHeaderCell(createHeaderCell("عرض (مم)"));
        table.addHeaderCell(createHeaderCell("متر مربع"));
        table.addHeaderCell(createHeaderCell("متر طولي"));
        table.addHeaderCell(createHeaderCell("عدد"));
        table.addHeaderCell(createHeaderCell("إجمالي متر مربع"));
        table.addHeaderCell(createHeaderCell("إجمالي متر طولي"));
        
        // البيانات
        int index = 1;
        int totalCount = 0;
        double totalSquareMeters = 0;
        double totalLinearMeters = 0;
        
        for (RequiredMeasurement item : items) {
            table.addCell(createDataCell(String.valueOf(index++)));
            table.addCell(createDataCell(item.getDescription()));
            table.addCell(createDataCell(decimalFormat.format(item.getLength())));
            table.addCell(createDataCell(decimalFormat.format(item.getWidth())));
            table.addCell(createDataCell(decimalFormat.format(item.getSquareMeters())));
            table.addCell(createDataCell(decimalFormat.format(item.getLinearMeters())));
            table.addCell(createDataCell(String.valueOf(item.getCount())));
            table.addCell(createDataCell(decimalFormat.format(item.getTotalSquareMeters())));
            table.addCell(createDataCell(decimalFormat.format(item.getTotalLinearMeters())));
            
            totalCount += item.getCount();
            totalSquareMeters += item.getTotalSquareMeters();
            totalLinearMeters += item.getTotalLinearMeters();
        }
        
        // صف الإجماليات
        table.addCell(createTotalCell("الإجمالي"));
        table.addCell(createTotalCell(""));
        table.addCell(createTotalCell(""));
        table.addCell(createTotalCell(""));
        table.addCell(createTotalCell(""));
        table.addCell(createTotalCell(""));
        table.addCell(createTotalCell(String.valueOf(totalCount)));
        table.addCell(createTotalCell(decimalFormat.format(totalSquareMeters) + " م²"));
        table.addCell(createTotalCell(decimalFormat.format(totalLinearMeters) + " م"));
        
        document.add(table);
        document.add(new Paragraph("\n"));
    }
    
    /**
     * إضافة عنوان الجدول
     */
    private void addTableTitle(Document document, String title) {
        Paragraph tableTitleParagraph = new Paragraph(title)
            .setFontSize(12)
            .setBold()
            .setBackgroundColor(ColorConstants.LIGHT_GRAY)
            .setPadding(5)
            .setTextAlignment(TextAlignment.CENTER);
        
        document.add(tableTitleParagraph);
    }
    
    /**
     * إنشاء خلية رأس
     */
    private Cell createHeaderCell(String text) {
        return new Cell()
            .add(new Paragraph(text))
            .setBackgroundColor(ColorConstants.GRAY)
            .setTextAlignment(TextAlignment.CENTER)
            .setBold()
            .setFontSize(9)
            .setPadding(3);
    }
    
    /**
     * إنشاء خلية بيانات
     */
    private Cell createDataCell(String text) {
        return new Cell()
            .add(new Paragraph(text))
            .setTextAlignment(TextAlignment.CENTER)
            .setFontSize(8)
            .setPadding(2);
    }
    
    /**
     * إنشاء خلية إجماليات
     */
    private Cell createTotalCell(String text) {
        return new Cell()
            .add(new Paragraph(text))
            .setBackgroundColor(ColorConstants.LIGHT_GRAY)
            .setTextAlignment(TextAlignment.CENTER)
            .setBold()
            .setFontSize(9)
            .setPadding(3);
    }
    
    /**
     * إضافة الملاحظات
     */
    private void addNotes(Document document, String notes) {
        Paragraph notesTitle = new Paragraph("ملاحظات:")
            .setFontSize(12)
            .setBold()
            .setBackgroundColor(ColorConstants.LIGHT_GRAY)
            .setPadding(5);
        
        Paragraph notesContent = new Paragraph(notes)
            .setFontSize(10)
            .setPaddingLeft(10)
            .setPaddingTop(5);
        
        document.add(notesTitle);
        document.add(notesContent);
        document.add(new Paragraph("\n"));
    }
    
    /**
     * إضافة تذييل التقرير
     */
    private void addReportFooter(Document document) {
        document.add(new LineSeparator());
        
        Paragraph footer = new Paragraph("تم إنشاء هذا التقرير بواسطة نظام إدارة التصنيع - " + 
                                       java.time.LocalDateTime.now().format(
                                           java.time.format.DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm")))
            .setFontSize(8)
            .setTextAlignment(TextAlignment.CENTER)
            .setFontColor(ColorConstants.GRAY)
            .setPaddingTop(10);
        
        document.add(footer);
    }
    
    /**
     * الحصول على نص الحالة
     */
    private String getStatusText(ManufacturingOrder.OrderStatus status) {
        switch (status) {
            case DRAFT: return "مسودة";
            case CONFIRMED: return "مؤكد";
            case IN_PROGRESS: return "قيد التنفيذ";
            case COMPLETED: return "مكتمل";
            case CANCELLED: return "ملغي";
            default: return status.toString();
        }
    }
}
