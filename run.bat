@echo off
chcp 65001 >nul

:: عرض اللوجو
if exist "logo.txt" (
    type logo.txt
) else (
    echo ========================================
    echo    نظام الحسابات المتكامل
    echo    Integrated Accounting System
    echo ========================================
)
echo.

echo 🔍 التحقق من متطلبات النظام...
echo.

:: فحص Java
echo ⏳ فحص Java...
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Java غير مثبت على النظام
    echo.
    echo 📥 يرجى تثبيت Java 17 أو أحدث من:
    echo    🔗 https://adoptium.net/
    echo.
    echo 💡 تأكد من اختيار "Add to PATH" أثناء التثبيت
    echo.
    echo 🔧 أو جرب تشغيل: check-system.bat للمساعدة
    pause
    exit /b 1
)

echo ✅ Java متاح
java -version 2>&1 | findstr "version"
echo.

:: فحص الملفات المطلوبة
echo ⏳ فحص ملفات المشروع...
if not exist "mvnw.cmd" (
    echo ❌ Maven Wrapper غير موجود
    pause
    exit /b 1
)

if not exist "pom.xml" (
    echo ❌ ملف pom.xml غير موجود
    pause
    exit /b 1
)

echo ✅ ملفات المشروع موجودة
echo.

:: فحص الاتصال
echo ⏳ فحص الاتصال بالإنترنت...
ping -n 1 repo1.maven.org >nul 2>&1
if %errorlevel% neq 0 (
    echo ⚠️  تحذير: لا يوجد اتصال بالإنترنت
    echo    قد يفشل التحميل إذا كانت هذه المرة الأولى
    echo.
) else (
    echo ✅ الاتصال بالإنترنت متاح
    echo.
)

echo 🚀 بدء تشغيل البرنامج...
echo ⏳ يرجى الانتظار، قد يستغرق دقائق في المرة الأولى...
echo.

:: تشغيل البرنامج
call mvnw.cmd clean javafx:run

:: التحقق من نتيجة التشغيل
if %errorlevel% neq 0 (
    echo.
    echo ❌ حدث خطأ أثناء تشغيل البرنامج
    echo.
    echo 🔧 حلول مقترحة:
    echo    1. تأكد من اتصالك بالإنترنت
    echo    2. جرب تشغيل: run-dev.bat للمزيد من التفاصيل
    echo    3. تأكد من Java 17 أو أحدث
    echo    4. أعد تشغيل Command Prompt كمدير
    echo.
    pause
) else (
    echo.
    echo ✅ تم إغلاق البرنامج بنجاح
    echo 👋 شكراً لاستخدام نظام الحسابات المتكامل
)
