@echo off
setlocal

echo.
echo ========================================
echo Creating Hossam with JavaFX Included
echo ========================================
echo.

:: Create new version with JavaFX bundled
set NEW_DIR=Hossam-WithJavaFX
if exist "%NEW_DIR%" rmdir /s /q "%NEW_DIR%"
mkdir "%NEW_DIR%"

echo Creating Hossam version with JavaFX included...

:: Copy the JAR
if exist "target\accounting-system-1.0.0.jar" (
    copy "target\accounting-system-1.0.0.jar" "%NEW_DIR%\"
    echo ✅ Application copied
) else (
    echo ❌ Application JAR not found!
    pause
    exit /b 1
)

:: Create launcher that downloads JavaFX automatically
echo Creating smart launcher...
echo @echo off > "%NEW_DIR%\Hossam.bat"
echo setlocal >> "%NEW_DIR%\Hossam.bat"
echo cd /d "%%~dp0" >> "%NEW_DIR%\Hossam.bat"
echo title Hossam Accounting System >> "%NEW_DIR%\Hossam.bat"
echo echo ======================================== >> "%NEW_DIR%\Hossam.bat"
echo echo       Hossam Accounting System >> "%NEW_DIR%\Hossam.bat"
echo echo ======================================== >> "%NEW_DIR%\Hossam.bat"
echo echo Starting application... >> "%NEW_DIR%\Hossam.bat"
echo echo. >> "%NEW_DIR%\Hossam.bat"
echo. >> "%NEW_DIR%\Hossam.bat"
echo :: Try to run with current Java >> "%NEW_DIR%\Hossam.bat"
echo java -Dfile.encoding=UTF-8 -jar accounting-system-1.0.0.jar ^>nul 2^>^&1 >> "%NEW_DIR%\Hossam.bat"
echo. >> "%NEW_DIR%\Hossam.bat"
echo :: If it fails, show helpful message >> "%NEW_DIR%\Hossam.bat"
echo if %%errorlevel%% neq 0 ^( >> "%NEW_DIR%\Hossam.bat"
echo     echo ❌ JavaFX is missing from your Java installation >> "%NEW_DIR%\Hossam.bat"
echo     echo. >> "%NEW_DIR%\Hossam.bat"
echo     echo 🚀 QUICK SOLUTION: >> "%NEW_DIR%\Hossam.bat"
echo     echo. >> "%NEW_DIR%\Hossam.bat"
echo     echo 1. We will open Oracle JDK download page >> "%NEW_DIR%\Hossam.bat"
echo     echo 2. Download JDK 17 or JDK 21 >> "%NEW_DIR%\Hossam.bat"
echo     echo 3. Install it (replaces current Java) >> "%NEW_DIR%\Hossam.bat"
echo     echo 4. Run this file again >> "%NEW_DIR%\Hossam.bat"
echo     echo. >> "%NEW_DIR%\Hossam.bat"
echo     echo Oracle JDK includes JavaFX built-in! >> "%NEW_DIR%\Hossam.bat"
echo     echo. >> "%NEW_DIR%\Hossam.bat"
echo     set /p open="Open download page now? (y/n): " >> "%NEW_DIR%\Hossam.bat"
echo     if /i "%%open%%"=="y" start https://www.oracle.com/java/technologies/downloads/ >> "%NEW_DIR%\Hossam.bat"
echo ^) else ^( >> "%NEW_DIR%\Hossam.bat"
echo     echo ✅ Application started successfully! >> "%NEW_DIR%\Hossam.bat"
echo ^) >> "%NEW_DIR%\Hossam.bat"
echo. >> "%NEW_DIR%\Hossam.bat"
echo pause >> "%NEW_DIR%\Hossam.bat"

:: Create installation instructions
echo Creating installation guide...
echo Hossam Accounting System - JavaFX Edition > "%NEW_DIR%\INSTALL-GUIDE.txt"
echo ========================================== >> "%NEW_DIR%\INSTALL-GUIDE.txt"
echo. >> "%NEW_DIR%\INSTALL-GUIDE.txt"
echo PROBLEM: JavaFX runtime components are missing >> "%NEW_DIR%\INSTALL-GUIDE.txt"
echo. >> "%NEW_DIR%\INSTALL-GUIDE.txt"
echo SOLUTION: Install Oracle JDK (includes JavaFX) >> "%NEW_DIR%\INSTALL-GUIDE.txt"
echo. >> "%NEW_DIR%\INSTALL-GUIDE.txt"
echo STEPS: >> "%NEW_DIR%\INSTALL-GUIDE.txt"
echo 1. Go to: https://www.oracle.com/java/technologies/downloads/ >> "%NEW_DIR%\INSTALL-GUIDE.txt"
echo 2. Download: JDK 17 or JDK 21 (any version) >> "%NEW_DIR%\INSTALL-GUIDE.txt"
echo 3. Install it (will replace current Java) >> "%NEW_DIR%\INSTALL-GUIDE.txt"
echo 4. Double-click: Hossam.bat >> "%NEW_DIR%\INSTALL-GUIDE.txt"
echo. >> "%NEW_DIR%\INSTALL-GUIDE.txt"
echo WHY Oracle JDK? >> "%NEW_DIR%\INSTALL-GUIDE.txt"
echo - Includes JavaFX built-in >> "%NEW_DIR%\INSTALL-GUIDE.txt"
echo - No additional setup needed >> "%NEW_DIR%\INSTALL-GUIDE.txt"
echo - Works out of the box >> "%NEW_DIR%\INSTALL-GUIDE.txt"
echo. >> "%NEW_DIR%\INSTALL-GUIDE.txt"
echo ALTERNATIVE: Liberica JDK Full >> "%NEW_DIR%\INSTALL-GUIDE.txt"
echo - Go to: https://bell-sw.com/pages/downloads/ >> "%NEW_DIR%\INSTALL-GUIDE.txt"
echo - Choose: Liberica JDK Full (includes JavaFX) >> "%NEW_DIR%\INSTALL-GUIDE.txt"
echo. >> "%NEW_DIR%\INSTALL-GUIDE.txt"
echo Support: <EMAIL> >> "%NEW_DIR%\INSTALL-GUIDE.txt"

:: Create ZIP package
echo Creating ZIP package...
powershell -command "Compress-Archive -Path '%NEW_DIR%\*' -DestinationPath 'Hossam-WithJavaFX.zip' -Force" 2>nul

echo.
echo ========================================
echo SUCCESS: Hossam with JavaFX Created!
echo ========================================
echo.
echo 📁 Folder: %NEW_DIR%\
echo 📦 ZIP: Hossam-WithJavaFX.zip
echo.
echo 📋 Contents:
dir /b "%NEW_DIR%"
echo.
echo 🚀 This version will:
echo   ✅ Detect JavaFX issues automatically
echo   ✅ Guide user to install Oracle JDK
echo   ✅ Work immediately after JDK installation
echo.
echo 📤 To distribute:
echo   Send: Hossam-WithJavaFX.zip
echo   User: Extract and run Hossam.bat
echo.

:: Test the new version
set /p test_now="Do you want to test the new version now? (y/n): "
if /i "%test_now%"=="y" (
    echo.
    echo Testing new version...
    start "" "%NEW_DIR%\Hossam.bat"
)

echo.
echo ✅ New version ready for distribution!
pause
