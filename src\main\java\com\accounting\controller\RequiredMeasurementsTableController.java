package com.accounting.controller;

import com.accounting.model.RequiredMeasurement;
import javafx.geometry.Insets;
import javafx.geometry.Pos;
import javafx.scene.control.*;
import javafx.scene.control.cell.PropertyValueFactory;
import javafx.scene.control.cell.TextFieldTableCell;
import javafx.scene.layout.*;
import javafx.collections.ObservableList;
import javafx.util.converter.DoubleStringConverter;
import javafx.util.converter.IntegerStringConverter;
import de.jensd.fx.glyphs.fontawesome.FontAwesomeIcon;
import de.jensd.fx.glyphs.fontawesome.FontAwesomeIconView;
import java.text.DecimalFormat;

/**
 * كنترولر جدول المقاسات المطلوبة التفاعلي
 */
public class RequiredMeasurementsTableController {
    
    private final ObservableList<RequiredMeasurement> requiredMeasurements;
    private final DecimalFormat decimalFormat = new DecimalFormat("#,##0.00");
    private TableView<RequiredMeasurement> table;
    private Label totalCountLabel;
    private Label totalSquareMetersLabel;
    private Label totalLinearMetersLabel;
    
    public RequiredMeasurementsTableController(ObservableList<RequiredMeasurement> items) {
        this.requiredMeasurements = items;
    }
    
    /**
     * إنشاء جدول المقاسات المطلوبة التفاعلي
     */
    public VBox createRequiredMeasurementsTable() {
        VBox container = new VBox(15);
        container.setPadding(new Insets(15));
        container.setFillWidth(true);
        VBox.setVgrow(container, Priority.ALWAYS);
        
        // العنوان
        Label title = new Label("📏 جدول المقاسات المطلوبة (أساسي)");
        title.getStyleClass().add("section-title");
        
        // شريط الأدوات
        HBox toolbar = createToolbar();
        
        // الجدول
        table = createTable();
        table.setMinHeight(350);
        table.setPrefHeight(500);
        table.setColumnResizePolicy(TableView.CONSTRAINED_RESIZE_POLICY);
        VBox.setVgrow(table, Priority.ALWAYS);
        
        // وضع الجدول في ScrollPane
        ScrollPane scrollPane = new ScrollPane(table);
        scrollPane.setFitToWidth(true);
        scrollPane.setVbarPolicy(ScrollPane.ScrollBarPolicy.AS_NEEDED);
        scrollPane.setHbarPolicy(ScrollPane.ScrollBarPolicy.NEVER);
        scrollPane.setPrefHeight(500);
        VBox.setVgrow(scrollPane, Priority.ALWAYS);
        
        // الإجماليات
        HBox totalsBox = createTotalsBox();
        
        container.getChildren().addAll(title, toolbar, scrollPane, totalsBox);
        return container;
    }
    
    /**
     * إنشاء شريط الأدوات
     */
    private HBox createToolbar() {
        HBox toolbar = new HBox(12);
        toolbar.setAlignment(Pos.CENTER_LEFT);
        toolbar.setPadding(new Insets(10));
        toolbar.getStyleClass().add("toolbar");
        
        Button addRowBtn = new Button("إضافة صف");
        FontAwesomeIconView addIcon = new FontAwesomeIconView(FontAwesomeIcon.PLUS_CIRCLE);
        addIcon.setSize("16px");
        addIcon.setStyle("-fx-fill: #27ae60;");
        addRowBtn.setGraphic(addIcon);
        addRowBtn.getStyleClass().add("add-button");
        addRowBtn.setStyle("-fx-background-radius: 20; -fx-font-weight: bold; -fx-background-color: #eafaf1;");
        addRowBtn.setTooltip(new Tooltip("إضافة صف جديد"));
        addRowBtn.setOnAction(e -> addNewRow());
        
        Button deleteRowBtn = new Button("حذف صف");
        FontAwesomeIconView deleteIcon = new FontAwesomeIconView(FontAwesomeIcon.TRASH);
        deleteIcon.setSize("16px");
        deleteIcon.setStyle("-fx-fill: #e74c3c;");
        deleteRowBtn.setGraphic(deleteIcon);
        deleteRowBtn.getStyleClass().add("delete-button");
        deleteRowBtn.setStyle("-fx-background-radius: 20; -fx-background-color: #fdecea;");
        deleteRowBtn.setTooltip(new Tooltip("حذف الصف المحدد"));
        deleteRowBtn.setOnAction(e -> deleteSelectedRow());
        
        Button clearAllBtn = new Button("مسح الكل");
        FontAwesomeIconView clearIcon = new FontAwesomeIconView(FontAwesomeIcon.ERASER);
        clearIcon.setSize("16px");
        clearIcon.setStyle("-fx-fill: #f39c12;");
        clearAllBtn.setGraphic(clearIcon);
        clearAllBtn.getStyleClass().add("unpost-button");
        clearAllBtn.setStyle("-fx-background-radius: 20; -fx-background-color: #fff6e3;");
        clearAllBtn.setTooltip(new Tooltip("مسح كل الصفوف"));
        clearAllBtn.setOnAction(e -> clearAllRows());
        
        toolbar.getChildren().addAll(addRowBtn, deleteRowBtn, clearAllBtn);
        return toolbar;
    }
    
    /**
     * إنشاء الجدول
     */
    private TableView<RequiredMeasurement> createTable() {
        TableView<RequiredMeasurement> tableView = new TableView<>();
        tableView.setEditable(true);
        tableView.getStyleClass().add("required-measurements-table");
        tableView.setMinHeight(350);
        tableView.setPrefHeight(500);
        tableView.setColumnResizePolicy(TableView.CONSTRAINED_RESIZE_POLICY);
        VBox.setVgrow(tableView, Priority.ALWAYS);
        
        // عمود الترقيم
        TableColumn<RequiredMeasurement, Integer> indexCol = new TableColumn<>("ت");
        indexCol.setCellValueFactory(cellData -> {
            int index = tableView.getItems().indexOf(cellData.getValue()) + 1;
            return new javafx.beans.property.SimpleIntegerProperty(index).asObject();
        });
        indexCol.setPrefWidth(40);
        indexCol.setSortable(false);
        
        // عمود الوصف
        TableColumn<RequiredMeasurement, String> descCol = new TableColumn<>("الوصف");
        descCol.setCellValueFactory(new PropertyValueFactory<>("description"));
        descCol.setCellFactory(TextFieldTableCell.forTableColumn());
        descCol.setOnEditCommit(event -> {
            event.getRowValue().setDescription(event.getNewValue());
            updateTotals();
        });
        descCol.setPrefWidth(150);
        
        // عمود الطول
        TableColumn<RequiredMeasurement, Double> lengthCol = new TableColumn<>("طول (مم)");
        lengthCol.setCellValueFactory(new PropertyValueFactory<>("length"));
        lengthCol.setCellFactory(TextFieldTableCell.forTableColumn(new DoubleStringConverter()));
        lengthCol.setOnEditCommit(event -> {
            event.getRowValue().setLength(event.getNewValue());
            updateTotals();
        });
        lengthCol.setPrefWidth(100);
        
        // عمود العرض
        TableColumn<RequiredMeasurement, Double> widthCol = new TableColumn<>("عرض (مم)");
        widthCol.setCellValueFactory(new PropertyValueFactory<>("width"));
        widthCol.setCellFactory(TextFieldTableCell.forTableColumn(new DoubleStringConverter()));
        widthCol.setOnEditCommit(event -> {
            event.getRowValue().setWidth(event.getNewValue());
            updateTotals();
        });
        widthCol.setPrefWidth(100);
        
        // عمود المتر المربع
        TableColumn<RequiredMeasurement, Double> squareMetersCol = new TableColumn<>("متر مربع");
        squareMetersCol.setCellValueFactory(new PropertyValueFactory<>("squareMeters"));
        squareMetersCol.setCellFactory(column -> new TableCell<RequiredMeasurement, Double>() {
            @Override
            protected void updateItem(Double item, boolean empty) {
                super.updateItem(item, empty);
                if (empty || item == null) {
                    setText(null);
                } else {
                    setText(decimalFormat.format(item));
                }
            }
        });
        squareMetersCol.setPrefWidth(100);
        squareMetersCol.setEditable(false);
        
        // عمود المتر الطولي
        TableColumn<RequiredMeasurement, Double> linearMetersCol = new TableColumn<>("متر طولي");
        linearMetersCol.setCellValueFactory(new PropertyValueFactory<>("linearMeters"));
        linearMetersCol.setCellFactory(column -> new TableCell<RequiredMeasurement, Double>() {
            @Override
            protected void updateItem(Double item, boolean empty) {
                super.updateItem(item, empty);
                if (empty || item == null) {
                    setText(null);
                } else {
                    setText(decimalFormat.format(item));
                }
            }
        });
        linearMetersCol.setPrefWidth(100);
        linearMetersCol.setEditable(false);
        
        // عمود العدد
        TableColumn<RequiredMeasurement, Integer> countCol = new TableColumn<>("عدد");
        countCol.setCellValueFactory(new PropertyValueFactory<>("count"));
        countCol.setCellFactory(TextFieldTableCell.forTableColumn(new IntegerStringConverter()));
        countCol.setOnEditCommit(event -> {
            event.getRowValue().setCount(event.getNewValue());
            updateTotals();
        });
        countCol.setPrefWidth(80);
        
        // عمود إجمالي المتر المربع
        TableColumn<RequiredMeasurement, Double> totalSquareMetersCol = new TableColumn<>("إجمالي متر مربع");
        totalSquareMetersCol.setCellValueFactory(new PropertyValueFactory<>("totalSquareMeters"));
        totalSquareMetersCol.setCellFactory(column -> new TableCell<RequiredMeasurement, Double>() {
            @Override
            protected void updateItem(Double item, boolean empty) {
                super.updateItem(item, empty);
                if (empty || item == null) {
                    setText(null);
                } else {
                    setText(decimalFormat.format(item));
                }
            }
        });
        totalSquareMetersCol.setPrefWidth(120);
        totalSquareMetersCol.setEditable(false);
        
        // عمود إجمالي المتر الطولي
        TableColumn<RequiredMeasurement, Double> totalLinearMetersCol = new TableColumn<>("إجمالي متر طولي");
        totalLinearMetersCol.setCellValueFactory(new PropertyValueFactory<>("totalLinearMeters"));
        totalLinearMetersCol.setCellFactory(column -> new TableCell<RequiredMeasurement, Double>() {
            @Override
            protected void updateItem(Double item, boolean empty) {
                super.updateItem(item, empty);
                if (empty || item == null) {
                    setText(null);
                } else {
                    setText(decimalFormat.format(item));
                }
            }
        });
        totalLinearMetersCol.setPrefWidth(120);
        totalLinearMetersCol.setEditable(false);
        
        // إضافة الأعمدة
        tableView.getColumns().addAll(
            indexCol, descCol, lengthCol, widthCol, 
            squareMetersCol, linearMetersCol, countCol, 
            totalSquareMetersCol, totalLinearMetersCol
        );
        
        // ربط البيانات
        tableView.setItems(requiredMeasurements);
        
        // إضافة مستمع للتحديث التلقائي للترقيم
        requiredMeasurements.addListener((javafx.collections.ListChangeListener<RequiredMeasurement>) change -> {
            tableView.refresh();
            updateTotals();
        });
        
        return tableView;
    }
    
    /**
     * إنشاء صندوق الإجماليات
     */
    private HBox createTotalsBox() {
        HBox totalsBox = new HBox(30);
        totalsBox.setAlignment(Pos.CENTER);
        totalsBox.setPadding(new Insets(15));
        totalsBox.getStyleClass().add("totals-container");
        
        // إجمالي العدد
        VBox totalCountBox = new VBox(5);
        totalCountBox.setAlignment(Pos.CENTER);
        Label totalCountTitleLabel = new Label("إجمالي العدد");
        totalCountTitleLabel.getStyleClass().add("totals-title");
        totalCountLabel = new Label("0");
        totalCountLabel.getStyleClass().add("totals-value");
        totalCountBox.getChildren().addAll(totalCountTitleLabel, totalCountLabel);
        
        // إجمالي المتر المربع
        VBox totalSquareMetersBox = new VBox(5);
        totalSquareMetersBox.setAlignment(Pos.CENTER);
        Label totalSquareMetersTitleLabel = new Label("إجمالي المتر المربع");
        totalSquareMetersTitleLabel.getStyleClass().add("totals-title");
        totalSquareMetersLabel = new Label("0.00");
        totalSquareMetersLabel.getStyleClass().add("totals-value");
        totalSquareMetersBox.getChildren().addAll(totalSquareMetersTitleLabel, totalSquareMetersLabel);
        
        // إجمالي المتر الطولي
        VBox totalLinearMetersBox = new VBox(5);
        totalLinearMetersBox.setAlignment(Pos.CENTER);
        Label totalLinearMetersTitleLabel = new Label("إجمالي المتر الطولي");
        totalLinearMetersTitleLabel.getStyleClass().add("totals-title");
        totalLinearMetersLabel = new Label("0.00");
        totalLinearMetersLabel.getStyleClass().add("totals-value");
        totalLinearMetersBox.getChildren().addAll(totalLinearMetersTitleLabel, totalLinearMetersLabel);
        
        totalsBox.getChildren().addAll(totalCountBox, totalSquareMetersBox, totalLinearMetersBox);
        return totalsBox;
    }
    
    /**
     * إضافة صف جديد
     */
    private void addNewRow() {
        RequiredMeasurement newItem = new RequiredMeasurement();
        newItem.setDescription("مقاس جديد");
        newItem.setLength(1000.0);
        newItem.setWidth(1000.0);
        newItem.setCount(1);
        requiredMeasurements.add(newItem);
        
        // تحديد الصف الجديد
        table.getSelectionModel().selectLast();
        table.scrollTo(requiredMeasurements.size() - 1);
    }
    
    /**
     * حذف الصف المحدد
     */
    private void deleteSelectedRow() {
        RequiredMeasurement selectedItem = table.getSelectionModel().getSelectedItem();
        if (selectedItem != null) {
            Alert confirmAlert = new Alert(Alert.AlertType.CONFIRMATION);
            confirmAlert.setTitle("تأكيد الحذف");
            confirmAlert.setHeaderText("حذف صف");
            confirmAlert.setContentText("هل أنت متأكد من حذف هذا الصف؟");
            
            confirmAlert.showAndWait().ifPresent(response -> {
                if (response == ButtonType.OK) {
                    requiredMeasurements.remove(selectedItem);
                }
            });
        } else {
            showWarningAlert("يرجى اختيار صف للحذف.");
        }
    }
    
    /**
     * مسح جميع الصفوف
     */
    private void clearAllRows() {
        if (!requiredMeasurements.isEmpty()) {
            Alert confirmAlert = new Alert(Alert.AlertType.CONFIRMATION);
            confirmAlert.setTitle("تأكيد المسح");
            confirmAlert.setHeaderText("مسح جميع الصفوف");
            confirmAlert.setContentText("هل أنت متأكد من مسح جميع الصفوف؟");
            
            confirmAlert.showAndWait().ifPresent(response -> {
                if (response == ButtonType.OK) {
                    requiredMeasurements.clear();
                }
            });
        }
    }
    
    /**
     * تحديث الإجماليات
     */
    private void updateTotals() {
        int totalCount = requiredMeasurements.stream()
                .mapToInt(RequiredMeasurement::getCount)
                .sum();
        
        double totalSquareMeters = requiredMeasurements.stream()
                .mapToDouble(RequiredMeasurement::getTotalSquareMeters)
                .sum();
        
        double totalLinearMeters = requiredMeasurements.stream()
                .mapToDouble(RequiredMeasurement::getTotalLinearMeters)
                .sum();
        
        totalCountLabel.setText(String.valueOf(totalCount));
        totalSquareMetersLabel.setText(decimalFormat.format(totalSquareMeters) + " م²");
        totalLinearMetersLabel.setText(decimalFormat.format(totalLinearMeters) + " م");
    }
    
    /**
     * الحصول على الجدول
     */
    public TableView<RequiredMeasurement> getTable() {
        return table;
    }
    
    /**
     * تحديث العرض
     */
    public void refresh() {
        if (table != null) {
            table.refresh();
            updateTotals();
        }
    }
    
    /**
     * عرض رسالة تحذير
     */
    private void showWarningAlert(String message) {
        Alert alert = new Alert(Alert.AlertType.WARNING);
        alert.setTitle("تحذير");
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }
}
