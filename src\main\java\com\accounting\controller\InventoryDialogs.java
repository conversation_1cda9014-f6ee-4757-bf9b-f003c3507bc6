package com.accounting.controller;

import com.accounting.model.*;
import javafx.collections.ObservableList;
import javafx.geometry.Insets;
import javafx.geometry.Pos;
import javafx.scene.Scene;
import javafx.scene.control.*;
import javafx.scene.layout.*;
import javafx.stage.Modality;
import javafx.stage.Stage;
import de.jensd.fx.glyphs.fontawesome.FontAwesomeIcon;
import de.jensd.fx.glyphs.fontawesome.FontAwesomeIconView;

import java.text.DecimalFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * نوافذ الحوار لموديول المخازن
 * Inventory Dialogs
 */
public class InventoryDialogs {
    
    private DecimalFormat decimalFormat = new DecimalFormat("#,##0.00");
    
    /**
     * نافذة إضافة/تعديل صنف
     */
    public void showItemDialog(Item item, ObservableList<Warehouse> warehouses, ObservableList<Item> items) {
        boolean isEdit = (item != null);
        
        Stage dialog = new Stage();
        dialog.setTitle(isEdit ? "تعديل صنف" : "إضافة صنف جديد");
        dialog.initModality(Modality.APPLICATION_MODAL);
        dialog.setWidth(600);
        dialog.setHeight(700);
        
        VBox mainLayout = new VBox(20);
        mainLayout.setPadding(new Insets(20));
        
        // العنوان
        Label titleLabel = new Label(isEdit ? "تعديل بيانات الصنف" : "إضافة صنف جديد");
        titleLabel.getStyleClass().add("dialog-title");
        
        // نموذج البيانات
        GridPane form = new GridPane();
        form.setHgap(15);
        form.setVgap(15);
        
        // كود الصنف
        Label idLabel = new Label("كود الصنف:");
        TextField idField = new TextField();
        idField.setPromptText("GL001");
        if (isEdit) {
            idField.setText(item.getItemId());
            idField.setDisable(true);
        }
        
        // اسم الصنف
        Label nameLabel = new Label("اسم الصنف:");
        TextField nameField = new TextField();
        nameField.setPromptText("أدخل اسم الصنف");
        if (isEdit) nameField.setText(item.getName());
        
        // الفئة
        Label categoryLabel = new Label("الفئة:");
        ComboBox<String> categoryCombo = new ComboBox<>();
        categoryCombo.getItems().addAll("زجاج", "ألومنيوم", "أكسسوارات", "مواد كيميائية", "مواد خام", "أخرى");
        categoryCombo.setEditable(true);
        if (isEdit) categoryCombo.setValue(item.getCategory());
        
        // وحدة القياس
        Label unitLabel = new Label("وحدة القياس:");
        ComboBox<Unit> unitCombo = new ComboBox<>();
        unitCombo.getItems().addAll(Unit.values());
        if (isEdit) unitCombo.setValue(item.getUnit());
        
        // المخزن
        Label warehouseLabel = new Label("المخزن:");
        ComboBox<Warehouse> warehouseCombo = new ComboBox<>();
        warehouseCombo.setItems(warehouses);
        if (isEdit) {
            warehouseCombo.setValue(warehouses.stream()
                .filter(w -> w.getWarehouseId().equals(item.getWarehouseId()))
                .findFirst().orElse(null));
        }
        
        // الكمية الحالية
        Label quantityLabel = new Label("الكمية الحالية:");
        TextField quantityField = new TextField();
        quantityField.setPromptText("0.00");
        if (isEdit) quantityField.setText(String.valueOf(item.getCurrentQuantity()));
        
        // سعر الوحدة
        Label priceLabel = new Label("سعر الوحدة:");
        TextField priceField = new TextField();
        priceField.setPromptText("0.00");
        if (isEdit) priceField.setText(String.valueOf(item.getUnitPrice()));
        
        // المورد
        Label supplierLabel = new Label("المورد:");
        TextField supplierField = new TextField();
        supplierField.setPromptText("اسم المورد");
        if (isEdit) supplierField.setText(item.getSupplier());

        // خيار الأبعاد
        CheckBox hasDimensionsCheckBox = new CheckBox("الصنف له أبعاد (طول × عرض)");
        hasDimensionsCheckBox.getStyleClass().add("dimensions-checkbox");
        if (isEdit) hasDimensionsCheckBox.setSelected(item.hasDimensions());
        
        // خصائص الأبعاد
        VBox dimensionsBox = new VBox(10);
        dimensionsBox.setVisible(false);

        Label dimensionsLabel = new Label("خصائص الأبعاد:");
        dimensionsLabel.getStyleClass().add("section-title");
        
        GridPane dimensionsForm = new GridPane();
        dimensionsForm.setHgap(10);
        dimensionsForm.setVgap(10);

        Label lengthLabel = new Label("الطول (مم):");
        TextField lengthField = new TextField();
        lengthField.setPromptText("1000");

        Label widthLabel = new Label("العرض (مم):");
        TextField widthField = new TextField();
        widthField.setPromptText("500");

        Label piecesLabel = new Label("العدد:");
        TextField piecesField = new TextField();
        piecesField.setPromptText("1");

        Label calculatedAreaLabel = new Label("المساحة المحسوبة (م²):");
        TextField calculatedAreaField = new TextField();
        calculatedAreaField.setPromptText("0.00");
        calculatedAreaField.setDisable(true);
        calculatedAreaField.getStyleClass().add("calculated-field");

        // حساب المساحة تلقائياً عند تغيير القيم
        Runnable calculateArea = () -> {
            try {
                double length = lengthField.getText().isEmpty() ? 0 : Double.parseDouble(lengthField.getText());
                double width = widthField.getText().isEmpty() ? 0 : Double.parseDouble(widthField.getText());
                int pieces = piecesField.getText().isEmpty() ? 1 : Integer.parseInt(piecesField.getText());

                double area = (length / 1000.0) * (width / 1000.0) * pieces;
                calculatedAreaField.setText(decimalFormat.format(area));
            } catch (NumberFormatException e) {
                calculatedAreaField.setText("0.00");
            }
        };

        lengthField.textProperty().addListener((obs, oldVal, newVal) -> calculateArea.run());
        widthField.textProperty().addListener((obs, oldVal, newVal) -> calculateArea.run());
        piecesField.textProperty().addListener((obs, oldVal, newVal) -> calculateArea.run());

        // خصائص إضافية
        Label thicknessLabel = new Label("السمك (مم):");
        TextField thicknessField = new TextField();
        thicknessField.setPromptText("6");

        Label colorLabel = new Label("اللون:");
        ComboBox<String> colorCombo = new ComboBox<>();
        colorCombo.getItems().addAll("شفاف", "أبيض", "أزرق", "أخضر", "رمادي", "بني", "أسود");
        colorCombo.setEditable(true);

        Label typeLabel = new Label("النوع:");
        ComboBox<String> typeCombo = new ComboBox<>();
        typeCombo.getItems().addAll("عادي", "مقسى", "مصفح", "عاكس", "ملون", "مزخرف");
        typeCombo.setEditable(true);

        dimensionsForm.add(lengthLabel, 0, 0);
        dimensionsForm.add(lengthField, 1, 0);
        dimensionsForm.add(widthLabel, 2, 0);
        dimensionsForm.add(widthField, 3, 0);
        dimensionsForm.add(piecesLabel, 0, 1);
        dimensionsForm.add(piecesField, 1, 1);
        dimensionsForm.add(calculatedAreaLabel, 2, 1);
        dimensionsForm.add(calculatedAreaField, 3, 1);
        dimensionsForm.add(thicknessLabel, 0, 2);
        dimensionsForm.add(thicknessField, 1, 2);
        dimensionsForm.add(colorLabel, 2, 2);
        dimensionsForm.add(colorCombo, 3, 2);
        dimensionsForm.add(typeLabel, 0, 3);
        dimensionsForm.add(typeCombo, 1, 3);

        dimensionsBox.getChildren().addAll(dimensionsLabel, dimensionsForm);
        
        // إظهار/إخفاء خصائص الأبعاد حسب الخيار المحدد
        hasDimensionsCheckBox.setOnAction(e -> {
            boolean showDimensions = hasDimensionsCheckBox.isSelected();
            dimensionsBox.setVisible(showDimensions);

            // تعطيل/تفعيل حقل الكمية حسب نوع الصنف
            quantityField.setDisable(showDimensions);
            if (showDimensions) {
                quantityField.setPromptText("محسوبة تلقائياً من الأبعاد");
            } else {
                quantityField.setPromptText("0.00");
            }
        });

        // ملء البيانات في حالة التعديل
        if (isEdit) {
            if (item.hasDimensions()) {
                hasDimensionsCheckBox.setSelected(true);
                dimensionsBox.setVisible(true);
                quantityField.setDisable(true);
                quantityField.setPromptText("محسوبة تلقائياً من الأبعاد");

                if (item.getLength() != null) lengthField.setText(String.valueOf(item.getLength()));
                if (item.getWidth() != null) widthField.setText(String.valueOf(item.getWidth()));
                if (item.getPieces() != null) piecesField.setText(String.valueOf(item.getPieces()));
                if (item.getThickness() != null) thicknessField.setText(String.valueOf(item.getThickness()));
                if (item.getColor() != null) colorCombo.setValue(item.getColor());
                if (item.getType() != null) typeCombo.setValue(item.getType());

                // حساب المساحة
                calculateArea.run();
            }
        }
        
        // الملاحظات
        Label notesLabel = new Label("الملاحظات:");
        TextArea notesArea = new TextArea();
        notesArea.setPromptText("ملاحظات اختيارية");
        notesArea.setPrefRowCount(3);
        if (isEdit) notesArea.setText(item.getNotes());
        
        // ترتيب الحقول
        form.add(idLabel, 0, 0);
        form.add(idField, 1, 0);
        form.add(nameLabel, 0, 1);
        form.add(nameField, 1, 1);
        form.add(categoryLabel, 0, 2);
        form.add(categoryCombo, 1, 2);
        form.add(unitLabel, 0, 3);
        form.add(unitCombo, 1, 3);
        form.add(warehouseLabel, 0, 4);
        form.add(warehouseCombo, 1, 4);
        form.add(quantityLabel, 0, 5);
        form.add(quantityField, 1, 5);
        form.add(priceLabel, 0, 6);
        form.add(priceField, 1, 6);
        form.add(supplierLabel, 0, 7);
        form.add(supplierField, 1, 7);
        form.add(hasDimensionsCheckBox, 0, 8);
        GridPane.setColumnSpan(hasDimensionsCheckBox, 2);
        form.add(notesLabel, 0, 9);
        form.add(notesArea, 1, 9);
        
        // أزرار الحوار
        HBox buttonBox = new HBox(10);
        buttonBox.setAlignment(Pos.CENTER_RIGHT);
        
        Button saveButton = new Button(isEdit ? "حفظ التعديلات" : "إضافة الصنف");
        saveButton.getStyleClass().add("add-button");
        saveButton.setOnAction(e -> {
            if (validateItemForm(nameField, categoryCombo, unitCombo, warehouseCombo)) {
                try {
                    if (isEdit) {
                        // تحديث الصنف الموجود
                        item.setName(nameField.getText().trim());
                        item.setCategory(categoryCombo.getValue());
                        item.setUnit(unitCombo.getValue());
                        item.setWarehouseId(warehouseCombo.getValue().getWarehouseId());
                        item.setWarehouseName(warehouseCombo.getValue().getName());
                        item.setUnitPrice(Double.parseDouble(priceField.getText().trim()));
                        item.setSupplier(supplierField.getText().trim());
                        item.setNotes(notesArea.getText().trim());
                        item.setLastUpdated(LocalDate.now());

                        // تحديث خاصية الأبعاد
                        item.setHasDimensions(hasDimensionsCheckBox.isSelected());

                        if (hasDimensionsCheckBox.isSelected()) {
                            // للأصناف ذات الأبعاد
                            if (!lengthField.getText().trim().isEmpty()) {
                                item.setLength(Double.parseDouble(lengthField.getText().trim()));
                            }
                            if (!widthField.getText().trim().isEmpty()) {
                                item.setWidth(Double.parseDouble(widthField.getText().trim()));
                            }
                            if (!piecesField.getText().trim().isEmpty()) {
                                item.setPieces(Integer.parseInt(piecesField.getText().trim()));
                            }
                            // الكمية محسوبة من المساحة
                            item.setCurrentQuantity(item.calculateArea());
                        } else {
                            // للأصناف العادية
                            item.setCurrentQuantity(Double.parseDouble(quantityField.getText().trim()));
                        }

                        // تحديث الخصائص الإضافية
                        if (!thicknessField.getText().trim().isEmpty()) {
                            item.setThickness(Double.parseDouble(thicknessField.getText().trim()));
                        }
                        item.setColor(colorCombo.getValue());
                        item.setType(typeCombo.getValue());
                        
                        showPlaceholderDialog("نجح", "تم تحديث بيانات الصنف بنجاح");
                    } else {
                        // إضافة صنف جديد
                        String itemId = idField.getText().trim();
                        if (itemId.isEmpty()) {
                            itemId = generateItemId(categoryCombo.getValue());
                        }
                        
                        Item newItem = new Item(
                            itemId,
                            nameField.getText().trim(),
                            categoryCombo.getValue(),
                            unitCombo.getValue(),
                            warehouseCombo.getValue().getWarehouseId()
                        );
                        
                        newItem.setWarehouseName(warehouseCombo.getValue().getName());
                        newItem.setUnitPrice(Double.parseDouble(priceField.getText().trim()));
                        newItem.setSupplier(supplierField.getText().trim());
                        newItem.setNotes(notesArea.getText().trim());

                        // تحديد نوع الصنف
                        newItem.setHasDimensions(hasDimensionsCheckBox.isSelected());

                        if (hasDimensionsCheckBox.isSelected()) {
                            // للأصناف ذات الأبعاد
                            if (!lengthField.getText().trim().isEmpty()) {
                                newItem.setLength(Double.parseDouble(lengthField.getText().trim()));
                            }
                            if (!widthField.getText().trim().isEmpty()) {
                                newItem.setWidth(Double.parseDouble(widthField.getText().trim()));
                            }
                            if (!piecesField.getText().trim().isEmpty()) {
                                newItem.setPieces(Integer.parseInt(piecesField.getText().trim()));
                            }
                            // الكمية محسوبة من المساحة
                            newItem.setCurrentQuantity(newItem.calculateArea());
                        } else {
                            // للأصناف العادية
                            newItem.setCurrentQuantity(Double.parseDouble(quantityField.getText().trim()));
                        }

                        // إضافة الخصائص الإضافية
                        if (!thicknessField.getText().trim().isEmpty()) {
                            newItem.setThickness(Double.parseDouble(thicknessField.getText().trim()));
                        }
                        newItem.setColor(colorCombo.getValue());
                        newItem.setType(typeCombo.getValue());
                        
                        items.add(newItem);
                        showPlaceholderDialog("نجح", "تم إضافة الصنف بنجاح");
                    }
                    
                    dialog.close();
                } catch (NumberFormatException ex) {
                    showPlaceholderDialog("خطأ", "يرجى إدخال قيم صحيحة للأرقام");
                }
            }
        });
        
        Button cancelButton = new Button("إلغاء");
        cancelButton.setOnAction(e -> dialog.close());
        
        buttonBox.getChildren().addAll(saveButton, cancelButton);
        
        ScrollPane scrollPane = new ScrollPane();
        VBox contentBox = new VBox(15);
        contentBox.getChildren().addAll(titleLabel, form, dimensionsBox, buttonBox);
        scrollPane.setContent(contentBox);
        scrollPane.setFitToWidth(true);

        mainLayout.getChildren().add(scrollPane);
        
        Scene scene = new Scene(mainLayout);
        scene.getStylesheets().add(getClass().getResource("/css/styles.css").toExternalForm());
        dialog.setScene(scene);
        dialog.showAndWait();
    }
    
    /**
     * توليد كود صنف تلقائي
     */
    private String generateItemId(String category) {
        String prefix;
        switch (category) {
            case "زجاج": prefix = "GL"; break;
            case "ألومنيوم": prefix = "AL"; break;
            case "أكسسوارات": prefix = "AC"; break;
            case "مواد كيميائية": prefix = "CH"; break;
            default: prefix = "IT"; break;
        }
        return prefix + String.format("%03d", (int)(Math.random() * 1000));
    }
    
    /**
     * التحقق من صحة نموذج الصنف
     */
    private boolean validateItemForm(TextField nameField, ComboBox<String> categoryCombo, 
                                   ComboBox<Unit> unitCombo, ComboBox<Warehouse> warehouseCombo) {
        if (nameField.getText().trim().isEmpty()) {
            showPlaceholderDialog("تحذير", "يرجى إدخال اسم الصنف");
            return false;
        }
        
        if (categoryCombo.getValue() == null || categoryCombo.getValue().trim().isEmpty()) {
            showPlaceholderDialog("تحذير", "يرجى اختيار فئة الصنف");
            return false;
        }
        
        if (unitCombo.getValue() == null) {
            showPlaceholderDialog("تحذير", "يرجى اختيار وحدة القياس");
            return false;
        }
        
        if (warehouseCombo.getValue() == null) {
            showPlaceholderDialog("تحذير", "يرجى اختيار المخزن");
            return false;
        }
        
        return true;
    }
    
    private void showPlaceholderDialog(String title, String message) {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle(title);
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }
}
