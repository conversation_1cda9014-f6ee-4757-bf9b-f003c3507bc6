package com.accounting.model;

import javafx.beans.property.*;
import java.util.UUID;

/**
 * نموذج عنصر دبل جلاس (خامات)
 */
public class DoubleGlassItem {
    
    private final StringProperty itemId = new SimpleStringProperty();
    private final StringProperty description = new SimpleStringProperty();
    private final IntegerProperty totalCount = new SimpleIntegerProperty(); // إجمالي العدد
    private final DoubleProperty totalQuantity = new SimpleDoubleProperty(); // إجمالي الكمية
    private final StringProperty unit = new SimpleStringProperty(); // الوحدة (متر، كيلو، قطعة، إلخ)
    private final StringProperty notes = new SimpleStringProperty();
    
    /**
     * المنشئ الافتراضي
     */
    public DoubleGlassItem() {
        this.itemId.set(UUID.randomUUID().toString());
        this.unit.set("قطعة"); // الوحدة الافتراضية
    }
    
    /**
     * منشئ مع المعاملات
     */
    public DoubleGlassItem(String description, int totalCount, double totalQuantity, String unit) {
        this();
        this.description.set(description);
        this.totalCount.set(totalCount);
        this.totalQuantity.set(totalQuantity);
        this.unit.set(unit);
    }
    
    // Getters and Setters
    public String getItemId() { return itemId.get(); }
    public void setItemId(String itemId) { this.itemId.set(itemId); }
    public StringProperty itemIdProperty() { return itemId; }
    
    public String getDescription() { return description.get(); }
    public void setDescription(String description) { this.description.set(description); }
    public StringProperty descriptionProperty() { return description; }
    
    public int getTotalCount() { return totalCount.get(); }
    public void setTotalCount(int totalCount) { this.totalCount.set(totalCount); }
    public IntegerProperty totalCountProperty() { return totalCount; }
    
    public double getTotalQuantity() { return totalQuantity.get(); }
    public void setTotalQuantity(double totalQuantity) { this.totalQuantity.set(totalQuantity); }
    public DoubleProperty totalQuantityProperty() { return totalQuantity; }
    
    public String getUnit() { return unit.get(); }
    public void setUnit(String unit) { this.unit.set(unit); }
    public StringProperty unitProperty() { return unit; }
    
    public String getNotes() { return notes.get(); }
    public void setNotes(String notes) { this.notes.set(notes); }
    public StringProperty notesProperty() { return notes; }
    
    /**
     * التحقق من صحة البيانات
     */
    public boolean isValid() {
        return description.get() != null && !description.get().trim().isEmpty() &&
               totalCount.get() >= 0 && totalQuantity.get() >= 0;
    }
    
    /**
     * نسخ العنصر
     */
    public DoubleGlassItem copy() {
        DoubleGlassItem copy = new DoubleGlassItem();
        copy.setDescription(this.getDescription());
        copy.setTotalCount(this.getTotalCount());
        copy.setTotalQuantity(this.getTotalQuantity());
        copy.setUnit(this.getUnit());
        copy.setNotes(this.getNotes());
        return copy;
    }
    
    /**
     * تمثيل نصي للعنصر
     */
    @Override
    public String toString() {
        return String.format("%s - %d عدد - %.2f %s", 
                description.get() != null ? description.get() : "",
                totalCount.get(), totalQuantity.get(), 
                unit.get() != null ? unit.get() : "");
    }
}
