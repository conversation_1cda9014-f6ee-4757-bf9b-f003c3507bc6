@echo off
setlocal

echo.
echo ========================================
echo Creating Distribution Package
echo ========================================
echo.

:: Check if portable app exists
if not exist "AccountingSystem-Portable" (
    echo ERROR: AccountingSystem-Portable folder not found
    echo Please run create-portable-app.bat first
    pause
    exit /b 1
)

echo Creating distribution package...

:: Create distribution folder
set DIST_FOLDER=AccountingSystem-Distribution
if exist "%DIST_FOLDER%" rmdir /s /q "%DIST_FOLDER%"
mkdir "%DIST_FOLDER%"

:: Copy portable app
echo Copying portable application...
xcopy "AccountingSystem-Portable\*" "%DIST_FOLDER%\" /E /I /Y

:: Create installation guide
echo Creating installation guide...
echo # نظام المحاسبة المتكامل - دليل التثبيت > "%DIST_FOLDER%\دليل-التثبيت.txt"
echo ## Integrated Accounting System - Installation Guide >> "%DIST_FOLDER%\دليل-التثبيت.txt"
echo. >> "%DIST_FOLDER%\دليل-التثبيت.txt"
echo ### المتطلبات: >> "%DIST_FOLDER%\دليل-التثبيت.txt"
echo 1. Windows 10/11 (64-bit) >> "%DIST_FOLDER%\دليل-التثبيت.txt"
echo 2. Java 17 أو أحدث >> "%DIST_FOLDER%\دليل-التثبيت.txt"
echo 3. 4 GB RAM كحد أدنى >> "%DIST_FOLDER%\دليل-التثبيت.txt"
echo 4. 500 MB مساحة قرص >> "%DIST_FOLDER%\دليل-التثبيت.txt"
echo. >> "%DIST_FOLDER%\دليل-التثبيت.txt"
echo ### تحميل Java: >> "%DIST_FOLDER%\دليل-التثبيت.txt"
echo 1. اذهب إلى: https://adoptium.net/temurin/releases/ >> "%DIST_FOLDER%\دليل-التثبيت.txt"
echo 2. اختر: OpenJDK 17 LTS >> "%DIST_FOLDER%\دليل-التثبيت.txt"
echo 3. النوع: JRE (Java Runtime Environment) >> "%DIST_FOLDER%\دليل-التثبيت.txt"
echo 4. النظام: Windows x64 >> "%DIST_FOLDER%\دليل-التثبيت.txt"
echo 5. حمل وثبت الملف >> "%DIST_FOLDER%\دليل-التثبيت.txt"
echo. >> "%DIST_FOLDER%\دليل-التثبيت.txt"
echo ### طريقة التشغيل: >> "%DIST_FOLDER%\دليل-التثبيت.txt"
echo 1. تأكد من تثبيت Java >> "%DIST_FOLDER%\دليل-التثبيت.txt"
echo 2. انقر نقراً مزدوجاً على AccountingSystem.bat >> "%DIST_FOLDER%\دليل-التثبيت.txt"
echo 3. انتظر حتى يبدأ البرنامج >> "%DIST_FOLDER%\دليل-التثبيت.txt"
echo. >> "%DIST_FOLDER%\دليل-التثبيت.txt"
echo ### الدعم التقني: >> "%DIST_FOLDER%\دليل-التثبيت.txt"
echo البريد الإلكتروني: <EMAIL> >> "%DIST_FOLDER%\دليل-التثبيت.txt"
echo الهاتف: 123-456-7890 >> "%DIST_FOLDER%\دليل-التثبيت.txt"
echo. >> "%DIST_FOLDER%\دليل-التثبيت.txt"
echo © 2025 شركة الزجاج والألومنيوم المتقدمة >> "%DIST_FOLDER%\دليل-التثبيت.txt"

:: Create Java download helper
echo Creating Java download helper...
echo @echo off > "%DIST_FOLDER%\تحميل-Java.bat"
echo echo. >> "%DIST_FOLDER%\تحميل-Java.bat"
echo echo ======================================== >> "%DIST_FOLDER%\تحميل-Java.bat"
echo echo Java Download Helper >> "%DIST_FOLDER%\تحميل-Java.bat"
echo echo ======================================== >> "%DIST_FOLDER%\تحميل-Java.bat"
echo echo. >> "%DIST_FOLDER%\تحميل-Java.bat"
echo echo Opening Java download page... >> "%DIST_FOLDER%\تحميل-Java.bat"
echo echo. >> "%DIST_FOLDER%\تحميل-Java.bat"
echo echo Please download and install: >> "%DIST_FOLDER%\تحميل-Java.bat"
echo echo - OpenJDK 17 LTS or newer >> "%DIST_FOLDER%\تحميل-Java.bat"
echo echo - JRE (Java Runtime Environment) >> "%DIST_FOLDER%\تحميل-Java.bat"
echo echo - Windows x64 >> "%DIST_FOLDER%\تحميل-Java.bat"
echo echo. >> "%DIST_FOLDER%\تحميل-Java.bat"
echo start https://adoptium.net/temurin/releases/ >> "%DIST_FOLDER%\تحميل-Java.bat"
echo pause >> "%DIST_FOLDER%\تحميل-Java.bat"

:: Try to create ZIP using PowerShell
echo Creating ZIP package...
powershell -command "Compress-Archive -Path '%DIST_FOLDER%\*' -DestinationPath 'AccountingSystem-v1.0.0.zip' -Force"

if %errorlevel%==0 (
    echo ✅ ZIP package created: AccountingSystem-v1.0.0.zip
) else (
    echo ⚠️ ZIP creation failed, but distribution folder is ready
)

:: Show results
echo.
echo ========================================
echo Distribution Package Created!
echo ========================================
echo.
echo Distribution folder: %DIST_FOLDER%\
echo ZIP package: AccountingSystem-v1.0.0.zip
echo.
echo Contents:
dir /b "%DIST_FOLDER%"
echo.
echo File sizes:
for %%f in ("%DIST_FOLDER%\*") do echo   %%~nxf: %%~zf bytes

if exist "AccountingSystem-v1.0.0.zip" (
    echo.
    echo ZIP package size:
    for %%f in ("AccountingSystem-v1.0.0.zip") do echo   %%~nxf: %%~zf bytes
)

echo.
echo Ready for distribution!
echo.
echo To distribute:
echo 1. Send the ZIP file: AccountingSystem-v1.0.0.zip
echo 2. Or copy the folder: %DIST_FOLDER%\
echo 3. Include installation instructions
echo.

pause
