package com.accounting.model;

import javafx.beans.property.*;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * نموذج الحساب في دليل الحسابات
 * يدعم التفرع الهرمي وشجرة الحسابات
 */
public class Account {
    
    // الخصائص الأساسية
    private final StringProperty accountCode = new SimpleStringProperty();
    private final StringProperty accountName = new SimpleStringProperty();
    private final StringProperty accountNameEn = new SimpleStringProperty();
    private final ObjectProperty<AccountType> accountType = new SimpleObjectProperty<>();
    private final ObjectProperty<AccountNature> accountNature = new SimpleObjectProperty<>();
    
    // التفرع الهرمي
    private final StringProperty parentAccountCode = new SimpleStringProperty();
    private final IntegerProperty level = new SimpleIntegerProperty();
    private final BooleanProperty isParent = new SimpleBooleanProperty();
    private final BooleanProperty isActive = new SimpleBooleanProperty(true);
    
    // الأرصدة
    private final DoubleProperty openingBalance = new SimpleDoubleProperty(0.0);
    private final DoubleProperty currentBalance = new SimpleDoubleProperty(0.0);
    private final DoubleProperty debitTotal = new SimpleDoubleProperty(0.0);
    private final DoubleProperty creditTotal = new SimpleDoubleProperty(0.0);
    
    // معلومات إضافية
    private final StringProperty description = new SimpleStringProperty();
    private final StringProperty currency = new SimpleStringProperty("EGP");
    private final ObjectProperty<LocalDateTime> createdDate = new SimpleObjectProperty<>(LocalDateTime.now());
    private final ObjectProperty<LocalDateTime> lastModified = new SimpleObjectProperty<>(LocalDateTime.now());
    
    // الحسابات الفرعية
    private final List<Account> subAccounts = new ArrayList<>();
    private Account parentAccount;
    
    /**
     * أنواع الحسابات الرئيسية
     */
    public enum AccountType {
        ASSETS("الأصول", "Assets"),
        LIABILITIES("الخصوم", "Liabilities"),
        EQUITY("حقوق الملكية", "Equity"),
        REVENUE("الإيرادات", "Revenue"),
        EXPENSES("المصروفات", "Expenses"),
        COST_OF_GOODS_SOLD("تكلفة البضاعة المباعة", "Cost of Goods Sold");
        
        private final String arabicName;
        private final String englishName;
        
        AccountType(String arabicName, String englishName) {
            this.arabicName = arabicName;
            this.englishName = englishName;
        }
        
        public String getArabicName() { return arabicName; }
        public String getEnglishName() { return englishName; }
        
        @Override
        public String toString() { return arabicName; }
    }
    
    /**
     * طبيعة الحساب (مدين أم دائن)
     */
    public enum AccountNature {
        DEBIT("مدين", "Debit"),
        CREDIT("دائن", "Credit");
        
        private final String arabicName;
        private final String englishName;
        
        AccountNature(String arabicName, String englishName) {
            this.arabicName = arabicName;
            this.englishName = englishName;
        }
        
        public String getArabicName() { return arabicName; }
        public String getEnglishName() { return englishName; }
        
        @Override
        public String toString() { return arabicName; }
    }
    
    // Constructors
    public Account() {}
    
    public Account(String accountCode, String accountName, AccountType accountType, AccountNature accountNature) {
        setAccountCode(accountCode);
        setAccountName(accountName);
        setAccountType(accountType);
        setAccountNature(accountNature);
        setLevel(calculateLevel(accountCode));
    }
    
    // Property getters
    public StringProperty accountCodeProperty() { return accountCode; }
    public StringProperty accountNameProperty() { return accountName; }
    public StringProperty accountNameEnProperty() { return accountNameEn; }
    public ObjectProperty<AccountType> accountTypeProperty() { return accountType; }
    public ObjectProperty<AccountNature> accountNatureProperty() { return accountNature; }
    public StringProperty parentAccountCodeProperty() { return parentAccountCode; }
    public IntegerProperty levelProperty() { return level; }
    public BooleanProperty isParentProperty() { return isParent; }
    public BooleanProperty isActiveProperty() { return isActive; }
    public DoubleProperty openingBalanceProperty() { return openingBalance; }
    public DoubleProperty currentBalanceProperty() { return currentBalance; }
    public DoubleProperty debitTotalProperty() { return debitTotal; }
    public DoubleProperty creditTotalProperty() { return creditTotal; }
    public StringProperty descriptionProperty() { return description; }
    public StringProperty currencyProperty() { return currency; }
    public ObjectProperty<LocalDateTime> createdDateProperty() { return createdDate; }
    public ObjectProperty<LocalDateTime> lastModifiedProperty() { return lastModified; }
    
    // Value getters and setters
    public String getAccountCode() { return accountCode.get(); }
    public void setAccountCode(String accountCode) { 
        this.accountCode.set(accountCode);
        setLevel(calculateLevel(accountCode));
    }
    
    public String getAccountName() { return accountName.get(); }
    public void setAccountName(String accountName) { this.accountName.set(accountName); }
    
    public String getAccountNameEn() { return accountNameEn.get(); }
    public void setAccountNameEn(String accountNameEn) { this.accountNameEn.set(accountNameEn); }
    
    public AccountType getAccountType() { return accountType.get(); }
    public void setAccountType(AccountType accountType) { this.accountType.set(accountType); }
    
    public AccountNature getAccountNature() { return accountNature.get(); }
    public void setAccountNature(AccountNature accountNature) { this.accountNature.set(accountNature); }
    
    public String getParentAccountCode() { return parentAccountCode.get(); }
    public void setParentAccountCode(String parentAccountCode) { this.parentAccountCode.set(parentAccountCode); }
    
    public int getLevel() { return level.get(); }
    public void setLevel(int level) { this.level.set(level); }
    
    public boolean isParent() { return isParent.get(); }
    public void setIsParent(boolean isParent) { this.isParent.set(isParent); }
    
    public boolean isActive() { return isActive.get(); }
    public void setIsActive(boolean isActive) { this.isActive.set(isActive); }
    
    public double getOpeningBalance() { return openingBalance.get(); }
    public void setOpeningBalance(double openingBalance) { this.openingBalance.set(openingBalance); }
    
    public double getCurrentBalance() { return currentBalance.get(); }
    public void setCurrentBalance(double currentBalance) { this.currentBalance.set(currentBalance); }
    
    public double getDebitTotal() { return debitTotal.get(); }
    public void setDebitTotal(double debitTotal) { this.debitTotal.set(debitTotal); }
    
    public double getCreditTotal() { return creditTotal.get(); }
    public void setCreditTotal(double creditTotal) { this.creditTotal.set(creditTotal); }
    
    public String getDescription() { return description.get(); }
    public void setDescription(String description) { this.description.set(description); }
    
    public String getCurrency() { return currency.get(); }
    public void setCurrency(String currency) { this.currency.set(currency); }
    
    public LocalDateTime getCreatedDate() { return createdDate.get(); }
    public void setCreatedDate(LocalDateTime createdDate) { this.createdDate.set(createdDate); }
    
    public LocalDateTime getLastModified() { return lastModified.get(); }
    public void setLastModified(LocalDateTime lastModified) { this.lastModified.set(lastModified); }
    
    // Sub-accounts management
    public List<Account> getSubAccounts() { return subAccounts; }
    public Account getParentAccount() { return parentAccount; }
    public void setParentAccount(Account parentAccount) { this.parentAccount = parentAccount; }
    
    public void addSubAccount(Account subAccount) {
        if (!subAccounts.contains(subAccount)) {
            subAccounts.add(subAccount);
            subAccount.setParentAccount(this);
            setIsParent(true);
        }
    }
    
    public void removeSubAccount(Account subAccount) {
        subAccounts.remove(subAccount);
        subAccount.setParentAccount(null);
        if (subAccounts.isEmpty()) {
            setIsParent(false);
        }
    }
    
    /**
     * حساب مستوى الحساب بناءً على رمز الحساب
     */
    private int calculateLevel(String code) {
        if (code == null || code.trim().isEmpty()) return 0;
        
        // حساب المستوى بناءً على عدد الأرقام أو النقاط
        String[] parts = code.split("\\.");
        return parts.length;
    }
    
    /**
     * تحديث الرصيد الحالي
     */
    public void updateBalance(double debitAmount, double creditAmount) {
        setDebitTotal(getDebitTotal() + debitAmount);
        setCreditTotal(getCreditTotal() + creditAmount);
        
        // حساب الرصيد الحالي حسب طبيعة الحساب
        if (getAccountNature() == AccountNature.DEBIT) {
            setCurrentBalance(getOpeningBalance() + getDebitTotal() - getCreditTotal());
        } else {
            setCurrentBalance(getOpeningBalance() + getCreditTotal() - getDebitTotal());
        }
        
        setLastModified(LocalDateTime.now());
    }
    
    /**
     * الحصول على الرصيد المدين
     */
    public double getDebitBalance() {
        double balance = getCurrentBalance();
        return balance > 0 && getAccountNature() == AccountNature.DEBIT ? balance : 0.0;
    }
    
    /**
     * الحصول على الرصيد الدائن
     */
    public double getCreditBalance() {
        double balance = getCurrentBalance();
        return balance > 0 && getAccountNature() == AccountNature.CREDIT ? balance : 0.0;
    }
    
    /**
     * التحقق من إمكانية الحذف
     */
    public boolean canBeDeleted() {
        return !isParent() && getDebitTotal() == 0 && getCreditTotal() == 0;
    }
    
    /**
     * الحصول على المسار الكامل للحساب
     */
    public String getFullPath() {
        if (parentAccount == null) {
            return getAccountName();
        }
        return parentAccount.getFullPath() + " > " + getAccountName();
    }
    
    /**
     * نسخ الحساب
     */
    public Account copy() {
        Account copy = new Account();
        copy.setAccountCode(getAccountCode());
        copy.setAccountName(getAccountName());
        copy.setAccountNameEn(getAccountNameEn());
        copy.setAccountType(getAccountType());
        copy.setAccountNature(getAccountNature());
        copy.setParentAccountCode(getParentAccountCode());
        copy.setLevel(getLevel());
        copy.setIsParent(isParent());
        copy.setIsActive(isActive());
        copy.setOpeningBalance(getOpeningBalance());
        copy.setDescription(getDescription());
        copy.setCurrency(getCurrency());
        return copy;
    }
    
    @Override
    public String toString() {
        return getAccountCode() + " - " + getAccountName();
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        Account account = (Account) obj;
        return getAccountCode() != null && getAccountCode().equals(account.getAccountCode());
    }
    
    @Override
    public int hashCode() {
        return getAccountCode() != null ? getAccountCode().hashCode() : 0;
    }
}
