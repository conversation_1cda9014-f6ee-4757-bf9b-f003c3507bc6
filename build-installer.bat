@echo off
chcp 65001 > nul
setlocal enabledelayedexpansion

echo.
echo ========================================
echo 🏗️  بناء ملف التثبيت - نظام المحاسبة
echo ========================================
echo.

:: تحديد متغيرات البيئة
set JAVA_HOME=C:\Program Files\Eclipse Adoptium\jdk-********-hotspot
set PATH=%JAVA_HOME%\bin;%PATH%

echo 📋 فحص متطلبات البناء...
echo.

:: فحص Java
java -version > nul 2>&1
if not %errorlevel%==0 (
    echo ERROR: Java not found in PATH
    echo Please install Java 17 or newer
    pause
    exit /b 1
)

echo ✅ Java متوفر
java -version

:: فحص Maven
call mvnw.cmd -version > nul 2>&1
if not %errorlevel%==0 (
    echo ERROR: Maven not available
    pause
    exit /b 1
)

echo ✅ Maven متوفر

echo.
echo 🧹 تنظيف المشروع...
call mvnw.cmd clean
if not %errorlevel%==0 (
    echo ERROR: Failed to clean project
    pause
    exit /b 1
)

echo.
echo 🔨 بناء المشروع...
call mvnw.cmd compile
if %errorlevel% neq 0 (
    echo ❌ فشل في بناء المشروع
    pause
    exit /b 1
)

echo.
echo 📦 إنشاء JAR قابل للتشغيل...
call mvnw.cmd package
if %errorlevel% neq 0 (
    echo ❌ فشل في إنشاء JAR
    pause
    exit /b 1
)

echo.
echo 🎯 إنشاء ملف التثبيت...

:: التحقق من وجود JPackage
jpackage --version > nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ JPackage غير متوفر
    echo يرجى التأكد من استخدام Java 17 أو أحدث
    pause
    exit /b 1
)

:: إنشاء مجلد الـ installer إذا لم يكن موجوداً
if not exist "target\installer" mkdir "target\installer"

:: بناء الـ installer باستخدام JPackage مباشرة
echo 🔧 بناء Windows Installer...

jpackage ^
    --type msi ^
    --input target ^
    --dest target\installer ^
    --name "AccountingSystem" ^
    --main-jar accounting-system-1.0.0.jar ^
    --main-class com.accounting.AccountingApplication ^
    --app-version 1.0.0 ^
    --vendor "Advanced Glass and Aluminum Company" ^
    --description "Integrated accounting system for glass and aluminum companies" ^
    --copyright "© 2025 Advanced Glass and Aluminum Company" ^
    --win-dir-chooser ^
    --win-menu ^
    --win-shortcut ^
    --win-upgrade-uuid ************************************ ^
    --java-options "-Dfile.encoding=UTF-8" ^
    --java-options "-Djava.awt.headless=false" ^
    --java-options "--add-opens javafx.graphics/javafx.scene=ALL-UNNAMED" ^
    --java-options "--add-opens javafx.controls/javafx.scene.control=ALL-UNNAMED" ^
    --java-options "--add-opens javafx.base/javafx.beans.property=ALL-UNNAMED"

if %errorlevel% neq 0 (
    echo ❌ فشل في إنشاء ملف التثبيت
    echo.
    echo 🔍 محاولة إنشاء EXE بدلاً من MSI...
    
    jpackage ^
        --type exe ^
        --input target ^
        --dest target\installer ^
        --name "AccountingSystem" ^
        --main-jar accounting-system-1.0.0.jar ^
        --main-class com.accounting.AccountingApplication ^
        --app-version 1.0.0 ^
        --vendor "Advanced Glass and Aluminum Company" ^
        --description "Integrated accounting system for glass and aluminum companies" ^
        --copyright "© 2025 Advanced Glass and Aluminum Company" ^
        --win-dir-chooser ^
        --win-menu ^
        --win-shortcut ^
        --java-options "-Dfile.encoding=UTF-8" ^
        --java-options "-Djava.awt.headless=false" ^
        --java-options "--add-opens javafx.graphics/javafx.scene=ALL-UNNAMED" ^
        --java-options "--add-opens javafx.controls/javafx.scene.control=ALL-UNNAMED" ^
        --java-options "--add-opens javafx.base/javafx.beans.property=ALL-UNNAMED"
    
    if !errorlevel! neq 0 (
        echo ❌ فشل في إنشاء ملف التثبيت
        pause
        exit /b 1
    )
)

echo.
echo ✅ تم إنشاء ملف التثبيت بنجاح!
echo.
echo 📁 موقع الملفات:
echo    - JAR: target\accounting-system-1.0.0.jar
echo    - Installer: target\installer\
echo.

:: عرض محتويات مجلد الـ installer
if exist "target\installer" (
    echo 📋 ملفات التثبيت المتوفرة:
    dir /b "target\installer\*.msi" "target\installer\*.exe" 2>nul
    echo.

    :: عرض حجم الملفات
    for %%f in ("target\installer\*.msi" "target\installer\*.exe") do (
        if exist "%%f" (
            echo 📊 حجم الملف: %%~zf bytes
        )
    )
    echo.
)

echo 🎉 اكتمل البناء بنجاح!
echo.
echo 💡 لتشغيل البرنامج مباشرة:
echo    java -jar target\accounting-system-1.0.0.jar
echo.
echo 💡 لتثبيت البرنامج:
echo    قم بتشغيل ملف التثبيت من مجلد target\installer
echo.
echo 📋 ملاحظات مهمة:
echo    - ملف التثبيت يحتوي على Java مدمجة
echo    - لا يحتاج المستخدم النهائي لتثبيت Java منفصل
echo    - حجم الملف حوالي 80-120 MB
echo    - متوافق مع Windows 10/11 (64-bit)
echo.
echo 📞 للدعم التقني: <EMAIL>
echo.

pause
