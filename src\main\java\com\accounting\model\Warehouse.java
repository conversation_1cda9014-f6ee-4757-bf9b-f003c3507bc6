package com.accounting.model;

/**
 * نموذج المخزن
 * Warehouse Model
 */
public class Warehouse {
    private String warehouseId;
    private String name;
    private String location;
    private String description;
    private boolean isActive;
    
    public Warehouse() {
        this.isActive = true;
    }
    
    public Warehouse(String warehouseId, String name, String location, String description) {
        this.warehouseId = warehouseId;
        this.name = name;
        this.location = location;
        this.description = description;
        this.isActive = true;
    }
    
    // Getters and Setters
    public String getWarehouseId() { return warehouseId; }
    public void setWarehouseId(String warehouseId) { this.warehouseId = warehouseId; }
    
    public String getName() { return name; }
    public void setName(String name) { this.name = name; }
    
    public String getLocation() { return location; }
    public void setLocation(String location) { this.location = location; }
    
    public String getDescription() { return description; }
    public void setDescription(String description) { this.description = description; }
    
    public boolean isActive() { return isActive; }
    public void setActive(boolean active) { isActive = active; }
    
    @Override
    public String toString() {
        return name + " (" + warehouseId + ")";
    }
}
