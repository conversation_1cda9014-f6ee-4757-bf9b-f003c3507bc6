@echo off
setlocal

echo.
echo ========================================
echo Quick Fix for Hossam JavaFX Issue
echo ========================================
echo.

:: Set installation directory
set INSTALL_DIR=%USERPROFILE%\Hossam

echo The issue: JavaFX runtime components are missing
echo.
echo QUICK SOLUTION:
echo Install Oracle JDK (includes JavaFX built-in)
echo.

:: Create super simple launcher that works with Oracle JDK
echo Creating new launcher...
echo @echo off > "%INSTALL_DIR%\Hossam-Fixed.bat"
echo setlocal >> "%INSTALL_DIR%\Hossam-Fixed.bat"
echo cd /d "%%~dp0" >> "%INSTALL_DIR%\Hossam-Fixed.bat"
echo title Hossam Accounting System >> "%INSTALL_DIR%\Hossam-Fixed.bat"
echo echo ======================================== >> "%INSTALL_DIR%\Hossam-Fixed.bat"
echo echo       Hossam Accounting System >> "%INSTALL_DIR%\Hossam-Fixed.bat"
echo echo ======================================== >> "%INSTALL_DIR%\Hossam-Fixed.bat"
echo echo Starting application... >> "%INSTALL_DIR%\Hossam-Fixed.bat"
echo echo. >> "%INSTALL_DIR%\Hossam-Fixed.bat"
echo. >> "%INSTALL_DIR%\Hossam-Fixed.bat"
echo :: This should work with Oracle JDK >> "%INSTALL_DIR%\Hossam-Fixed.bat"
echo java -Dfile.encoding=UTF-8 -jar accounting-system-1.0.0.jar >> "%INSTALL_DIR%\Hossam-Fixed.bat"
echo. >> "%INSTALL_DIR%\Hossam-Fixed.bat"
echo if %%errorlevel%% neq 0 ^( >> "%INSTALL_DIR%\Hossam-Fixed.bat"
echo     echo. >> "%INSTALL_DIR%\Hossam-Fixed.bat"
echo     echo Still not working? >> "%INSTALL_DIR%\Hossam-Fixed.bat"
echo     echo Please install Oracle JDK from the link that will open... >> "%INSTALL_DIR%\Hossam-Fixed.bat"
echo     echo. >> "%INSTALL_DIR%\Hossam-Fixed.bat"
echo     start https://www.oracle.com/java/technologies/downloads/ >> "%INSTALL_DIR%\Hossam-Fixed.bat"
echo     pause >> "%INSTALL_DIR%\Hossam-Fixed.bat"
echo ^) >> "%INSTALL_DIR%\Hossam-Fixed.bat"

echo ✅ Created fixed launcher: Hossam-Fixed.bat
echo.
echo ========================================
echo SOLUTION STEPS:
echo ========================================
echo.
echo 1. Install Oracle JDK (includes JavaFX):
echo    https://www.oracle.com/java/technologies/downloads/
echo.
echo 2. Choose: JDK 17 or JDK 21
echo.
echo 3. Download and install it
echo.
echo 4. Run: %INSTALL_DIR%\Hossam-Fixed.bat
echo.

:: Open the download page
echo Opening Oracle JDK download page...
start https://www.oracle.com/java/technologies/downloads/

echo.
echo After installing Oracle JDK, try running:
echo %INSTALL_DIR%\Hossam-Fixed.bat
echo.

pause
