package com.accounting.controller;

import com.accounting.model.Item;
import javafx.animation.FadeTransition;
import javafx.animation.PauseTransition;
import javafx.collections.ObservableList;
import javafx.geometry.Insets;
import javafx.geometry.Pos;
import javafx.scene.control.Label;
import javafx.scene.layout.HBox;
import javafx.scene.layout.StackPane;
import javafx.scene.layout.VBox;
import javafx.stage.Popup;
import javafx.stage.Stage;
import javafx.util.Duration;
import de.jensd.fx.glyphs.fontawesome.FontAwesomeIcon;
import de.jensd.fx.glyphs.fontawesome.FontAwesomeIconView;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * نظام الإشعارات والتنبيهات للمخزون
 * Inventory Notification System
 */
public class NotificationSystem {
    
    private Stage primaryStage;
    private List<NotificationItem> notifications;
    private VBox notificationPanel;
    private DateTimeFormatter timeFormatter = DateTimeFormatter.ofPattern("HH:mm");
    
    public NotificationSystem(Stage primaryStage) {
        this.primaryStage = primaryStage;
        this.notifications = new ArrayList<>();
        createNotificationPanel();
    }
    
    /**
     * إنشاء لوحة الإشعارات
     */
    private void createNotificationPanel() {
        notificationPanel = new VBox(5);
        notificationPanel.setAlignment(Pos.TOP_RIGHT);
        notificationPanel.setPadding(new Insets(10));
        notificationPanel.setMaxWidth(350);
        notificationPanel.setStyle("-fx-background-color: transparent;");
    }
    
    /**
     * فحص الأصناف المنخفضة وإرسال تنبيهات
     */
    public void checkLowStockItems(ObservableList<Item> items) {
        List<Item> lowStockItems = new ArrayList<>();
        List<Item> criticalStockItems = new ArrayList<>();
        
        for (Item item : items) {
            if (item.getCurrentQuantity() <= 0) {
                criticalStockItems.add(item);
            } else if (item.getCurrentQuantity() < 5) {
                criticalStockItems.add(item);
            } else if (item.getCurrentQuantity() < 10) {
                lowStockItems.add(item);
            }
        }
        
        // إشعارات الأصناف النفدت
        if (!criticalStockItems.isEmpty()) {
            showCriticalStockNotification(criticalStockItems);
        }
        
        // إشعارات الأصناف المنخفضة
        if (!lowStockItems.isEmpty()) {
            showLowStockNotification(lowStockItems);
        }
    }
    
    /**
     * إظهار إشعار الأصناف الحرجة
     */
    private void showCriticalStockNotification(List<Item> criticalItems) {
        String message = "⚠️ تنبيه حرج: " + criticalItems.size() + " صنف نفد أو أوشك على النفاد";
        String details = "الأصناف: " + criticalItems.stream()
            .limit(3)
            .map(Item::getName)
            .reduce((a, b) -> a + "، " + b)
            .orElse("");
        
        if (criticalItems.size() > 3) {
            details += " و " + (criticalItems.size() - 3) + " أصناف أخرى";
        }
        
        showNotification(message, details, NotificationType.CRITICAL);
    }
    
    /**
     * إظهار إشعار الأصناف المنخفضة
     */
    private void showLowStockNotification(List<Item> lowStockItems) {
        String message = "⚠️ تنبيه: " + lowStockItems.size() + " صنف منخفض الكمية";
        String details = "يحتاج إعادة تموين قريباً";
        
        showNotification(message, details, NotificationType.WARNING);
    }
    
    /**
     * إظهار إشعار عام
     */
    public void showNotification(String title, String message, NotificationType type) {
        NotificationItem notification = new NotificationItem(title, message, type, LocalDateTime.now());
        notifications.add(notification);
        
        // إنشاء عنصر الإشعار المرئي
        VBox notificationBox = createNotificationBox(notification);
        
        // إضافة الإشعار للوحة
        notificationPanel.getChildren().add(0, notificationBox);
        
        // إنشاء popup للإشعار
        showNotificationPopup(notificationBox);
        
        // إزالة الإشعارات القديمة (الاحتفاظ بآخر 10)
        if (notificationPanel.getChildren().size() > 10) {
            notificationPanel.getChildren().remove(10, notificationPanel.getChildren().size());
        }
    }
    
    /**
     * إنشاء صندوق الإشعار
     */
    private VBox createNotificationBox(NotificationItem notification) {
        VBox box = new VBox(5);
        box.setPadding(new Insets(12));
        box.setMaxWidth(320);
        
        // تحديد الألوان حسب نوع الإشعار
        String backgroundColor, borderColor, iconColor;
        FontAwesomeIcon icon;
        
        switch (notification.getType()) {
            case CRITICAL:
                backgroundColor = "#f8d7da";
                borderColor = "#f5c6cb";
                iconColor = "#721c24";
                icon = FontAwesomeIcon.EXCLAMATION_TRIANGLE;
                break;
            case WARNING:
                backgroundColor = "#fff3cd";
                borderColor = "#ffeaa7";
                iconColor = "#856404";
                icon = FontAwesomeIcon.EXCLAMATION_CIRCLE;
                break;
            case SUCCESS:
                backgroundColor = "#d4edda";
                borderColor = "#c3e6cb";
                iconColor = "#155724";
                icon = FontAwesomeIcon.CHECK_CIRCLE;
                break;
            case INFO:
            default:
                backgroundColor = "#d1ecf1";
                borderColor = "#bee5eb";
                iconColor = "#0c5460";
                icon = FontAwesomeIcon.INFO_CIRCLE;
                break;
        }
        
        box.setStyle("-fx-background-color: " + backgroundColor + "; " +
                    "-fx-border-color: " + borderColor + "; " +
                    "-fx-border-width: 1; " +
                    "-fx-border-radius: 8; " +
                    "-fx-background-radius: 8; " +
                    "-fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.1), 4, 0, 0, 2);");
        
        // الرأس مع الأيقونة والوقت
        HBox header = new HBox(8);
        header.setAlignment(Pos.CENTER_LEFT);
        
        FontAwesomeIconView iconView = new FontAwesomeIconView(icon);
        iconView.setSize("14px");
        iconView.setStyle("-fx-fill: " + iconColor + ";");
        
        Label timeLabel = new Label(notification.getTimestamp().format(timeFormatter));
        timeLabel.setStyle("-fx-font-size: 10px; -fx-text-fill: " + iconColor + "; -fx-opacity: 0.8;");
        
        header.getChildren().addAll(iconView, new Label(), timeLabel);
        HBox.setHgrow(header.getChildren().get(1), javafx.scene.layout.Priority.ALWAYS);
        
        // العنوان
        Label titleLabel = new Label(notification.getTitle());
        titleLabel.setStyle("-fx-font-size: 12px; -fx-font-weight: bold; -fx-text-fill: " + iconColor + ";");
        titleLabel.setWrapText(true);
        
        // الرسالة
        Label messageLabel = new Label(notification.getMessage());
        messageLabel.setStyle("-fx-font-size: 11px; -fx-text-fill: " + iconColor + "; -fx-opacity: 0.9;");
        messageLabel.setWrapText(true);
        
        box.getChildren().addAll(header, titleLabel, messageLabel);
        
        // إضافة تأثير الإغلاق عند النقر
        box.setOnMouseClicked(e -> {
            FadeTransition fade = new FadeTransition(Duration.millis(300), box);
            fade.setFromValue(1.0);
            fade.setToValue(0.0);
            fade.setOnFinished(event -> notificationPanel.getChildren().remove(box));
            fade.play();
        });
        
        return box;
    }
    
    /**
     * إظهار popup للإشعار
     */
    private void showNotificationPopup(VBox notificationBox) {
        Popup popup = new Popup();
        popup.setAutoHide(true);
        popup.setHideOnEscape(true);
        
        // إنشاء حاوية للpopup
        StackPane popupContainer = new StackPane();
        popupContainer.getChildren().add(notificationBox);
        popupContainer.setStyle("-fx-background-color: transparent;");
        
        popup.getContent().add(popupContainer);
        
        // تحديد موقع الإشعار (أعلى يمين الشاشة)
        double x = primaryStage.getX() + primaryStage.getWidth() - 350;
        double y = primaryStage.getY() + 50;
        
        popup.show(primaryStage, x, y);
        
        // تأثير الظهور
        FadeTransition fadeIn = new FadeTransition(Duration.millis(300), popupContainer);
        fadeIn.setFromValue(0.0);
        fadeIn.setToValue(1.0);
        fadeIn.play();
        
        // إخفاء تلقائي بعد 5 ثوان
        PauseTransition pause = new PauseTransition(Duration.seconds(5));
        pause.setOnFinished(e -> {
            FadeTransition fadeOut = new FadeTransition(Duration.millis(300), popupContainer);
            fadeOut.setFromValue(1.0);
            fadeOut.setToValue(0.0);
            fadeOut.setOnFinished(event -> popup.hide());
            fadeOut.play();
        });
        pause.play();
    }
    
    /**
     * إظهار إشعار نجاح
     */
    public void showSuccessNotification(String title, String message) {
        showNotification(title, message, NotificationType.SUCCESS);
    }
    
    /**
     * إظهار إشعار معلومات
     */
    public void showInfoNotification(String title, String message) {
        showNotification(title, message, NotificationType.INFO);
    }
    
    /**
     * إظهار إشعار تحذير
     */
    public void showWarningNotification(String title, String message) {
        showNotification(title, message, NotificationType.WARNING);
    }
    
    /**
     * إظهار إشعار حرج
     */
    public void showCriticalNotification(String title, String message) {
        showNotification(title, message, NotificationType.CRITICAL);
    }
    
    /**
     * الحصول على لوحة الإشعارات
     */
    public VBox getNotificationPanel() {
        return notificationPanel;
    }
    
    /**
     * الحصول على عدد الإشعارات
     */
    public int getNotificationCount() {
        return notifications.size();
    }
    
    /**
     * مسح جميع الإشعارات
     */
    public void clearAllNotifications() {
        notifications.clear();
        notificationPanel.getChildren().clear();
    }
    
    // ===============================
    // الفئات المساعدة
    // ===============================
    
    /**
     * أنواع الإشعارات
     */
    public enum NotificationType {
        INFO, SUCCESS, WARNING, CRITICAL
    }
    
    /**
     * عنصر الإشعار
     */
    public static class NotificationItem {
        private String title;
        private String message;
        private NotificationType type;
        private LocalDateTime timestamp;
        
        public NotificationItem(String title, String message, NotificationType type, LocalDateTime timestamp) {
            this.title = title;
            this.message = message;
            this.type = type;
            this.timestamp = timestamp;
        }
        
        // Getters
        public String getTitle() { return title; }
        public String getMessage() { return message; }
        public NotificationType getType() { return type; }
        public LocalDateTime getTimestamp() { return timestamp; }
    }
}
