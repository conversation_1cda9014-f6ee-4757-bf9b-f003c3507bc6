package com.accounting.controller;

import com.accounting.model.*;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.geometry.Insets;
import javafx.geometry.Pos;
import javafx.scene.Scene;
import javafx.scene.control.*;
import javafx.scene.control.cell.PropertyValueFactory;
import javafx.scene.control.cell.TextFieldTableCell;
import javafx.scene.layout.*;
import javafx.stage.Modality;
import javafx.stage.Stage;
import javafx.util.converter.DoubleStringConverter;
import de.jensd.fx.glyphs.fontawesome.FontAwesomeIcon;
import de.jensd.fx.glyphs.fontawesome.FontAwesomeIconView;

import java.text.DecimalFormat;
import java.time.LocalDate;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.stream.Collectors;

/**
 * متحكم موديول الرواتب والأجور
 * Payroll Module Controller
 */
public class PayrollController {
    
    private ObservableList<Employee> employees = FXCollections.observableArrayList();
    private ObservableList<Manager> managers = FXCollections.observableArrayList();
    private ObservableList<Advance> advances = FXCollections.observableArrayList();
    private ObservableList<SalaryDue> salaryDues = FXCollections.observableArrayList();
    private ObservableList<SalaryPaid> salaryPaids = FXCollections.observableArrayList();
    
    private DecimalFormat decimalFormat = new DecimalFormat("#,##0.00");
    private DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("dd/MM/yyyy");
    
    public PayrollController() {
        initializeData();
    }
    
    /**
     * تهيئة البيانات الافتراضية
     */
    private void initializeData() {
        // إضافة موظفين افتراضيين
        employees.addAll(
            new Employee("EMP001", "أحمد محمد علي", LocalDate.of(2023, 1, 15), 5000.0, "محاسب", "المحاسبة", "01234567890", "<EMAIL>", "القاهرة"),
            new Employee("EMP002", "فاطمة حسن", LocalDate.of(2023, 3, 1), 4500.0, "سكرتيرة", "الإدارة", "01098765432", "<EMAIL>", "الجيزة"),
            new Employee("EMP003", "محمد أحمد", LocalDate.of(2022, 6, 10), 6000.0, "مهندس", "الإنتاج", "01555666777", "<EMAIL>", "الإسكندرية"),
            new Employee("EMP004", "سارة علي", LocalDate.of(2024, 1, 1), 3500.0, "موظفة استقبال", "الإدارة", "01777888999", "<EMAIL>", "المنصورة")
        );
        
        // إضافة مدراء افتراضيين
        managers.addAll(
            new Manager("MGR001", "خالد إبراهيم", "مدير عام", "الإدارة العليا"),
            new Manager("MGR002", "نادية محمود", "مدير الموارد البشرية", "الموارد البشرية"),
            new Manager("MGR003", "عمرو سالم", "مدير المحاسبة", "المحاسبة"),
            new Manager("MGR004", "هند أحمد", "مدير الإنتاج", "الإنتاج")
        );
        
        // إضافة بعض البيانات التجريبية
        addSampleData();
    }
    
    /**
     * إضافة بيانات تجريبية
     */
    private void addSampleData() {
        // إضافة سلف تجريبية
        advances.addAll(
            new Advance("ADV001", "EMP001", "أحمد محمد علي", 1000.0, LocalDate.now().minusDays(10), "خالد إبراهيم", "سلفة طارئة"),
            new Advance("ADV002", "EMP002", "فاطمة حسن", 500.0, LocalDate.now().minusDays(5), "نادية محمود", "سلفة شخصية")
        );
        
        // إضافة مستحقات تجريبية
        salaryDues.addAll(
            new SalaryDue("DUE001", "EMP001", "أحمد محمد علي", YearMonth.now().minusMonths(1), 5000.0, 200.0, 300.0, 100.0),
            new SalaryDue("DUE002", "EMP002", "فاطمة حسن", YearMonth.now().minusMonths(1), 4500.0, 0.0, 200.0, 0.0)
        );
        
        // إضافة مدفوعات تجريبية
        salaryPaids.addAll(
            new SalaryPaid("PAY001", "EMP001", "أحمد محمد علي", YearMonth.now().minusMonths(1), 5000.0, LocalDate.now().minusDays(3), "خالد إبراهيم", "خزنة", "سداد راتب كامل"),
            new SalaryPaid("PAY002", "EMP002", "فاطمة حسن", YearMonth.now().minusMonths(1), 2000.0, LocalDate.now().minusDays(2), "نادية محمود", "بنك", "سداد جزئي")
        );
    }
    
    /**
     * إنشاء واجهة موديول الرواتب
     */
    public VBox createPayrollModule() {
        VBox mainContainer = new VBox(20);
        mainContainer.setPadding(new Insets(20));
        mainContainer.getStyleClass().add("payroll-module");
        
        // العنوان الرئيسي
        Label titleLabel = new Label("💰 موديول الرواتب والأجور");
        titleLabel.getStyleClass().add("module-title");
        
        // شريط الأدوات
        HBox toolbar = createToolbar();
        
        // منطقة المحتوى الرئيسية
        TabPane tabPane = createMainTabs();
        
        mainContainer.getChildren().addAll(titleLabel, toolbar, tabPane);
        return mainContainer;
    }
    
    /**
     * إنشاء شريط الأدوات
     */
    private HBox createToolbar() {
        HBox toolbar = new HBox(15);
        toolbar.setAlignment(Pos.CENTER_LEFT);
        toolbar.setPadding(new Insets(10));
        toolbar.getStyleClass().add("toolbar");
        
        // زر إدارة الموظفين
        Button employeesBtn = createToolbarButton("إدارة الموظفين", FontAwesomeIcon.USERS, this::showEmployeeManagement);
        
        // زر تسجيل سلفة
        Button advanceBtn = createToolbarButton("تسجيل سلفة", FontAwesomeIcon.MONEY, this::showAdvanceDialog);
        
        // زر تسجيل مستحق
        Button dueBtn = createToolbarButton("تسجيل مستحق", FontAwesomeIcon.CALCULATOR, this::showSalaryDueDialog);
        
        // زر تسجيل سداد
        Button paymentBtn = createToolbarButton("تسجيل سداد", FontAwesomeIcon.CREDIT_CARD, this::showPaymentDialog);
        
        // فاصل
        Separator separator = new Separator();
        separator.setOrientation(javafx.geometry.Orientation.VERTICAL);
        
        // زر تقارير الموظفين
        Button reportsBtn = createToolbarButton("تقارير الموظفين", FontAwesomeIcon.BAR_CHART, this::showEmployeeReports);
        
        // زر الإعدادات
        Button settingsBtn = createToolbarButton("الإعدادات", FontAwesomeIcon.COG, this::showSettings);
        
        toolbar.getChildren().addAll(employeesBtn, advanceBtn, dueBtn, paymentBtn, separator, reportsBtn, settingsBtn);
        return toolbar;
    }
    
    /**
     * إنشاء زر شريط الأدوات
     */
    private Button createToolbarButton(String text, FontAwesomeIcon icon, Runnable action) {
        Button button = new Button(text);
        FontAwesomeIconView iconView = new FontAwesomeIconView(icon);
        iconView.setSize("16px");
        button.setGraphic(iconView);
        button.getStyleClass().add("toolbar-button");
        button.setOnAction(e -> action.run());
        return button;
    }
    
    /**
     * إنشاء التبويبات الرئيسية
     */
    private TabPane createMainTabs() {
        TabPane tabPane = new TabPane();
        tabPane.getStyleClass().add("main-tabs");
        
        // تبويب الموظفين
        Tab employeesTab = new Tab("الموظفين");
        employeesTab.setContent(createEmployeesTab());
        employeesTab.setClosable(false);
        
        // تبويب السلف
        Tab advancesTab = new Tab("السلف");
        advancesTab.setContent(createAdvancesTab());
        advancesTab.setClosable(false);
        
        // تبويب المستحقات
        Tab duesTab = new Tab("المستحقات");
        duesTab.setContent(createDuesTab());
        duesTab.setClosable(false);
        
        // تبويب المدفوعات
        Tab paymentsTab = new Tab("المدفوعات");
        paymentsTab.setContent(createPaymentsTab());
        paymentsTab.setClosable(false);
        
        tabPane.getTabs().addAll(employeesTab, advancesTab, duesTab, paymentsTab);
        return tabPane;
    }
    
    /**
     * إنشاء تبويب الموظفين
     */
    private VBox createEmployeesTab() {
        VBox container = new VBox(15);
        container.setPadding(new Insets(15));
        
        // شريط البحث
        HBox searchBox = new HBox(10);
        searchBox.setAlignment(Pos.CENTER_LEFT);
        
        Label searchLabel = new Label("البحث:");
        TextField searchField = new TextField();
        searchField.setPromptText("ابحث بالاسم أو الرقم الوظيفي");
        searchField.setPrefWidth(200);
        
        Button addEmployeeBtn = new Button("إضافة موظف جديد");
        FontAwesomeIconView addIcon = new FontAwesomeIconView(FontAwesomeIcon.USER_PLUS);
        addIcon.setSize("14px");
        addEmployeeBtn.setGraphic(addIcon);
        addEmployeeBtn.getStyleClass().add("add-button");
        addEmployeeBtn.setOnAction(e -> showAddEmployeeDialog());
        
        searchBox.getChildren().addAll(searchLabel, searchField, addEmployeeBtn);
        
        // جدول الموظفين
        TableView<Employee> employeesTable = createEmployeesTable();
        
        container.getChildren().addAll(searchBox, employeesTable);
        return container;
    }
    
    /**
     * إنشاء جدول الموظفين
     */
    private TableView<Employee> createEmployeesTable() {
        TableView<Employee> table = new TableView<>();
        table.setItems(employees);
        // إزالة قيد الارتفاع ليتوسع الجدول تلقائياً
        table.setMinHeight(300);
        VBox.setVgrow(table, Priority.ALWAYS);
        table.getStyleClass().add("data-table");
        
        // العمود: الرقم الوظيفي
        TableColumn<Employee, String> idCol = new TableColumn<>("الرقم الوظيفي");
        idCol.setPrefWidth(100);
        idCol.setCellValueFactory(new PropertyValueFactory<>("employeeId"));
        
        // العمود: الاسم
        TableColumn<Employee, String> nameCol = new TableColumn<>("الاسم");
        nameCol.setPrefWidth(150);
        nameCol.setCellValueFactory(new PropertyValueFactory<>("name"));
        
        // العمود: تاريخ البدء
        TableColumn<Employee, LocalDate> startDateCol = new TableColumn<>("تاريخ البدء");
        startDateCol.setPrefWidth(100);
        startDateCol.setCellValueFactory(new PropertyValueFactory<>("startDate"));
        startDateCol.setCellFactory(col -> new TableCell<Employee, LocalDate>() {
            @Override
            protected void updateItem(LocalDate item, boolean empty) {
                super.updateItem(item, empty);
                if (empty || item == null) {
                    setText(null);
                } else {
                    setText(item.format(dateFormatter));
                }
            }
        });
        
        // العمود: الراتب الأساسي
        TableColumn<Employee, Double> salaryCol = new TableColumn<>("الراتب الأساسي");
        salaryCol.setPrefWidth(120);
        salaryCol.setCellValueFactory(new PropertyValueFactory<>("baseSalary"));
        salaryCol.setCellFactory(col -> new TableCell<Employee, Double>() {
            @Override
            protected void updateItem(Double item, boolean empty) {
                super.updateItem(item, empty);
                if (empty || item == null) {
                    setText(null);
                } else {
                    setText(decimalFormat.format(item) + " ج.م");
                }
            }
        });
        
        // العمود: المنصب
        TableColumn<Employee, String> positionCol = new TableColumn<>("المنصب");
        positionCol.setPrefWidth(120);
        positionCol.setCellValueFactory(new PropertyValueFactory<>("position"));
        
        // العمود: الإجراءات
        TableColumn<Employee, Void> actionsCol = new TableColumn<>("الإجراءات");
        actionsCol.setPrefWidth(200);
        actionsCol.setCellFactory(col -> new TableCell<Employee, Void>() {
            private final HBox actionBox = new HBox(5);
            private final Button reportBtn = new Button("تقرير");
            private final Button editBtn = new Button("تعديل");
            private final Button deleteBtn = new Button("حذف");
            
            {
                // زر التقرير
                FontAwesomeIconView reportIcon = new FontAwesomeIconView(FontAwesomeIcon.FILE_TEXT);
                reportIcon.setSize("12px");
                reportBtn.setGraphic(reportIcon);
                reportBtn.getStyleClass().add("report-button");
                reportBtn.setOnAction(e -> {
                    Employee employee = getTableView().getItems().get(getIndex());
                    showEmployeeReport(employee);
                });
                
                // زر التعديل
                FontAwesomeIconView editIcon = new FontAwesomeIconView(FontAwesomeIcon.EDIT);
                editIcon.setSize("12px");
                editBtn.setGraphic(editIcon);
                editBtn.getStyleClass().add("edit-button");
                editBtn.setOnAction(e -> {
                    Employee employee = getTableView().getItems().get(getIndex());
                    showEditEmployeeDialog(employee);
                });
                
                // زر الحذف
                FontAwesomeIconView deleteIcon = new FontAwesomeIconView(FontAwesomeIcon.TRASH);
                deleteIcon.setSize("12px");
                deleteBtn.setGraphic(deleteIcon);
                deleteBtn.getStyleClass().add("delete-button");
                deleteBtn.setOnAction(e -> {
                    Employee employee = getTableView().getItems().get(getIndex());
                    deleteEmployee(employee);
                });
                
                actionBox.setAlignment(Pos.CENTER);
                actionBox.getChildren().addAll(reportBtn, editBtn, deleteBtn);
            }
            
            @Override
            protected void updateItem(Void item, boolean empty) {
                super.updateItem(item, empty);
                if (empty) {
                    setGraphic(null);
                } else {
                    setGraphic(actionBox);
                }
            }
        });
        
        table.getColumns().addAll(idCol, nameCol, startDateCol, salaryCol, positionCol, actionsCol);
        return table;
    }

    // أحداث الأزرار الرئيسية
    private void showEmployeeManagement() {
        showPlaceholderDialog("إدارة الموظفين", "يمكنك إدارة الموظفين من تبويب الموظفين أعلاه");
    }

    private void showAdvanceDialog() {
        showAdvanceDialog(null);
    }

    private void showSalaryDueDialog() {
        showSalaryDueDialog(null);
    }

    private void showPaymentDialog() {
        showPaymentDialog(null);
    }

    private void showEmployeeReports() {
        showPlaceholderDialog("تقارير الموظفين", "اختر موظف من قائمة الموظفين وانقر على 'تقرير' لعرض تقريره");
    }

    private void showSettings() {
        showManagersDialog();
    }

    // وظائف الموظفين
    private void showAddEmployeeDialog() {
        showEmployeeDialog(null);
    }

    private void showEditEmployeeDialog(Employee employee) {
        showEmployeeDialog(employee);
    }

    private void deleteEmployee(Employee employee) {
        Alert alert = new Alert(Alert.AlertType.CONFIRMATION);
        alert.setTitle("تأكيد الحذف");
        alert.setHeaderText("حذف الموظف");
        alert.setContentText("هل أنت متأكد من حذف الموظف: " + employee.getName() + "؟");

        alert.showAndWait().ifPresent(response -> {
            if (response == ButtonType.OK) {
                employees.remove(employee);
                showPlaceholderDialog("نجح", "تم حذف الموظف بنجاح");
            }
        });
    }

    private void showEmployeeReport(Employee employee) {
        showEmployeeReportDialog(employee);
    }

    private void showPlaceholderDialog(String title, String message) {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle(title);
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }

    /**
     * نافذة إضافة/تعديل موظف
     */
    private void showEmployeeDialog(Employee employee) {
        boolean isEdit = (employee != null);

        Stage dialog = new Stage();
        dialog.setTitle(isEdit ? "تعديل موظف" : "إضافة موظف جديد");
        dialog.initModality(Modality.APPLICATION_MODAL);
        dialog.setWidth(500);
        dialog.setHeight(600);

        VBox mainLayout = new VBox(20);
        mainLayout.setPadding(new Insets(20));

        // العنوان
        Label titleLabel = new Label(isEdit ? "تعديل بيانات الموظف" : "إضافة موظف جديد");
        titleLabel.getStyleClass().add("dialog-title");

        // نموذج البيانات
        GridPane form = new GridPane();
        form.setHgap(15);
        form.setVgap(15);

        // الرقم الوظيفي
        Label idLabel = new Label("الرقم الوظيفي:");
        TextField idField = new TextField();
        idField.setPromptText("EMP001");
        if (isEdit) {
            idField.setText(employee.getEmployeeId());
            idField.setDisable(true);
        }

        // الاسم
        Label nameLabel = new Label("الاسم:");
        TextField nameField = new TextField();
        nameField.setPromptText("أدخل اسم الموظف");
        if (isEdit) nameField.setText(employee.getName());

        // تاريخ البدء
        Label startDateLabel = new Label("تاريخ بدء العمل:");
        DatePicker startDatePicker = new DatePicker();
        if (isEdit) startDatePicker.setValue(employee.getStartDate());
        else startDatePicker.setValue(LocalDate.now());

        // الراتب الأساسي
        Label salaryLabel = new Label("الراتب الأساسي:");
        TextField salaryField = new TextField();
        salaryField.setPromptText("0.00");
        if (isEdit) salaryField.setText(String.valueOf(employee.getBaseSalary()));

        // المنصب
        Label positionLabel = new Label("المنصب:");
        TextField positionField = new TextField();
        positionField.setPromptText("أدخل المنصب");
        if (isEdit) positionField.setText(employee.getPosition());

        // القسم
        Label departmentLabel = new Label("القسم:");
        TextField departmentField = new TextField();
        departmentField.setPromptText("أدخل القسم");
        if (isEdit) departmentField.setText(employee.getDepartment());

        // الهاتف
        Label phoneLabel = new Label("الهاتف:");
        TextField phoneField = new TextField();
        phoneField.setPromptText("01234567890");
        if (isEdit) phoneField.setText(employee.getPhone());

        // البريد الإلكتروني
        Label emailLabel = new Label("البريد الإلكتروني:");
        TextField emailField = new TextField();
        emailField.setPromptText("<EMAIL>");
        if (isEdit) emailField.setText(employee.getEmail());

        // العنوان
        Label addressLabel = new Label("العنوان:");
        TextArea addressArea = new TextArea();
        addressArea.setPromptText("أدخل العنوان");
        addressArea.setPrefRowCount(2);
        if (isEdit) addressArea.setText(employee.getAddress());

        // ترتيب الحقول في الشبكة
        form.add(idLabel, 0, 0);
        form.add(idField, 1, 0);
        form.add(nameLabel, 0, 1);
        form.add(nameField, 1, 1);
        form.add(startDateLabel, 0, 2);
        form.add(startDatePicker, 1, 2);
        form.add(salaryLabel, 0, 3);
        form.add(salaryField, 1, 3);
        form.add(positionLabel, 0, 4);
        form.add(positionField, 1, 4);
        form.add(departmentLabel, 0, 5);
        form.add(departmentField, 1, 5);
        form.add(phoneLabel, 0, 6);
        form.add(phoneField, 1, 6);
        form.add(emailLabel, 0, 7);
        form.add(emailField, 1, 7);
        form.add(addressLabel, 0, 8);
        form.add(addressArea, 1, 8);

        // أزرار الحوار
        HBox buttonBox = new HBox(10);
        buttonBox.setAlignment(Pos.CENTER_RIGHT);

        Button saveButton = new Button(isEdit ? "حفظ التعديلات" : "إضافة الموظف");
        saveButton.getStyleClass().add("add-button");
        saveButton.setOnAction(e -> {
            if (validateEmployeeForm(nameField, salaryField)) {
                try {
                    if (isEdit) {
                        // تحديث الموظف الموجود
                        employee.setName(nameField.getText().trim());
                        employee.setStartDate(startDatePicker.getValue());
                        employee.setBaseSalary(Double.parseDouble(salaryField.getText().trim()));
                        employee.setPosition(positionField.getText().trim());
                        employee.setDepartment(departmentField.getText().trim());
                        employee.setPhone(phoneField.getText().trim());
                        employee.setEmail(emailField.getText().trim());
                        employee.setAddress(addressArea.getText().trim());

                        showPlaceholderDialog("نجح", "تم تحديث بيانات الموظف بنجاح");
                    } else {
                        // إضافة موظف جديد
                        String employeeId = idField.getText().trim();
                        if (employeeId.isEmpty()) {
                            employeeId = "EMP" + String.format("%03d", employees.size() + 1);
                        }

                        Employee newEmployee = new Employee(
                            employeeId,
                            nameField.getText().trim(),
                            startDatePicker.getValue(),
                            Double.parseDouble(salaryField.getText().trim()),
                            positionField.getText().trim(),
                            departmentField.getText().trim(),
                            phoneField.getText().trim(),
                            emailField.getText().trim(),
                            addressArea.getText().trim()
                        );

                        employees.add(newEmployee);
                        showPlaceholderDialog("نجح", "تم إضافة الموظف بنجاح");
                    }

                    dialog.close();
                } catch (NumberFormatException ex) {
                    showPlaceholderDialog("خطأ", "يرجى إدخال راتب صحيح");
                }
            }
        });

        Button cancelButton = new Button("إلغاء");
        cancelButton.setOnAction(e -> dialog.close());

        buttonBox.getChildren().addAll(saveButton, cancelButton);

        mainLayout.getChildren().addAll(titleLabel, form, buttonBox);

        Scene scene = new Scene(mainLayout);
        scene.getStylesheets().add(getClass().getResource("/css/styles.css").toExternalForm());
        dialog.setScene(scene);
        dialog.showAndWait();
    }

    /**
     * التحقق من صحة نموذج الموظف
     */
    private boolean validateEmployeeForm(TextField nameField, TextField salaryField) {
        if (nameField.getText().trim().isEmpty()) {
            showPlaceholderDialog("تحذير", "يرجى إدخال اسم الموظف");
            return false;
        }

        if (salaryField.getText().trim().isEmpty()) {
            showPlaceholderDialog("تحذير", "يرجى إدخال الراتب الأساسي");
            return false;
        }

        try {
            double salary = Double.parseDouble(salaryField.getText().trim());
            if (salary <= 0) {
                showPlaceholderDialog("تحذير", "يرجى إدخال راتب أكبر من صفر");
                return false;
            }
        } catch (NumberFormatException e) {
            showPlaceholderDialog("تحذير", "يرجى إدخال راتب صحيح");
            return false;
        }

        return true;
    }

    /**
     * نافذة تسجيل سلفة
     */
    private void showAdvanceDialog(Employee selectedEmployee) {
        Stage dialog = new Stage();
        dialog.setTitle("تسجيل سلفة جديدة");
        dialog.initModality(Modality.APPLICATION_MODAL);
        dialog.setWidth(450);
        dialog.setHeight(400);

        VBox mainLayout = new VBox(20);
        mainLayout.setPadding(new Insets(20));

        // العنوان
        Label titleLabel = new Label("تسجيل سلفة جديدة");
        titleLabel.getStyleClass().add("dialog-title");

        // نموذج البيانات
        GridPane form = new GridPane();
        form.setHgap(15);
        form.setVgap(15);

        // اختيار الموظف
        Label employeeLabel = new Label("الموظف:");
        ComboBox<Employee> employeeCombo = new ComboBox<>();
        employeeCombo.setItems(employees);
        employeeCombo.setPrefWidth(200);
        employeeCombo.setPromptText("اختر الموظف");
        if (selectedEmployee != null) {
            employeeCombo.setValue(selectedEmployee);
        }

        // المبلغ
        Label amountLabel = new Label("مبلغ السلفة:");
        TextField amountField = new TextField();
        amountField.setPromptText("0.00");

        // التاريخ
        Label dateLabel = new Label("تاريخ السلفة:");
        DatePicker datePicker = new DatePicker();
        datePicker.setValue(LocalDate.now());

        // المدير
        Label managerLabel = new Label("المدير:");
        ComboBox<Manager> managerCombo = new ComboBox<>();
        managerCombo.setItems(managers);
        managerCombo.setPrefWidth(200);
        managerCombo.setPromptText("اختر المدير");

        // الملاحظات
        Label notesLabel = new Label("الملاحظات:");
        TextArea notesArea = new TextArea();
        notesArea.setPromptText("ملاحظات اختيارية");
        notesArea.setPrefRowCount(3);

        // ترتيب الحقول
        form.add(employeeLabel, 0, 0);
        form.add(employeeCombo, 1, 0);
        form.add(amountLabel, 0, 1);
        form.add(amountField, 1, 1);
        form.add(dateLabel, 0, 2);
        form.add(datePicker, 1, 2);
        form.add(managerLabel, 0, 3);
        form.add(managerCombo, 1, 3);
        form.add(notesLabel, 0, 4);
        form.add(notesArea, 1, 4);

        // أزرار الحوار
        HBox buttonBox = new HBox(10);
        buttonBox.setAlignment(Pos.CENTER_RIGHT);

        Button saveButton = new Button("حفظ السلفة");
        saveButton.getStyleClass().add("add-button");
        saveButton.setOnAction(e -> {
            if (validateAdvanceForm(employeeCombo, amountField, managerCombo)) {
                try {
                    String advanceId = "ADV" + String.format("%03d", advances.size() + 1);
                    Employee employee = employeeCombo.getValue();
                    Manager manager = managerCombo.getValue();

                    Advance newAdvance = new Advance(
                        advanceId,
                        employee.getEmployeeId(),
                        employee.getName(),
                        Double.parseDouble(amountField.getText().trim()),
                        datePicker.getValue(),
                        manager.getName(),
                        notesArea.getText().trim()
                    );

                    advances.add(newAdvance);
                    showPlaceholderDialog("نجح", "تم تسجيل السلفة بنجاح");
                    dialog.close();
                } catch (NumberFormatException ex) {
                    showPlaceholderDialog("خطأ", "يرجى إدخال مبلغ صحيح");
                }
            }
        });

        Button cancelButton = new Button("إلغاء");
        cancelButton.setOnAction(e -> dialog.close());

        buttonBox.getChildren().addAll(saveButton, cancelButton);

        mainLayout.getChildren().addAll(titleLabel, form, buttonBox);

        Scene scene = new Scene(mainLayout);
        scene.getStylesheets().add(getClass().getResource("/css/styles.css").toExternalForm());
        dialog.setScene(scene);
        dialog.showAndWait();
    }

    /**
     * التحقق من صحة نموذج السلفة
     */
    private boolean validateAdvanceForm(ComboBox<Employee> employeeCombo, TextField amountField, ComboBox<Manager> managerCombo) {
        if (employeeCombo.getValue() == null) {
            showPlaceholderDialog("تحذير", "يرجى اختيار الموظف");
            return false;
        }

        if (amountField.getText().trim().isEmpty()) {
            showPlaceholderDialog("تحذير", "يرجى إدخال مبلغ السلفة");
            return false;
        }

        try {
            double amount = Double.parseDouble(amountField.getText().trim());
            if (amount <= 0) {
                showPlaceholderDialog("تحذير", "يرجى إدخال مبلغ أكبر من صفر");
                return false;
            }
        } catch (NumberFormatException e) {
            showPlaceholderDialog("تحذير", "يرجى إدخال مبلغ صحيح");
            return false;
        }

        if (managerCombo.getValue() == null) {
            showPlaceholderDialog("تحذير", "يرجى اختيار المدير");
            return false;
        }

        return true;
    }

    /**
     * نافذة تسجيل مستحق
     */
    private void showSalaryDueDialog(Employee selectedEmployee) {
        Stage dialog = new Stage();
        dialog.setTitle("تسجيل راتب مستحق");
        dialog.initModality(Modality.APPLICATION_MODAL);
        dialog.setWidth(500);
        dialog.setHeight(550);

        VBox mainLayout = new VBox(20);
        mainLayout.setPadding(new Insets(20));

        // العنوان
        Label titleLabel = new Label("تسجيل راتب مستحق");
        titleLabel.getStyleClass().add("dialog-title");

        // نموذج البيانات
        GridPane form = new GridPane();
        form.setHgap(15);
        form.setVgap(15);

        // اختيار الموظف
        Label employeeLabel = new Label("الموظف:");
        ComboBox<Employee> employeeCombo = new ComboBox<>();
        employeeCombo.setItems(employees);
        employeeCombo.setPrefWidth(200);
        employeeCombo.setPromptText("اختر الموظف");
        if (selectedEmployee != null) {
            employeeCombo.setValue(selectedEmployee);
        }

        // الشهر المستحق
        Label monthLabel = new Label("الشهر المستحق:");
        ComboBox<YearMonth> monthCombo = new ComboBox<>();
        // إضافة الأشهر الحالية والسابقة
        for (int i = 0; i < 12; i++) {
            monthCombo.getItems().add(YearMonth.now().minusMonths(i));
        }
        monthCombo.setValue(YearMonth.now().minusMonths(1)); // الشهر الماضي افتراضياً
        monthCombo.setPrefWidth(150);

        // الراتب الأساسي
        Label baseSalaryLabel = new Label("الراتب الأساسي:");
        TextField baseSalaryField = new TextField();
        baseSalaryField.setPromptText("0.00");
        baseSalaryField.setDisable(true); // سيتم ملؤه تلقائياً

        // أوفر تايم
        Label overtimeLabel = new Label("أوفر تايم:");
        TextField overtimeField = new TextField();
        overtimeField.setPromptText("0.00");
        overtimeField.setText("0.00");

        // مكافآت
        Label bonusLabel = new Label("مكافآت:");
        TextField bonusField = new TextField();
        bonusField.setPromptText("0.00");
        bonusField.setText("0.00");

        // خصومات
        Label deductionLabel = new Label("خصومات:");
        TextField deductionField = new TextField();
        deductionField.setPromptText("0.00");
        deductionField.setText("0.00");

        // المستحق النهائي
        Label finalDueLabel = new Label("المستحق النهائي:");
        TextField finalDueField = new TextField();
        finalDueField.setPromptText("0.00");
        finalDueField.setDisable(true);
        finalDueField.getStyleClass().add("calculated-field");

        // الملاحظات
        Label notesLabel = new Label("الملاحظات:");
        TextArea notesArea = new TextArea();
        notesArea.setPromptText("ملاحظات اختيارية");
        notesArea.setPrefRowCount(2);

        // تحديث الراتب الأساسي عند اختيار الموظف
        employeeCombo.setOnAction(e -> {
            Employee employee = employeeCombo.getValue();
            if (employee != null) {
                baseSalaryField.setText(decimalFormat.format(employee.getBaseSalary()));
                calculateFinalDue(baseSalaryField, overtimeField, bonusField, deductionField, finalDueField);
            }
        });

        // تحديث المستحق النهائي عند تغيير أي قيمة
        Runnable updateFinalDue = () -> calculateFinalDue(baseSalaryField, overtimeField, bonusField, deductionField, finalDueField);
        overtimeField.textProperty().addListener((obs, oldVal, newVal) -> updateFinalDue.run());
        bonusField.textProperty().addListener((obs, oldVal, newVal) -> updateFinalDue.run());
        deductionField.textProperty().addListener((obs, oldVal, newVal) -> updateFinalDue.run());

        // ترتيب الحقول
        form.add(employeeLabel, 0, 0);
        form.add(employeeCombo, 1, 0);
        form.add(monthLabel, 0, 1);
        form.add(monthCombo, 1, 1);
        form.add(baseSalaryLabel, 0, 2);
        form.add(baseSalaryField, 1, 2);
        form.add(overtimeLabel, 0, 3);
        form.add(overtimeField, 1, 3);
        form.add(bonusLabel, 0, 4);
        form.add(bonusField, 1, 4);
        form.add(deductionLabel, 0, 5);
        form.add(deductionField, 1, 5);
        form.add(finalDueLabel, 0, 6);
        form.add(finalDueField, 1, 6);
        form.add(notesLabel, 0, 7);
        form.add(notesArea, 1, 7);

        // أزرار الحوار
        HBox buttonBox = new HBox(10);
        buttonBox.setAlignment(Pos.CENTER_RIGHT);

        Button saveButton = new Button("حفظ المستحق");
        saveButton.getStyleClass().add("add-button");
        saveButton.setOnAction(e -> {
            if (validateSalaryDueForm(employeeCombo, baseSalaryField)) {
                try {
                    String dueId = "DUE" + String.format("%03d", salaryDues.size() + 1);
                    Employee employee = employeeCombo.getValue();

                    SalaryDue newDue = new SalaryDue(
                        dueId,
                        employee.getEmployeeId(),
                        employee.getName(),
                        monthCombo.getValue(),
                        Double.parseDouble(baseSalaryField.getText().replace(",", "")),
                        Double.parseDouble(overtimeField.getText().trim()),
                        Double.parseDouble(bonusField.getText().trim()),
                        Double.parseDouble(deductionField.getText().trim())
                    );

                    newDue.setNotes(notesArea.getText().trim());
                    salaryDues.add(newDue);
                    showPlaceholderDialog("نجح", "تم تسجيل المستحق بنجاح");
                    dialog.close();
                } catch (NumberFormatException ex) {
                    showPlaceholderDialog("خطأ", "يرجى إدخال قيم صحيحة");
                }
            }
        });

        Button cancelButton = new Button("إلغاء");
        cancelButton.setOnAction(e -> dialog.close());

        buttonBox.getChildren().addAll(saveButton, cancelButton);

        mainLayout.getChildren().addAll(titleLabel, form, buttonBox);

        Scene scene = new Scene(mainLayout);
        scene.getStylesheets().add(getClass().getResource("/css/styles.css").toExternalForm());
        dialog.setScene(scene);
        dialog.showAndWait();
    }

    /**
     * حساب المستحق النهائي
     */
    private void calculateFinalDue(TextField baseSalaryField, TextField overtimeField,
                                  TextField bonusField, TextField deductionField, TextField finalDueField) {
        try {
            double baseSalary = parseDouble(baseSalaryField.getText());
            double overtime = parseDouble(overtimeField.getText());
            double bonus = parseDouble(bonusField.getText());
            double deduction = parseDouble(deductionField.getText());

            double finalDue = baseSalary + overtime + bonus - deduction;
            finalDueField.setText(decimalFormat.format(finalDue));
        } catch (NumberFormatException e) {
            finalDueField.setText("0.00");
        }
    }

    /**
     * تحويل النص إلى رقم مع معالجة الأخطاء
     */
    private double parseDouble(String text) {
        if (text == null || text.trim().isEmpty()) {
            return 0.0;
        }
        return Double.parseDouble(text.replace(",", "").trim());
    }

    /**
     * التحقق من صحة نموذج المستحق
     */
    private boolean validateSalaryDueForm(ComboBox<Employee> employeeCombo, TextField baseSalaryField) {
        if (employeeCombo.getValue() == null) {
            showPlaceholderDialog("تحذير", "يرجى اختيار الموظف");
            return false;
        }

        if (baseSalaryField.getText().trim().isEmpty()) {
            showPlaceholderDialog("تحذير", "يرجى اختيار موظف لتحديد الراتب الأساسي");
            return false;
        }

        return true;
    }

    /**
     * نافذة تسجيل سداد
     */
    private void showPaymentDialog(Employee selectedEmployee) {
        PayrollDialogs dialogs = new PayrollDialogs();
        dialogs.showPaymentDialog(selectedEmployee, employees, managers, salaryPaids);
    }

    /**
     * نافذة إدارة المدراء
     */
    private void showManagersDialog() {
        PayrollDialogs dialogs = new PayrollDialogs();
        dialogs.showManagersDialog(managers);
    }

    /**
     * نافذة تقرير الموظف
     */
    private void showEmployeeReportDialog(Employee employee) {
        Stage dialog = new Stage();
        dialog.setTitle("تقرير الموظف - " + employee.getName());
        dialog.initModality(Modality.APPLICATION_MODAL);
        dialog.setWidth(800);
        dialog.setHeight(700);

        VBox mainLayout = new VBox(20);
        mainLayout.setPadding(new Insets(20));

        // العنوان
        Label titleLabel = new Label("تقرير الموظف: " + employee.getName());
        titleLabel.getStyleClass().add("dialog-title");

        // معلومات الموظف الأساسية
        GridPane employeeInfo = new GridPane();
        employeeInfo.setHgap(15);
        employeeInfo.setVgap(10);
        employeeInfo.getStyleClass().add("employee-info");

        employeeInfo.add(new Label("الرقم الوظيفي:"), 0, 0);
        employeeInfo.add(new Label(employee.getEmployeeId()), 1, 0);
        employeeInfo.add(new Label("تاريخ البدء:"), 2, 0);
        employeeInfo.add(new Label(employee.getStartDate().format(dateFormatter)), 3, 0);

        employeeInfo.add(new Label("الراتب الأساسي:"), 0, 1);
        employeeInfo.add(new Label(decimalFormat.format(employee.getBaseSalary()) + " ج.م"), 1, 1);
        employeeInfo.add(new Label("المنصب:"), 2, 1);
        employeeInfo.add(new Label(employee.getPosition() != null ? employee.getPosition() : "غير محدد"), 3, 1);

        // حساب الملخص المالي
        double totalAdvances = advances.stream()
            .filter(a -> a.getEmployeeId().equals(employee.getEmployeeId()))
            .mapToDouble(Advance::getAmount)
            .sum();

        double totalDues = salaryDues.stream()
            .filter(d -> d.getEmployeeId().equals(employee.getEmployeeId()))
            .mapToDouble(SalaryDue::getFinalDue)
            .sum();

        double totalPaid = salaryPaids.stream()
            .filter(p -> p.getEmployeeId().equals(employee.getEmployeeId()))
            .mapToDouble(SalaryPaid::getAmount)
            .sum();

        double balance = totalDues - totalPaid - totalAdvances;

        // الملخص المالي
        GridPane financialSummary = new GridPane();
        financialSummary.setHgap(20);
        financialSummary.setVgap(10);
        financialSummary.getStyleClass().add("financial-summary");

        financialSummary.add(new Label("إجمالي السلف:"), 0, 0);
        financialSummary.add(new Label(decimalFormat.format(totalAdvances) + " ج.م"), 1, 0);

        financialSummary.add(new Label("إجمالي المستحقات:"), 0, 1);
        financialSummary.add(new Label(decimalFormat.format(totalDues) + " ج.م"), 1, 1);

        financialSummary.add(new Label("إجمالي المسدد:"), 0, 2);
        financialSummary.add(new Label(decimalFormat.format(totalPaid) + " ج.م"), 1, 2);

        financialSummary.add(new Label("الرصيد:"), 0, 3);
        Label balanceLabel = new Label(decimalFormat.format(Math.abs(balance)) + " ج.م " +
                                     (balance >= 0 ? "(لصالح الموظف)" : "(على الموظف)"));
        balanceLabel.getStyleClass().add(balance >= 0 ? "positive-balance" : "negative-balance");
        financialSummary.add(balanceLabel, 1, 3);

        // تبويبات التفاصيل
        TabPane detailsTabs = new TabPane();

        // تبويب السلف
        Tab advancesTab = new Tab("السلف");
        TableView<Advance> advancesTable = createEmployeeAdvancesTable(employee);
        advancesTab.setContent(advancesTable);
        advancesTab.setClosable(false);

        // تبويب المستحقات
        Tab duesTab = new Tab("المستحقات");
        TableView<SalaryDue> duesTable = createEmployeeDuesTable(employee);
        duesTab.setContent(duesTable);
        duesTab.setClosable(false);

        // تبويب المدفوعات
        Tab paymentsTab = new Tab("المدفوعات");
        TableView<SalaryPaid> paymentsTable = createEmployeePaymentsTable(employee);
        paymentsTab.setContent(paymentsTable);
        paymentsTab.setClosable(false);

        detailsTabs.getTabs().addAll(advancesTab, duesTab, paymentsTab);

        // أزرار الحوار
        HBox buttonBox = new HBox(15);
        buttonBox.setAlignment(Pos.CENTER_RIGHT);

        Button printButton = new Button("طباعة");
        FontAwesomeIconView printIcon = new FontAwesomeIconView(FontAwesomeIcon.PRINT);
        printIcon.setSize("14px");
        printButton.setGraphic(printIcon);
        printButton.getStyleClass().add("print-button");
        printButton.setOnAction(e -> showEmployeeReportPrint(employee, "طباعة"));

        Button pdfButton = new Button("حفظ PDF");
        FontAwesomeIconView pdfIcon = new FontAwesomeIconView(FontAwesomeIcon.FILE_PDF_ALT);
        pdfIcon.setSize("14px");
        pdfButton.setGraphic(pdfIcon);
        pdfButton.getStyleClass().add("pdf-button");
        pdfButton.setOnAction(e -> showEmployeeReportPrint(employee, "PDF"));

        Button closeButton = new Button("إغلاق");
        closeButton.setOnAction(e -> dialog.close());

        buttonBox.getChildren().addAll(printButton, pdfButton, closeButton);

        mainLayout.getChildren().addAll(
            titleLabel,
            new Label("معلومات الموظف:"),
            employeeInfo,
            new Separator(),
            new Label("الملخص المالي:"),
            financialSummary,
            new Separator(),
            new Label("تفاصيل العمليات:"),
            detailsTabs,
            buttonBox
        );

        Scene scene = new Scene(mainLayout);
        scene.getStylesheets().add(getClass().getResource("/css/styles.css").toExternalForm());
        dialog.setScene(scene);
        dialog.showAndWait();
    }

    /**
     * إنشاء جدول سلف الموظف
     */
    private TableView<Advance> createEmployeeAdvancesTable(Employee employee) {
        TableView<Advance> table = new TableView<>();
        table.setMinHeight(150);
        VBox.setVgrow(table, Priority.ALWAYS);

        // تصفية السلف للموظف المحدد
        ObservableList<Advance> employeeAdvances = advances.filtered(
            advance -> advance.getEmployeeId().equals(employee.getEmployeeId())
        );
        table.setItems(employeeAdvances);

        // الأعمدة
        TableColumn<Advance, String> idCol = new TableColumn<>("رقم السلفة");
        idCol.setPrefWidth(100);
        idCol.setCellValueFactory(new PropertyValueFactory<>("advanceId"));

        TableColumn<Advance, Double> amountCol = new TableColumn<>("المبلغ");
        amountCol.setPrefWidth(100);
        amountCol.setCellValueFactory(new PropertyValueFactory<>("amount"));
        amountCol.setCellFactory(col -> new TableCell<Advance, Double>() {
            @Override
            protected void updateItem(Double item, boolean empty) {
                super.updateItem(item, empty);
                if (empty || item == null) {
                    setText(null);
                } else {
                    setText(decimalFormat.format(item) + " ج.م");
                }
            }
        });

        TableColumn<Advance, LocalDate> dateCol = new TableColumn<>("التاريخ");
        dateCol.setPrefWidth(100);
        dateCol.setCellValueFactory(new PropertyValueFactory<>("date"));
        dateCol.setCellFactory(col -> new TableCell<Advance, LocalDate>() {
            @Override
            protected void updateItem(LocalDate item, boolean empty) {
                super.updateItem(item, empty);
                if (empty || item == null) {
                    setText(null);
                } else {
                    setText(item.format(dateFormatter));
                }
            }
        });

        TableColumn<Advance, String> managerCol = new TableColumn<>("المدير");
        managerCol.setPrefWidth(120);
        managerCol.setCellValueFactory(new PropertyValueFactory<>("managerName"));

        TableColumn<Advance, String> notesCol = new TableColumn<>("الملاحظات");
        notesCol.setPrefWidth(200);
        notesCol.setCellValueFactory(new PropertyValueFactory<>("notes"));

        table.getColumns().addAll(idCol, amountCol, dateCol, managerCol, notesCol);
        return table;
    }

    /**
     * إنشاء جدول مستحقات الموظف
     */
    private TableView<SalaryDue> createEmployeeDuesTable(Employee employee) {
        TableView<SalaryDue> table = new TableView<>();
        table.setPrefHeight(200);

        // تصفية المستحقات للموظف المحدد
        ObservableList<SalaryDue> employeeDues = salaryDues.filtered(
            due -> due.getEmployeeId().equals(employee.getEmployeeId())
        );
        table.setItems(employeeDues);

        // الأعمدة
        TableColumn<SalaryDue, String> idCol = new TableColumn<>("رقم المستحق");
        idCol.setPrefWidth(100);
        idCol.setCellValueFactory(new PropertyValueFactory<>("dueId"));

        TableColumn<SalaryDue, YearMonth> monthCol = new TableColumn<>("الشهر");
        monthCol.setPrefWidth(80);
        monthCol.setCellValueFactory(new PropertyValueFactory<>("month"));
        monthCol.setCellFactory(col -> new TableCell<SalaryDue, YearMonth>() {
            @Override
            protected void updateItem(YearMonth item, boolean empty) {
                super.updateItem(item, empty);
                if (empty || item == null) {
                    setText(null);
                } else {
                    setText(item.getMonthValue() + "/" + item.getYear());
                }
            }
        });

        TableColumn<SalaryDue, Double> baseSalaryCol = new TableColumn<>("الراتب الأساسي");
        baseSalaryCol.setPrefWidth(100);
        baseSalaryCol.setCellValueFactory(new PropertyValueFactory<>("baseSalary"));
        baseSalaryCol.setCellFactory(col -> new TableCell<SalaryDue, Double>() {
            @Override
            protected void updateItem(Double item, boolean empty) {
                super.updateItem(item, empty);
                if (empty || item == null) {
                    setText(null);
                } else {
                    setText(decimalFormat.format(item));
                }
            }
        });

        TableColumn<SalaryDue, Double> overtimeCol = new TableColumn<>("أوفر تايم");
        overtimeCol.setPrefWidth(80);
        overtimeCol.setCellValueFactory(new PropertyValueFactory<>("overtime"));
        overtimeCol.setCellFactory(col -> new TableCell<SalaryDue, Double>() {
            @Override
            protected void updateItem(Double item, boolean empty) {
                super.updateItem(item, empty);
                if (empty || item == null) {
                    setText(null);
                } else {
                    setText(decimalFormat.format(item));
                }
            }
        });

        TableColumn<SalaryDue, Double> bonusCol = new TableColumn<>("مكافآت");
        bonusCol.setPrefWidth(80);
        bonusCol.setCellValueFactory(new PropertyValueFactory<>("bonus"));
        bonusCol.setCellFactory(col -> new TableCell<SalaryDue, Double>() {
            @Override
            protected void updateItem(Double item, boolean empty) {
                super.updateItem(item, empty);
                if (empty || item == null) {
                    setText(null);
                } else {
                    setText(decimalFormat.format(item));
                }
            }
        });

        TableColumn<SalaryDue, Double> deductionCol = new TableColumn<>("خصومات");
        deductionCol.setPrefWidth(80);
        deductionCol.setCellValueFactory(new PropertyValueFactory<>("deduction"));
        deductionCol.setCellFactory(col -> new TableCell<SalaryDue, Double>() {
            @Override
            protected void updateItem(Double item, boolean empty) {
                super.updateItem(item, empty);
                if (empty || item == null) {
                    setText(null);
                } else {
                    setText(decimalFormat.format(item));
                }
            }
        });

        TableColumn<SalaryDue, Double> finalDueCol = new TableColumn<>("المستحق النهائي");
        finalDueCol.setPrefWidth(120);
        finalDueCol.setCellValueFactory(new PropertyValueFactory<>("finalDue"));
        finalDueCol.setCellFactory(col -> new TableCell<SalaryDue, Double>() {
            @Override
            protected void updateItem(Double item, boolean empty) {
                super.updateItem(item, empty);
                getStyleClass().add("calculated-cell");
                if (empty || item == null) {
                    setText(null);
                } else {
                    setText(decimalFormat.format(item) + " ج.م");
                }
            }
        });

        table.getColumns().addAll(idCol, monthCol, baseSalaryCol, overtimeCol, bonusCol, deductionCol, finalDueCol);
        return table;
    }

    /**
     * إنشاء جدول مدفوعات الموظف
     */
    private TableView<SalaryPaid> createEmployeePaymentsTable(Employee employee) {
        TableView<SalaryPaid> table = new TableView<>();
        table.setPrefHeight(200);

        // تصفية المدفوعات للموظف المحدد
        ObservableList<SalaryPaid> employeePayments = salaryPaids.filtered(
            payment -> payment.getEmployeeId().equals(employee.getEmployeeId())
        );
        table.setItems(employeePayments);

        // الأعمدة
        TableColumn<SalaryPaid, String> idCol = new TableColumn<>("رقم السداد");
        idCol.setPrefWidth(100);
        idCol.setCellValueFactory(new PropertyValueFactory<>("paymentId"));

        TableColumn<SalaryPaid, YearMonth> periodCol = new TableColumn<>("الفترة");
        periodCol.setPrefWidth(80);
        periodCol.setCellValueFactory(new PropertyValueFactory<>("period"));
        periodCol.setCellFactory(col -> new TableCell<SalaryPaid, YearMonth>() {
            @Override
            protected void updateItem(YearMonth item, boolean empty) {
                super.updateItem(item, empty);
                if (empty || item == null) {
                    setText(null);
                } else {
                    setText(item.getMonthValue() + "/" + item.getYear());
                }
            }
        });

        TableColumn<SalaryPaid, Double> amountCol = new TableColumn<>("المبلغ");
        amountCol.setPrefWidth(100);
        amountCol.setCellValueFactory(new PropertyValueFactory<>("amount"));
        amountCol.setCellFactory(col -> new TableCell<SalaryPaid, Double>() {
            @Override
            protected void updateItem(Double item, boolean empty) {
                super.updateItem(item, empty);
                if (empty || item == null) {
                    setText(null);
                } else {
                    setText(decimalFormat.format(item) + " ج.م");
                }
            }
        });

        TableColumn<SalaryPaid, LocalDate> dateCol = new TableColumn<>("تاريخ السداد");
        dateCol.setPrefWidth(100);
        dateCol.setCellValueFactory(new PropertyValueFactory<>("date"));
        dateCol.setCellFactory(col -> new TableCell<SalaryPaid, LocalDate>() {
            @Override
            protected void updateItem(LocalDate item, boolean empty) {
                super.updateItem(item, empty);
                if (empty || item == null) {
                    setText(null);
                } else {
                    setText(item.format(dateFormatter));
                }
            }
        });

        TableColumn<SalaryPaid, String> paidByCol = new TableColumn<>("المدير");
        paidByCol.setPrefWidth(120);
        paidByCol.setCellValueFactory(new PropertyValueFactory<>("paidBy"));

        TableColumn<SalaryPaid, String> sourceCol = new TableColumn<>("المصدر");
        sourceCol.setPrefWidth(100);
        sourceCol.setCellValueFactory(new PropertyValueFactory<>("source"));

        table.getColumns().addAll(idCol, periodCol, amountCol, dateCol, paidByCol, sourceCol);
        return table;
    }

    /**
     * إنشاء تبويب السلف
     */
    private VBox createAdvancesTab() {
        VBox container = new VBox(15);
        container.setPadding(new Insets(15));

        // شريط الأدوات
        HBox toolbar = new HBox(10);
        toolbar.setAlignment(Pos.CENTER_LEFT);

        Button addAdvanceBtn = new Button("إضافة سلفة جديدة");
        FontAwesomeIconView addIcon = new FontAwesomeIconView(FontAwesomeIcon.PLUS);
        addIcon.setSize("14px");
        addAdvanceBtn.setGraphic(addIcon);
        addAdvanceBtn.getStyleClass().add("add-button");
        addAdvanceBtn.setOnAction(e -> showAdvanceDialog());

        toolbar.getChildren().add(addAdvanceBtn);

        // جدول السلف
        TableView<Advance> advancesTable = createAdvancesTable();

        container.getChildren().addAll(toolbar, advancesTable);
        return container;
    }

    /**
     * إنشاء جدول السلف
     */
    private TableView<Advance> createAdvancesTable() {
        TableView<Advance> table = new TableView<>();
        table.setItems(advances);
        // إزالة قيد الارتفاع ليتوسع الجدول تلقائياً
        table.setMinHeight(300);
        VBox.setVgrow(table, Priority.ALWAYS);
        table.getStyleClass().add("data-table");

        // العمود: رقم السلفة
        TableColumn<Advance, String> idCol = new TableColumn<>("رقم السلفة");
        idCol.setPrefWidth(100);
        idCol.setCellValueFactory(new PropertyValueFactory<>("advanceId"));

        // العمود: اسم الموظف
        TableColumn<Advance, String> employeeCol = new TableColumn<>("الموظف");
        employeeCol.setPrefWidth(150);
        employeeCol.setCellValueFactory(new PropertyValueFactory<>("employeeName"));

        // العمود: المبلغ
        TableColumn<Advance, Double> amountCol = new TableColumn<>("المبلغ");
        amountCol.setPrefWidth(100);
        amountCol.setCellValueFactory(new PropertyValueFactory<>("amount"));
        amountCol.setCellFactory(col -> new TableCell<Advance, Double>() {
            @Override
            protected void updateItem(Double item, boolean empty) {
                super.updateItem(item, empty);
                if (empty || item == null) {
                    setText(null);
                } else {
                    setText(decimalFormat.format(item) + " ج.م");
                }
            }
        });

        // العمود: التاريخ
        TableColumn<Advance, LocalDate> dateCol = new TableColumn<>("التاريخ");
        dateCol.setPrefWidth(100);
        dateCol.setCellValueFactory(new PropertyValueFactory<>("date"));
        dateCol.setCellFactory(col -> new TableCell<Advance, LocalDate>() {
            @Override
            protected void updateItem(LocalDate item, boolean empty) {
                super.updateItem(item, empty);
                if (empty || item == null) {
                    setText(null);
                } else {
                    setText(item.format(dateFormatter));
                }
            }
        });

        // العمود: المدير
        TableColumn<Advance, String> managerCol = new TableColumn<>("المدير");
        managerCol.setPrefWidth(120);
        managerCol.setCellValueFactory(new PropertyValueFactory<>("managerName"));

        // العمود: الملاحظات
        TableColumn<Advance, String> notesCol = new TableColumn<>("الملاحظات");
        notesCol.setPrefWidth(150);
        notesCol.setCellValueFactory(new PropertyValueFactory<>("notes"));

        table.getColumns().addAll(idCol, employeeCol, amountCol, dateCol, managerCol, notesCol);
        return table;
    }

    /**
     * إنشاء تبويب المستحقات
     */
    private VBox createDuesTab() {
        VBox container = new VBox(15);
        container.setPadding(new Insets(15));

        // شريط الأدوات
        HBox toolbar = new HBox(10);
        toolbar.setAlignment(Pos.CENTER_LEFT);

        Button addDueBtn = new Button("إضافة مستحق جديد");
        FontAwesomeIconView addIcon = new FontAwesomeIconView(FontAwesomeIcon.PLUS);
        addIcon.setSize("14px");
        addDueBtn.setGraphic(addIcon);
        addDueBtn.getStyleClass().add("add-button");
        addDueBtn.setOnAction(e -> showSalaryDueDialog());

        toolbar.getChildren().add(addDueBtn);

        // جدول المستحقات
        TableView<SalaryDue> duesTable = createDuesTable();

        container.getChildren().addAll(toolbar, duesTable);
        return container;
    }

    /**
     * إنشاء جدول المستحقات
     */
    private TableView<SalaryDue> createDuesTable() {
        TableView<SalaryDue> table = new TableView<>();
        table.setItems(salaryDues);
        // إزالة قيد الارتفاع ليتوسع الجدول تلقائياً
        table.setMinHeight(300);
        VBox.setVgrow(table, Priority.ALWAYS);
        table.getStyleClass().add("data-table");

        // العمود: رقم المستحق
        TableColumn<SalaryDue, String> idCol = new TableColumn<>("رقم المستحق");
        idCol.setPrefWidth(100);
        idCol.setCellValueFactory(new PropertyValueFactory<>("dueId"));

        // العمود: اسم الموظف
        TableColumn<SalaryDue, String> employeeCol = new TableColumn<>("الموظف");
        employeeCol.setPrefWidth(150);
        employeeCol.setCellValueFactory(new PropertyValueFactory<>("employeeName"));

        // العمود: الشهر
        TableColumn<SalaryDue, YearMonth> monthCol = new TableColumn<>("الشهر");
        monthCol.setPrefWidth(100);
        monthCol.setCellValueFactory(new PropertyValueFactory<>("month"));
        monthCol.setCellFactory(col -> new TableCell<SalaryDue, YearMonth>() {
            @Override
            protected void updateItem(YearMonth item, boolean empty) {
                super.updateItem(item, empty);
                if (empty || item == null) {
                    setText(null);
                } else {
                    setText(item.getMonthValue() + "/" + item.getYear());
                }
            }
        });

        // العمود: الراتب الأساسي
        TableColumn<SalaryDue, Double> baseSalaryCol = new TableColumn<>("الراتب الأساسي");
        baseSalaryCol.setPrefWidth(100);
        baseSalaryCol.setCellValueFactory(new PropertyValueFactory<>("baseSalary"));
        baseSalaryCol.setCellFactory(col -> new TableCell<SalaryDue, Double>() {
            @Override
            protected void updateItem(Double item, boolean empty) {
                super.updateItem(item, empty);
                if (empty || item == null) {
                    setText(null);
                } else {
                    setText(decimalFormat.format(item));
                }
            }
        });

        // العمود: أوفر تايم
        TableColumn<SalaryDue, Double> overtimeCol = new TableColumn<>("أوفر تايم");
        overtimeCol.setPrefWidth(80);
        overtimeCol.setCellValueFactory(new PropertyValueFactory<>("overtime"));
        overtimeCol.setCellFactory(col -> new TableCell<SalaryDue, Double>() {
            @Override
            protected void updateItem(Double item, boolean empty) {
                super.updateItem(item, empty);
                if (empty || item == null) {
                    setText(null);
                } else {
                    setText(decimalFormat.format(item));
                }
            }
        });

        // العمود: مكافآت
        TableColumn<SalaryDue, Double> bonusCol = new TableColumn<>("مكافآت");
        bonusCol.setPrefWidth(80);
        bonusCol.setCellValueFactory(new PropertyValueFactory<>("bonus"));
        bonusCol.setCellFactory(col -> new TableCell<SalaryDue, Double>() {
            @Override
            protected void updateItem(Double item, boolean empty) {
                super.updateItem(item, empty);
                if (empty || item == null) {
                    setText(null);
                } else {
                    setText(decimalFormat.format(item));
                }
            }
        });

        // العمود: خصومات
        TableColumn<SalaryDue, Double> deductionCol = new TableColumn<>("خصومات");
        deductionCol.setPrefWidth(80);
        deductionCol.setCellValueFactory(new PropertyValueFactory<>("deduction"));
        deductionCol.setCellFactory(col -> new TableCell<SalaryDue, Double>() {
            @Override
            protected void updateItem(Double item, boolean empty) {
                super.updateItem(item, empty);
                if (empty || item == null) {
                    setText(null);
                } else {
                    setText(decimalFormat.format(item));
                }
            }
        });

        // العمود: المستحق النهائي
        TableColumn<SalaryDue, Double> finalDueCol = new TableColumn<>("المستحق النهائي");
        finalDueCol.setPrefWidth(120);
        finalDueCol.setCellValueFactory(new PropertyValueFactory<>("finalDue"));
        finalDueCol.setCellFactory(col -> new TableCell<SalaryDue, Double>() {
            @Override
            protected void updateItem(Double item, boolean empty) {
                super.updateItem(item, empty);
                getStyleClass().add("calculated-cell");
                if (empty || item == null) {
                    setText(null);
                } else {
                    setText(decimalFormat.format(item) + " ج.م");
                }
            }
        });

        table.getColumns().addAll(idCol, employeeCol, monthCol, baseSalaryCol, overtimeCol, bonusCol, deductionCol, finalDueCol);
        return table;
    }

    /**
     * إنشاء تبويب المدفوعات
     */
    private VBox createPaymentsTab() {
        VBox container = new VBox(15);
        container.setPadding(new Insets(15));

        // شريط الأدوات
        HBox toolbar = new HBox(10);
        toolbar.setAlignment(Pos.CENTER_LEFT);

        Button addPaymentBtn = new Button("إضافة سداد جديد");
        FontAwesomeIconView addIcon = new FontAwesomeIconView(FontAwesomeIcon.PLUS);
        addIcon.setSize("14px");
        addPaymentBtn.setGraphic(addIcon);
        addPaymentBtn.getStyleClass().add("add-button");
        addPaymentBtn.setOnAction(e -> showPaymentDialog());

        toolbar.getChildren().add(addPaymentBtn);

        // جدول المدفوعات
        TableView<SalaryPaid> paymentsTable = createPaymentsTable();

        container.getChildren().addAll(toolbar, paymentsTable);
        return container;
    }

    /**
     * إنشاء جدول المدفوعات
     */
    private TableView<SalaryPaid> createPaymentsTable() {
        TableView<SalaryPaid> table = new TableView<>();
        table.setItems(salaryPaids);
        // إزالة قيد الارتفاع ليتوسع الجدول تلقائياً
        table.setMinHeight(300);
        VBox.setVgrow(table, Priority.ALWAYS);
        table.getStyleClass().add("data-table");

        // العمود: رقم السداد
        TableColumn<SalaryPaid, String> idCol = new TableColumn<>("رقم السداد");
        idCol.setPrefWidth(100);
        idCol.setCellValueFactory(new PropertyValueFactory<>("paymentId"));

        // العمود: اسم الموظف
        TableColumn<SalaryPaid, String> employeeCol = new TableColumn<>("الموظف");
        employeeCol.setPrefWidth(150);
        employeeCol.setCellValueFactory(new PropertyValueFactory<>("employeeName"));

        // العمود: الفترة
        TableColumn<SalaryPaid, YearMonth> periodCol = new TableColumn<>("الفترة");
        periodCol.setPrefWidth(100);
        periodCol.setCellValueFactory(new PropertyValueFactory<>("period"));
        periodCol.setCellFactory(col -> new TableCell<SalaryPaid, YearMonth>() {
            @Override
            protected void updateItem(YearMonth item, boolean empty) {
                super.updateItem(item, empty);
                if (empty || item == null) {
                    setText(null);
                } else {
                    setText(item.getMonthValue() + "/" + item.getYear());
                }
            }
        });

        // العمود: المبلغ
        TableColumn<SalaryPaid, Double> amountCol = new TableColumn<>("المبلغ");
        amountCol.setPrefWidth(100);
        amountCol.setCellValueFactory(new PropertyValueFactory<>("amount"));
        amountCol.setCellFactory(col -> new TableCell<SalaryPaid, Double>() {
            @Override
            protected void updateItem(Double item, boolean empty) {
                super.updateItem(item, empty);
                if (empty || item == null) {
                    setText(null);
                } else {
                    setText(decimalFormat.format(item) + " ج.م");
                }
            }
        });

        // العمود: تاريخ السداد
        TableColumn<SalaryPaid, LocalDate> dateCol = new TableColumn<>("تاريخ السداد");
        dateCol.setPrefWidth(100);
        dateCol.setCellValueFactory(new PropertyValueFactory<>("date"));
        dateCol.setCellFactory(col -> new TableCell<SalaryPaid, LocalDate>() {
            @Override
            protected void updateItem(LocalDate item, boolean empty) {
                super.updateItem(item, empty);
                if (empty || item == null) {
                    setText(null);
                } else {
                    setText(item.format(dateFormatter));
                }
            }
        });

        // العمود: المدير
        TableColumn<SalaryPaid, String> paidByCol = new TableColumn<>("المدير");
        paidByCol.setPrefWidth(120);
        paidByCol.setCellValueFactory(new PropertyValueFactory<>("paidBy"));

        // العمود: المصدر
        TableColumn<SalaryPaid, String> sourceCol = new TableColumn<>("المصدر");
        sourceCol.setPrefWidth(100);
        sourceCol.setCellValueFactory(new PropertyValueFactory<>("source"));

        table.getColumns().addAll(idCol, employeeCol, periodCol, amountCol, dateCol, paidByCol, sourceCol);
        return table;
    }

    /**
     * عرض معاينة طباعة تقرير الموظف
     */
    private void showEmployeeReportPrint(Employee employee, String action) {
        Stage printStage = new Stage();
        printStage.setTitle("معاينة " + action + " - تقرير " + employee.getName());
        printStage.initModality(Modality.APPLICATION_MODAL);
        printStage.setWidth(800);
        printStage.setHeight(900);

        VBox printLayout = new VBox(20);
        printLayout.setPadding(new Insets(30));
        printLayout.setStyle("-fx-background-color: white;");

        // رأس التقرير
        VBox header = createEmployeeReportHeader(employee);

        // معلومات الموظف
        VBox employeeInfo = createEmployeeReportInfo(employee);

        // الملخص المالي
        VBox financialSummary = createEmployeeReportFinancialSummary(employee);

        // تذييل التقرير
        VBox footer = createEmployeeReportFooter();

        printLayout.getChildren().addAll(header, employeeInfo, financialSummary, footer);

        // أزرار الطباعة
        HBox buttonBox = new HBox(15);
        buttonBox.setAlignment(Pos.CENTER);
        buttonBox.setPadding(new Insets(20, 0, 0, 0));

        Button printBtn = new Button("طباعة");
        FontAwesomeIconView printIcon = new FontAwesomeIconView(FontAwesomeIcon.PRINT);
        printIcon.setSize("14px");
        printBtn.setGraphic(printIcon);
        printBtn.getStyleClass().add("print-button");
        printBtn.setOnAction(e -> {
            showPlaceholderDialog("طباعة", "تم إرسال تقرير " + employee.getName() + " للطباعة!");
            printStage.close();
        });

        Button savePdfBtn = new Button("حفظ PDF");
        FontAwesomeIconView pdfIcon = new FontAwesomeIconView(FontAwesomeIcon.FILE_PDF_ALT);
        pdfIcon.setSize("14px");
        savePdfBtn.setGraphic(pdfIcon);
        savePdfBtn.getStyleClass().add("save-button");
        savePdfBtn.setOnAction(e -> {
            showPlaceholderDialog("حفظ PDF", "تم حفظ تقرير " + employee.getName() + " كملف PDF!");
            printStage.close();
        });

        Button closeBtn = new Button("إغلاق");
        closeBtn.setOnAction(e -> printStage.close());

        buttonBox.getChildren().addAll(printBtn, savePdfBtn, closeBtn);

        ScrollPane scrollPane = new ScrollPane(printLayout);
        scrollPane.setFitToWidth(true);
        scrollPane.setStyle("-fx-background-color: #f5f5f5;");

        VBox mainContainer = new VBox();
        mainContainer.getChildren().addAll(scrollPane, buttonBox);

        Scene scene = new Scene(mainContainer);
        scene.getStylesheets().add(getClass().getResource("/css/styles.css").toExternalForm());
        printStage.setScene(scene);
        printStage.showAndWait();
    }

    /**
     * إنشاء رأس تقرير الموظف للطباعة
     */
    private VBox createEmployeeReportHeader(Employee employee) {
        VBox header = new VBox(10);
        header.setAlignment(Pos.CENTER);
        header.setStyle("-fx-border-color: #000000; -fx-border-width: 0 0 2 0; -fx-padding: 0 0 15 0;");

        Label companyName = new Label("شركة الزجاج والألومنيوم المتقدمة");
        companyName.setStyle("-fx-font-size: 24px; -fx-font-weight: bold;");

        Label companyInfo = new Label("قسم الموارد البشرية والرواتب");
        companyInfo.setStyle("-fx-font-size: 14px; -fx-text-fill: #666666;");

        Label reportTitle = new Label("تقرير الموظف - " + employee.getName());
        reportTitle.setStyle("-fx-font-size: 20px; -fx-font-weight: bold; -fx-text-fill: #2c3e50;");

        header.getChildren().addAll(companyName, companyInfo, reportTitle);
        return header;
    }

    /**
     * إنشاء معلومات الموظف للطباعة
     */
    private VBox createEmployeeReportInfo(Employee employee) {
        VBox infoSection = new VBox(15);
        infoSection.setStyle("-fx-border-color: #dee2e6; -fx-border-width: 1; -fx-padding: 15; -fx-background-color: #f8f9fa;");

        Label infoTitle = new Label("معلومات الموظف:");
        infoTitle.setStyle("-fx-font-size: 16px; -fx-font-weight: bold;");

        GridPane infoGrid = new GridPane();
        infoGrid.setHgap(30);
        infoGrid.setVgap(10);

        infoGrid.add(new Label("الرقم الوظيفي:"), 0, 0);
        infoGrid.add(new Label(employee.getEmployeeId()), 1, 0);
        infoGrid.add(new Label("تاريخ البدء:"), 2, 0);
        infoGrid.add(new Label(employee.getStartDate().format(dateFormatter)), 3, 0);

        infoGrid.add(new Label("الراتب الأساسي:"), 0, 1);
        infoGrid.add(new Label(decimalFormat.format(employee.getBaseSalary()) + " ج.م"), 1, 1);
        infoGrid.add(new Label("المنصب:"), 2, 1);
        infoGrid.add(new Label(employee.getPosition() != null ? employee.getPosition() : "غير محدد"), 3, 1);

        infoGrid.add(new Label("الهاتف:"), 0, 2);
        infoGrid.add(new Label(employee.getPhone() != null ? employee.getPhone() : "غير محدد"), 1, 2);
        infoGrid.add(new Label("تاريخ التقرير:"), 2, 2);
        infoGrid.add(new Label(java.time.LocalDate.now().format(dateFormatter)), 3, 2);

        infoSection.getChildren().addAll(infoTitle, infoGrid);
        return infoSection;
    }

    /**
     * إنشاء الملخص المالي للطباعة
     */
    private VBox createEmployeeReportFinancialSummary(Employee employee) {
        VBox summarySection = new VBox(15);
        summarySection.setStyle("-fx-border-color: #dee2e6; -fx-border-width: 1; -fx-padding: 15;");

        Label summaryTitle = new Label("الملخص المالي:");
        summaryTitle.setStyle("-fx-font-size: 16px; -fx-font-weight: bold;");

        // حساب الملخص المالي
        double totalAdvances = advances.stream()
            .filter(a -> a.getEmployeeId().equals(employee.getEmployeeId()))
            .mapToDouble(Advance::getAmount)
            .sum();

        double totalDues = salaryDues.stream()
            .filter(d -> d.getEmployeeId().equals(employee.getEmployeeId()))
            .mapToDouble(SalaryDue::getFinalDue)
            .sum();

        double totalPaid = salaryPaids.stream()
            .filter(p -> p.getEmployeeId().equals(employee.getEmployeeId()))
            .mapToDouble(SalaryPaid::getAmount)
            .sum();

        double balance = totalDues - totalPaid - totalAdvances;

        GridPane summaryGrid = new GridPane();
        summaryGrid.setHgap(30);
        summaryGrid.setVgap(10);
        summaryGrid.setStyle("-fx-background-color: white; -fx-padding: 15;");

        summaryGrid.add(new Label("إجمالي السلف:"), 0, 0);
        summaryGrid.add(new Label(decimalFormat.format(totalAdvances) + " ج.م"), 1, 0);

        summaryGrid.add(new Label("إجمالي المستحقات:"), 0, 1);
        summaryGrid.add(new Label(decimalFormat.format(totalDues) + " ج.م"), 1, 1);

        summaryGrid.add(new Label("إجمالي المسدد:"), 0, 2);
        summaryGrid.add(new Label(decimalFormat.format(totalPaid) + " ج.م"), 1, 2);

        summaryGrid.add(new Label("الرصيد النهائي:"), 0, 3);
        Label balanceLabel = new Label(decimalFormat.format(Math.abs(balance)) + " ج.م " +
                                     (balance >= 0 ? "(لصالح الموظف)" : "(على الموظف)"));
        balanceLabel.setStyle("-fx-font-weight: bold; -fx-font-size: 14px; " +
                            (balance >= 0 ? "-fx-text-fill: #27ae60;" : "-fx-text-fill: #e74c3c;"));
        summaryGrid.add(balanceLabel, 1, 3);

        summarySection.getChildren().addAll(summaryTitle, summaryGrid);
        return summarySection;
    }

    /**
     * إنشاء تذييل تقرير الموظف للطباعة
     */
    private VBox createEmployeeReportFooter() {
        VBox footer = new VBox(10);
        footer.setAlignment(Pos.CENTER);
        footer.setStyle("-fx-border-color: #000000; -fx-border-width: 2 0 0 0; -fx-padding: 15 0 0 0;");

        Label footerInfo = new Label("هذا التقرير صادر إلكترونياً من نظام إدارة الرواتب");
        footerInfo.setStyle("-fx-font-size: 12px; -fx-text-fill: #666666;");

        Label printDate = new Label("تاريخ الطباعة: " + java.time.LocalDateTime.now().format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        printDate.setStyle("-fx-font-size: 10px; -fx-text-fill: #666666;");

        footer.getChildren().addAll(footerInfo, printDate);
        return footer;
    }
}
