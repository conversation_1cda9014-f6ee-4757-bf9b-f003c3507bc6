package com.accounting.service;

import com.accounting.model.*;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * خدمة قائمة الدخل
 * Income Statement Service
 */
public class IncomeStatementService {
    
    private static IncomeStatementService instance;
    private final AccountService accountService;
    private final LedgerService ledgerService;
    private final List<IncomeStatement> incomeStatements;
    private final Map<String, IncomeStatement> statementsMap;
    
    private IncomeStatementService() {
        this.accountService = AccountService.getInstance();
        this.ledgerService = LedgerService.getInstance();
        this.incomeStatements = new ArrayList<>();
        this.statementsMap = new HashMap<>();
        
        // إنشاء بيانات تجريبية
        initializeSampleData();
    }
    
    public static synchronized IncomeStatementService getInstance() {
        if (instance == null) {
            instance = new IncomeStatementService();
        }
        return instance;
    }
    
    /**
     * إنشاء قائمة دخل جديدة
     */
    public IncomeStatement generateIncomeStatement(LocalDate fromDate, LocalDate toDate, String companyName) {
        String statementId = generateStatementId();
        IncomeStatement statement = new IncomeStatement(statementId, fromDate, toDate, companyName);
        statement.setGeneratedBy("النظام");
        
        // جمع البيانات من دفتر الأستاذ
        populateStatementFromLedger(statement, fromDate, toDate);
        
        // حساب المجاميع
        statement.calculateTotals();
        
        // حفظ القائمة
        incomeStatements.add(statement);
        statementsMap.put(statementId, statement);
        
        return statement;
    }
    
    /**
     * ملء قائمة الدخل من دفتر الأستاذ
     */
    private void populateStatementFromLedger(IncomeStatement statement, LocalDate fromDate, LocalDate toDate) {
        // الحصول على جميع الحسابات
        List<Account> allAccounts = accountService.getAllAccounts();
        
        for (Account account : allAccounts) {
            // الحصول على رصيد الحساب للفترة المحددة
            double balance = ledgerService.getAccountBalanceForPeriod(account.getAccountCode(), fromDate, toDate);
            
            if (Math.abs(balance) > 0.01) { // تجاهل الأرصدة الصغيرة جداً
                IncomeStatementItem item = new IncomeStatementItem(
                    account.getAccountCode(),
                    account.getAccountName(),
                    Math.abs(balance),
                    account.getDescription(),
                    determineItemType(account)
                );
                
                // إضافة البند للقسم المناسب
                addItemToStatement(statement, item);
            }
        }
    }
    
    /**
     * تحديد نوع البند حسب نوع الحساب
     */
    private IncomeStatementItem.ItemType determineItemType(Account account) {
        String code = account.getAccountCode();
        Account.AccountType type = account.getAccountType();
        
        // الإيرادات (4xxxx)
        if (code.startsWith("4") || type == Account.AccountType.REVENUE) {
            if (code.startsWith("41")) {
                return IncomeStatementItem.ItemType.REVENUE; // إيرادات التشغيل
            } else {
                return IncomeStatementItem.ItemType.OTHER_INCOME; // إيرادات أخرى
            }
        }
        
        // المصروفات (5xxxx)
        if (code.startsWith("5") || type == Account.AccountType.EXPENSES) {
            if (code.startsWith("51")) {
                return IncomeStatementItem.ItemType.COST_OF_GOODS_SOLD; // تكلفة البضاعة المباعة
            } else if (code.startsWith("52")) {
                return IncomeStatementItem.ItemType.OPERATING_EXPENSE; // مصروفات تشغيلية
            } else if (code.startsWith("53")) {
                return IncomeStatementItem.ItemType.OTHER_EXPENSE; // مصروفات أخرى
            } else if (code.startsWith("54")) {
                return IncomeStatementItem.ItemType.TAX; // ضرائب
            } else {
                return IncomeStatementItem.ItemType.OPERATING_EXPENSE; // افتراضي
            }
        }
        
        // افتراضي
        return IncomeStatementItem.ItemType.OTHER_EXPENSE;
    }
    
    /**
     * إضافة البند للقسم المناسب في قائمة الدخل
     */
    private void addItemToStatement(IncomeStatement statement, IncomeStatementItem item) {
        switch (item.getType()) {
            case REVENUE:
                statement.addRevenue(item);
                break;
            case COST_OF_GOODS_SOLD:
                statement.addCostOfGoodsSold(item);
                break;
            case OPERATING_EXPENSE:
                statement.addOperatingExpense(item);
                break;
            case OTHER_INCOME:
                statement.addOtherIncome(item);
                break;
            case OTHER_EXPENSE:
                statement.addOtherExpense(item);
                break;
            case TAX:
                statement.setTaxes(statement.getTaxes() + item.getAmount());
                break;
        }
    }
    
    /**
     * الحصول على قائمة دخل بالمعرف
     */
    public IncomeStatement getIncomeStatementById(String statementId) {
        return statementsMap.get(statementId);
    }
    
    /**
     * الحصول على جميع قوائم الدخل
     */
    public List<IncomeStatement> getAllIncomeStatements() {
        return new ArrayList<>(incomeStatements);
    }
    
    /**
     * البحث في قوائم الدخل
     */
    public List<IncomeStatement> searchIncomeStatements(String searchText) {
        if (searchText == null || searchText.trim().isEmpty()) {
            return getAllIncomeStatements();
        }
        
        String searchLower = searchText.toLowerCase();
        return incomeStatements.stream()
                .filter(statement -> 
                    statement.getStatementId().toLowerCase().contains(searchLower) ||
                    statement.getCompanyName().toLowerCase().contains(searchLower) ||
                    statement.getPeriod().toLowerCase().contains(searchLower)
                )
                .collect(Collectors.toList());
    }
    
    /**
     * فلترة قوائم الدخل حسب الفترة
     */
    public List<IncomeStatement> getIncomeStatementsByPeriod(LocalDate fromDate, LocalDate toDate) {
        return incomeStatements.stream()
                .filter(statement -> 
                    !statement.getFromDate().isBefore(fromDate) &&
                    !statement.getToDate().isAfter(toDate)
                )
                .collect(Collectors.toList());
    }
    
    /**
     * حذف قائمة دخل
     */
    public boolean deleteIncomeStatement(String statementId) {
        IncomeStatement statement = getIncomeStatementById(statementId);
        if (statement != null) {
            incomeStatements.remove(statement);
            statementsMap.remove(statementId);
            return true;
        }
        return false;
    }
    
    /**
     * توليد معرف قائمة دخل جديد
     */
    private String generateStatementId() {
        String dateStr = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        int sequence = incomeStatements.size() + 1;
        return String.format("IS%s%03d", dateStr, sequence);
    }
    
    /**
     * إنشاء بيانات تجريبية
     */
    private void initializeSampleData() {
        // قائمة دخل للشهر الماضي
        LocalDate lastMonth = LocalDate.now().minusMonths(1);
        LocalDate fromDate = lastMonth.withDayOfMonth(1);
        LocalDate toDate = lastMonth.withDayOfMonth(lastMonth.lengthOfMonth());
        
        IncomeStatement sampleStatement = new IncomeStatement(
            generateStatementId(),
            fromDate,
            toDate,
            "شركة الزجاج والألومنيوم المتقدمة"
        );
        
        // إضافة بيانات تجريبية
        sampleStatement.addRevenue(new IncomeStatementItem("4101", "إيرادات المبيعات", 150000.0, IncomeStatementItem.ItemType.REVENUE));
        sampleStatement.addRevenue(new IncomeStatementItem("4102", "إيرادات الخدمات", 25000.0, IncomeStatementItem.ItemType.REVENUE));
        
        sampleStatement.addCostOfGoodsSold(new IncomeStatementItem("5101", "تكلفة المواد الخام", 60000.0, IncomeStatementItem.ItemType.COST_OF_GOODS_SOLD));
        sampleStatement.addCostOfGoodsSold(new IncomeStatementItem("5102", "تكلفة العمالة المباشرة", 20000.0, IncomeStatementItem.ItemType.COST_OF_GOODS_SOLD));
        
        sampleStatement.addOperatingExpense(new IncomeStatementItem("5201", "مصروفات إدارية", 15000.0, IncomeStatementItem.ItemType.OPERATING_EXPENSE));
        sampleStatement.addOperatingExpense(new IncomeStatementItem("5202", "مصروفات تسويق", 8000.0, IncomeStatementItem.ItemType.OPERATING_EXPENSE));
        sampleStatement.addOperatingExpense(new IncomeStatementItem("5203", "مصروفات عمومية", 5000.0, IncomeStatementItem.ItemType.OPERATING_EXPENSE));
        
        sampleStatement.addOtherIncome(new IncomeStatementItem("4201", "إيرادات استثمارات", 2000.0, IncomeStatementItem.ItemType.OTHER_INCOME));
        sampleStatement.addOtherExpense(new IncomeStatementItem("5301", "مصروفات مالية", 1500.0, IncomeStatementItem.ItemType.OTHER_EXPENSE));
        
        sampleStatement.setTaxes(8000.0);
        sampleStatement.setGeneratedBy("النظام");
        sampleStatement.calculateTotals();
        
        incomeStatements.add(sampleStatement);
        statementsMap.put(sampleStatement.getStatementId(), sampleStatement);
        
        // قائمة دخل للربع الحالي
        LocalDate quarterStart = LocalDate.now().withDayOfMonth(1).minusMonths(2);
        LocalDate quarterEnd = LocalDate.now();
        
        IncomeStatement quarterStatement = new IncomeStatement(
            generateStatementId(),
            quarterStart,
            quarterEnd,
            "شركة الزجاج والألومنيوم المتقدمة"
        );
        
        quarterStatement.addRevenue(new IncomeStatementItem("4101", "إيرادات المبيعات", 450000.0, IncomeStatementItem.ItemType.REVENUE));
        quarterStatement.addRevenue(new IncomeStatementItem("4102", "إيرادات الخدمات", 75000.0, IncomeStatementItem.ItemType.REVENUE));
        
        quarterStatement.addCostOfGoodsSold(new IncomeStatementItem("5101", "تكلفة المواد الخام", 180000.0, IncomeStatementItem.ItemType.COST_OF_GOODS_SOLD));
        quarterStatement.addCostOfGoodsSold(new IncomeStatementItem("5102", "تكلفة العمالة المباشرة", 60000.0, IncomeStatementItem.ItemType.COST_OF_GOODS_SOLD));
        
        quarterStatement.addOperatingExpense(new IncomeStatementItem("5201", "مصروفات إدارية", 45000.0, IncomeStatementItem.ItemType.OPERATING_EXPENSE));
        quarterStatement.addOperatingExpense(new IncomeStatementItem("5202", "مصروفات تسويق", 24000.0, IncomeStatementItem.ItemType.OPERATING_EXPENSE));
        quarterStatement.addOperatingExpense(new IncomeStatementItem("5203", "مصروفات عمومية", 15000.0, IncomeStatementItem.ItemType.OPERATING_EXPENSE));
        
        quarterStatement.addOtherIncome(new IncomeStatementItem("4201", "إيرادات استثمارات", 6000.0, IncomeStatementItem.ItemType.OTHER_INCOME));
        quarterStatement.addOtherExpense(new IncomeStatementItem("5301", "مصروفات مالية", 4500.0, IncomeStatementItem.ItemType.OTHER_EXPENSE));
        
        quarterStatement.setTaxes(24000.0);
        quarterStatement.setGeneratedBy("النظام");
        quarterStatement.calculateTotals();
        
        incomeStatements.add(quarterStatement);
        statementsMap.put(quarterStatement.getStatementId(), quarterStatement);
    }
}
