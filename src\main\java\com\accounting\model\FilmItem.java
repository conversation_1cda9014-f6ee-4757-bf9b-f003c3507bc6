package com.accounting.model;

import javafx.beans.property.*;
import java.util.UUID;

/**
 * نموذج عنصر الفيلم
 */
public class FilmItem {
    
    private final StringProperty itemId = new SimpleStringProperty();
    private final StringProperty description = new SimpleStringProperty();
    private final DoubleProperty length = new SimpleDoubleProperty(); // بالمليمتر
    private final DoubleProperty width = new SimpleDoubleProperty(); // بالمليمتر
    private final DoubleProperty squareMeters = new SimpleDoubleProperty(); // المتر المربع
    private final IntegerProperty count = new SimpleIntegerProperty(); // العدد
    private final DoubleProperty totalSquareMeters = new SimpleDoubleProperty(); // إجمالي المتر المربع
    private final DoubleProperty totalLengthMeters = new SimpleDoubleProperty(); // إجمالي الأطوال بالمتر
    private final StringProperty notes = new SimpleStringProperty();
    
    /**
     * المنشئ الافتراضي
     */
    public FilmItem() {
        this.itemId.set(UUID.randomUUID().toString());
        
        // ربط الحسابات التلقائية
        setupCalculations();
    }
    
    /**
     * منشئ مع المعاملات
     */
    public FilmItem(String description, double length, double width, int count) {
        this();
        this.description.set(description);
        this.length.set(length);
        this.width.set(width);
        this.count.set(count);
    }
    
    /**
     * إعداد الحسابات التلقائية
     */
    private void setupCalculations() {
        // حساب المتر المربع عند تغيير الطول أو العرض
        length.addListener((obs, oldVal, newVal) -> calculateSquareMeters());
        width.addListener((obs, oldVal, newVal) -> calculateSquareMeters());
        
        // حساب الإجماليات عند تغيير العدد أو المتر المربع أو الطول
        squareMeters.addListener((obs, oldVal, newVal) -> calculateTotals());
        count.addListener((obs, oldVal, newVal) -> calculateTotals());
        length.addListener((obs, oldVal, newVal) -> calculateTotals());
    }
    
    /**
     * حساب المتر المربع
     */
    private void calculateSquareMeters() {
        double lengthM = length.get() / 1000.0; // تحويل من مم إلى متر
        double widthM = width.get() / 1000.0; // تحويل من مم إلى متر
        squareMeters.set(lengthM * widthM);
    }
    
    /**
     * حساب الإجماليات
     */
    private void calculateTotals() {
        // إجمالي المتر المربع
        totalSquareMeters.set(squareMeters.get() * count.get());
        
        // إجمالي الأطوال بالمتر
        double lengthM = length.get() / 1000.0; // تحويل من مم إلى متر
        totalLengthMeters.set(lengthM * count.get());
    }
    
    // Getters and Setters
    public String getItemId() { return itemId.get(); }
    public void setItemId(String itemId) { this.itemId.set(itemId); }
    public StringProperty itemIdProperty() { return itemId; }
    
    public String getDescription() { return description.get(); }
    public void setDescription(String description) { this.description.set(description); }
    public StringProperty descriptionProperty() { return description; }
    
    public double getLength() { return length.get(); }
    public void setLength(double length) { this.length.set(length); }
    public DoubleProperty lengthProperty() { return length; }
    
    public double getWidth() { return width.get(); }
    public void setWidth(double width) { this.width.set(width); }
    public DoubleProperty widthProperty() { return width; }
    
    public double getSquareMeters() { return squareMeters.get(); }
    public void setSquareMeters(double squareMeters) { this.squareMeters.set(squareMeters); }
    public DoubleProperty squareMetersProperty() { return squareMeters; }
    
    public int getCount() { return count.get(); }
    public void setCount(int count) { this.count.set(count); }
    public IntegerProperty countProperty() { return count; }
    
    public double getTotalSquareMeters() { return totalSquareMeters.get(); }
    public void setTotalSquareMeters(double totalSquareMeters) { this.totalSquareMeters.set(totalSquareMeters); }
    public DoubleProperty totalSquareMetersProperty() { return totalSquareMeters; }
    
    public double getTotalLengthMeters() { return totalLengthMeters.get(); }
    public void setTotalLengthMeters(double totalLengthMeters) { this.totalLengthMeters.set(totalLengthMeters); }
    public DoubleProperty totalLengthMetersProperty() { return totalLengthMeters; }
    
    public String getNotes() { return notes.get(); }
    public void setNotes(String notes) { this.notes.set(notes); }
    public StringProperty notesProperty() { return notes; }
    
    /**
     * التحقق من صحة البيانات
     */
    public boolean isValid() {
        return description.get() != null && !description.get().trim().isEmpty() &&
               length.get() > 0 && width.get() > 0 && count.get() > 0;
    }
    
    /**
     * نسخ العنصر
     */
    public FilmItem copy() {
        FilmItem copy = new FilmItem();
        copy.setDescription(this.getDescription());
        copy.setLength(this.getLength());
        copy.setWidth(this.getWidth());
        copy.setCount(this.getCount());
        copy.setNotes(this.getNotes());
        return copy;
    }
    
    /**
     * تمثيل نصي للعنصر
     */
    @Override
    public String toString() {
        return String.format("%s - %.0f×%.0f مم - %d قطعة - %.2f م² - %.2f م طولي", 
                description.get() != null ? description.get() : "",
                length.get(), width.get(), count.get(), 
                totalSquareMeters.get(), totalLengthMeters.get());
    }
}
