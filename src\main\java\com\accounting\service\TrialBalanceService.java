package com.accounting.service;

import com.accounting.model.*;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * خدمة إدارة ميزان المراجعة
 */
public class TrialBalanceService {
    
    private static TrialBalanceService instance;
    
    // المراجع للخدمات الأخرى
    private final AccountService accountService;
    private final LedgerService ledgerService;
    
    private TrialBalanceService() {
        this.accountService = AccountService.getInstance();
        this.ledgerService = LedgerService.getInstance();
    }
    
    public static TrialBalanceService getInstance() {
        if (instance == null) {
            instance = new TrialBalanceService();
        }
        return instance;
    }
    
    /**
     * إنشاء ميزان المراجعة لفترة محددة
     */
    public ObservableList<TrialBalance> generateTrialBalance(LocalDate fromDate, LocalDate toDate, 
                                                           boolean includeZeroBalances, boolean includeSubAccounts) {
        ObservableList<TrialBalance> trialBalanceList = FXCollections.observableArrayList();
        
        // الحصول على جميع الحسابات
        List<Account> accounts = includeSubAccounts ? 
                accountService.getAllAccounts() : 
                accountService.getLeafAccounts();
        
        for (Account account : accounts) {
            TrialBalance trialBalance = createTrialBalanceForAccount(account, fromDate, toDate);
            
            // إضافة فقط إذا كان هناك حركة أو أرصدة أو إذا كان مطلوب إدراج الأرصدة الصفرية
            if (includeZeroBalances || trialBalance.hasMovement()) {
                trialBalanceList.add(trialBalance);
            }
        }
        
        // ترتيب حسب رمز الحساب
        trialBalanceList.sort(Comparator.comparing(TrialBalance::getAccountCode));
        
        return trialBalanceList;
    }
    
    /**
     * إنشاء ميزان مراجعة لحساب محدد
     */
    private TrialBalance createTrialBalanceForAccount(Account account, LocalDate fromDate, LocalDate toDate) {
        TrialBalance trialBalance = new TrialBalance(account, fromDate, toDate);
        
        // الحصول على مجاميع الحساب خلال الفترة
        Map<String, Double> totals = ledgerService.getAccountTotals(account.getAccountCode(), fromDate, toDate);
        
        // تعيين مجاميع الفترة
        trialBalance.setPeriodDebitTotal(totals.getOrDefault("totalDebits", 0.0));
        trialBalance.setPeriodCreditTotal(totals.getOrDefault("totalCredits", 0.0));
        
        // تعيين الرصيد الافتتاحي
        double openingBalance = totals.getOrDefault("openingBalance", 0.0);
        if (openingBalance > 0) {
            if (account.getAccountNature() == Account.AccountNature.DEBIT) {
                trialBalance.setOpeningDebit(openingBalance);
            } else {
                trialBalance.setOpeningCredit(openingBalance);
            }
        } else if (openingBalance < 0) {
            if (account.getAccountNature() == Account.AccountNature.DEBIT) {
                trialBalance.setOpeningCredit(Math.abs(openingBalance));
            } else {
                trialBalance.setOpeningDebit(Math.abs(openingBalance));
            }
        }
        
        return trialBalance;
    }
    
    /**
     * إنشاء ميزان المراجعة بالمجاميع
     */
    public ObservableList<TrialBalance> generateTrialBalanceWithTotals(LocalDate fromDate, LocalDate toDate) {
        ObservableList<TrialBalance> trialBalance = generateTrialBalance(fromDate, toDate, false, false);
        
        // إضافة صفوف المجاميع لكل نوع حساب
        Map<Account.AccountType, List<TrialBalance>> groupedByType = trialBalance.stream()
                .collect(Collectors.groupingBy(TrialBalance::getAccountType));
        
        ObservableList<TrialBalance> result = FXCollections.observableArrayList();
        
        for (Account.AccountType accountType : Account.AccountType.values()) {
            List<TrialBalance> typeAccounts = groupedByType.getOrDefault(accountType, new ArrayList<>());
            
            if (!typeAccounts.isEmpty()) {
                // إضافة حسابات النوع
                result.addAll(typeAccounts);
                
                // إضافة صف المجموع
                TrialBalance totalRow = createTotalRowForAccountType(accountType, typeAccounts, fromDate, toDate);
                result.add(totalRow);
            }
        }
        
        // إضافة المجموع العام
        TrialBalance grandTotal = createGrandTotalRow(trialBalance, fromDate, toDate);
        result.add(grandTotal);
        
        return result;
    }
    
    /**
     * إنشاء صف المجموع لنوع حساب محدد
     */
    private TrialBalance createTotalRowForAccountType(Account.AccountType accountType, 
                                                    List<TrialBalance> typeAccounts,
                                                    LocalDate fromDate, LocalDate toDate) {
        TrialBalance totalRow = new TrialBalance();
        totalRow.setAccountCode("مجموع");
        totalRow.setAccountName("مجموع " + accountType.getArabicName());
        totalRow.setAccountType(accountType);
        totalRow.setFromDate(fromDate);
        totalRow.setToDate(toDate);
        totalRow.setIsParent(true);
        
        // حساب المجاميع
        double openingDebit = typeAccounts.stream().mapToDouble(TrialBalance::getOpeningDebit).sum();
        double openingCredit = typeAccounts.stream().mapToDouble(TrialBalance::getOpeningCredit).sum();
        double periodDebit = typeAccounts.stream().mapToDouble(TrialBalance::getPeriodDebitTotal).sum();
        double periodCredit = typeAccounts.stream().mapToDouble(TrialBalance::getPeriodCreditTotal).sum();
        double closingDebit = typeAccounts.stream().mapToDouble(TrialBalance::getClosingDebit).sum();
        double closingCredit = typeAccounts.stream().mapToDouble(TrialBalance::getClosingCredit).sum();
        
        totalRow.setOpeningDebit(openingDebit);
        totalRow.setOpeningCredit(openingCredit);
        totalRow.setPeriodDebitTotal(periodDebit);
        totalRow.setPeriodCreditTotal(periodCredit);
        totalRow.setClosingDebit(closingDebit);
        totalRow.setClosingCredit(closingCredit);
        
        return totalRow;
    }
    
    /**
     * إنشاء صف المجموع العام
     */
    private TrialBalance createGrandTotalRow(List<TrialBalance> trialBalanceList, 
                                           LocalDate fromDate, LocalDate toDate) {
        TrialBalance grandTotal = new TrialBalance();
        grandTotal.setAccountCode("المجموع العام");
        grandTotal.setAccountName("المجموع العام");
        grandTotal.setFromDate(fromDate);
        grandTotal.setToDate(toDate);
        grandTotal.setIsParent(true);
        
        // حساب المجاميع العامة
        double openingDebit = trialBalanceList.stream().mapToDouble(TrialBalance::getOpeningDebit).sum();
        double openingCredit = trialBalanceList.stream().mapToDouble(TrialBalance::getOpeningCredit).sum();
        double periodDebit = trialBalanceList.stream().mapToDouble(TrialBalance::getPeriodDebitTotal).sum();
        double periodCredit = trialBalanceList.stream().mapToDouble(TrialBalance::getPeriodCreditTotal).sum();
        double closingDebit = trialBalanceList.stream().mapToDouble(TrialBalance::getClosingDebit).sum();
        double closingCredit = trialBalanceList.stream().mapToDouble(TrialBalance::getClosingCredit).sum();
        
        grandTotal.setOpeningDebit(openingDebit);
        grandTotal.setOpeningCredit(openingCredit);
        grandTotal.setPeriodDebitTotal(periodDebit);
        grandTotal.setPeriodCreditTotal(periodCredit);
        grandTotal.setClosingDebit(closingDebit);
        grandTotal.setClosingCredit(closingCredit);
        
        return grandTotal;
    }
    
    /**
     * إنشاء ميزان المراجعة بالأرصدة فقط
     */
    public ObservableList<TrialBalance> generateTrialBalanceWithBalancesOnly(LocalDate asOfDate) {
        ObservableList<TrialBalance> trialBalance = FXCollections.observableArrayList();
        
        List<Account> accounts = accountService.getLeafAccounts();
        
        for (Account account : accounts) {
            double balance = ledgerService.getAccountBalance(account.getAccountCode(), asOfDate);
            
            if (Math.abs(balance) > 0.01) { // تجاهل الأرصدة الصغيرة جداً
                TrialBalance tb = new TrialBalance();
                tb.setAccountCode(account.getAccountCode());
                tb.setAccountName(account.getAccountName());
                tb.setAccountType(account.getAccountType());
                tb.setAccountNature(account.getAccountNature());
                tb.setFromDate(asOfDate);
                tb.setToDate(asOfDate);
                
                if (balance > 0) {
                    if (account.getAccountNature() == Account.AccountNature.DEBIT) {
                        tb.setClosingDebit(balance);
                    } else {
                        tb.setClosingCredit(balance);
                    }
                } else {
                    if (account.getAccountNature() == Account.AccountNature.DEBIT) {
                        tb.setClosingCredit(Math.abs(balance));
                    } else {
                        tb.setClosingDebit(Math.abs(balance));
                    }
                }
                
                trialBalance.add(tb);
            }
        }
        
        trialBalance.sort(Comparator.comparing(TrialBalance::getAccountCode));
        return trialBalance;
    }
    
    /**
     * التحقق من توازن ميزان المراجعة
     */
    public boolean isTrialBalanceBalanced(List<TrialBalance> trialBalance) {
        double totalDebits = trialBalance.stream()
                .filter(tb -> !tb.isParent()) // استبعاد صفوف المجاميع
                .mapToDouble(tb -> tb.getOpeningDebit() + tb.getPeriodDebitTotal())
                .sum();
        
        double totalCredits = trialBalance.stream()
                .filter(tb -> !tb.isParent()) // استبعاد صفوف المجاميع
                .mapToDouble(tb -> tb.getOpeningCredit() + tb.getPeriodCreditTotal())
                .sum();
        
        return Math.abs(totalDebits - totalCredits) < 0.01;
    }
    
    /**
     * الحصول على إحصائيات ميزان المراجعة
     */
    public Map<String, Object> getTrialBalanceStatistics(List<TrialBalance> trialBalance) {
        Map<String, Object> stats = new HashMap<>();
        
        // استبعاد صفوف المجاميع
        List<TrialBalance> accountRows = trialBalance.stream()
                .filter(tb -> !tb.isParent())
                .collect(Collectors.toList());
        
        stats.put("عدد الحسابات", accountRows.size());
        
        // المجاميع
        double totalOpeningDebits = accountRows.stream().mapToDouble(TrialBalance::getOpeningDebit).sum();
        double totalOpeningCredits = accountRows.stream().mapToDouble(TrialBalance::getOpeningCredit).sum();
        double totalPeriodDebits = accountRows.stream().mapToDouble(TrialBalance::getPeriodDebitTotal).sum();
        double totalPeriodCredits = accountRows.stream().mapToDouble(TrialBalance::getPeriodCreditTotal).sum();
        double totalClosingDebits = accountRows.stream().mapToDouble(TrialBalance::getClosingDebit).sum();
        double totalClosingCredits = accountRows.stream().mapToDouble(TrialBalance::getClosingCredit).sum();
        
        stats.put("إجمالي الأرصدة الافتتاحية المدينة", totalOpeningDebits);
        stats.put("إجمالي الأرصدة الافتتاحية الدائنة", totalOpeningCredits);
        stats.put("إجمالي حركة الفترة المدينة", totalPeriodDebits);
        stats.put("إجمالي حركة الفترة الدائنة", totalPeriodCredits);
        stats.put("إجمالي الأرصدة الختامية المدينة", totalClosingDebits);
        stats.put("إجمالي الأرصدة الختامية الدائنة", totalClosingCredits);
        
        // التوازن
        stats.put("متوازن", isTrialBalanceBalanced(trialBalance));
        stats.put("فرق الأرصدة الافتتاحية", Math.abs(totalOpeningDebits - totalOpeningCredits));
        stats.put("فرق حركة الفترة", Math.abs(totalPeriodDebits - totalPeriodCredits));
        stats.put("فرق الأرصدة الختامية", Math.abs(totalClosingDebits - totalClosingCredits));
        
        // إحصائيات حسب نوع الحساب
        for (Account.AccountType accountType : Account.AccountType.values()) {
            long count = accountRows.stream()
                    .filter(tb -> tb.getAccountType() == accountType)
                    .count();
            stats.put("عدد حسابات " + accountType.getArabicName(), count);
        }
        
        return stats;
    }
    
    /**
     * تصدير ميزان المراجعة إلى نص
     */
    public String exportTrialBalanceToText(List<TrialBalance> trialBalance, String title) {
        StringBuilder export = new StringBuilder();
        export.append(title).append("\n");
        export.append("تاريخ الإنشاء: ").append(LocalDateTime.now()).append("\n");
        export.append("=".repeat(120)).append("\n\n");
        
        // رأس الجدول
        export.append(String.format("%-10s %-30s %-12s %-12s %-12s %-12s %-12s %-12s\n",
                "رمز الحساب", "اسم الحساب", "افتتاحي مدين", "افتتاحي دائن", 
                "حركة مدين", "حركة دائن", "ختامي مدين", "ختامي دائن"));
        export.append("-".repeat(120)).append("\n");
        
        // البيانات
        for (TrialBalance tb : trialBalance) {
            export.append(String.format("%-10s %-30s %12.2f %12.2f %12.2f %12.2f %12.2f %12.2f\n",
                    tb.getAccountCode(),
                    tb.getAccountName(),
                    tb.getOpeningDebit(),
                    tb.getOpeningCredit(),
                    tb.getPeriodDebitTotal(),
                    tb.getPeriodCreditTotal(),
                    tb.getClosingDebit(),
                    tb.getClosingCredit()));
        }
        
        export.append("-".repeat(120)).append("\n");
        
        // المجاميع
        double totalOpeningDebits = trialBalance.stream().mapToDouble(TrialBalance::getOpeningDebit).sum();
        double totalOpeningCredits = trialBalance.stream().mapToDouble(TrialBalance::getOpeningCredit).sum();
        double totalPeriodDebits = trialBalance.stream().mapToDouble(TrialBalance::getPeriodDebitTotal).sum();
        double totalPeriodCredits = trialBalance.stream().mapToDouble(TrialBalance::getPeriodCreditTotal).sum();
        double totalClosingDebits = trialBalance.stream().mapToDouble(TrialBalance::getClosingDebit).sum();
        double totalClosingCredits = trialBalance.stream().mapToDouble(TrialBalance::getClosingCredit).sum();
        
        export.append(String.format("%-10s %-30s %12.2f %12.2f %12.2f %12.2f %12.2f %12.2f\n",
                "", "المجموع العام",
                totalOpeningDebits, totalOpeningCredits,
                totalPeriodDebits, totalPeriodCredits,
                totalClosingDebits, totalClosingCredits));
        
        // التحقق من التوازن
        export.append("\n");
        export.append("حالة التوازن: ").append(isTrialBalanceBalanced(trialBalance) ? "متوازن" : "غير متوازن").append("\n");
        
        if (!isTrialBalanceBalanced(trialBalance)) {
            export.append("فرق الأرصدة الافتتاحية: ").append(String.format("%.2f", Math.abs(totalOpeningDebits - totalOpeningCredits))).append("\n");
            export.append("فرق حركة الفترة: ").append(String.format("%.2f", Math.abs(totalPeriodDebits - totalPeriodCredits))).append("\n");
            export.append("فرق الأرصدة الختامية: ").append(String.format("%.2f", Math.abs(totalClosingDebits - totalClosingCredits))).append("\n");
        }
        
        return export.toString();
    }
    
    /**
     * إنشاء ميزان مراجعة مقارن بين فترتين
     */
    public ObservableList<TrialBalance> generateComparativeTrialBalance(LocalDate fromDate1, LocalDate toDate1,
                                                                       LocalDate fromDate2, LocalDate toDate2) {
        ObservableList<TrialBalance> period1 = generateTrialBalance(fromDate1, toDate1, false, false);
        ObservableList<TrialBalance> period2 = generateTrialBalance(fromDate2, toDate2, false, false);
        
        // دمج البيانات (هذه الوظيفة يمكن تطويرها أكثر)
        ObservableList<TrialBalance> comparative = FXCollections.observableArrayList();
        
        // إضافة جميع الحسابات من الفترتين
        Set<String> allAccounts = new HashSet<>();
        period1.forEach(tb -> allAccounts.add(tb.getAccountCode()));
        period2.forEach(tb -> allAccounts.add(tb.getAccountCode()));
        
        for (String accountCode : allAccounts) {
            TrialBalance tb1 = period1.stream()
                    .filter(tb -> tb.getAccountCode().equals(accountCode))
                    .findFirst().orElse(null);
            
            TrialBalance tb2 = period2.stream()
                    .filter(tb -> tb.getAccountCode().equals(accountCode))
                    .findFirst().orElse(null);
            
            if (tb1 != null) {
                comparative.add(tb1);
            } else if (tb2 != null) {
                comparative.add(tb2);
            }
        }
        
        comparative.sort(Comparator.comparing(TrialBalance::getAccountCode));
        return comparative;
    }
}
