@echo off 
setlocal 
set JAVA_HOME=C:\Program Files\Java\jdk-24 
set PATH=%JAVA_HOME%\bin;%PATH% 
title Hossam Accounting System 
echo ======================================== 
echo       Hossam Accounting System 
echo       Using JDK 24 with JavaFX 
echo ======================================== 
echo Starting application... 
echo. 
if exist "Hossam-WithJavaFX\accounting-system-1.0.0.jar" ( 
    cd /d "Hossam-WithJavaFX" 
    java -Dfile.encoding=UTF-8 -jar accounting-system-1.0.0.jar 
) else if exist "target\accounting-system-1.0.0.jar" ( 
    java -Dfile.encoding=UTF-8 -jar target\accounting-system-1.0.0.jar 
) else ( 
    echo Application JAR not found! 
    pause 
) 
