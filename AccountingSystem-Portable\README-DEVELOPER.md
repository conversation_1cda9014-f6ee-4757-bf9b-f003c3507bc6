# نظام المحاسبة المتكامل - دليل المطور

## 📋 نظرة عامة

نظام محاسبة متكامل مصمم خصيصاً لشركات الزجاج والألومنيوم، مبني بتقنية JavaFX ويدعم اللغة العربية بالكامل.

## 🛠️ المتطلبات التقنية

### متطلبات التطوير:
- **Java**: 17 أو أحدث (يُنصح بـ OpenJDK 21)
- **Maven**: 3.8.0 أو أحدث (مضمن في المشروع)
- **JavaFX**: 21.0.1 (يتم تحميلها تلقائياً)
- **IDE**: IntelliJ IDEA أو Eclipse أو VS Code

### متطلبات التشغيل:
- **نظام التشغيل**: Windows 10/11 (64-bit)
- **الذاكرة**: 4 GB RAM كحد أدنى، 8 GB مُنصح به
- **مساحة القرص**: 500 MB للبرنامج + مساحة للبيانات

## 🏗️ هيكل المشروع

```
src/
├── main/
│   ├── java/com/accounting/
│   │   ├── controller/          # كونترولرز الواجهات
│   │   ├── model/              # نماذج البيانات
│   │   ├── service/            # خدمات الأعمال
│   │   ├── util/               # أدوات مساعدة
│   │   └── AccountingApplication.java  # الكلاس الرئيسي
│   └── resources/
│       ├── css/                # ملفات التنسيق
│       ├── fxml/               # ملفات الواجهات
│       ├── images/             # الصور والأيقونات
│       └── data/               # البيانات التجريبية
```

## 🚀 بدء التطوير

### 1. استنساخ المشروع
```bash
git clone [repository-url]
cd accounting-system
```

### 2. تشغيل البرنامج للتطوير
```bash
# Windows
run-app.bat

# أو باستخدام Maven مباشرة
./mvnw javafx:run
```

### 3. بناء JAR قابل للتشغيل
```bash
./mvnw clean package
```

### 4. إنشاء ملف التثبيت
```bash
# Windows
build-installer.bat
```

## 📦 الموديولات الرئيسية

### 1. موديول المحاسبة
- **دليل الحسابات**: إدارة شجرة الحسابات
- **القيود اليومية**: إنشاء وإدارة القيود المحاسبية
- **دفتر الأستاذ**: عرض حركة الحسابات
- **قائمة الدخل**: تقارير الأرباح والخسائر
- **الميزان التجريبي**: موازين الحسابات

### 2. موديول المبيعات
- **الفواتير**: إنشاء وإدارة فواتير المبيعات
- **العملاء**: إدارة بيانات العملاء
- **تقارير الخدمات**: تقارير مفصلة للخدمات

### 3. موديول التصنيع
- **أوامر التصنيع**: إدارة أوامر الإنتاج
- **أوامر التسليم**: متابعة التسليم للعملاء
- **أوامر الاستلام**: استلام المواد الخام
- **التقارير**: تقارير شاملة للإنتاج

### 4. موديول المخازن
- **إدارة الأصناف**: أصناف بأبعاد وبدون أبعاد
- **حركة المخزون**: استلام وصرف الأصناف
- **المخازن المتعددة**: إدارة عدة مخازن
- **التقارير**: تقارير المخزون والقيم

### 5. موديول الرواتب
- **الموظفين**: إدارة بيانات الموظفين
- **السلف**: إدارة سلف الموظفين
- **المستحقات**: حساب الرواتب والمستحقات
- **التقارير**: تقارير الرواتب

## 🔧 إضافة ميزات جديدة

### إضافة كونترولر جديد:
1. إنشاء كلاس في `src/main/java/com/accounting/controller/`
2. وراثة من `javafx.fxml.Initializable` إذا لزم الأمر
3. إضافة الكونترولر للتنقل في `MainController.java`

### إضافة نموذج بيانات جديد:
1. إنشاء كلاس في `src/main/java/com/accounting/model/`
2. استخدام JavaFX Properties للربط مع الواجهة
3. إضافة الـ getters/setters المطلوبة

### إضافة خدمة جديدة:
1. إنشاء كلاس في `src/main/java/com/accounting/service/`
2. تطبيق منطق الأعمال
3. إدارة البيانات والحفظ/الاسترجاع

## 🎨 تخصيص الواجهة

### ملفات CSS:
- `styles.css`: التنسيق الأساسي
- `enhanced-inventory.css`: تنسيق المخازن
- `accounting.css`: تنسيق المحاسبة

### الألوان الأساسية:
```css
:root {
    --primary-color: #2c3e50;
    --secondary-color: #3498db;
    --success-color: #27ae60;
    --warning-color: #f39c12;
    --danger-color: #e74c3c;
    --background-color: #ecf0f1;
}
```

## 📊 إدارة البيانات

### البيانات التجريبية:
- موجودة في `src/main/resources/data/`
- يتم تحميلها تلقائياً عند بدء التشغيل
- يمكن تعديلها لأغراض الاختبار

### حفظ البيانات:
- البيانات محفوظة في الذاكرة حالياً
- يمكن إضافة قاعدة بيانات SQLite
- دعم JSON للتصدير/الاستيراد

## 🔍 اختبار البرنامج

### اختبار الوظائف:
1. تشغيل البرنامج: `run-app.bat`
2. اختبار كل موديول على حدة
3. التأكد من حفظ البيانات
4. اختبار التقارير والتصدير

### اختبار الأداء:
- مراقبة استخدام الذاكرة
- اختبار مع بيانات كبيرة
- قياس سرعة الاستجابة

## 📦 إنشاء الإصدار النهائي

### 1. تحديث رقم الإصدار:
```xml
<!-- في pom.xml -->
<version>1.0.1</version>
```

### 2. بناء الـ JAR:
```bash
./mvnw clean package
```

### 3. إنشاء الـ Installer:
```bash
build-installer.bat
```

### 4. اختبار الـ Installer:
- تثبيت على جهاز نظيف
- اختبار جميع الوظائف
- التأكد من إنشاء الاختصارات

## 🛡️ الأمان والصيانة

### النسخ الاحتياطي:
- نسخ احتياطي دوري للكود المصدري
- نسخ احتياطي لبيانات المستخدمين
- توثيق التغييرات

### التحديثات:
- تحديث المكتبات بانتظام
- اختبار التوافق مع Java الجديدة
- إصلاح الأخطاء المكتشفة

## 📞 الدعم التقني

### للمطورين:
- مراجعة الكود المصدري
- استخدام التعليقات العربية
- اتباع معايير البرمجة

### للمستخدمين:
- دليل المستخدم منفصل
- فيديوهات تعليمية
- دعم فني مباشر

## 📝 ملاحظات مهمة

1. **الترميز**: جميع الملفات بترميز UTF-8
2. **اللغة**: الواجهة عربية بالكامل
3. **التوافق**: مصمم لـ Windows أساساً
4. **الأداء**: محسن للشركات الصغيرة والمتوسطة
5. **التوسع**: قابل للتطوير والإضافة

## 🔄 خطة التطوير المستقبلية

### الإصدار 1.1:
- [ ] قاعدة بيانات SQLite
- [ ] تصدير PDF محسن
- [ ] تقارير إضافية

### الإصدار 1.2:
- [ ] دعم الشبكة المحلية
- [ ] نسخ احتياطي تلقائي
- [ ] واجهة ويب اختيارية

### الإصدار 2.0:
- [ ] دعم عدة شركات
- [ ] تكامل مع البنوك
- [ ] تطبيق موبايل

## 🚀 بناء ملف التثبيت خطوة بخطوة

### الطريقة الأولى: استخدام الـ Batch Script (مُنصح به)

1. **تشغيل ملف البناء**:
   ```bash
   build-installer.bat
   ```

2. **انتظار اكتمال العملية** (قد تستغرق 5-10 دقائق)

3. **العثور على ملف التثبيت**:
   - الموقع: `target\installer\`
   - اسم الملف: `نظام المحاسبة-1.0.0.msi` أو `.exe`

### الطريقة الثانية: البناء اليدوي

1. **تنظيف المشروع**:
   ```bash
   ./mvnw clean
   ```

2. **بناء JAR**:
   ```bash
   ./mvnw package
   ```

3. **إنشاء الـ Installer**:
   ```bash
   jpackage --type msi --input target --dest target\installer --name "نظام المحاسبة" --main-jar accounting-system-1.0.0-shaded.jar --main-class com.accounting.AccountingApplication --app-version 1.0.0 --vendor "شركة الزجاج والألومنيوم المتقدمة" --win-dir-chooser --win-menu --win-shortcut
   ```

## 📋 قائمة التحقق قبل الإصدار

### ✅ اختبار الوظائف:
- [ ] تشغيل البرنامج بنجاح
- [ ] إنشاء قيد يومي جديد
- [ ] عرض قائمة الدخل
- [ ] إنشاء فاتورة مبيعات
- [ ] إدارة المخزون
- [ ] طباعة التقارير

### ✅ اختبار التثبيت:
- [ ] تثبيت على جهاز نظيف
- [ ] إنشاء اختصارات سطح المكتب
- [ ] إضافة للقائمة الرئيسية
- [ ] إلغاء التثبيت بنجاح

### ✅ اختبار الأداء:
- [ ] سرعة بدء التشغيل
- [ ] استجابة الواجهة
- [ ] استهلاك الذاكرة
- [ ] استقرار النظام

## 🔧 حل المشاكل الشائعة

### مشكلة: فشل في بناء JAR
**الحل**:
```bash
./mvnw clean
./mvnw dependency:resolve
./mvnw package
```

### مشكلة: JPackage غير متوفر
**الحل**: التأكد من استخدام Java 17 أو أحدث

### مشكلة: فشل في إنشاء MSI
**الحل**: تجربة إنشاء EXE بدلاً من MSI

### مشكلة: مشاكل في الترميز العربي
**الحل**: إضافة `-Dfile.encoding=UTF-8` لخيارات Java

---

**© 2025 شركة الزجاج والألومنيوم المتقدمة**
