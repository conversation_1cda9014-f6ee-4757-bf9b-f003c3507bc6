@echo off
setlocal

echo.
echo ========================================
echo Creating Hossam Application Package
echo ========================================
echo.

:: Build the application first
echo Building application...
set JAVA_HOME=C:\Program Files\Eclipse Adoptium\jdk-********-hotspot
set PATH=%JAVA_HOME%\bin;%PATH%

call mvnw.cmd clean package -q
if not %errorlevel%==0 (
    echo ERROR: Build failed
    pause
    exit /b 1
)

echo ✅ Application built successfully

:: Create Hossam folder
set APP_NAME=Hossam
if exist "%APP_NAME%" rmdir /s /q "%APP_NAME%"
mkdir "%APP_NAME%"

echo Creating Hossam application package...

:: Copy the JAR file
copy "target\accounting-system-1.0.0.jar" "%APP_NAME%\"

:: Create simple launcher
echo @echo off > "%APP_NAME%\Hossam.bat"
echo setlocal >> "%APP_NAME%\Hossam.bat"
echo. >> "%APP_NAME%\Hossam.bat"
echo echo ======================================== >> "%APP_NAME%\Hossam.bat"
echo echo       نظام المحاسبة - Hossam >> "%APP_NAME%\Hossam.bat"
echo echo       Accounting System >> "%APP_NAME%\Hossam.bat"
echo echo ======================================== >> "%APP_NAME%\Hossam.bat"
echo echo. >> "%APP_NAME%\Hossam.bat"
echo echo Starting application... >> "%APP_NAME%\Hossam.bat"
echo echo. >> "%APP_NAME%\Hossam.bat"
echo. >> "%APP_NAME%\Hossam.bat"
echo :: Check for Java >> "%APP_NAME%\Hossam.bat"
echo java -version ^>nul 2^>^&1 >> "%APP_NAME%\Hossam.bat"
echo if not %%errorlevel%%==0 ^( >> "%APP_NAME%\Hossam.bat"
echo     echo Java not found! >> "%APP_NAME%\Hossam.bat"
echo     echo. >> "%APP_NAME%\Hossam.bat"
echo     echo Please install Java 17 from: >> "%APP_NAME%\Hossam.bat"
echo     echo https://adoptium.net/ >> "%APP_NAME%\Hossam.bat"
echo     echo. >> "%APP_NAME%\Hossam.bat"
echo     echo Opening download page... >> "%APP_NAME%\Hossam.bat"
echo     start https://adoptium.net/temurin/releases/ >> "%APP_NAME%\Hossam.bat"
echo     pause >> "%APP_NAME%\Hossam.bat"
echo     exit /b 1 >> "%APP_NAME%\Hossam.bat"
echo ^) >> "%APP_NAME%\Hossam.bat"
echo. >> "%APP_NAME%\Hossam.bat"
echo :: Start the application >> "%APP_NAME%\Hossam.bat"
echo java -Dfile.encoding=UTF-8 -jar accounting-system-1.0.0.jar >> "%APP_NAME%\Hossam.bat"
echo. >> "%APP_NAME%\Hossam.bat"
echo if not %%errorlevel%%==0 ^( >> "%APP_NAME%\Hossam.bat"
echo     echo. >> "%APP_NAME%\Hossam.bat"
echo     echo Application failed to start! >> "%APP_NAME%\Hossam.bat"
echo     pause >> "%APP_NAME%\Hossam.bat"
echo ^) >> "%APP_NAME%\Hossam.bat"

:: Create VBS launcher (no console window)
echo Set WshShell = CreateObject("WScript.Shell") > "%APP_NAME%\Hossam.vbs"
echo WshShell.Run chr(34) ^& "Hossam.bat" ^& Chr(34), 0 >> "%APP_NAME%\Hossam.vbs"
echo Set WshShell = Nothing >> "%APP_NAME%\Hossam.vbs"

:: Create simple README
echo # نظام المحاسبة - Hossam > "%APP_NAME%\اقرأني.txt"
echo ## Accounting System >> "%APP_NAME%\اقرأني.txt"
echo. >> "%APP_NAME%\اقرأني.txt"
echo ### طريقة التشغيل: >> "%APP_NAME%\اقرأني.txt"
echo. >> "%APP_NAME%\اقرأني.txt"
echo انقر نقراً مزدوجاً على أحد الملفات التالية: >> "%APP_NAME%\اقرأني.txt"
echo. >> "%APP_NAME%\اقرأني.txt"
echo 1. Hossam.bat (مع نافذة الأوامر) >> "%APP_NAME%\اقرأني.txt"
echo 2. Hossam.vbs (بدون نافذة الأوامر) >> "%APP_NAME%\اقرأني.txt"
echo. >> "%APP_NAME%\اقرأني.txt"
echo ### المتطلبات: >> "%APP_NAME%\اقرأني.txt"
echo - Windows 10/11 >> "%APP_NAME%\اقرأني.txt"
echo - Java 17 أو أحدث >> "%APP_NAME%\اقرأني.txt"
echo. >> "%APP_NAME%\اقرأني.txt"
echo إذا لم يكن Java مثبت، سيتم توجيهك لتحميله تلقائياً. >> "%APP_NAME%\اقرأني.txt"
echo. >> "%APP_NAME%\اقرأني.txt"
echo الدعم التقني: <EMAIL> >> "%APP_NAME%\اقرأني.txt"

:: Create installer script
echo @echo off > "%APP_NAME%\تثبيت-Java.bat"
echo echo ======================================== >> "%APP_NAME%\تثبيت-Java.bat"
echo echo Java Installation Helper >> "%APP_NAME%\تثبيت-Java.bat"
echo echo ======================================== >> "%APP_NAME%\تثبيت-Java.bat"
echo echo. >> "%APP_NAME%\تثبيت-Java.bat"
echo echo Opening Java download page... >> "%APP_NAME%\تثبيت-Java.bat"
echo echo. >> "%APP_NAME%\تثبيت-Java.bat"
echo echo Please download and install: >> "%APP_NAME%\تثبيت-Java.bat"
echo echo - OpenJDK 17 LTS >> "%APP_NAME%\تثبيت-Java.bat"
echo echo - Choose: JRE (Java Runtime Environment) >> "%APP_NAME%\تثبيت-Java.bat"
echo echo - Choose: Windows x64 >> "%APP_NAME%\تثبيت-Java.bat"
echo echo. >> "%APP_NAME%\تثبيت-Java.bat"
echo start https://adoptium.net/temurin/releases/ >> "%APP_NAME%\تثبيت-Java.bat"
echo pause >> "%APP_NAME%\تثبيت-Java.bat"

:: Create ZIP package
echo Creating ZIP package...
powershell -command "Compress-Archive -Path '%APP_NAME%\*' -DestinationPath 'Hossam.zip' -Force" 2>nul

echo.
echo ========================================
echo SUCCESS: Hossam Application Created!
echo ========================================
echo.
echo 📁 Folder: %APP_NAME%\
echo 📦 ZIP File: Hossam.zip
echo.
echo 📋 Contents:
dir /b "%APP_NAME%"
echo.

:: Show file sizes
echo 📊 File sizes:
for %%f in ("%APP_NAME%\*") do (
    echo   %%~nxf: %%~zf bytes
)

if exist "Hossam.zip" (
    echo.
    for %%f in ("Hossam.zip") do echo 📦 ZIP size: %%~zf bytes
)

echo.
echo ✅ Ready to use!
echo.
echo 🚀 To run the application:
echo   1. Double-click: Hossam\Hossam.bat
echo   2. Or double-click: Hossam\Hossam.vbs (silent mode)
echo.
echo 📤 To distribute:
echo   1. Send the ZIP file: Hossam.zip
echo   2. Or copy the folder: Hossam\
echo.
echo 💡 The application will automatically guide users to install Java if needed.
echo.

pause
