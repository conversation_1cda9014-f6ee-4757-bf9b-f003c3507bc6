@echo off
setlocal enabledelayedexpansion

echo.
echo ========================================
echo Building Accounting System Installer
echo ========================================
echo.

:: Set Java environment
set JAVA_HOME=C:\Program Files\Eclipse Adoptium\jdk-********-hotspot
set PATH=%JAVA_HOME%\bin;%PATH%

echo Checking requirements...
echo.

:: Check Java
java -version >nul 2>&1
if not %errorlevel%==0 (
    echo ERROR: Java not found
    echo Please install Java 17 or newer
    pause
    exit /b 1
)

echo Java is available
java -version

:: Check Maven
call mvnw.cmd -version >nul 2>&1
if not %errorlevel%==0 (
    echo ERROR: Maven not available
    pause
    exit /b 1
)

echo Maven is available

echo.
echo Cleaning project...
call mvnw.cmd clean
if not %errorlevel%==0 (
    echo ERROR: Failed to clean project
    pause
    exit /b 1
)

echo.
echo Building project...
call mvnw.cmd compile
if not %errorlevel%==0 (
    echo ERROR: Failed to compile project
    pause
    exit /b 1
)

echo.
echo Creating JAR...
call mvnw.cmd package
if not %errorlevel%==0 (
    echo ERROR: Failed to create JAR
    pause
    exit /b 1
)

echo.
echo Creating installer...

:: Check JPackage
jpackage --version >nul 2>&1
if not %errorlevel%==0 (
    echo ERROR: JPackage not available
    echo Please make sure you are using Java 17 or newer
    pause
    exit /b 1
)

:: Create installer directory
if not exist "target\installer" mkdir "target\installer"

:: Build installer using JPackage
echo Building Windows Installer...

jpackage --type msi --input target --dest target\installer --name "AccountingSystem" --main-jar accounting-system-1.0.0.jar --main-class com.accounting.AccountingApplication --app-version 1.0.0 --vendor "Advanced Glass and Aluminum Company" --description "Integrated accounting system for glass and aluminum companies" --copyright "© 2025 Advanced Glass and Aluminum Company" --win-dir-chooser --win-menu --win-shortcut --win-upgrade-uuid ************************************ --java-options "-Dfile.encoding=UTF-8" --java-options "-Djava.awt.headless=false" --java-options "--add-opens javafx.graphics/javafx.scene=ALL-UNNAMED" --java-options "--add-opens javafx.controls/javafx.scene.control=ALL-UNNAMED" --java-options "--add-opens javafx.base/javafx.beans.property=ALL-UNNAMED"

if not %errorlevel%==0 (
    echo ERROR: Failed to create MSI installer
    echo.
    echo Trying to create EXE installer instead...
    
    jpackage --type exe --input target --dest target\installer --name "AccountingSystem" --main-jar accounting-system-1.0.0.jar --main-class com.accounting.AccountingApplication --app-version 1.0.0 --vendor "Advanced Glass and Aluminum Company" --description "Integrated accounting system for glass and aluminum companies" --copyright "© 2025 Advanced Glass and Aluminum Company" --win-dir-chooser --win-menu --win-shortcut --java-options "-Dfile.encoding=UTF-8" --java-options "-Djava.awt.headless=false" --java-options "--add-opens javafx.graphics/javafx.scene=ALL-UNNAMED" --java-options "--add-opens javafx.controls/javafx.scene.control=ALL-UNNAMED" --java-options "--add-opens javafx.base/javafx.beans.property=ALL-UNNAMED"
    
    if not %errorlevel%==0 (
        echo ERROR: Failed to create installer
        pause
        exit /b 1
    )
)

echo.
echo SUCCESS: Installer created successfully!
echo.
echo File locations:
echo    - JAR: target\accounting-system-1.0.0.jar
echo    - Installer: target\installer\
echo.

:: Show installer files
if exist "target\installer" (
    echo Available installer files:
    dir /b "target\installer\*.msi" "target\installer\*.exe" 2>nul
    echo.
    
    :: Show file sizes
    for %%f in ("target\installer\*.msi" "target\installer\*.exe") do (
        if exist "%%f" (
            echo File size: %%~zf bytes
        )
    )
    echo.
)

echo Build completed successfully!
echo.
echo To run the program directly:
echo    java -jar target\accounting-system-1.0.0.jar
echo.
echo To install the program:
echo    Run the installer file from target\installer folder
echo.
echo Important notes:
echo    - Installer includes embedded Java runtime
echo    - End users do not need to install Java separately
echo    - File size is approximately 80-120 MB
echo    - Compatible with Windows 10/11 (64-bit)
echo.
echo Technical support: <EMAIL>
echo.

pause
