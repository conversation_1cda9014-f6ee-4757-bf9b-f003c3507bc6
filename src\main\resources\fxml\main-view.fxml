<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>

<BorderPane fx:id="mainBorderPane" xmlns="http://javafx.com/javafx/17.0.2-ea" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.accounting.controller.MainController">
   <top>
      <VBox>
         <children>
            <!-- شريط القوائم الرئيسي -->
            <MenuBar fx:id="menuBar" styleClass="main-menu-bar">
               <!-- سيتم إضافة القوائم برمجياً في المتحكم -->
            </MenuBar>
            
            <!-- شريط الأدوات -->
            <ToolBar styleClass="main-toolbar">
               <items>
                  <Button styleClass="toolbar-button" text="جديد">
                     <graphic>
                        <Region styleClass="icon-new" />
                     </graphic>
                  </Button>
                  <Button styleClass="toolbar-button" text="حفظ">
                     <graphic>
                        <Region styleClass="icon-save" />
                     </graphic>
                  </Button>
                  <Button styleClass="toolbar-button" text="طباعة">
                     <graphic>
                        <Region styleClass="icon-print" />
                     </graphic>
                  </Button>
                  <Separator orientation="VERTICAL" />
                  <Button styleClass="toolbar-button" text="تراجع">
                     <graphic>
                        <Region styleClass="icon-undo" />
                     </graphic>
                  </Button>
                  <Button styleClass="toolbar-button" text="إعادة">
                     <graphic>
                        <Region styleClass="icon-redo" />
                     </graphic>
                  </Button>
                  <Separator orientation="VERTICAL" />
                  <Button styleClass="toolbar-button" text="بحث">
                     <graphic>
                        <Region styleClass="icon-search" />
                     </graphic>
                  </Button>
               </items>
            </ToolBar>
         </children>
      </VBox>
   </top>
   
   <center>
      <!-- منطقة المحتوى الرئيسية -->
      <ScrollPane fitToWidth="true" fitToHeight="true" styleClass="content-scroll-pane">
         <content>
            <VBox fx:id="contentArea" styleClass="content-area">
               <padding>
                  <Insets bottom="20.0" left="20.0" right="20.0" top="20.0" />
               </padding>
            </VBox>
         </content>
      </ScrollPane>
   </center>
   
   <bottom>
      <!-- شريط الحالة -->
      <HBox styleClass="status-bar">
         <children>
            <Label fx:id="statusLabel" text="جاهز" styleClass="status-label">
               <HBox.margin>
                  <Insets bottom="5.0" left="10.0" right="5.0" top="5.0" />
               </HBox.margin>
            </Label>
            <Region HBox.hgrow="ALWAYS" />
            <Label text="نظام الحسابات المتكامل v1.0" styleClass="version-label">
               <HBox.margin>
                  <Insets bottom="5.0" left="5.0" right="10.0" top="5.0" />
               </HBox.margin>
            </Label>
         </children>
      </HBox>
   </bottom>
</BorderPane>
