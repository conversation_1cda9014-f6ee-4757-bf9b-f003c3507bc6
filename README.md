# 🏢 نظام الحسابات المتكامل - Integrated Accounting System

<div align="center">

![Java](https://img.shields.io/badge/Java-17+-orange.svg)
![JavaFX](https://img.shields.io/badge/JavaFX-21-blue.svg)
![Maven](https://img.shields.io/badge/Maven-3.6+-green.svg)
![Status](https://img.shields.io/badge/Status-In%20Development-yellow.svg)

**نظام حسابات ديسك توب متكامل وعصري باستخدام JavaFX**

</div>

## 📋 وصف المشروع

نظام حسابات ديسك توب متكامل مبني باستخدام JavaFX يوفر حلول شاملة لإدارة الأعمال المحاسبية. يتميز بواجهة مستخدم أنيقة وعصرية مع دعم كامل للغة العربية.

## 🚀 البدء السريع

### 1️⃣ تثبيت Java (مطلوب)
```bash
# تحميل من الموقع الرسمي
https://adoptium.net/
# اختر OpenJDK 17 أو 21 (LTS)
```

### 2️⃣ تشغيل البرنامج
```bash
# الطريقة الأسهل (Windows)
run.bat

# أو باستخدام Maven Wrapper
mvnw.cmd clean javafx:run
```

### 3️⃣ اختبار النظام
```bash
# فحص متطلبات النظام
check-system.bat

# اختبار Java فقط
test-java.bat
```

## 📁 الموديولات المتاحة

| الموديول | الوصف | الحالة |
|---------|-------|--------|
| 🏢 **بيانات الشركة** | معلومات الشركة، الفروع، الإعدادات | ⚠️ قيد التطوير |
| 🛒 **المبيعات** | فواتير، عروض أسعار، عملاء، تقارير | ⚠️ قيد التطوير |
| 📦 **المخازن** | أصناف، حركة مخزن، جرد، تقارير | ⚠️ قيد التطوير |
| 💰 **الحسابات** | دليل حسابات، قيود، موردين، تقارير مالية | ⚠️ قيد التطوير |
| 👥 **الرواتب** | موظفين، حساب رواتب، حضور، تقارير | ⚠️ قيد التطوير |
| 🏭 **التصنيع** | أوامر إنتاج، وصفات، مراكز عمل، تقارير | ⚠️ قيد التطوير |

## 🎨 الميزات الرئيسية

- ✅ **واجهة عصرية**: تصميم أنيق ومتجاوب
- ✅ **دعم العربية**: واجهة مُحسنة للغة العربية
- ✅ **سهولة الاستخدام**: قوائم منظمة وأزرار وصول سريع
- ✅ **أيقونات واضحة**: رموز معبرة لجميع الوظائف
- ✅ **شريط حالة**: معلومات فورية عن حالة النظام
- ✅ **تشغيل محلي**: يعمل بدون اتصال إنترنت (بعد التثبيت)

## 🛠️ متطلبات النظام

### الحد الأدنى:
- **Java**: OpenJDK 17 أو أحدث
- **نظام التشغيل**: Windows 10+, macOS 10.14+, Linux Ubuntu 18.04+
- **الذاكرة**: 4 GB RAM
- **مساحة القرص**: 500 MB

### الموصى به:
- **Java**: OpenJDK 21 LTS
- **الذاكرة**: 8 GB RAM
- **مساحة القرص**: 2 GB

## 📂 هيكل المشروع

```
accounting-system/
├── 📄 run.bat                 # ملف التشغيل الرئيسي
├── 📄 check-system.bat        # فحص متطلبات النظام
├── 📄 test-java.bat          # اختبار Java
├── 📄 pom.xml                # إعدادات Maven
├── 📁 src/main/
│   ├── 📁 java/com/accounting/
│   │   ├── 📄 AccountingApplication.java
│   │   └── 📁 controller/
│   └── 📁 resources/
│       ├── 📁 fxml/          # ملفات الواجهة
│       ├── 📁 css/           # ملفات التصميم
│       └── 📄 application.properties
└── 📁 .mvn/wrapper/          # Maven Wrapper
```

## 🔧 ملفات التشغيل

| الملف | الوصف | الاستخدام |
|-------|-------|----------|
| `run.bat` | التشغيل الرئيسي | للمستخدمين العاديين |
| `run-dev.bat` | وضع المطور | للمطورين (معلومات تفصيلية) |
| `run-simple.bat` | تشغيل مبسط | عند وجود مشاكل |
| `check-system.bat` | فحص النظام | للتأكد من المتطلبات |
| `test-java.bat` | اختبار Java | للتأكد من عمل Java |

## 📖 الوثائق

- 📘 [دليل التثبيت](INSTALL.md) - تعليمات التثبيت المفصلة
- 🚀 [البدء السريع](QUICK_START.md) - خطوات سريعة للبدء
- 🎨 [لوجو النظام](logo.txt) - شعار النظام

## 🚧 الحالة الحالية

**المرحلة:** التطوير الأولي ✅

**ما تم إنجازه:**
- ✅ الهيكل الأساسي للمشروع
- ✅ الواجهة الرئيسية والقوائم
- ✅ التصميم العصري والأنيق
- ✅ نظام التنقل بين الموديولات
- ✅ ملفات التشغيل والاختبار
- ✅ دعم اللغة العربية

**قيد التطوير:**
- ⚠️ تفاصيل كل موديول
- ⚠️ قاعدة البيانات
- ⚠️ نظام التقارير
- ⚠️ نظام الأمان

## 🔮 التطوير المستقبلي

### المرحلة الثانية:
- 🔄 إضافة قاعدة بيانات SQLite
- 🔄 تطوير موديول المبيعات
- 🔄 تطوير موديول المخازن

### المرحلة الثالثة:
- 🔄 تطوير موديول الحسابات
- 🔄 نظام التقارير المتقدم
- 🔄 نظام النسخ الاحتياطي

### المرحلة الرابعة:
- 🔄 موديول الرواتب والأجور
- 🔄 موديول التصنيع
- 🔄 نظام الأمان والمستخدمين

## 🤝 المساهمة

هذا المشروع مفتوح للتطوير والتحسين. يمكنك المساهمة من خلال:
- 🐛 الإبلاغ عن الأخطاء
- 💡 اقتراح ميزات جديدة
- 🔧 تحسين الكود
- 📖 تحسين الوثائق

## 📞 الدعم الفني

إذا واجهت أي مشاكل:
1. 🔍 شغل `check-system.bat` للتحقق من النظام
2. 📖 راجع [دليل التثبيت](INSTALL.md)
3. 🧪 جرب `test-java.bat` لاختبار Java
4. 🔧 استخدم `run-dev.bat` للمزيد من التفاصيل

---

<div align="center">

**🎯 نظام الحسابات المتكامل v1.0**

*تم تطوير هذا النظام باستخدام أحدث تقنيات JavaFX لضمان الأداء والاستقرار*

[![Java](https://img.shields.io/badge/Made%20with-Java-orange.svg)](https://www.java.com/)
[![JavaFX](https://img.shields.io/badge/UI-JavaFX-blue.svg)](https://openjfx.io/)
[![Maven](https://img.shields.io/badge/Build-Maven-green.svg)](https://maven.apache.org/)

</div>
