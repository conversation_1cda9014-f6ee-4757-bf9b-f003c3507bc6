package com.accounting.controller;

import com.accounting.model.*;
import com.accounting.service.ManufacturingService;
import javafx.geometry.Insets;
import javafx.geometry.Pos;
import javafx.scene.Scene;
import javafx.scene.control.*;
import javafx.scene.layout.*;
import javafx.stage.Modality;
import javafx.stage.Stage;
import de.jensd.fx.glyphs.fontawesome.FontAwesomeIcon;
import de.jensd.fx.glyphs.fontawesome.FontAwesomeIconView;
import java.time.LocalDate;
import java.util.Optional;
import java.util.function.Consumer;

/**
 * كنترولر نموذج أمر التصنيع
 */
public class ManufacturingOrderFormController {

    private final ManufacturingService manufacturingService;
    private ManufacturingOrder currentOrder;

    private boolean isEditMode;
    
    // عناصر النموذج الأساسية
    private TextField customerNameField;
    private TextField invoiceNumberField;
    private TextField orderNumberField;
    private DatePicker orderDatePicker;
    private TextArea notesArea;
    
    // خانات اختيار الخدمات
    private CheckBox glassCuttingCheckBox;
    private CheckBox thermalGlassCheckBox;
    private CheckBox filmCheckBox;
    private CheckBox bulletProofFilmCheckBox;
    private CheckBox doubleGlassCheckBox;
    private CheckBox polishCheckBox;
    private CheckBox holeCheckBox;
    private CheckBox cncCheckBox;
    private CheckBox drawCheckBox;
    private CheckBox otherServicesCheckBox;
    
    // جداول التفاصيل
    private GlassCuttingTableController glassCuttingTableController;
    private FilmTableController filmTableController;
    private DoubleGlassTableController doubleGlassTableController;
    private RequiredMeasurementsTableController requiredMeasurementsTableController;
    // private IncreasedMeasurementsTableController increasedMeasurementsTableController;
    
    // خاصية الزيادة
    private CheckBox hasIncreaseCheckBox;
    private TextField increaseValueField;
    private VBox increasedMeasurementsContainer;
    
    // تبويبات
    private TabPane tabPane;
    
    public ManufacturingOrderFormController(ManufacturingService manufacturingService) {
        this.manufacturingService = manufacturingService;
    }

    /**
     * إنشاء صفحة نموذج أمر التصنيع للنافذة الواحدة
     */
    public VBox createManufacturingOrderFormPage(ManufacturingOrder order,
                                               Consumer<ManufacturingOrder> onSave,
                                               Consumer<ManufacturingOrder> onPrint) {
        currentOrder = order != null ? order : new ManufacturingOrder();

        VBox container = new VBox(20);
        container.setPadding(new Insets(20));

        // العنوان
        Label titleLabel = new Label(order == null ? "📝 أمر تصنيع جديد" : "📝 تعديل أمر التصنيع");
        titleLabel.getStyleClass().add("section-title");

        // معلومات الأمر الأساسية
        VBox basicInfoBox = createBasicInfoForm();

        // اختيار الخدمات
        VBox servicesBox = createServicesSelectionBox();

        // التبويبات
        TabPane tabPane = createDetailsTabPane();

        // تهيئة المتغيرات إذا لم تكن موجودة
        if (customerNameField == null) {
            customerNameField = new TextField();
            invoiceNumberField = new TextField();
            orderDatePicker = new DatePicker(LocalDate.now());
            notesArea = new TextArea();

            glassCuttingCheckBox = new CheckBox();
            thermalGlassCheckBox = new CheckBox();
            filmCheckBox = new CheckBox();
            bulletProofFilmCheckBox = new CheckBox();
            doubleGlassCheckBox = new CheckBox();
            polishCheckBox = new CheckBox();
            holeCheckBox = new CheckBox();
            cncCheckBox = new CheckBox();
            drawCheckBox = new CheckBox();
            otherServicesCheckBox = new CheckBox();

            hasIncreaseCheckBox = new CheckBox();
            increaseValueField = new TextField();
        }

        // أزرار الإجراءات
        HBox buttonsBox = createPageButtonsSection(onSave, onPrint);

        container.getChildren().addAll(titleLabel, basicInfoBox, servicesBox, tabPane, buttonsBox);

        // تحميل البيانات إذا كان الأمر موجود
        if (order != null) {
            loadOrderData(order);
        }

        return container;
    }

    /**
     * إنشاء قسم أزرار الصفحة
     */
    private HBox createPageButtonsSection(Consumer<ManufacturingOrder> onSave, Consumer<ManufacturingOrder> onPrint) {
        HBox buttonsBox = new HBox(15);
        buttonsBox.setAlignment(Pos.CENTER);
        buttonsBox.setPadding(new Insets(20, 0, 0, 0));

        Button saveBtn = new Button("حفظ");
        FontAwesomeIconView saveIcon = new FontAwesomeIconView(FontAwesomeIcon.SAVE);
        saveIcon.setSize("12px");
        saveBtn.setGraphic(saveIcon);
        saveBtn.getStyleClass().add("save-button");
        saveBtn.setOnAction(e -> saveOrderForPage(onSave));

        Button printBtn = new Button("طباعة");
        FontAwesomeIconView printIcon = new FontAwesomeIconView(FontAwesomeIcon.PRINT);
        printIcon.setSize("12px");
        printBtn.setGraphic(printIcon);
        printBtn.getStyleClass().add("print-button");
        printBtn.setOnAction(e -> {
            if (saveOrderForPage(onSave)) {
                onPrint.accept(currentOrder);
            }
        });

        Button saveAndPrintBtn = new Button("حفظ وطباعة");
        FontAwesomeIconView saveAndPrintIcon = new FontAwesomeIconView(FontAwesomeIcon.PRINT);
        saveAndPrintIcon.setSize("12px");
        saveAndPrintBtn.setGraphic(saveAndPrintIcon);
        saveAndPrintBtn.getStyleClass().add("save-print-button");
        saveAndPrintBtn.setOnAction(e -> {
            if (saveOrderForPage(onSave)) {
                onPrint.accept(currentOrder);
            }
        });

        buttonsBox.getChildren().addAll(saveBtn, printBtn, saveAndPrintBtn);
        return buttonsBox;
    }

    /**
     * حفظ الأمر للصفحة
     */
    private boolean saveOrderForPage(Consumer<ManufacturingOrder> onSave) {
        // التحقق من الحقول المطلوبة
        if (customerNameField != null && customerNameField.getText().trim().isEmpty()) {
            showErrorAlert("اسم العميل مطلوب.");
            return false;
        }

        // تحديث بيانات الأمر
        updateOrderFromForm();

        // حفظ في الخدمة
        boolean success;
        if (manufacturingService.findManufacturingOrderById(currentOrder.getOrderId()) != null) {
            success = manufacturingService.updateManufacturingOrder(currentOrder);
        } else {
            success = manufacturingService.addManufacturingOrder(currentOrder);
        }

        if (success) {
            onSave.accept(currentOrder);
            return true;
        } else {
            showErrorAlert("فشل في حفظ أمر التصنيع.");
            return false;
        }
    }

    /**
     * تحديث بيانات الأمر من النموذج
     */
    private void updateOrderFromForm() {
        currentOrder.setCustomerName(customerNameField.getText().trim());
        currentOrder.setInvoiceNumber(invoiceNumberField.getText().trim());
        currentOrder.setOrderDate(orderDatePicker.getValue());
        currentOrder.setNotes(notesArea.getText().trim());

        // تحديث الخدمات المختارة
        currentOrder.setGlassCuttingSelected(glassCuttingCheckBox.isSelected());
        currentOrder.setThermalGlassSelected(thermalGlassCheckBox.isSelected());
        currentOrder.setFilmSelected(filmCheckBox.isSelected());
        currentOrder.setBulletProofFilmSelected(bulletProofFilmCheckBox.isSelected());
        currentOrder.setDoubleGlassSelected(doubleGlassCheckBox.isSelected());
        currentOrder.setPolishSelected(polishCheckBox.isSelected());
        currentOrder.setHoleSelected(holeCheckBox.isSelected());
        currentOrder.setCncSelected(cncCheckBox.isSelected());
        currentOrder.setDrawSelected(drawCheckBox.isSelected());
        currentOrder.setOtherServicesSelected(otherServicesCheckBox.isSelected());

        // تحديث خاصية الزيادة
        currentOrder.setHasIncrease(hasIncreaseCheckBox.isSelected());
        if (hasIncreaseCheckBox.isSelected() && !increaseValueField.getText().trim().isEmpty()) {
            try {
                double increaseValue = Double.parseDouble(increaseValueField.getText().trim());
                currentOrder.setIncreaseValue(increaseValue);
                currentOrder.updateIncreasedMeasurements();
            } catch (NumberFormatException e) {
                currentOrder.setHasIncrease(false);
            }
        }
    }

    /**
     * تحميل بيانات الأمر
     */
    private void loadOrderData(ManufacturingOrder order) {
        customerNameField.setText(order.getCustomerName());
        invoiceNumberField.setText(order.getInvoiceNumber());
        orderDatePicker.setValue(order.getOrderDate());
        notesArea.setText(order.getNotes());

        // تحميل الخدمات المختارة
        glassCuttingCheckBox.setSelected(order.isGlassCuttingSelected());
        thermalGlassCheckBox.setSelected(order.isThermalGlassSelected());
        filmCheckBox.setSelected(order.isFilmSelected());
        bulletProofFilmCheckBox.setSelected(order.isBulletProofFilmSelected());
        doubleGlassCheckBox.setSelected(order.isDoubleGlassSelected());
        polishCheckBox.setSelected(order.isPolishSelected());
        holeCheckBox.setSelected(order.isHoleSelected());
        cncCheckBox.setSelected(order.isCncSelected());
        drawCheckBox.setSelected(order.isDrawSelected());
        otherServicesCheckBox.setSelected(order.isOtherServicesSelected());

        // تحميل خاصية الزيادة
        hasIncreaseCheckBox.setSelected(order.isHasIncrease());
        if (order.isHasIncrease()) {
            increaseValueField.setText(String.valueOf(order.getIncreaseValue()));
        }

        // تحديث التبويبات
        updateTabVisibility();
        updateIncreasedMeasurements();
    }


    
    /**
     * عرض نموذج أمر التصنيع
     */
    public Optional<ManufacturingOrder> showManufacturingOrderForm(ManufacturingOrder order) {
        this.currentOrder = order;
        this.isEditMode = (order != null);
        
        Stage dialog = new Stage();
        dialog.setTitle(isEditMode ? "تعديل أمر التصنيع" : "أمر تصنيع جديد - Manufacturing Order");
        dialog.initModality(Modality.APPLICATION_MODAL);
        
        // تحديد حجم النافذة
        javafx.stage.Screen screen = javafx.stage.Screen.getPrimary();
        double screenHeight = screen.getVisualBounds().getHeight();
        double screenWidth = screen.getVisualBounds().getWidth();
        
        dialog.setWidth(Math.min(1200, screenWidth * 0.9));
        dialog.setHeight(Math.min(800, screenHeight * 0.9));
        dialog.setResizable(true);
        
        VBox mainLayout = new VBox(15);
        mainLayout.setPadding(new Insets(20));
        
        // العنوان
        Label titleLabel = new Label(isEditMode ? "✏️ تعديل أمر التصنيع" : "➕ Manufacturing Order - أمر تصنيع جديد");
        titleLabel.getStyleClass().add("dialog-title");
        
        // النموذج الأساسي
        VBox basicInfoBox = createBasicInfoForm();
        
        // خانات اختيار الخدمات
        VBox servicesBox = createServicesSelectionBox();
        
        // التبويبات للتفاصيل
        tabPane = createDetailsTabPane();
        
        // تحميل البيانات إذا كان في وضع التعديل
        if (isEditMode) {
            loadOrderData();
        } else {
            // إنشاء أمر جديد
            currentOrder = new ManufacturingOrder();
            currentOrder.setOrderNumber(manufacturingService.generateOrderNumber());
        }
        
        // أزرار الحفظ والإلغاء
        HBox buttonBox = createButtonBox(dialog);
        
        // وضع المحتوى في ScrollPane
        VBox contentBox = new VBox(15);
        contentBox.getChildren().addAll(basicInfoBox, servicesBox, tabPane);
        
        ScrollPane scrollPane = new ScrollPane(contentBox);
        scrollPane.setFitToWidth(true);
        scrollPane.setVbarPolicy(ScrollPane.ScrollBarPolicy.AS_NEEDED);
        scrollPane.setHbarPolicy(ScrollPane.ScrollBarPolicy.NEVER);
        VBox.setVgrow(scrollPane, Priority.ALWAYS);
        
        mainLayout.getChildren().addAll(titleLabel, scrollPane, buttonBox);
        
        Scene scene = new Scene(mainLayout);
        scene.getStylesheets().add(getClass().getResource("/css/styles.css").toExternalForm());
        dialog.setScene(scene);
        
        // عرض النافذة وانتظار النتيجة
        dialog.showAndWait();
        
        return Optional.ofNullable(currentOrder);
    }
    
    /**
     * إنشاء نموذج المعلومات الأساسية
     */
    private VBox createBasicInfoForm() {
        VBox formBox = new VBox(15);
        formBox.setPadding(new Insets(20));
        formBox.setStyle("-fx-background-color: #f8f9fa; -fx-border-color: #dee2e6; -fx-border-width: 1; -fx-border-radius: 8;");
        
        Label sectionTitle = new Label("📋 المعلومات الأساسية");
        sectionTitle.getStyleClass().add("section-title");
        
        // الصف الأول
        HBox firstRow = new HBox(15);
        firstRow.setAlignment(Pos.CENTER_LEFT);
        
        // اسم العميل
        VBox customerBox = new VBox(5);
        Label customerLabel = new Label("اسم العميل:");
        customerLabel.getStyleClass().add("field-label");
        customerNameField = new TextField();
        customerNameField.setPromptText("أدخل اسم العميل");
        customerNameField.setPrefWidth(200);
        customerBox.getChildren().addAll(customerLabel, customerNameField);
        
        // رقم الفاتورة
        VBox invoiceBox = new VBox(5);
        Label invoiceLabel = new Label("رقم الفاتورة:");
        invoiceLabel.getStyleClass().add("field-label");
        invoiceNumberField = new TextField();
        invoiceNumberField.setPromptText("أدخل رقم الفاتورة");
        invoiceNumberField.setPrefWidth(150);
        invoiceBox.getChildren().addAll(invoiceLabel, invoiceNumberField);
        
        firstRow.getChildren().addAll(customerBox, invoiceBox);
        
        // الصف الثاني
        HBox secondRow = new HBox(15);
        secondRow.setAlignment(Pos.CENTER_LEFT);
        
        // رقم أمر العمل
        VBox orderNumberBox = new VBox(5);
        Label orderNumberLabel = new Label("رقم أمر العمل:");
        orderNumberLabel.getStyleClass().add("field-label");
        orderNumberField = new TextField();
        orderNumberField.setPromptText("يتولد تلقائياً");
        orderNumberField.setEditable(false);
        orderNumberField.setPrefWidth(150);
        orderNumberBox.getChildren().addAll(orderNumberLabel, orderNumberField);
        
        // التاريخ
        VBox dateBox = new VBox(5);
        Label dateLabel = new Label("التاريخ:");
        dateLabel.getStyleClass().add("field-label");
        orderDatePicker = new DatePicker();
        orderDatePicker.setValue(LocalDate.now());
        orderDatePicker.setPrefWidth(150);
        dateBox.getChildren().addAll(dateLabel, orderDatePicker);
        
        secondRow.getChildren().addAll(orderNumberBox, dateBox);
        
        // الملاحظات
        VBox notesBox = new VBox(5);
        Label notesLabel = new Label("ملاحظات:");
        notesLabel.getStyleClass().add("field-label");
        notesArea = new TextArea();
        notesArea.setPromptText("ملاحظات إضافية...");
        notesArea.setPrefRowCount(3);
        notesArea.setWrapText(true);
        notesBox.getChildren().addAll(notesLabel, notesArea);
        
        formBox.getChildren().addAll(sectionTitle, firstRow, secondRow, notesBox);
        return formBox;
    }
    
    /**
     * إنشاء صندوق اختيار الخدمات
     */
    private VBox createServicesSelectionBox() {
        VBox servicesBox = new VBox(15);
        servicesBox.setPadding(new Insets(20));
        servicesBox.setStyle("-fx-background-color: #f8f9fa; -fx-border-color: #dee2e6; -fx-border-width: 1; -fx-border-radius: 8;");
        
        Label sectionTitle = new Label("✅ اختيار الخدمات");
        sectionTitle.getStyleClass().add("section-title");
        
        // الصف الأول من الخدمات
        HBox firstServicesRow = new HBox(20);
        firstServicesRow.setAlignment(Pos.CENTER_LEFT);
        
        glassCuttingCheckBox = new CheckBox("قص زجاج");
        thermalGlassCheckBox = new CheckBox("حراري زجاج");
        filmCheckBox = new CheckBox("فيلم");
        bulletProofFilmCheckBox = new CheckBox("فيلم ضد الرصاص");
        doubleGlassCheckBox = new CheckBox("دبل جلاس");
        
        firstServicesRow.getChildren().addAll(
            glassCuttingCheckBox, thermalGlassCheckBox, filmCheckBox, 
            bulletProofFilmCheckBox, doubleGlassCheckBox
        );
        
        // الصف الثاني من الخدمات
        HBox secondServicesRow = new HBox(20);
        secondServicesRow.setAlignment(Pos.CENTER_LEFT);
        
        polishCheckBox = new CheckBox("Polish");
        holeCheckBox = new CheckBox("Hole");
        cncCheckBox = new CheckBox("CNC");
        drawCheckBox = new CheckBox("Draw");
        otherServicesCheckBox = new CheckBox("خدمات أخرى");
        
        secondServicesRow.getChildren().addAll(
            polishCheckBox, holeCheckBox, cncCheckBox, drawCheckBox, otherServicesCheckBox
        );
        
        // ربط الأحداث
        setupServiceCheckBoxListeners();
        
        servicesBox.getChildren().addAll(sectionTitle, firstServicesRow, secondServicesRow);
        return servicesBox;
    }
    
    /**
     * إعداد مستمعي أحداث خانات اختيار الخدمات
     */
    private void setupServiceCheckBoxListeners() {
        glassCuttingCheckBox.selectedProperty().addListener((obs, oldVal, newVal) -> {
            updateTabVisibility();
        });
        
        filmCheckBox.selectedProperty().addListener((obs, oldVal, newVal) -> {
            updateTabVisibility();
        });
        
        doubleGlassCheckBox.selectedProperty().addListener((obs, oldVal, newVal) -> {
            updateTabVisibility();
        });
        
        // يمكن إضافة المزيد من المستمعين للخدمات الأخرى
    }
    
    /**
     * إنشاء تبويبات التفاصيل
     */
    private TabPane createDetailsTabPane() {
        TabPane tabPane = new TabPane();
        tabPane.setTabClosingPolicy(TabPane.TabClosingPolicy.UNAVAILABLE);
        
        // تبويب قص الزجاج
        Tab glassCuttingTab = new Tab("قص زجاج");
        glassCuttingTab.setContent(createGlassCuttingTable());
        glassCuttingTab.setDisable(true);
        
        // تبويب الفيلم
        Tab filmTab = new Tab("فيلم");
        filmTab.setContent(createFilmTable());
        filmTab.setDisable(true);
        
        // تبويب دبل جلاس
        Tab doubleGlassTab = new Tab("دبل جلاس");
        doubleGlassTab.setContent(createDoubleGlassTable());
        doubleGlassTab.setDisable(true);
        
        // تبويب المقاسات المطلوبة
        Tab requiredMeasurementsTab = new Tab("المقاسات المطلوبة");
        requiredMeasurementsTab.setContent(createRequiredMeasurementsTable());
        
        // تبويب الزيادة
        Tab increaseTab = new Tab("الزيادة");
        increaseTab.setContent(createIncreaseSection());
        
        tabPane.getTabs().addAll(
            glassCuttingTab, filmTab, doubleGlassTab, 
            requiredMeasurementsTab, increaseTab
        );
        
        return tabPane;
    }
    
    /**
     * تحديث رؤية التبويبات حسب الخدمات المختارة
     */
    private void updateTabVisibility() {
        if (tabPane != null) {
            tabPane.getTabs().get(0).setDisable(!glassCuttingCheckBox.isSelected()); // قص زجاج
            tabPane.getTabs().get(1).setDisable(!filmCheckBox.isSelected()); // فيلم
            tabPane.getTabs().get(2).setDisable(!doubleGlassCheckBox.isSelected()); // دبل جلاس
        }
    }
    
    /**
     * إنشاء جدول قص الزجاج
     */
    private VBox createGlassCuttingTable() {
        if (currentOrder == null) {
            currentOrder = new ManufacturingOrder();
        }

        glassCuttingTableController = new GlassCuttingTableController(currentOrder.getGlassCuttingItems());
        return glassCuttingTableController.createGlassCuttingTable();
    }
    
    /**
     * إنشاء جدول الفيلم
     */
    private VBox createFilmTable() {
        if (currentOrder == null) {
            currentOrder = new ManufacturingOrder();
        }

        filmTableController = new FilmTableController(currentOrder.getFilmItems());
        return filmTableController.createFilmTable();
    }
    
    /**
     * إنشاء جدول دبل جلاس
     */
    private VBox createDoubleGlassTable() {
        if (currentOrder == null) {
            currentOrder = new ManufacturingOrder();
        }

        doubleGlassTableController = new DoubleGlassTableController(currentOrder.getDoubleGlassItems());
        return doubleGlassTableController.createDoubleGlassTable();
    }
    
    /**
     * إنشاء جدول المقاسات المطلوبة
     */
    private VBox createRequiredMeasurementsTable() {
        if (currentOrder == null) {
            currentOrder = new ManufacturingOrder();
        }

        requiredMeasurementsTableController = new RequiredMeasurementsTableController(currentOrder.getRequiredMeasurements());

        // إضافة مستمع لتحديث المقاسات مع الزيادة عند تغيير البيانات
        currentOrder.getRequiredMeasurements().addListener((javafx.collections.ListChangeListener<RequiredMeasurement>) change -> {
            updateIncreasedMeasurements();
        });

        return requiredMeasurementsTableController.createRequiredMeasurementsTable();
    }
    
    /**
     * إنشاء قسم الزيادة
     */
    private VBox createIncreaseSection() {
        VBox container = new VBox(15);
        container.setPadding(new Insets(15));
        
        Label title = new Label("📈 خاصية الزيادة");
        title.getStyleClass().add("section-title");
        
        // خانة اختيار الزيادة
        hasIncreaseCheckBox = new CheckBox("تطبيق زيادة على المقاسات");
        
        // حقل قيمة الزيادة
        HBox increaseValueBox = new HBox(10);
        increaseValueBox.setAlignment(Pos.CENTER_LEFT);
        
        Label increaseLabel = new Label("قيمة الزيادة (مم):");
        increaseValueField = new TextField();
        increaseValueField.setPromptText("0");
        increaseValueField.setPrefWidth(100);
        increaseValueField.setDisable(true);
        
        increaseValueBox.getChildren().addAll(increaseLabel, increaseValueField);
        
        // ربط الأحداث
        hasIncreaseCheckBox.selectedProperty().addListener((obs, oldVal, newVal) -> {
            increaseValueField.setDisable(!newVal);
            if (!newVal) {
                increaseValueField.clear();
            }
            updateIncreasedMeasurements();
        });

        increaseValueField.textProperty().addListener((obs, oldVal, newVal) -> {
            updateIncreasedMeasurements();
        });
        
        // منطقة عرض المقاسات مع الزيادة
        Label increasedMeasurementsLabel = new Label("المقاسات مع الزيادة:");
        increasedMeasurementsLabel.getStyleClass().add("field-label");
        
        increasedMeasurementsContainer = new VBox(10);
        increasedMeasurementsContainer.setPadding(new Insets(10));
        increasedMeasurementsContainer.setStyle("-fx-background-color: #f8f9fa; -fx-border-color: #dee2e6; -fx-border-width: 1; -fx-border-radius: 5;");

        // إنشاء كنترولر المقاسات مع الزيادة
        if (currentOrder == null) {
            currentOrder = new ManufacturingOrder();
        }

        // increasedMeasurementsTableController = new IncreasedMeasurementsTableController(currentOrder.getIncreasedMeasurements());

        Label placeholder = new Label("ستظهر المقاسات مع الزيادة هنا عند تفعيل الخاصية");
        placeholder.getStyleClass().add("placeholder-text");
        increasedMeasurementsContainer.getChildren().add(placeholder);
        
        container.getChildren().addAll(
            title, hasIncreaseCheckBox, increaseValueBox, 
            increasedMeasurementsLabel, increasedMeasurementsContainer
        );
        
        return container;
    }
    
    /**
     * إنشاء صندوق الأزرار
     */
    private HBox createButtonBox(Stage dialog) {
        HBox buttonBox = new HBox(15);
        buttonBox.setAlignment(Pos.CENTER_RIGHT);
        buttonBox.setPadding(new Insets(20, 0, 0, 0));
        
        Button saveButton = new Button(isEditMode ? "تحديث" : "حفظ");
        FontAwesomeIconView saveIcon = new FontAwesomeIconView(FontAwesomeIcon.SAVE);
        saveIcon.setSize("14px");
        saveButton.setGraphic(saveIcon);
        saveButton.getStyleClass().add("save-button");
        saveButton.setOnAction(e -> {
            if (validateAndSave()) {
                dialog.close();
            }
        });
        
        Button printButton = new Button("طباعة / PDF");
        FontAwesomeIconView printIcon = new FontAwesomeIconView(FontAwesomeIcon.PRINT);
        printIcon.setSize("14px");
        printButton.setGraphic(printIcon);
        printButton.getStyleClass().add("print-button");
        printButton.setOnAction(e -> printOrder());
        
        Button cancelButton = new Button("إلغاء");
        cancelButton.setOnAction(e -> {
            currentOrder = null;
            dialog.close();
        });
        
        buttonBox.getChildren().addAll(saveButton, printButton, cancelButton);
        return buttonBox;
    }
    
    /**
     * تحميل بيانات الأمر للتعديل
     */
    private void loadOrderData() {
        if (currentOrder != null) {
            customerNameField.setText(currentOrder.getCustomerName());
            invoiceNumberField.setText(currentOrder.getInvoiceNumber());
            orderNumberField.setText(currentOrder.getOrderNumber());
            orderDatePicker.setValue(currentOrder.getOrderDate());
            notesArea.setText(currentOrder.getNotes());
            
            // تحميل الخدمات المختارة
            glassCuttingCheckBox.setSelected(currentOrder.isGlassCuttingSelected());
            thermalGlassCheckBox.setSelected(currentOrder.isThermalGlassSelected());
            filmCheckBox.setSelected(currentOrder.isFilmSelected());
            bulletProofFilmCheckBox.setSelected(currentOrder.isBulletProofFilmSelected());
            doubleGlassCheckBox.setSelected(currentOrder.isDoubleGlassSelected());
            polishCheckBox.setSelected(currentOrder.isPolishSelected());
            holeCheckBox.setSelected(currentOrder.isHoleSelected());
            cncCheckBox.setSelected(currentOrder.isCncSelected());
            drawCheckBox.setSelected(currentOrder.isDrawSelected());
            otherServicesCheckBox.setSelected(currentOrder.isOtherServicesSelected());
            
            // تحميل خاصية الزيادة
            hasIncreaseCheckBox.setSelected(currentOrder.isHasIncrease());
            increaseValueField.setText(String.valueOf(currentOrder.getIncreaseValue()));
            
            // تحديث رؤية التبويبات
            updateTabVisibility();
        }
    }
    
    /**
     * التحقق من صحة البيانات والحفظ
     */
    private boolean validateAndSave() {
        // التحقق من الحقول المطلوبة
        if (customerNameField.getText().trim().isEmpty()) {
            showErrorAlert("اسم العميل مطلوب.");
            return false;
        }
        
        if (orderDatePicker.getValue() == null) {
            showErrorAlert("تاريخ الأمر مطلوب.");
            return false;
        }
        
        // حفظ البيانات في الأمر
        if (currentOrder == null) {
            currentOrder = new ManufacturingOrder();
        }
        
        currentOrder.setCustomerName(customerNameField.getText().trim());
        currentOrder.setInvoiceNumber(invoiceNumberField.getText().trim());
        currentOrder.setOrderNumber(orderNumberField.getText().trim());
        currentOrder.setOrderDate(orderDatePicker.getValue());
        currentOrder.setNotes(notesArea.getText().trim());
        
        // حفظ الخدمات المختارة
        currentOrder.setGlassCuttingSelected(glassCuttingCheckBox.isSelected());
        currentOrder.setThermalGlassSelected(thermalGlassCheckBox.isSelected());
        currentOrder.setFilmSelected(filmCheckBox.isSelected());
        currentOrder.setBulletProofFilmSelected(bulletProofFilmCheckBox.isSelected());
        currentOrder.setDoubleGlassSelected(doubleGlassCheckBox.isSelected());
        currentOrder.setPolishSelected(polishCheckBox.isSelected());
        currentOrder.setHoleSelected(holeCheckBox.isSelected());
        currentOrder.setCncSelected(cncCheckBox.isSelected());
        currentOrder.setDrawSelected(drawCheckBox.isSelected());
        currentOrder.setOtherServicesSelected(otherServicesCheckBox.isSelected());
        
        // حفظ خاصية الزيادة
        currentOrder.setHasIncrease(hasIncreaseCheckBox.isSelected());
        if (hasIncreaseCheckBox.isSelected() && !increaseValueField.getText().trim().isEmpty()) {
            try {
                double increaseValue = Double.parseDouble(increaseValueField.getText().trim());
                currentOrder.setIncreaseValue(increaseValue);
            } catch (NumberFormatException e) {
                showErrorAlert("قيمة الزيادة يجب أن تكون رقماً صحيحاً.");
                return false;
            }
        }
        
        // حفظ في الخدمة
        boolean success;
        if (isEditMode) {
            success = manufacturingService.updateManufacturingOrder(currentOrder);
        } else {
            success = manufacturingService.addManufacturingOrder(currentOrder);
        }
        
        if (!success) {
            showErrorAlert("فشل في حفظ أمر التصنيع.");
            return false;
        }
        
        return true;
    }
    
    /**
     * طباعة الأمر
     */
    private void printOrder() {
        showInfoAlert("ميزة الطباعة ستكون متاحة قريباً.");
    }
    
    /**
     * عرض رسالة خطأ
     */
    private void showErrorAlert(String message) {
        Alert alert = new Alert(Alert.AlertType.ERROR);
        alert.setTitle("خطأ");
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }
    
    /**
     * تحديث المقاسات مع الزيادة
     */
    private void updateIncreasedMeasurements() {
        if (currentOrder == null || increasedMeasurementsContainer == null) {
            return;
        }

        increasedMeasurementsContainer.getChildren().clear();

        if (hasIncreaseCheckBox.isSelected() && !increaseValueField.getText().trim().isEmpty()) {
            try {
                double increaseValue = Double.parseDouble(increaseValueField.getText().trim());
                if (increaseValue > 0) {
                    // تحديث المقاسات مع الزيادة
                    currentOrder.setIncreaseValue(increaseValue);
                    currentOrder.updateIncreasedMeasurements();

                    // عرض الجدول
                    // VBox increasedTable = increasedMeasurementsTableController.createIncreasedMeasurementsTable(increaseValue);
                    // increasedMeasurementsContainer.getChildren().add(increasedTable);

                    Label tempLabel = new Label("جدول المقاسات مع الزيادة قيد التطوير");
                    increasedMeasurementsContainer.getChildren().add(tempLabel);

                    return;
                }
            } catch (NumberFormatException e) {
                // قيمة غير صحيحة
            }
        }

        // عرض الرسالة الافتراضية
        Label placeholder = new Label("ستظهر المقاسات مع الزيادة هنا عند تفعيل الخاصية وإدخال قيمة صحيحة");
        placeholder.getStyleClass().add("placeholder-text");
        increasedMeasurementsContainer.getChildren().add(placeholder);
    }

    /**
     * عرض رسالة معلومات
     */
    private void showInfoAlert(String message) {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle("معلومات");
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }
}
