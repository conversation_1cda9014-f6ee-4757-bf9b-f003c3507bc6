package com.accounting.controller;

import com.accounting.model.*;
import com.accounting.service.ReceiptOrderService;
import com.accounting.service.ManufacturingService;
import javafx.geometry.Insets;
import javafx.geometry.Pos;
import javafx.scene.control.*;
import javafx.scene.control.cell.PropertyValueFactory;
import javafx.scene.control.cell.TextFieldTableCell;
import javafx.scene.layout.*;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.util.converter.DoubleStringConverter;
import javafx.util.converter.IntegerStringConverter;
import de.jensd.fx.glyphs.fontawesome.FontAwesomeIcon;
import de.jensd.fx.glyphs.fontawesome.FontAwesomeIconView;
import java.time.LocalDate;
import java.text.DecimalFormat;
import java.util.function.Consumer;

/**
 * كنترولر نموذج أمر الاستلام
 */
public class ReceiptOrderFormController {
    
    private final ReceiptOrderService receiptOrderService;
    private final ManufacturingService manufacturingService;
    private final DecimalFormat decimalFormat = new DecimalFormat("#,##0.00");
    
    // مكونات النموذج
    private TextField supplierNameField;
    private DatePicker receiptDatePicker;
    private TextField manufacturingOrderNumberField;
    private TextField invoiceNumberField;
    private TextArea notesArea;
    private ComboBox<ReceiptOrder.ReceiptStatus> statusComboBox;
    
    // جدول عناصر الاستلام
    private TableView<ReceiptItem> receiptItemsTable;
    private ObservableList<ReceiptItem> receiptItems;
    
    // الأمر الحالي
    private ReceiptOrder currentOrder;
    
    public ReceiptOrderFormController(ReceiptOrderService receiptOrderService) {
        this.receiptOrderService = receiptOrderService;
        this.manufacturingService = ManufacturingService.getInstance();
    }
    
    /**
     * إنشاء صفحة نموذج أمر الاستلام
     */
    public VBox createReceiptOrderFormPage(ReceiptOrder order, Consumer<ReceiptOrder> onSave, Consumer<ReceiptOrder> onPrint) {
        currentOrder = order != null ? order : new ReceiptOrder();
        receiptItems = FXCollections.observableArrayList(currentOrder.getReceiptItems());
        
        VBox container = new VBox(20);
        container.setPadding(new Insets(20));
        
        // العنوان
        Label titleLabel = new Label(order == null ? "📝 أمر استلام جديد" : "📝 تعديل أمر الاستلام");
        titleLabel.getStyleClass().add("section-title");
        
        // معلومات الأمر الأساسية
        VBox basicInfoBox = createBasicInfoSection();
        
        // جدول عناصر الاستلام
        VBox itemsTableBox = createReceiptItemsSection();
        
        // أزرار الإجراءات
        HBox buttonsBox = createButtonsSection(onSave, onPrint);
        
        container.getChildren().addAll(titleLabel, basicInfoBox, itemsTableBox, buttonsBox);
        return container;
    }
    
    /**
     * إنشاء قسم المعلومات الأساسية
     */
    private VBox createBasicInfoSection() {
        VBox section = new VBox(15);
        section.setPadding(new Insets(15));
        section.getStyleClass().add("form-section");
        
        Label sectionTitle = new Label("📋 معلومات أمر الاستلام");
        sectionTitle.getStyleClass().add("section-subtitle");
        
        GridPane grid = new GridPane();
        grid.setHgap(15);
        grid.setVgap(15);
        grid.setPadding(new Insets(10));
        
        // الصف الأول
        Label supplierLabel = new Label("اسم المورد:");
        supplierLabel.getStyleClass().add("field-label");
        supplierNameField = new TextField();
        supplierNameField.setPromptText("أدخل اسم المورد");
        supplierNameField.setPrefWidth(200);
        
        Label dateLabel = new Label("تاريخ الاستلام:");
        dateLabel.getStyleClass().add("field-label");
        receiptDatePicker = new DatePicker();
        receiptDatePicker.setValue(LocalDate.now());
        receiptDatePicker.setPrefWidth(150);
        
        // الصف الثاني
        Label manufacturingOrderLabel = new Label("رقم أمر العمل:");
        manufacturingOrderLabel.getStyleClass().add("field-label");
        manufacturingOrderNumberField = new TextField();
        manufacturingOrderNumberField.setPromptText("رقم أمر العمل");
        manufacturingOrderNumberField.setPrefWidth(200);
        
        Label invoiceLabel = new Label("رقم الفاتورة:");
        invoiceLabel.getStyleClass().add("field-label");
        invoiceNumberField = new TextField();
        invoiceNumberField.setPromptText("رقم الفاتورة");
        invoiceNumberField.setPrefWidth(200);
        
        // الصف الثالث
        Label statusLabel = new Label("الحالة:");
        statusLabel.getStyleClass().add("field-label");
        statusComboBox = new ComboBox<>();
        statusComboBox.getItems().addAll(ReceiptOrder.ReceiptStatus.values());
        statusComboBox.setValue(ReceiptOrder.ReceiptStatus.DRAFT);
        statusComboBox.setPrefWidth(150);
        
        // ترتيب العناصر في الشبكة
        grid.add(supplierLabel, 0, 0);
        grid.add(supplierNameField, 1, 0);
        grid.add(dateLabel, 2, 0);
        grid.add(receiptDatePicker, 3, 0);
        
        grid.add(manufacturingOrderLabel, 0, 1);
        grid.add(manufacturingOrderNumberField, 1, 1);
        grid.add(invoiceLabel, 2, 1);
        grid.add(invoiceNumberField, 3, 1);
        
        grid.add(statusLabel, 0, 2);
        grid.add(statusComboBox, 1, 2);
        
        // الملاحظات
        Label notesLabel = new Label("ملاحظات:");
        notesLabel.getStyleClass().add("field-label");
        notesArea = new TextArea();
        notesArea.setPromptText("ملاحظات إضافية...");
        notesArea.setPrefRowCount(3);
        notesArea.setWrapText(true);
        
        VBox notesBox = new VBox(5);
        notesBox.getChildren().addAll(notesLabel, notesArea);
        
        section.getChildren().addAll(sectionTitle, grid, notesBox);
        
        // تحميل البيانات إذا كان الأمر موجود
        if (currentOrder != null) {
            loadOrderData();
        }
        
        return section;
    }
    
    /**
     * إنشاء قسم جدول عناصر الاستلام
     */
    private VBox createReceiptItemsSection() {
        VBox section = new VBox(15);
        section.setPadding(new Insets(15));
        section.getStyleClass().add("form-section");
        
        Label sectionTitle = new Label("📦 عناصر الاستلام");
        sectionTitle.getStyleClass().add("section-subtitle");
        
        // شريط أدوات الجدول
        HBox toolbar = createTableToolbar();
        
        // الجدول
        receiptItemsTable = createReceiptItemsTable();
        
        // وضع الجدول في ScrollPane
        ScrollPane scrollPane = new ScrollPane(receiptItemsTable);
        scrollPane.setFitToWidth(true);
        scrollPane.setVbarPolicy(ScrollPane.ScrollBarPolicy.AS_NEEDED);
        scrollPane.setHbarPolicy(ScrollPane.ScrollBarPolicy.NEVER);
        scrollPane.setPrefHeight(300);
        VBox.setVgrow(scrollPane, Priority.ALWAYS);
        
        // الإجماليات
        HBox totalsBox = createTotalsBox();
        
        section.getChildren().addAll(sectionTitle, toolbar, scrollPane, totalsBox);
        return section;
    }
    
    /**
     * إنشاء شريط أدوات الجدول
     */
    private HBox createTableToolbar() {
        HBox toolbar = new HBox(10);
        toolbar.setAlignment(Pos.CENTER_LEFT);
        toolbar.setPadding(new Insets(10));
        toolbar.getStyleClass().add("toolbar");
        
        Button addItemBtn = new Button("إضافة عنصر");
        FontAwesomeIconView addIcon = new FontAwesomeIconView(FontAwesomeIcon.PLUS);
        addIcon.setSize("12px");
        addItemBtn.setGraphic(addIcon);
        addItemBtn.getStyleClass().add("add-button");
        addItemBtn.setOnAction(e -> addNewItem());
        
        Button deleteItemBtn = new Button("حذف عنصر");
        FontAwesomeIconView deleteIcon = new FontAwesomeIconView(FontAwesomeIcon.MINUS);
        deleteIcon.setSize("12px");
        deleteItemBtn.setGraphic(deleteIcon);
        deleteItemBtn.getStyleClass().add("delete-button");
        deleteItemBtn.setOnAction(e -> deleteSelectedItem());
        
        Button clearAllBtn = new Button("مسح الكل");
        FontAwesomeIconView clearIcon = new FontAwesomeIconView(FontAwesomeIcon.TRASH);
        clearIcon.setSize("12px");
        clearAllBtn.setGraphic(clearIcon);
        clearAllBtn.getStyleClass().add("unpost-button");
        clearAllBtn.setOnAction(e -> clearAllItems());
        
        toolbar.getChildren().addAll(addItemBtn, deleteItemBtn, clearAllBtn);
        return toolbar;
    }
    
    /**
     * إنشاء جدول عناصر الاستلام
     */
    private TableView<ReceiptItem> createReceiptItemsTable() {
        TableView<ReceiptItem> table = new TableView<>();
        table.setEditable(true);
        table.getStyleClass().add("receipt-items-table");
        
        // عمود الترقيم
        TableColumn<ReceiptItem, Integer> indexCol = new TableColumn<>("ت");
        indexCol.setCellValueFactory(cellData -> {
            int index = table.getItems().indexOf(cellData.getValue()) + 1;
            return new javafx.beans.property.SimpleIntegerProperty(index).asObject();
        });
        indexCol.setPrefWidth(40);
        indexCol.setSortable(false);
        
        // عمود التفاصيل
        TableColumn<ReceiptItem, String> descCol = new TableColumn<>("التفاصيل");
        descCol.setCellValueFactory(new PropertyValueFactory<>("description"));
        descCol.setCellFactory(TextFieldTableCell.forTableColumn());
        descCol.setOnEditCommit(event -> {
            event.getRowValue().setDescription(event.getNewValue());
            updateTotals();
        });
        descCol.setPrefWidth(200);
        
        // عمود الكمية المطلوبة
        TableColumn<ReceiptItem, Integer> requiredQtyCol = new TableColumn<>("الكمية المطلوبة");
        requiredQtyCol.setCellValueFactory(new PropertyValueFactory<>("requiredQuantity"));
        requiredQtyCol.setCellFactory(TextFieldTableCell.forTableColumn(new IntegerStringConverter()));
        requiredQtyCol.setOnEditCommit(event -> {
            event.getRowValue().setRequiredQuantity(event.getNewValue());
            updateTotals();
        });
        requiredQtyCol.setPrefWidth(120);
        
        // عمود الكمية المستلمة
        TableColumn<ReceiptItem, Integer> receivedQtyCol = new TableColumn<>("الكمية المستلمة");
        receivedQtyCol.setCellValueFactory(new PropertyValueFactory<>("receivedQuantity"));
        receivedQtyCol.setCellFactory(TextFieldTableCell.forTableColumn(new IntegerStringConverter()));
        receivedQtyCol.setOnEditCommit(event -> {
            event.getRowValue().setReceivedQuantity(event.getNewValue());
            updateTotals();
        });
        receivedQtyCol.setPrefWidth(120);
        
        // عمود الوحدة
        TableColumn<ReceiptItem, String> unitCol = new TableColumn<>("الوحدة");
        unitCol.setCellValueFactory(new PropertyValueFactory<>("unit"));
        unitCol.setCellFactory(TextFieldTableCell.forTableColumn());
        unitCol.setOnEditCommit(event -> {
            event.getRowValue().setUnit(event.getNewValue());
        });
        unitCol.setPrefWidth(80);
        
        // عمود الملاحظة
        TableColumn<ReceiptItem, String> notesCol = new TableColumn<>("ملاحظة");
        notesCol.setCellValueFactory(new PropertyValueFactory<>("notes"));
        notesCol.setCellFactory(TextFieldTableCell.forTableColumn());
        notesCol.setOnEditCommit(event -> {
            event.getRowValue().setNotes(event.getNewValue());
        });
        notesCol.setPrefWidth(150);
        
        // عمود حالة الاستلام
        TableColumn<ReceiptItem, String> statusCol = new TableColumn<>("حالة الاستلام");
        statusCol.setCellValueFactory(cellData -> 
            cellData.getValue().receiptStatusProperty().asString());
        statusCol.setPrefWidth(100);
        
        // إضافة الأعمدة
        table.getColumns().addAll(indexCol, descCol, requiredQtyCol, receivedQtyCol, unitCol, notesCol, statusCol);
        
        // ربط البيانات
        table.setItems(receiptItems);
        
        // إضافة مستمع للتحديث التلقائي
        receiptItems.addListener((javafx.collections.ListChangeListener<ReceiptItem>) change -> {
            table.refresh();
            updateTotals();
        });
        
        return table;
    }
    
    /**
     * إنشاء صندوق الإجماليات
     */
    private HBox createTotalsBox() {
        HBox totalsBox = new HBox(30);
        totalsBox.setAlignment(Pos.CENTER);
        totalsBox.setPadding(new Insets(15));
        totalsBox.getStyleClass().add("totals-container");
        
        // إجمالي العناصر
        VBox totalItemsBox = new VBox(5);
        totalItemsBox.setAlignment(Pos.CENTER);
        Label totalItemsTitleLabel = new Label("إجمالي العناصر");
        totalItemsTitleLabel.getStyleClass().add("totals-title");
        Label totalItemsLabel = new Label("0");
        totalItemsLabel.getStyleClass().add("totals-value");
        totalItemsBox.getChildren().addAll(totalItemsTitleLabel, totalItemsLabel);
        
        // إجمالي الكمية المطلوبة
        VBox totalRequiredBox = new VBox(5);
        totalRequiredBox.setAlignment(Pos.CENTER);
        Label totalRequiredTitleLabel = new Label("إجمالي المطلوب");
        totalRequiredTitleLabel.getStyleClass().add("totals-title");
        Label totalRequiredLabel = new Label("0");
        totalRequiredLabel.getStyleClass().add("totals-value");
        totalRequiredBox.getChildren().addAll(totalRequiredTitleLabel, totalRequiredLabel);
        
        // إجمالي الكمية المستلمة
        VBox totalReceivedBox = new VBox(5);
        totalReceivedBox.setAlignment(Pos.CENTER);
        Label totalReceivedTitleLabel = new Label("إجمالي المستلم");
        totalReceivedTitleLabel.getStyleClass().add("totals-title");
        Label totalReceivedLabel = new Label("0");
        totalReceivedLabel.getStyleClass().add("totals-value");
        totalReceivedBox.getChildren().addAll(totalReceivedTitleLabel, totalReceivedLabel);
        
        totalsBox.getChildren().addAll(totalItemsBox, totalRequiredBox, totalReceivedBox);
        return totalsBox;
    }

    /**
     * إنشاء قسم الأزرار
     */
    private HBox createButtonsSection(Consumer<ReceiptOrder> onSave, Consumer<ReceiptOrder> onPrint) {
        HBox buttonsBox = new HBox(15);
        buttonsBox.setAlignment(Pos.CENTER);
        buttonsBox.setPadding(new Insets(20, 0, 0, 0));

        Button saveBtn = new Button("حفظ");
        FontAwesomeIconView saveIcon = new FontAwesomeIconView(FontAwesomeIcon.SAVE);
        saveIcon.setSize("12px");
        saveBtn.setGraphic(saveIcon);
        saveBtn.getStyleClass().add("save-button");
        saveBtn.setOnAction(e -> saveOrder(onSave));

        Button printBtn = new Button("طباعة");
        FontAwesomeIconView printIcon = new FontAwesomeIconView(FontAwesomeIcon.PRINT);
        printIcon.setSize("12px");
        printBtn.setGraphic(printIcon);
        printBtn.getStyleClass().add("print-button");
        printBtn.setOnAction(e -> {
            if (saveOrder(onSave)) {
                onPrint.accept(currentOrder);
            }
        });

        Button saveAndPrintBtn = new Button("حفظ وطباعة");
        FontAwesomeIconView saveAndPrintIcon = new FontAwesomeIconView(FontAwesomeIcon.PRINT);
        saveAndPrintIcon.setSize("12px");
        saveAndPrintBtn.setGraphic(saveAndPrintIcon);
        saveAndPrintBtn.getStyleClass().add("save-print-button");
        saveAndPrintBtn.setOnAction(e -> {
            if (saveOrder(onSave)) {
                onPrint.accept(currentOrder);
            }
        });

        buttonsBox.getChildren().addAll(saveBtn, printBtn, saveAndPrintBtn);
        return buttonsBox;
    }

    /**
     * تحميل بيانات الأمر
     */
    private void loadOrderData() {
        supplierNameField.setText(currentOrder.getSupplierName());
        receiptDatePicker.setValue(currentOrder.getReceiptDate());
        manufacturingOrderNumberField.setText(currentOrder.getManufacturingOrderNumber());
        invoiceNumberField.setText(currentOrder.getInvoiceNumber());
        statusComboBox.setValue(currentOrder.getStatus());
        notesArea.setText(currentOrder.getNotes());
    }

    /**
     * إضافة عنصر جديد
     */
    private void addNewItem() {
        ReceiptItem newItem = new ReceiptItem();
        newItem.setDescription("عنصر جديد");
        newItem.setRequiredQuantity(1);
        newItem.setReceivedQuantity(0);
        newItem.setUnit("قطعة");
        receiptItems.add(newItem);

        // تحديد الصف الجديد
        receiptItemsTable.getSelectionModel().selectLast();
        receiptItemsTable.scrollTo(receiptItems.size() - 1);
    }

    /**
     * حذف العنصر المحدد
     */
    private void deleteSelectedItem() {
        ReceiptItem selectedItem = receiptItemsTable.getSelectionModel().getSelectedItem();
        if (selectedItem != null) {
            Alert confirmAlert = new Alert(Alert.AlertType.CONFIRMATION);
            confirmAlert.setTitle("تأكيد الحذف");
            confirmAlert.setHeaderText("حذف عنصر");
            confirmAlert.setContentText("هل أنت متأكد من حذف هذا العنصر؟");

            confirmAlert.showAndWait().ifPresent(response -> {
                if (response == ButtonType.OK) {
                    receiptItems.remove(selectedItem);
                }
            });
        } else {
            showWarningAlert("يرجى اختيار عنصر للحذف.");
        }
    }

    /**
     * مسح جميع العناصر
     */
    private void clearAllItems() {
        if (!receiptItems.isEmpty()) {
            Alert confirmAlert = new Alert(Alert.AlertType.CONFIRMATION);
            confirmAlert.setTitle("تأكيد المسح");
            confirmAlert.setHeaderText("مسح جميع العناصر");
            confirmAlert.setContentText("هل أنت متأكد من مسح جميع العناصر؟");

            confirmAlert.showAndWait().ifPresent(response -> {
                if (response == ButtonType.OK) {
                    receiptItems.clear();
                }
            });
        }
    }

    /**
     * تحديث الإجماليات
     */
    private void updateTotals() {
        // سيتم تحديث الإجماليات هنا
    }

    /**
     * حفظ الأمر
     */
    private boolean saveOrder(Consumer<ReceiptOrder> onSave) {
        if (!validateForm()) {
            return false;
        }

        // تحديث بيانات الأمر
        currentOrder.setSupplierName(supplierNameField.getText().trim());
        currentOrder.setReceiptDate(receiptDatePicker.getValue());
        currentOrder.setManufacturingOrderNumber(manufacturingOrderNumberField.getText().trim());
        currentOrder.setInvoiceNumber(invoiceNumberField.getText().trim());
        currentOrder.setStatus(statusComboBox.getValue());
        currentOrder.setNotes(notesArea.getText().trim());

        // تحديث عناصر الاستلام
        currentOrder.clearReceiptItems();
        for (ReceiptItem item : receiptItems) {
            currentOrder.addReceiptItem(item);
        }

        // حفظ في الخدمة
        boolean success;
        if (receiptOrderService.findReceiptOrderById(currentOrder.getReceiptOrderId()) != null) {
            success = receiptOrderService.updateReceiptOrder(currentOrder);
        } else {
            success = receiptOrderService.addReceiptOrder(currentOrder);
        }

        if (success) {
            onSave.accept(currentOrder);
            return true;
        } else {
            showErrorAlert("فشل في حفظ أمر الاستلام.");
            return false;
        }
    }

    /**
     * التحقق من صحة النموذج
     */
    private boolean validateForm() {
        if (supplierNameField.getText().trim().isEmpty()) {
            showErrorAlert("اسم المورد مطلوب.");
            return false;
        }

        if (receiptDatePicker.getValue() == null) {
            showErrorAlert("تاريخ الاستلام مطلوب.");
            return false;
        }

        return true;
    }

    /**
     * عرض رسالة خطأ
     */
    private void showErrorAlert(String message) {
        Alert alert = new Alert(Alert.AlertType.ERROR);
        alert.setTitle("خطأ");
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }

    /**
     * عرض رسالة تحذير
     */
    private void showWarningAlert(String message) {
        Alert alert = new Alert(Alert.AlertType.WARNING);
        alert.setTitle("تحذير");
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }
}
