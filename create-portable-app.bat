@echo off
setlocal enabledelayedexpansion

echo.
echo ========================================
echo Creating Portable Accounting System
echo ========================================
echo.

:: Set Java environment
set JAVA_HOME=C:\Program Files\Eclipse Adoptium\jdk-********-hotspot
set PATH=%JAVA_HOME%\bin;%PATH%

echo Checking requirements...
echo.

:: Check Java
java -version >nul 2>&1
if not %errorlevel%==0 (
    echo ERROR: Java not found
    echo Please install Java 17 or newer
    pause
    exit /b 1
)

echo Java is available
java -version

:: Check Maven
call mvnw.cmd -version >nul 2>&1
if not %errorlevel%==0 (
    echo ERROR: Maven not available
    pause
    exit /b 1
)

echo Maven is available

echo.
echo Building portable application...

:: Clean and build
call mvnw.cmd clean package
if not %errorlevel%==0 (
    echo ERROR: Failed to build project
    pause
    exit /b 1
)

:: Create portable app directory
set PORTABLE_DIR=AccountingSystem-Portable
if exist "%PORTABLE_DIR%" rmdir /s /q "%PORTABLE_DIR%"
mkdir "%PORTABLE_DIR%"

:: Copy JAR file
copy "target\accounting-system-1.0.0.jar" "%PORTABLE_DIR%\"
if not %errorlevel%==0 (
    echo ERROR: Failed to copy JAR file
    pause
    exit /b 1
)

:: Create launcher script
echo @echo off > "%PORTABLE_DIR%\AccountingSystem.bat"
echo setlocal >> "%PORTABLE_DIR%\AccountingSystem.bat"
echo. >> "%PORTABLE_DIR%\AccountingSystem.bat"
echo echo ======================================== >> "%PORTABLE_DIR%\AccountingSystem.bat"
echo echo Starting Accounting System >> "%PORTABLE_DIR%\AccountingSystem.bat"
echo echo ======================================== >> "%PORTABLE_DIR%\AccountingSystem.bat"
echo echo. >> "%PORTABLE_DIR%\AccountingSystem.bat"
echo. >> "%PORTABLE_DIR%\AccountingSystem.bat"
echo :: Check Java >> "%PORTABLE_DIR%\AccountingSystem.bat"
echo java -version ^>nul 2^>^&1 >> "%PORTABLE_DIR%\AccountingSystem.bat"
echo if not %%errorlevel%%==0 ^( >> "%PORTABLE_DIR%\AccountingSystem.bat"
echo     echo ERROR: Java not found >> "%PORTABLE_DIR%\AccountingSystem.bat"
echo     echo Please install Java 17 or newer from: >> "%PORTABLE_DIR%\AccountingSystem.bat"
echo     echo https://adoptium.net/ >> "%PORTABLE_DIR%\AccountingSystem.bat"
echo     echo. >> "%PORTABLE_DIR%\AccountingSystem.bat"
echo     pause >> "%PORTABLE_DIR%\AccountingSystem.bat"
echo     exit /b 1 >> "%PORTABLE_DIR%\AccountingSystem.bat"
echo ^) >> "%PORTABLE_DIR%\AccountingSystem.bat"
echo. >> "%PORTABLE_DIR%\AccountingSystem.bat"
echo echo Starting application... >> "%PORTABLE_DIR%\AccountingSystem.bat"
echo echo. >> "%PORTABLE_DIR%\AccountingSystem.bat"
echo. >> "%PORTABLE_DIR%\AccountingSystem.bat"
echo :: Start the application >> "%PORTABLE_DIR%\AccountingSystem.bat"
echo java -Dfile.encoding=UTF-8 -Djava.awt.headless=false --add-opens javafx.graphics/javafx.scene=ALL-UNNAMED --add-opens javafx.controls/javafx.scene.control=ALL-UNNAMED --add-opens javafx.base/javafx.beans.property=ALL-UNNAMED -jar accounting-system-1.0.0.jar >> "%PORTABLE_DIR%\AccountingSystem.bat"
echo. >> "%PORTABLE_DIR%\AccountingSystem.bat"
echo if not %%errorlevel%%==0 ^( >> "%PORTABLE_DIR%\AccountingSystem.bat"
echo     echo. >> "%PORTABLE_DIR%\AccountingSystem.bat"
echo     echo ERROR: Failed to start application >> "%PORTABLE_DIR%\AccountingSystem.bat"
echo     echo. >> "%PORTABLE_DIR%\AccountingSystem.bat"
echo     echo Troubleshooting: >> "%PORTABLE_DIR%\AccountingSystem.bat"
echo     echo 1. Make sure Java 17+ is installed >> "%PORTABLE_DIR%\AccountingSystem.bat"
echo     echo 2. Check if antivirus is blocking the application >> "%PORTABLE_DIR%\AccountingSystem.bat"
echo     echo 3. Try running as administrator >> "%PORTABLE_DIR%\AccountingSystem.bat"
echo     echo. >> "%PORTABLE_DIR%\AccountingSystem.bat"
echo     pause >> "%PORTABLE_DIR%\AccountingSystem.bat"
echo     exit /b 1 >> "%PORTABLE_DIR%\AccountingSystem.bat"
echo ^) >> "%PORTABLE_DIR%\AccountingSystem.bat"
echo. >> "%PORTABLE_DIR%\AccountingSystem.bat"
echo echo. >> "%PORTABLE_DIR%\AccountingSystem.bat"
echo echo Application closed successfully >> "%PORTABLE_DIR%\AccountingSystem.bat"
echo pause >> "%PORTABLE_DIR%\AccountingSystem.bat"

:: Copy documentation
copy "README.md" "%PORTABLE_DIR%\" 2>nul
copy "دليل-المستخدم.md" "%PORTABLE_DIR%\" 2>nul
copy "README-DEVELOPER.md" "%PORTABLE_DIR%\" 2>nul
copy "تعليمات-التثبيت.md" "%PORTABLE_DIR%\" 2>nul

:: Create README for portable version
echo # نظام المحاسبة المتكامل - النسخة المحمولة > "%PORTABLE_DIR%\اقرأني.txt"
echo ## Integrated Accounting System - Portable Version >> "%PORTABLE_DIR%\اقرأني.txt"
echo. >> "%PORTABLE_DIR%\اقرأني.txt"
echo هذه النسخة المحمولة من نظام المحاسبة المتكامل. >> "%PORTABLE_DIR%\اقرأني.txt"
echo. >> "%PORTABLE_DIR%\اقرأني.txt"
echo ### المتطلبات: >> "%PORTABLE_DIR%\اقرأني.txt"
echo - Java 17 أو أحدث مثبت على النظام >> "%PORTABLE_DIR%\اقرأني.txt"
echo - Windows 10/11 (64-bit) >> "%PORTABLE_DIR%\اقرأني.txt"
echo - 4 GB RAM كحد أدنى >> "%PORTABLE_DIR%\اقرأني.txt"
echo. >> "%PORTABLE_DIR%\اقرأني.txt"
echo ### طريقة التشغيل: >> "%PORTABLE_DIR%\اقرأني.txt"
echo 1. تأكد من تثبيت Java 17 أو أحدث >> "%PORTABLE_DIR%\اقرأني.txt"
echo 2. انقر نقراً مزدوجاً على AccountingSystem.bat >> "%PORTABLE_DIR%\اقرأني.txt"
echo 3. انتظر حتى يبدأ البرنامج >> "%PORTABLE_DIR%\اقرأني.txt"
echo. >> "%PORTABLE_DIR%\اقرأني.txt"
echo ### تحميل Java: >> "%PORTABLE_DIR%\اقرأني.txt"
echo إذا لم يكن Java مثبتاً، يمكنك تحميله من: >> "%PORTABLE_DIR%\اقرأني.txt"
echo https://adoptium.net/temurin/releases/ >> "%PORTABLE_DIR%\اقرأني.txt"
echo اختر: OpenJDK 17 LTS أو 21 LTS >> "%PORTABLE_DIR%\اقرأني.txt"
echo النوع: JRE (Java Runtime Environment) >> "%PORTABLE_DIR%\اقرأني.txt"
echo النظام: Windows x64 >> "%PORTABLE_DIR%\اقرأني.txt"
echo. >> "%PORTABLE_DIR%\اقرأني.txt"
echo ### الدعم التقني: >> "%PORTABLE_DIR%\اقرأني.txt"
echo البريد الإلكتروني: <EMAIL> >> "%PORTABLE_DIR%\اقرأني.txt"
echo الهاتف: 123-456-7890 >> "%PORTABLE_DIR%\اقرأني.txt"
echo. >> "%PORTABLE_DIR%\اقرأني.txt"
echo © 2025 شركة الزجاج والألومنيوم المتقدمة >> "%PORTABLE_DIR%\اقرأني.txt"

:: Create ZIP package (if 7zip is available)
where 7z >nul 2>&1
if %errorlevel%==0 (
    echo Creating ZIP package...
    7z a -tzip "AccountingSystem-Portable-v1.0.0.zip" "%PORTABLE_DIR%\*"
    if %errorlevel%==0 (
        echo ZIP package created: AccountingSystem-Portable-v1.0.0.zip
    )
) else (
    echo 7-Zip not found. ZIP package not created.
    echo You can manually compress the %PORTABLE_DIR% folder.
)

echo.
echo ========================================
echo SUCCESS: Portable application created!
echo ========================================
echo.
echo Location: %PORTABLE_DIR%\
echo.
echo Files created:
echo   - AccountingSystem.bat (launcher)
echo   - accounting-system-1.0.0.jar (application)
echo   - اقرأني.txt (instructions in Arabic)
echo   - Documentation files
echo.
echo To run the application:
echo   1. Double-click AccountingSystem.bat
echo   2. Or run: java -jar accounting-system-1.0.0.jar
echo.
echo To distribute:
echo   1. Copy the entire %PORTABLE_DIR% folder
echo   2. Or use the ZIP file if created
echo   3. Recipients need Java 17+ installed
echo.
echo File size: ~40 MB (JAR only)
echo Requirements: Java 17+ on target system
echo.
echo Technical support: <EMAIL>
echo.

pause
