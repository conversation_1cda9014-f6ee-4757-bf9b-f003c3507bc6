@echo off
setlocal

echo.
echo ========================================
echo Creating Bundled Distribution with Java
echo ========================================
echo.

:: Set Java environment
set JAVA_HOME=C:\Program Files\Eclipse Adoptium\jdk-********-hotspot
set PATH=%JAVA_HOME%\bin;%PATH%

echo Step 1: Building application...
call mvnw.cmd clean package -q
if not %errorlevel%==0 (
    echo ERROR: Build failed
    pause
    exit /b 1
)

echo ✅ Application built successfully

echo.
echo Step 2: Creating distribution folder...

:: Create distribution folder
set DIST_NAME=AccountingSystem-WithJava
if exist "%DIST_NAME%" rmdir /s /q "%DIST_NAME%"
mkdir "%DIST_NAME%"

:: Copy application JAR
echo Copying application...
copy "target\accounting-system-1.0.0.jar" "%DIST_NAME%\"

:: Create minimal Java runtime using jlink
echo Creating minimal Java runtime...
if exist "%DIST_NAME%\java-runtime" rmdir /s /q "%DIST_NAME%\java-runtime"

jlink --module-path "%JAVA_HOME%\jmods" --add-modules java.base,java.desktop,java.logging,java.management,java.naming,java.prefs,java.xml --output "%DIST_NAME%\java-runtime" --compress=2 --no-header-files --no-man-pages

if not %errorlevel%==0 (
    echo Warning: jlink failed, copying full JRE instead...
    
    :: Fallback: copy essential JRE files
    mkdir "%DIST_NAME%\java-runtime"
    mkdir "%DIST_NAME%\java-runtime\bin"
    mkdir "%DIST_NAME%\java-runtime\lib"
    
    :: Copy essential Java files
    copy "%JAVA_HOME%\bin\java.exe" "%DIST_NAME%\java-runtime\bin\"
    copy "%JAVA_HOME%\bin\javaw.exe" "%DIST_NAME%\java-runtime\bin\"
    xcopy "%JAVA_HOME%\lib" "%DIST_NAME%\java-runtime\lib\" /E /I /Y /Q
    
    echo ✅ JRE files copied
) else (
    echo ✅ Minimal Java runtime created
)

:: Create launcher script
echo Creating launcher...
echo @echo off > "%DIST_NAME%\AccountingSystem.bat"
echo setlocal >> "%DIST_NAME%\AccountingSystem.bat"
echo. >> "%DIST_NAME%\AccountingSystem.bat"
echo echo ======================================== >> "%DIST_NAME%\AccountingSystem.bat"
echo echo Starting Accounting System >> "%DIST_NAME%\AccountingSystem.bat"
echo echo ======================================== >> "%DIST_NAME%\AccountingSystem.bat"
echo echo. >> "%DIST_NAME%\AccountingSystem.bat"
echo. >> "%DIST_NAME%\AccountingSystem.bat"
echo :: Set Java path >> "%DIST_NAME%\AccountingSystem.bat"
echo set JAVA_HOME=%%~dp0java-runtime >> "%DIST_NAME%\AccountingSystem.bat"
echo set PATH=%%JAVA_HOME%%\bin;%%PATH%% >> "%DIST_NAME%\AccountingSystem.bat"
echo. >> "%DIST_NAME%\AccountingSystem.bat"
echo :: Check if Java runtime exists >> "%DIST_NAME%\AccountingSystem.bat"
echo if not exist "%%JAVA_HOME%%\bin\java.exe" ^( >> "%DIST_NAME%\AccountingSystem.bat"
echo     echo ERROR: Java runtime not found >> "%DIST_NAME%\AccountingSystem.bat"
echo     echo Please ensure the java-runtime folder is present >> "%DIST_NAME%\AccountingSystem.bat"
echo     pause >> "%DIST_NAME%\AccountingSystem.bat"
echo     exit /b 1 >> "%DIST_NAME%\AccountingSystem.bat"
echo ^) >> "%DIST_NAME%\AccountingSystem.bat"
echo. >> "%DIST_NAME%\AccountingSystem.bat"
echo echo Starting application with bundled Java... >> "%DIST_NAME%\AccountingSystem.bat"
echo echo. >> "%DIST_NAME%\AccountingSystem.bat"
echo. >> "%DIST_NAME%\AccountingSystem.bat"
echo :: Start the application >> "%DIST_NAME%\AccountingSystem.bat"
echo "%%JAVA_HOME%%\bin\java.exe" -Dfile.encoding=UTF-8 -Djava.awt.headless=false --add-opens javafx.graphics/javafx.scene=ALL-UNNAMED --add-opens javafx.controls/javafx.scene.control=ALL-UNNAMED --add-opens javafx.base/javafx.beans.property=ALL-UNNAMED -jar accounting-system-1.0.0.jar >> "%DIST_NAME%\AccountingSystem.bat"
echo. >> "%DIST_NAME%\AccountingSystem.bat"
echo if not %%errorlevel%%==0 ^( >> "%DIST_NAME%\AccountingSystem.bat"
echo     echo. >> "%DIST_NAME%\AccountingSystem.bat"
echo     echo ERROR: Failed to start application >> "%DIST_NAME%\AccountingSystem.bat"
echo     echo. >> "%DIST_NAME%\AccountingSystem.bat"
echo     pause >> "%DIST_NAME%\AccountingSystem.bat"
echo ^) >> "%DIST_NAME%\AccountingSystem.bat"

:: Create silent launcher (no console window)
echo Creating silent launcher...
echo Set WshShell = CreateObject("WScript.Shell") > "%DIST_NAME%\AccountingSystem-Silent.vbs"
echo WshShell.Run chr(34) ^& "AccountingSystem.bat" ^& Chr(34), 0 >> "%DIST_NAME%\AccountingSystem-Silent.vbs"
echo Set WshShell = Nothing >> "%DIST_NAME%\AccountingSystem-Silent.vbs"

:: Create installation guide
echo Creating installation guide...
echo # نظام المحاسبة المتكامل - مع Java مدمجة > "%DIST_NAME%\اقرأني.txt"
echo ## Integrated Accounting System - With Bundled Java >> "%DIST_NAME%\اقرأني.txt"
echo. >> "%DIST_NAME%\اقرأني.txt"
echo ### المميزات: >> "%DIST_NAME%\اقرأني.txt"
echo ✅ لا يحتاج تثبيت Java منفصل >> "%DIST_NAME%\اقرأني.txt"
echo ✅ Java مدمجة مع البرنامج >> "%DIST_NAME%\اقرأني.txt"
echo ✅ يعمل على أي جهاز Windows 10/11 >> "%DIST_NAME%\اقرأني.txt"
echo ✅ سهل النقل والتوزيع >> "%DIST_NAME%\اقرأني.txt"
echo. >> "%DIST_NAME%\اقرأني.txt"
echo ### طريقة التشغيل: >> "%DIST_NAME%\اقرأني.txt"
echo 1. انقر نقراً مزدوجاً على AccountingSystem.bat >> "%DIST_NAME%\اقرأني.txt"
echo 2. أو انقر على AccountingSystem-Silent.vbs (بدون نافذة الأوامر) >> "%DIST_NAME%\اقرأني.txt"
echo 3. انتظر حتى يبدأ البرنامج >> "%DIST_NAME%\اقرأني.txt"
echo. >> "%DIST_NAME%\اقرأني.txt"
echo ### المتطلبات: >> "%DIST_NAME%\اقرأني.txt"
echo - Windows 10/11 (64-bit) >> "%DIST_NAME%\اقرأني.txt"
echo - 4 GB RAM كحد أدنى >> "%DIST_NAME%\اقرأني.txt"
echo - 300 MB مساحة قرص >> "%DIST_NAME%\اقرأني.txt"
echo. >> "%DIST_NAME%\اقرأني.txt"
echo ### طريقة التوزيع: >> "%DIST_NAME%\اقرأني.txt"
echo 1. انسخ المجلد كاملاً >> "%DIST_NAME%\اقرأني.txt"
echo 2. أو اضغطه في ملف ZIP >> "%DIST_NAME%\اقرأني.txt"
echo 3. المستخدم ينسخ المجلد ويشغل البرنامج >> "%DIST_NAME%\اقرأني.txt"
echo. >> "%DIST_NAME%\اقرأني.txt"
echo ### الدعم التقني: >> "%DIST_NAME%\اقرأني.txt"
echo البريد الإلكتروني: <EMAIL> >> "%DIST_NAME%\اقرأني.txt"
echo الهاتف: 123-456-7890 >> "%DIST_NAME%\اقرأني.txt"
echo. >> "%DIST_NAME%\اقرأني.txt"
echo © 2025 شركة الزجاج والألومنيوم المتقدمة >> "%DIST_NAME%\اقرأني.txt"

:: Copy documentation
copy "README.md" "%DIST_NAME%\" 2>nul
copy "دليل-المستخدم.md" "%DIST_NAME%\" 2>nul

echo.
echo Step 3: Creating ZIP package...

:: Create ZIP using PowerShell
powershell -command "Compress-Archive -Path '%DIST_NAME%\*' -DestinationPath '%DIST_NAME%.zip' -Force" 2>nul

if exist "%DIST_NAME%.zip" (
    echo ✅ ZIP package created: %DIST_NAME%.zip
) else (
    echo ⚠️ ZIP creation failed (PowerShell not available)
    echo You can manually compress the folder
)

echo.
echo ========================================
echo SUCCESS: Bundled Distribution Created!
echo ========================================
echo.
echo Distribution folder: %DIST_NAME%\
if exist "%DIST_NAME%.zip" echo ZIP package: %DIST_NAME%.zip
echo.
echo Contents:
dir /b "%DIST_NAME%"
echo.

:: Calculate sizes
echo Calculating sizes...
for /f "tokens=3" %%a in ('dir "%DIST_NAME%" /s /-c ^| find "File(s)"') do set foldersize=%%a
echo Folder size: %foldersize% bytes

if exist "%DIST_NAME%.zip" (
    for %%f in ("%DIST_NAME%.zip") do echo ZIP size: %%~zf bytes
)

echo.
echo ✅ Features:
echo   - No Java installation required on target system
echo   - Bundled Java runtime included
echo   - Works on any Windows 10/11 system
echo   - Easy to distribute (copy folder or ZIP)
echo   - Silent launcher option available
echo.
echo 🚀 Ready for distribution!
echo.
echo To distribute:
echo   1. Send the ZIP file: %DIST_NAME%.zip
echo   2. Or copy the folder: %DIST_NAME%\
echo   3. Users run AccountingSystem.bat or AccountingSystem-Silent.vbs
echo   4. No additional installation required
echo.

pause
