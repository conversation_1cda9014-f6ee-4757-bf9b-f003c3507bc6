package com.accounting.model;

import javafx.beans.property.*;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.UUID;

/**
 * نموذج القيد المحاسبي في دفتر اليومية
 */
public class JournalEntry {
    
    // الخصائص الأساسية
    private final StringProperty entryId = new SimpleStringProperty();
    private final StringProperty entryNumber = new SimpleStringProperty();
    private final ObjectProperty<LocalDate> entryDate = new SimpleObjectProperty<>();
    private final StringProperty description = new SimpleStringProperty();
    private final StringProperty reference = new SimpleStringProperty();
    
    // حالة القيد
    private final ObjectProperty<EntryStatus> status = new SimpleObjectProperty<>(EntryStatus.DRAFT);
    private final ObjectProperty<EntryType> entryType = new SimpleObjectProperty<>(EntryType.MANUAL);
    private final StringProperty sourceDocument = new SimpleStringProperty();
    
    // المبالغ
    private final DoubleProperty totalDebit = new SimpleDoubleProperty(0.0);
    private final DoubleProperty totalCredit = new SimpleDoubleProperty(0.0);
    private final BooleanProperty isBalanced = new SimpleBooleanProperty(false);
    
    // معلومات التدقيق
    private final StringProperty createdBy = new SimpleStringProperty();
    private final ObjectProperty<LocalDateTime> createdDate = new SimpleObjectProperty<>(LocalDateTime.now());
    private final StringProperty approvedBy = new SimpleStringProperty();
    private final ObjectProperty<LocalDateTime> approvedDate = new SimpleObjectProperty<>();
    private final StringProperty postedBy = new SimpleStringProperty();
    private final ObjectProperty<LocalDateTime> postedDate = new SimpleObjectProperty<>();
    
    // تفاصيل القيد
    private final ObservableList<JournalEntryDetail> entryDetails = FXCollections.observableArrayList();
    
    /**
     * حالات القيد
     */
    public enum EntryStatus {
        DRAFT("مسودة", "Draft"),
        APPROVED("معتمد", "Approved"),
        POSTED("مرحل", "Posted"),
        CANCELLED("ملغي", "Cancelled");
        
        private final String arabicName;
        private final String englishName;
        
        EntryStatus(String arabicName, String englishName) {
            this.arabicName = arabicName;
            this.englishName = englishName;
        }
        
        public String getArabicName() { return arabicName; }
        public String getEnglishName() { return englishName; }
        
        @Override
        public String toString() { return arabicName; }
    }
    
    /**
     * أنواع القيود
     */
    public enum EntryType {
        MANUAL("يدوي", "Manual"),
        SALES("مبيعات", "Sales"),
        PURCHASES("مشتريات", "Purchases"),
        PAYMENT("دفع", "Payment"),
        RECEIPT("قبض", "Receipt"),
        ADJUSTMENT("تسوية", "Adjustment"),
        OPENING("رصيد افتتاحي", "Opening Balance"),
        CLOSING("رصيد ختامي", "Closing Balance");
        
        private final String arabicName;
        private final String englishName;
        
        EntryType(String arabicName, String englishName) {
            this.arabicName = arabicName;
            this.englishName = englishName;
        }
        
        public String getArabicName() { return arabicName; }
        public String getEnglishName() { return englishName; }
        
        @Override
        public String toString() { return arabicName; }
    }
    
    // Constructors
    public JournalEntry() {
        setEntryId(UUID.randomUUID().toString());
        setEntryDate(LocalDate.now());
    }
    
    public JournalEntry(String entryNumber, LocalDate entryDate, String description) {
        this();
        setEntryNumber(entryNumber);
        setEntryDate(entryDate);
        setDescription(description);
    }
    
    // Property getters
    public StringProperty entryIdProperty() { return entryId; }
    public StringProperty entryNumberProperty() { return entryNumber; }
    public ObjectProperty<LocalDate> entryDateProperty() { return entryDate; }
    public StringProperty descriptionProperty() { return description; }
    public StringProperty referenceProperty() { return reference; }
    public ObjectProperty<EntryStatus> statusProperty() { return status; }
    public ObjectProperty<EntryType> entryTypeProperty() { return entryType; }
    public StringProperty sourceDocumentProperty() { return sourceDocument; }
    public DoubleProperty totalDebitProperty() { return totalDebit; }
    public DoubleProperty totalCreditProperty() { return totalCredit; }
    public BooleanProperty isBalancedProperty() { return isBalanced; }
    public StringProperty createdByProperty() { return createdBy; }
    public ObjectProperty<LocalDateTime> createdDateProperty() { return createdDate; }
    public StringProperty approvedByProperty() { return approvedBy; }
    public ObjectProperty<LocalDateTime> approvedDateProperty() { return approvedDate; }
    public StringProperty postedByProperty() { return postedBy; }
    public ObjectProperty<LocalDateTime> postedDateProperty() { return postedDate; }
    
    // Value getters and setters
    public String getEntryId() { return entryId.get(); }
    public void setEntryId(String entryId) { this.entryId.set(entryId); }
    
    public String getEntryNumber() { return entryNumber.get(); }
    public void setEntryNumber(String entryNumber) { this.entryNumber.set(entryNumber); }
    
    public LocalDate getEntryDate() { return entryDate.get(); }
    public void setEntryDate(LocalDate entryDate) { this.entryDate.set(entryDate); }
    
    public String getDescription() { return description.get(); }
    public void setDescription(String description) { this.description.set(description); }
    
    public String getReference() { return reference.get(); }
    public void setReference(String reference) { this.reference.set(reference); }
    
    public EntryStatus getStatus() { return status.get(); }
    public void setStatus(EntryStatus status) { this.status.set(status); }
    
    public EntryType getEntryType() { return entryType.get(); }
    public void setEntryType(EntryType entryType) { this.entryType.set(entryType); }
    
    public String getSourceDocument() { return sourceDocument.get(); }
    public void setSourceDocument(String sourceDocument) { this.sourceDocument.set(sourceDocument); }
    
    public double getTotalDebit() { return totalDebit.get(); }
    public void setTotalDebit(double totalDebit) { this.totalDebit.set(totalDebit); }
    
    public double getTotalCredit() { return totalCredit.get(); }
    public void setTotalCredit(double totalCredit) { this.totalCredit.set(totalCredit); }
    
    public boolean isBalanced() { return isBalanced.get(); }
    public void setIsBalanced(boolean isBalanced) { this.isBalanced.set(isBalanced); }
    
    public String getCreatedBy() { return createdBy.get(); }
    public void setCreatedBy(String createdBy) { this.createdBy.set(createdBy); }
    
    public LocalDateTime getCreatedDate() { return createdDate.get(); }
    public void setCreatedDate(LocalDateTime createdDate) { this.createdDate.set(createdDate); }
    
    public String getApprovedBy() { return approvedBy.get(); }
    public void setApprovedBy(String approvedBy) { this.approvedBy.set(approvedBy); }
    
    public LocalDateTime getApprovedDate() { return approvedDate.get(); }
    public void setApprovedDate(LocalDateTime approvedDate) { this.approvedDate.set(approvedDate); }
    
    public String getPostedBy() { return postedBy.get(); }
    public void setPostedBy(String postedBy) { this.postedBy.set(postedBy); }
    
    public LocalDateTime getPostedDate() { return postedDate.get(); }
    public void setPostedDate(LocalDateTime postedDate) { this.postedDate.set(postedDate); }
    
    // Entry details management
    public ObservableList<JournalEntryDetail> getEntryDetails() { return entryDetails; }
    
    public void addEntryDetail(JournalEntryDetail detail) {
        detail.setJournalEntry(this);
        entryDetails.add(detail);
        recalculateTotals();
    }
    
    public void removeEntryDetail(JournalEntryDetail detail) {
        entryDetails.remove(detail);
        detail.setJournalEntry(null);
        recalculateTotals();
    }
    
    public void clearEntryDetails() {
        entryDetails.clear();
        recalculateTotals();
    }
    
    /**
     * إعادة حساب المجاميع
     */
    public void recalculateTotals() {
        double debitSum = entryDetails.stream()
                .mapToDouble(JournalEntryDetail::getDebitAmount)
                .sum();
        double creditSum = entryDetails.stream()
                .mapToDouble(JournalEntryDetail::getCreditAmount)
                .sum();
        
        setTotalDebit(debitSum);
        setTotalCredit(creditSum);
        setIsBalanced(Math.abs(debitSum - creditSum) < 0.01); // تسامح في الفروق الصغيرة
    }
    
    /**
     * التحقق من صحة القيد
     */
    public boolean isValid() {
        return isBalanced() && 
               !entryDetails.isEmpty() && 
               entryDetails.size() >= 2 &&
               getEntryNumber() != null && !getEntryNumber().trim().isEmpty() &&
               getDescription() != null && !getDescription().trim().isEmpty() &&
               getEntryDate() != null;
    }
    
    /**
     * اعتماد القيد
     */
    public boolean approve(String approvedBy) {
        if (getStatus() == EntryStatus.DRAFT && isValid()) {
            setStatus(EntryStatus.APPROVED);
            setApprovedBy(approvedBy);
            setApprovedDate(LocalDateTime.now());
            return true;
        }
        return false;
    }
    
    /**
     * ترحيل القيد
     */
    public boolean post(String postedBy) {
        if (getStatus() == EntryStatus.APPROVED) {
            setStatus(EntryStatus.POSTED);
            setPostedBy(postedBy);
            setPostedDate(LocalDateTime.now());
            return true;
        }
        return false;
    }
    
    /**
     * إلغاء القيد
     */
    public boolean cancel() {
        if (getStatus() != EntryStatus.POSTED) {
            setStatus(EntryStatus.CANCELLED);
            return true;
        }
        return false;
    }
    
    /**
     * التحقق من إمكانية التعديل
     */
    public boolean canBeEdited() {
        return getStatus() == EntryStatus.DRAFT;
    }
    
    /**
     * التحقق من إمكانية الحذف
     */
    public boolean canBeDeleted() {
        return getStatus() == EntryStatus.DRAFT || getStatus() == EntryStatus.CANCELLED;
    }
    
    /**
     * نسخ القيد
     */
    public JournalEntry copy() {
        JournalEntry copy = new JournalEntry();
        copy.setEntryNumber(getEntryNumber() + "_COPY");
        copy.setEntryDate(getEntryDate());
        copy.setDescription(getDescription() + " (نسخة)");
        copy.setReference(getReference());
        copy.setEntryType(getEntryType());
        copy.setSourceDocument(getSourceDocument());
        copy.setCreatedBy(getCreatedBy());
        
        // نسخ التفاصيل
        for (JournalEntryDetail detail : entryDetails) {
            copy.addEntryDetail(detail.copy());
        }
        
        return copy;
    }
    
    @Override
    public String toString() {
        return getEntryNumber() + " - " + getDescription() + " (" + getStatus().getArabicName() + ")";
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        JournalEntry entry = (JournalEntry) obj;
        return getEntryId() != null && getEntryId().equals(entry.getEntryId());
    }
    
    @Override
    public int hashCode() {
        return getEntryId() != null ? getEntryId().hashCode() : 0;
    }
}
