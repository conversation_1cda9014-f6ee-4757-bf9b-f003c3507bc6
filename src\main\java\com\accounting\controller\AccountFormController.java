package com.accounting.controller;

import com.accounting.model.Account;
import com.accounting.service.AccountService;
import javafx.geometry.Insets;
import javafx.geometry.Pos;
import javafx.scene.Scene;
import javafx.scene.control.*;
import javafx.scene.layout.*;
import javafx.stage.Modality;
import javafx.stage.Stage;
import de.jensd.fx.glyphs.fontawesome.FontAwesomeIcon;
import de.jensd.fx.glyphs.fontawesome.FontAwesomeIconView;
import java.util.Optional;

/**
 * كنترولر نموذج إضافة/تعديل الحساب
 */
public class AccountFormController {
    
    private final AccountService accountService;
    private Account currentAccount;
    private boolean isEditMode;
    
    // عناصر النموذج
    private TextField accountCodeField;
    private TextField accountNameField;
    private TextField accountNameEnField;
    private ComboBox<Account.AccountType> accountTypeCombo;
    private ComboBox<Account.AccountNature> accountNatureCombo;
    private ComboBox<Account> parentAccountCombo;
    private TextField openingBalanceField;
    private TextArea descriptionArea;
    private CheckBox isActiveCheckBox;
    private TextField currencyField;
    
    public AccountFormController(AccountService accountService) {
        this.accountService = accountService;
    }
    
    /**
     * عرض نموذج الحساب
     */
    public Optional<Account> showAccountForm(Account account) {
        this.currentAccount = account;
        this.isEditMode = (account != null);
        
        Stage dialog = new Stage();
        dialog.setTitle(isEditMode ? "تعديل الحساب" : "إضافة حساب جديد");
        dialog.initModality(Modality.APPLICATION_MODAL);
        dialog.setWidth(600);
        dialog.setHeight(700);
        dialog.setResizable(false);
        
        VBox mainLayout = new VBox(20);
        mainLayout.setPadding(new Insets(30));
        
        // العنوان
        Label titleLabel = new Label(isEditMode ? "✏️ تعديل الحساب" : "➕ إضافة حساب جديد");
        titleLabel.getStyleClass().add("dialog-title");
        
        // النموذج
        VBox formBox = createForm();
        
        // تحميل البيانات إذا كان في وضع التعديل
        if (isEditMode) {
            loadAccountData();
        }
        
        // أزرار الحفظ والإلغاء
        HBox buttonBox = createButtonBox(dialog);
        
        mainLayout.getChildren().addAll(titleLabel, formBox, buttonBox);
        
        Scene scene = new Scene(mainLayout);
        scene.getStylesheets().add(getClass().getResource("/css/styles.css").toExternalForm());
        dialog.setScene(scene);
        
        // عرض النافذة وانتظار النتيجة
        dialog.showAndWait();
        
        return Optional.ofNullable(currentAccount);
    }
    
    /**
     * إنشاء النموذج
     */
    private VBox createForm() {
        VBox formBox = new VBox(15);
        formBox.setPadding(new Insets(20));
        formBox.setStyle("-fx-background-color: #f8f9fa; -fx-border-color: #dee2e6; -fx-border-width: 1; -fx-border-radius: 8;");
        
        // رمز الحساب
        Label codeLabel = new Label("رمز الحساب:");
        codeLabel.getStyleClass().add("field-label");
        accountCodeField = new TextField();
        accountCodeField.setPromptText("أدخل رمز الحساب (أرقام فقط)");
        
        // اسم الحساب (عربي)
        Label nameLabel = new Label("اسم الحساب (عربي):");
        nameLabel.getStyleClass().add("field-label");
        accountNameField = new TextField();
        accountNameField.setPromptText("أدخل اسم الحساب باللغة العربية");
        
        // اسم الحساب (إنجليزي)
        Label nameEnLabel = new Label("اسم الحساب (إنجليزي):");
        nameEnLabel.getStyleClass().add("field-label");
        accountNameEnField = new TextField();
        accountNameEnField.setPromptText("أدخل اسم الحساب باللغة الإنجليزية (اختياري)");
        
        // نوع الحساب
        Label typeLabel = new Label("نوع الحساب:");
        typeLabel.getStyleClass().add("field-label");
        accountTypeCombo = new ComboBox<>();
        accountTypeCombo.getItems().addAll(Account.AccountType.values());
        accountTypeCombo.setPromptText("اختر نوع الحساب");
        accountTypeCombo.setPrefWidth(Double.MAX_VALUE);
        
        // طبيعة الحساب
        Label natureLabel = new Label("طبيعة الحساب:");
        natureLabel.getStyleClass().add("field-label");
        accountNatureCombo = new ComboBox<>();
        accountNatureCombo.getItems().addAll(Account.AccountNature.values());
        accountNatureCombo.setPromptText("اختر طبيعة الحساب");
        accountNatureCombo.setPrefWidth(Double.MAX_VALUE);
        
        // الحساب الأب
        Label parentLabel = new Label("الحساب الأب:");
        parentLabel.getStyleClass().add("field-label");
        parentAccountCombo = new ComboBox<>();
        parentAccountCombo.getItems().addAll(accountService.getAllAccounts());
        parentAccountCombo.setPromptText("اختر الحساب الأب (اختياري)");
        parentAccountCombo.setPrefWidth(Double.MAX_VALUE);
        
        // الرصيد الافتتاحي
        Label balanceLabel = new Label("الرصيد الافتتاحي:");
        balanceLabel.getStyleClass().add("field-label");
        openingBalanceField = new TextField();
        openingBalanceField.setPromptText("0.00");
        openingBalanceField.setText("0.00");
        
        // العملة
        Label currencyLabel = new Label("العملة:");
        currencyLabel.getStyleClass().add("field-label");
        currencyField = new TextField();
        currencyField.setText("EGP");
        currencyField.setPromptText("رمز العملة");
        
        // الوصف
        Label descLabel = new Label("الوصف:");
        descLabel.getStyleClass().add("field-label");
        descriptionArea = new TextArea();
        descriptionArea.setPromptText("وصف الحساب (اختياري)");
        descriptionArea.setPrefRowCount(3);
        descriptionArea.setWrapText(true);
        
        // نشط
        isActiveCheckBox = new CheckBox("الحساب نشط");
        isActiveCheckBox.setSelected(true);
        
        formBox.getChildren().addAll(
            codeLabel, accountCodeField,
            nameLabel, accountNameField,
            nameEnLabel, accountNameEnField,
            typeLabel, accountTypeCombo,
            natureLabel, accountNatureCombo,
            parentLabel, parentAccountCombo,
            balanceLabel, openingBalanceField,
            currencyLabel, currencyField,
            descLabel, descriptionArea,
            isActiveCheckBox
        );
        
        return formBox;
    }
    
    /**
     * إنشاء صندوق الأزرار
     */
    private HBox createButtonBox(Stage dialog) {
        HBox buttonBox = new HBox(15);
        buttonBox.setAlignment(Pos.CENTER_RIGHT);
        buttonBox.setPadding(new Insets(20, 0, 0, 0));
        
        Button saveButton = new Button(isEditMode ? "تحديث" : "حفظ");
        FontAwesomeIconView saveIcon = new FontAwesomeIconView(FontAwesomeIcon.SAVE);
        saveIcon.setSize("14px");
        saveButton.setGraphic(saveIcon);
        saveButton.getStyleClass().add("save-button");
        saveButton.setOnAction(e -> {
            if (validateAndSave()) {
                dialog.close();
            }
        });
        
        Button cancelButton = new Button("إلغاء");
        cancelButton.setOnAction(e -> {
            currentAccount = null;
            dialog.close();
        });
        
        // زر توليد رمز تلقائي
        Button generateCodeButton = new Button("توليد رمز");
        FontAwesomeIconView generateIcon = new FontAwesomeIconView(FontAwesomeIcon.MAGIC);
        generateIcon.setSize("12px");
        generateCodeButton.setGraphic(generateIcon);
        generateCodeButton.getStyleClass().add("generate-button");
        generateCodeButton.setOnAction(e -> generateAccountCode());
        
        buttonBox.getChildren().addAll(generateCodeButton, saveButton, cancelButton);
        return buttonBox;
    }
    
    /**
     * تحميل بيانات الحساب للتعديل
     */
    private void loadAccountData() {
        if (currentAccount != null) {
            accountCodeField.setText(currentAccount.getAccountCode());
            accountCodeField.setEditable(false); // لا يمكن تعديل رمز الحساب
            accountNameField.setText(currentAccount.getAccountName());
            accountNameEnField.setText(currentAccount.getAccountNameEn());
            accountTypeCombo.setValue(currentAccount.getAccountType());
            accountNatureCombo.setValue(currentAccount.getAccountNature());
            openingBalanceField.setText(String.valueOf(currentAccount.getOpeningBalance()));
            currencyField.setText(currentAccount.getCurrency());
            descriptionArea.setText(currentAccount.getDescription());
            isActiveCheckBox.setSelected(currentAccount.isActive());
            
            // تحديد الحساب الأب
            if (currentAccount.getParentAccountCode() != null) {
                Account parent = accountService.getAccountByCode(currentAccount.getParentAccountCode());
                if (parent != null) {
                    parentAccountCombo.setValue(parent);
                }
            }
        }
    }
    
    /**
     * التحقق من صحة البيانات والحفظ
     */
    private boolean validateAndSave() {
        // التحقق من الحقول المطلوبة
        if (accountCodeField.getText().trim().isEmpty()) {
            showErrorAlert("رمز الحساب مطلوب.");
            return false;
        }
        
        if (accountNameField.getText().trim().isEmpty()) {
            showErrorAlert("اسم الحساب مطلوب.");
            return false;
        }
        
        if (accountTypeCombo.getValue() == null) {
            showErrorAlert("نوع الحساب مطلوب.");
            return false;
        }
        
        if (accountNatureCombo.getValue() == null) {
            showErrorAlert("طبيعة الحساب مطلوبة.");
            return false;
        }
        
        // التحقق من صحة رمز الحساب
        String accountCode = accountCodeField.getText().trim();
        if (!accountCode.matches("^[0-9]+$")) {
            showErrorAlert("رمز الحساب يجب أن يحتوي على أرقام فقط.");
            return false;
        }
        
        // التحقق من عدم تكرار رمز الحساب (في حالة الإضافة)
        if (!isEditMode && accountService.getAccountByCode(accountCode) != null) {
            showErrorAlert("رمز الحساب موجود مسبقاً. يرجى اختيار رمز آخر.");
            return false;
        }
        
        // التحقق من صحة الرصيد الافتتاحي
        double openingBalance;
        try {
            openingBalance = Double.parseDouble(openingBalanceField.getText().trim());
        } catch (NumberFormatException e) {
            showErrorAlert("الرصيد الافتتاحي يجب أن يكون رقماً صحيحاً.");
            return false;
        }
        
        // إنشاء أو تحديث الحساب
        if (isEditMode) {
            // تحديث الحساب الموجود
            currentAccount.setAccountName(accountNameField.getText().trim());
            currentAccount.setAccountNameEn(accountNameEnField.getText().trim());
            currentAccount.setAccountType(accountTypeCombo.getValue());
            currentAccount.setAccountNature(accountNatureCombo.getValue());
            currentAccount.setOpeningBalance(openingBalance);
            currentAccount.setCurrency(currencyField.getText().trim());
            currentAccount.setDescription(descriptionArea.getText().trim());
            currentAccount.setIsActive(isActiveCheckBox.isSelected());
            
            // تحديث الحساب الأب
            Account selectedParent = parentAccountCombo.getValue();
            if (selectedParent != null) {
                currentAccount.setParentAccountCode(selectedParent.getAccountCode());
            } else {
                currentAccount.setParentAccountCode(null);
            }
        } else {
            // إنشاء حساب جديد
            currentAccount = new Account(
                accountCode,
                accountNameField.getText().trim(),
                accountTypeCombo.getValue(),
                accountNatureCombo.getValue()
            );
            
            currentAccount.setAccountNameEn(accountNameEnField.getText().trim());
            currentAccount.setOpeningBalance(openingBalance);
            currentAccount.setCurrency(currencyField.getText().trim());
            currentAccount.setDescription(descriptionArea.getText().trim());
            currentAccount.setIsActive(isActiveCheckBox.isSelected());
            
            // تحديد الحساب الأب
            Account selectedParent = parentAccountCombo.getValue();
            if (selectedParent != null) {
                currentAccount.setParentAccountCode(selectedParent.getAccountCode());
            }
        }
        
        return true;
    }
    
    /**
     * توليد رمز حساب تلقائي
     */
    private void generateAccountCode() {
        Account selectedParent = parentAccountCombo.getValue();
        String parentCode = selectedParent != null ? selectedParent.getAccountCode() : null;
        String generatedCode = accountService.generateAccountCode(parentCode);
        accountCodeField.setText(generatedCode);
    }
    
    /**
     * عرض رسالة خطأ
     */
    private void showErrorAlert(String message) {
        Alert alert = new Alert(Alert.AlertType.ERROR);
        alert.setTitle("خطأ");
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }
}
