package com.accounting.controller;

import com.accounting.model.InvoiceItem;
import com.accounting.model.Service;
import com.accounting.model.Customer;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.geometry.Insets;
import javafx.geometry.Pos;
import javafx.scene.Scene;
import javafx.scene.control.*;
import javafx.scene.control.cell.PropertyValueFactory;
import javafx.scene.control.cell.TextFieldTableCell;
import javafx.scene.control.cell.ComboBoxTableCell;
import javafx.scene.layout.*;
import javafx.stage.Modality;
import javafx.stage.Stage;
import javafx.util.converter.DoubleStringConverter;
import javafx.util.converter.IntegerStringConverter;
import de.jensd.fx.glyphs.fontawesome.FontAwesomeIcon;
import de.jensd.fx.glyphs.fontawesome.FontAwesomeIconView;

import java.text.DecimalFormat;

/**
 * متحكم موديول المبيعات
 * Sales Module Controller
 */
public class SalesController {
    
    private ObservableList<Service> services = FXCollections.observableArrayList();
    private ObservableList<Customer> customers = FXCollections.observableArrayList();
    private ObservableList<InvoiceItem> invoiceItems = FXCollections.observableArrayList();
    private DecimalFormat decimalFormat = new DecimalFormat("#,##0.00");

    // مراجع للملخص
    private Label subtotalValueLabel;
    private TextField discountField;
    private Label totalValueLabel;

    // مراجع للفاتورة
    private ComboBox<Customer> customerComboBox;
    
    public SalesController() {
        // إضافة بعض الخدمات الافتراضية
        services.addAll(
            new Service("1", "قص ليزر", "قص المعادن بالليزر", 50.0),
            new Service("2", "ثني معادن", "ثني وتشكيل المعادن", 30.0),
            new Service("3", "لحام", "لحام القطع المعدنية", 40.0),
            new Service("4", "طلاء", "طلاء وتشطيب المعادن", 25.0),
            new Service("5", "تفريز CNC", "تفريز دقيق بالكمبيوتر", 75.0),
            new Service("6", "خراطة", "خراطة القطع المعدنية", 35.0)
        );

        // إضافة بعض العملاء الافتراضيين
        customers.addAll(
            new Customer("1", "أحمد محمد", "01234567890", "<EMAIL>", "القاهرة", "شركة الأمل", "عميل مميز"),
            new Customer("2", "فاطمة علي", "01098765432", "<EMAIL>", "الجيزة", "مؤسسة النور", ""),
            new Customer("3", "محمد حسن", "01555666777", "<EMAIL>", "الإسكندرية", "شركة المستقبل", "عميل جديد"),
            new Customer("4", "سارة أحمد", "01777888999", "<EMAIL>", "المنصورة", "", "عميل فردي")
        );

        // إضافة صف فارغ للبدء
        addEmptyRow();
    }

    /**
     * إضافة صف فارغ للبدء
     */
    private void addEmptyRow() {
        InvoiceItem emptyItem = new InvoiceItem();
        // ترك الصف فارغ ليقوم المستخدم بملئه
        invoiceItems.add(emptyItem);
    }
    
    /**
     * إنشاء واجهة موديول المبيعات
     */
    public VBox createSalesModule() {
        VBox mainContainer = new VBox(20);
        mainContainer.setPadding(new Insets(20));
        mainContainer.getStyleClass().add("sales-module");
        
        // العنوان الرئيسي
        Label titleLabel = new Label("🛒 موديول المبيعات");
        titleLabel.getStyleClass().add("module-title");
        
        // شريط الأدوات
        HBox toolbar = createToolbar();
        
        // منطقة الفاتورة
        VBox invoiceArea = createInvoiceArea();
        
        mainContainer.getChildren().addAll(titleLabel, toolbar, invoiceArea);
        return mainContainer;
    }
    
    /**
     * إنشاء شريط الأدوات
     */
    private HBox createToolbar() {
        HBox toolbar = new HBox(15);
        toolbar.setAlignment(Pos.CENTER_LEFT);
        toolbar.setPadding(new Insets(10));
        toolbar.getStyleClass().add("toolbar");
        
        // زر إدارة الخدمات
        Button servicesBtn = createToolbarButton("الخدمات", FontAwesomeIcon.COG, this::showServicesDialog);
        
        // زر إدارة العملاء
        Button customersBtn = createToolbarButton("العملاء", FontAwesomeIcon.USERS, this::showCustomersDialog);
        
        // زر تقارير المبيعات
        Button reportsBtn = createToolbarButton("التقارير", FontAwesomeIcon.BAR_CHART, this::showReportsDialog);
        
        // فاصل
        Separator separator = new Separator();
        separator.setOrientation(javafx.geometry.Orientation.VERTICAL);
        
        // زر فاتورة جديدة
        Button newInvoiceBtn = createToolbarButton("فاتورة جديدة", FontAwesomeIcon.PLUS, this::newInvoice);
        
        // زر حفظ الفاتورة
        Button saveInvoiceBtn = createToolbarButton("حفظ الفاتورة", FontAwesomeIcon.SAVE, this::saveInvoice);

        // زر حفظ PDF
        Button savePdfBtn = createToolbarButton("حفظ PDF", FontAwesomeIcon.FILE_PDF_ALT, this::savePdf);

        // زر طباعة
        Button printBtn = createToolbarButton("طباعة", FontAwesomeIcon.PRINT, this::printInvoice);

        toolbar.getChildren().addAll(servicesBtn, customersBtn, reportsBtn, separator,
                                   newInvoiceBtn, saveInvoiceBtn, savePdfBtn, printBtn);
        return toolbar;
    }
    
    /**
     * إنشاء زر شريط الأدوات
     */
    private Button createToolbarButton(String text, FontAwesomeIcon icon, Runnable action) {
        Button button = new Button(text);
        FontAwesomeIconView iconView = new FontAwesomeIconView(icon);
        iconView.setSize("16px");
        button.setGraphic(iconView);
        button.getStyleClass().add("toolbar-button");
        button.setOnAction(e -> action.run());
        return button;
    }
    
    /**
     * إنشاء منطقة الفاتورة
     */
    private VBox createInvoiceArea() {
        VBox invoiceArea = new VBox(15);
        invoiceArea.getStyleClass().add("invoice-area");
        
        // معلومات الفاتورة
        HBox invoiceHeader = createInvoiceHeader();
        
        // جدول عناصر الفاتورة
        TableView<InvoiceItem> invoiceTable = createInvoiceTable();
        
        // أزرار إدارة الصفوف
        HBox rowControls = createRowControls(invoiceTable);
        
        // ملخص الفاتورة
        VBox invoiceSummary = createInvoiceSummary();
        
        invoiceArea.getChildren().addAll(invoiceHeader, invoiceTable, rowControls, invoiceSummary);
        return invoiceArea;
    }
    
    /**
     * إنشاء رأس الفاتورة
     */
    private HBox createInvoiceHeader() {
        HBox header = new HBox(25);
        header.setAlignment(Pos.CENTER_LEFT);
        header.setPadding(new Insets(15));
        header.getStyleClass().add("invoice-header");

        // رقم الفاتورة
        VBox invoiceNoBox = new VBox(5);
        Label invoiceNoLabel = new Label("رقم الفاتورة:");
        invoiceNoLabel.getStyleClass().add("field-label");
        TextField invoiceNoField = new TextField("INV-" + System.currentTimeMillis() % 10000);
        invoiceNoField.setPrefWidth(130);
        invoiceNoField.getStyleClass().add("invoice-field");
        invoiceNoBox.getChildren().addAll(invoiceNoLabel, invoiceNoField);

        // تاريخ الفاتورة
        VBox dateBox = new VBox(5);
        Label dateLabel = new Label("التاريخ:");
        dateLabel.getStyleClass().add("field-label");
        DatePicker datePicker = new DatePicker();
        datePicker.setValue(java.time.LocalDate.now());
        datePicker.setPrefWidth(140);
        datePicker.getStyleClass().add("invoice-field");
        dateBox.getChildren().addAll(dateLabel, datePicker);

        // العميل
        VBox customerBox = new VBox(5);
        Label customerLabel = new Label("العميل:");
        customerLabel.getStyleClass().add("field-label");
        customerComboBox = new ComboBox<>();
        customerComboBox.setItems(customers);
        customerComboBox.setPrefWidth(200);
        customerComboBox.setEditable(false);
        customerComboBox.setPromptText("اختر عميل من القائمة");
        customerComboBox.getStyleClass().add("invoice-field");

        // زر إضافة عميل جديد
        Button addCustomerBtn = new Button("إضافة عميل");
        FontAwesomeIconView addCustomerIcon = new FontAwesomeIconView(FontAwesomeIcon.USER_PLUS);
        addCustomerIcon.setSize("12px");
        addCustomerBtn.setGraphic(addCustomerIcon);
        addCustomerBtn.getStyleClass().add("small-button");
        addCustomerBtn.setOnAction(e -> showAddCustomerDialog());

        HBox customerHBox = new HBox(5);
        customerHBox.getChildren().addAll(customerComboBox, addCustomerBtn);
        customerBox.getChildren().addAll(customerLabel, customerHBox);

        // حالة الفاتورة
        VBox statusBox = new VBox(5);
        Label statusLabel = new Label("حالة الفاتورة:");
        statusLabel.getStyleClass().add("field-label");
        ComboBox<String> statusCombo = new ComboBox<>();
        statusCombo.getItems().addAll("مسودة", "مؤكدة", "مدفوعة", "ملغية");
        statusCombo.setValue("مسودة");
        statusCombo.setPrefWidth(120);
        statusCombo.getStyleClass().add("invoice-field");
        statusBox.getChildren().addAll(statusLabel, statusCombo);

        header.getChildren().addAll(invoiceNoBox, dateBox, customerBox, statusBox);

        return header;
    }
    
    /**
     * إنشاء جدول عناصر الفاتورة
     */
    private TableView<InvoiceItem> createInvoiceTable() {
        TableView<InvoiceItem> table = new TableView<>();
        table.setItems(invoiceItems);
        table.setEditable(true);
        table.getStyleClass().add("invoice-table");
        // إزالة قيد الارتفاع ليتوسع الجدول تلقائياً
        table.setMinHeight(200);
        VBox.setVgrow(table, Priority.ALWAYS);
        table.setRowFactory(tv -> {
            TableRow<InvoiceItem> row = new TableRow<>();
            row.setPrefHeight(45); // ارتفاع الصف
            return row;
        });

        // إضافة placeholder عندما يكون الجدول فارغ
        Label placeholder = new Label("انقر على 'إضافة صف يدوي' لبدء إنشاء الفاتورة");
        placeholder.getStyleClass().add("table-placeholder");
        table.setPlaceholder(placeholder);
        
        // العمود: #
        TableColumn<InvoiceItem, String> indexCol = new TableColumn<>("#");
        indexCol.setPrefWidth(40);
        indexCol.setCellFactory(col -> {
            TableCell<InvoiceItem, String> cell = new TableCell<>();
            cell.textProperty().bind(cell.indexProperty().add(1).asString());
            return cell;
        });
        
        // العمود: نوع الخدمة
        TableColumn<InvoiceItem, String> serviceCol = new TableColumn<>("نوع الخدمة");
        serviceCol.setPrefWidth(140);
        serviceCol.setCellValueFactory(new PropertyValueFactory<>("serviceType"));

        ObservableList<String> serviceNames = FXCollections.observableArrayList();
        services.forEach(service -> serviceNames.add(service.getName()));
        serviceCol.setCellFactory(ComboBoxTableCell.forTableColumn(serviceNames));
        serviceCol.setOnEditCommit(event -> {
            event.getRowValue().setServiceType(event.getNewValue());
        });
        
        // العمود: السمك
        TableColumn<InvoiceItem, Integer> thicknessCol = new TableColumn<>("السمك (mm)");
        thicknessCol.setPrefWidth(90);
        thicknessCol.setCellValueFactory(new PropertyValueFactory<>("thickness"));

        // إنشاء قائمة السمك من 1 إلى 100
        ObservableList<Integer> thicknessList = FXCollections.observableArrayList();
        for (int i = 1; i <= 100; i++) {
            thicknessList.add(i);
        }
        thicknessCol.setCellFactory(ComboBoxTableCell.forTableColumn(thicknessList));
        thicknessCol.setOnEditCommit(event -> {
            event.getRowValue().setThickness(event.getNewValue());
        });
        
        // العمود: الطول
        TableColumn<InvoiceItem, Double> lengthCol = new TableColumn<>("الطول (mm)");
        lengthCol.setPrefWidth(90);
        lengthCol.setCellValueFactory(new PropertyValueFactory<>("length"));
        lengthCol.setCellFactory(TextFieldTableCell.forTableColumn(new DoubleStringConverter()));
        lengthCol.setOnEditCommit(event -> {
            event.getRowValue().setLength(event.getNewValue());
        });
        
        // العمود: العرض
        TableColumn<InvoiceItem, Double> widthCol = new TableColumn<>("العرض (mm)");
        widthCol.setPrefWidth(90);
        widthCol.setCellValueFactory(new PropertyValueFactory<>("width"));
        widthCol.setCellFactory(TextFieldTableCell.forTableColumn(new DoubleStringConverter()));
        widthCol.setOnEditCommit(event -> {
            event.getRowValue().setWidth(event.getNewValue());
        });
        
        // العمود: المتر المربع (محسوب تلقائياً)
        TableColumn<InvoiceItem, Double> squareMetersCol = new TableColumn<>("المتر المربع");
        squareMetersCol.setPrefWidth(100);
        squareMetersCol.setCellValueFactory(new PropertyValueFactory<>("squareMeters"));
        squareMetersCol.setCellFactory(col -> new TableCell<InvoiceItem, Double>() {
            @Override
            protected void updateItem(Double item, boolean empty) {
                super.updateItem(item, empty);
                getStyleClass().add("calculated-cell");
                if (empty || item == null) {
                    setText(null);
                } else {
                    setText(decimalFormat.format(item));
                }
            }
        });
        
        // العمود: العدد
        TableColumn<InvoiceItem, Integer> quantityCol = new TableColumn<>("العدد");
        quantityCol.setPrefWidth(60);
        quantityCol.setCellValueFactory(new PropertyValueFactory<>("quantity"));
        quantityCol.setCellFactory(TextFieldTableCell.forTableColumn(new IntegerStringConverter()));
        quantityCol.setOnEditCommit(event -> {
            event.getRowValue().setQuantity(event.getNewValue());
        });
        
        // العمود: إجمالي الكمية (محسوب تلقائياً)
        TableColumn<InvoiceItem, Double> totalQuantityCol = new TableColumn<>("إجمالي الكمية");
        totalQuantityCol.setPrefWidth(110);
        totalQuantityCol.setCellValueFactory(new PropertyValueFactory<>("totalQuantity"));
        totalQuantityCol.setCellFactory(col -> new TableCell<InvoiceItem, Double>() {
            @Override
            protected void updateItem(Double item, boolean empty) {
                super.updateItem(item, empty);
                getStyleClass().add("calculated-cell");
                if (empty || item == null) {
                    setText(null);
                } else {
                    setText(decimalFormat.format(item));
                }
            }
        });
        
        // العمود: السعر
        TableColumn<InvoiceItem, Double> priceCol = new TableColumn<>("السعر");
        priceCol.setPrefWidth(80);
        priceCol.setCellValueFactory(new PropertyValueFactory<>("price"));
        priceCol.setCellFactory(TextFieldTableCell.forTableColumn(new DoubleStringConverter()));
        priceCol.setOnEditCommit(event -> {
            event.getRowValue().setPrice(event.getNewValue());
        });
        
        // العمود: إجمالي القيمة (محسوب تلقائياً)
        TableColumn<InvoiceItem, Double> totalValueCol = new TableColumn<>("إجمالي القيمة");
        totalValueCol.setPrefWidth(110);
        totalValueCol.setCellValueFactory(new PropertyValueFactory<>("totalValue"));
        totalValueCol.setCellFactory(col -> new TableCell<InvoiceItem, Double>() {
            @Override
            protected void updateItem(Double item, boolean empty) {
                super.updateItem(item, empty);
                getStyleClass().add("calculated-cell");
                if (empty || item == null) {
                    setText(null);
                } else {
                    setText(decimalFormat.format(item) + " ج.م");
                }
            }
        });
        
        // العمود: الإجراءات
        TableColumn<InvoiceItem, Void> actionsCol = new TableColumn<>("إجراءات");
        actionsCol.setPrefWidth(120);
        actionsCol.setCellFactory(col -> new TableCell<InvoiceItem, Void>() {
            private final HBox actionBox = new HBox(5);
            private final Button addBtn = new Button();
            private final Button deleteBtn = new Button();

            {
                // زر الإضافة
                FontAwesomeIconView addIcon = new FontAwesomeIconView(FontAwesomeIcon.PLUS);
                addIcon.setSize("12px");
                addBtn.setGraphic(addIcon);
                addBtn.getStyleClass().add("add-row-action-button");
                addBtn.setTooltip(new Tooltip("إضافة صف جديد تحت هذا الصف"));
                addBtn.setOnAction(e -> {
                    int currentIndex = getIndex();
                    addRowAfter(getTableView(), currentIndex);
                });

                // زر الحذف
                FontAwesomeIconView deleteIcon = new FontAwesomeIconView(FontAwesomeIcon.TRASH);
                deleteIcon.setSize("12px");
                deleteBtn.setGraphic(deleteIcon);
                deleteBtn.getStyleClass().add("delete-row-action-button");
                deleteBtn.setTooltip(new Tooltip("حذف هذا الصف"));
                deleteBtn.setOnAction(e -> {
                    int currentIndex = getIndex();
                    deleteRow(getTableView(), currentIndex);
                });

                actionBox.setAlignment(Pos.CENTER);
                actionBox.getChildren().addAll(addBtn, deleteBtn);
            }

            @Override
            protected void updateItem(Void item, boolean empty) {
                super.updateItem(item, empty);
                if (empty) {
                    setGraphic(null);
                } else {
                    setGraphic(actionBox);
                }
            }
        });
        
        table.getColumns().addAll(indexCol, serviceCol, thicknessCol, lengthCol, widthCol,
                                 squareMetersCol, quantityCol, totalQuantityCol, priceCol,
                                 totalValueCol, actionsCol);
        
        return table;
    }
    
    /**
     * إنشاء أزرار إدارة الصفوف
     */
    private HBox createRowControls(TableView<InvoiceItem> table) {
        HBox controls = new HBox(15);
        controls.setAlignment(Pos.CENTER_LEFT);
        controls.setPadding(new Insets(15));
        controls.getStyleClass().add("row-controls");

        // زر إضافة صف عادي
        Button addNormalRowBtn = new Button("إضافة صف عادي");
        FontAwesomeIconView normalIcon = new FontAwesomeIconView(FontAwesomeIcon.PLUS);
        normalIcon.setSize("16px");
        addNormalRowBtn.setGraphic(normalIcon);
        addNormalRowBtn.getStyleClass().add("add-normal-row-button");
        addNormalRowBtn.setOnAction(e -> addNewRow(table, false));

        // زر إضافة صف يدوي
        Button addManualRowBtn = new Button("إضافة صف يدوي");
        FontAwesomeIconView manualIcon = new FontAwesomeIconView(FontAwesomeIcon.EDIT);
        manualIcon.setSize("16px");
        addManualRowBtn.setGraphic(manualIcon);
        addManualRowBtn.getStyleClass().add("add-manual-row-button");
        addManualRowBtn.setOnAction(e -> addNewRow(table, true));

        // معلومات إضافية
        Label infoLabel = new Label("الصف العادي: حسابات تلقائية | الصف اليدوي: إدخال يدوي للكمية والوصف");
        infoLabel.getStyleClass().add("info-label");

        controls.getChildren().addAll(addNormalRowBtn, addManualRowBtn, new Separator(), infoLabel);
        return controls;
    }

    /**
     * إضافة صف جديد
     */
    private void addNewRow(TableView<InvoiceItem> table, boolean isManual) {
        InvoiceItem newItem = new InvoiceItem(isManual);
        table.getItems().add(newItem);

        // التركيز على الصف الجديد
        table.getSelectionModel().selectLast();
        table.scrollTo(table.getItems().size() - 1);
    }

    /**
     * إضافة صف جديد بعد صف معين
     */
    private void addRowAfter(TableView<InvoiceItem> table, int index) {
        // تحديد نوع الصف بناءً على الصف الحالي
        boolean isManual = false;
        if (index >= 0 && index < table.getItems().size()) {
            isManual = table.getItems().get(index).isManualRow();
        }

        InvoiceItem newItem = new InvoiceItem(isManual);
        table.getItems().add(index + 1, newItem);

        // التركيز على الصف الجديد
        table.getSelectionModel().select(index + 1);
        table.scrollTo(index + 1);
    }

    /**
     * حذف صف معين
     */
    private void deleteRow(TableView<InvoiceItem> table, int index) {
        if (table.getItems().size() > 0 && index >= 0 && index < table.getItems().size()) {
            table.getItems().remove(index);
        }
    }
    
    /**
     * إنشاء ملخص الفاتورة
     */
    private VBox createInvoiceSummary() {
        VBox summary = new VBox(10);
        summary.setAlignment(Pos.CENTER_RIGHT);
        summary.setPadding(new Insets(15));
        summary.getStyleClass().add("invoice-summary");
        summary.setMaxWidth(300);
        
        // الإجمالي الفرعي
        HBox subtotalBox = new HBox();
        subtotalBox.setAlignment(Pos.CENTER_RIGHT);
        Label subtotalLabel = new Label("الإجمالي الفرعي:");
        subtotalValueLabel = new Label("0.00");
        subtotalValueLabel.getStyleClass().add("summary-value");
        subtotalBox.getChildren().addAll(subtotalLabel, new Region(), subtotalValueLabel);
        HBox.setHgrow(subtotalBox.getChildren().get(1), Priority.ALWAYS);

        // الخصم
        HBox discountBox = new HBox();
        discountBox.setAlignment(Pos.CENTER_RIGHT);
        Label discountLabel = new Label("الخصم:");
        discountField = new TextField("0");
        discountField.setPrefWidth(80);
        discountField.textProperty().addListener((obs, oldVal, newVal) -> updateTotals());
        discountBox.getChildren().addAll(discountLabel, new Region(), discountField);
        HBox.setHgrow(discountBox.getChildren().get(1), Priority.ALWAYS);

        // الإجمالي النهائي
        HBox totalBox = new HBox();
        totalBox.setAlignment(Pos.CENTER_RIGHT);
        totalBox.getStyleClass().add("total-box");
        Label totalLabel = new Label("الإجمالي النهائي:");
        totalLabel.getStyleClass().add("total-label");
        totalValueLabel = new Label("0.00");
        totalValueLabel.getStyleClass().add("total-value");
        totalBox.getChildren().addAll(totalLabel, new Region(), totalValueLabel);
        HBox.setHgrow(totalBox.getChildren().get(1), Priority.ALWAYS);
        
        // ملاحظات
        Label notesLabel = new Label("ملاحظات:");
        TextArea notesArea = new TextArea();
        notesArea.setPrefRowCount(3);
        notesArea.setPromptText("أدخل أي ملاحظات إضافية...");
        
        summary.getChildren().addAll(subtotalBox, discountBox, totalBox, notesLabel, notesArea);

        // إضافة مستمع لتحديث الإجماليات عند تغيير البيانات
        invoiceItems.addListener((javafx.collections.ListChangeListener<InvoiceItem>) change -> {
            while (change.next()) {
                if (change.wasAdded()) {
                    for (InvoiceItem item : change.getAddedSubList()) {
                        item.totalValueProperty().addListener((obs, oldVal, newVal) -> updateTotals());
                    }
                }
            }
            updateTotals();
        });

        return summary;
    }

    /**
     * تحديث الإجماليات
     */
    private void updateTotals() {
        double subtotal = invoiceItems.stream()
                .mapToDouble(InvoiceItem::getTotalValue)
                .sum();

        double discount = 0;
        try {
            discount = Double.parseDouble(discountField.getText());
        } catch (NumberFormatException e) {
            discount = 0;
        }

        double total = subtotal - discount;

        subtotalValueLabel.setText(decimalFormat.format(subtotal));
        totalValueLabel.setText(decimalFormat.format(total));
    }
    
    // أحداث الأزرار
    private void showServicesDialog() {
        showServiceManagementDialog();
    }

    private void showCustomersDialog() {
        showCustomerManagementDialog();
    }

    private void showReportsDialog() {
        showSalesReportsDialog();
    }

    private void newInvoice() {
        invoiceItems.clear();
        addEmptyRow(); // إضافة صف فارغ للبدء
        customerComboBox.getSelectionModel().clearSelection();
    }

    private void saveInvoice() {
        if (validateInvoice()) {
            // إنشاء رقم فاتورة جديد
            String invoiceNo = generateInvoiceNumber();

            // حفظ الفاتورة (هنا يمكن إضافة قاعدة البيانات لاحقاً)
            saveInvoiceToDatabase(invoiceNo);

            // إظهار رسالة النجاح
            Alert alert = new Alert(Alert.AlertType.INFORMATION);
            alert.setTitle("تم الحفظ");
            alert.setHeaderText("تم حفظ الفاتورة بنجاح");
            alert.setContentText("رقم الفاتورة: " + invoiceNo + "\nيمكنك العثور عليها في تقارير المبيعات");
            alert.showAndWait();

            // مسح الفاتورة الحالية وإنشاء فاتورة جديدة
            newInvoice();
        }
    }

    private void savePdf() {
        if (validateInvoice()) {
            showPrintPreview("PDF");
        }
    }

    private void printInvoice() {
        if (validateInvoice()) {
            showPrintPreview("طباعة");
        }
    }

    private boolean validateInvoice() {
        if (customerComboBox.getSelectionModel().getSelectedItem() == null) {
            showPlaceholderDialog("تحذير", "يرجى اختيار عميل للفاتورة");
            return false;
        }

        if (invoiceItems.isEmpty()) {
            showPlaceholderDialog("تحذير", "يرجى إضافة عناصر للفاتورة");
            return false;
        }

        return true;
    }
    
    private void showPlaceholderDialog(String title, String message) {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle(title);
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }

    /**
     * عرض نافذة إدارة الخدمات
     */
    private void showServiceManagementDialog() {
        Stage dialog = new Stage();
        dialog.setTitle("إدارة الخدمات");
        dialog.initModality(Modality.APPLICATION_MODAL);
        dialog.setWidth(600);
        dialog.setHeight(500);

        VBox mainLayout = new VBox(15);
        mainLayout.setPadding(new Insets(20));

        // العنوان
        Label titleLabel = new Label("إدارة الخدمات");
        titleLabel.getStyleClass().add("dialog-title");

        // جدول الخدمات
        TableView<Service> servicesTable = new TableView<>();
        servicesTable.setItems(services);
        servicesTable.setMinHeight(200);
        VBox.setVgrow(servicesTable, Priority.ALWAYS);

        // أعمدة الجدول
        TableColumn<Service, String> nameCol = new TableColumn<>("اسم الخدمة");
        nameCol.setPrefWidth(150);
        nameCol.setCellValueFactory(new PropertyValueFactory<>("name"));
        nameCol.setCellFactory(TextFieldTableCell.forTableColumn());
        nameCol.setOnEditCommit(event -> {
            event.getRowValue().setName(event.getNewValue());
        });

        TableColumn<Service, String> descCol = new TableColumn<>("الوصف");
        descCol.setPrefWidth(200);
        descCol.setCellValueFactory(new PropertyValueFactory<>("description"));
        descCol.setCellFactory(TextFieldTableCell.forTableColumn());
        descCol.setOnEditCommit(event -> {
            event.getRowValue().setDescription(event.getNewValue());
        });

        TableColumn<Service, Double> priceCol = new TableColumn<>("السعر الافتراضي");
        priceCol.setPrefWidth(120);
        priceCol.setCellValueFactory(new PropertyValueFactory<>("defaultPrice"));
        priceCol.setCellFactory(TextFieldTableCell.forTableColumn(new DoubleStringConverter()));
        priceCol.setOnEditCommit(event -> {
            event.getRowValue().setDefaultPrice(event.getNewValue());
        });

        TableColumn<Service, Void> actionsCol = new TableColumn<>("إجراءات");
        actionsCol.setPrefWidth(80);
        actionsCol.setCellFactory(col -> new TableCell<Service, Void>() {
            private final Button deleteBtn = new Button();

            {
                FontAwesomeIconView deleteIcon = new FontAwesomeIconView(FontAwesomeIcon.TRASH);
                deleteIcon.setSize("12px");
                deleteBtn.setGraphic(deleteIcon);
                deleteBtn.getStyleClass().add("delete-button");
                deleteBtn.setOnAction(e -> {
                    Service service = getTableView().getItems().get(getIndex());
                    getTableView().getItems().remove(service);
                });
            }

            @Override
            protected void updateItem(Void item, boolean empty) {
                super.updateItem(item, empty);
                if (empty) {
                    setGraphic(null);
                } else {
                    setGraphic(deleteBtn);
                }
            }
        });

        servicesTable.getColumns().addAll(nameCol, descCol, priceCol, actionsCol);
        servicesTable.setEditable(true);

        // نموذج إضافة خدمة جديدة
        GridPane addServiceForm = new GridPane();
        addServiceForm.setHgap(10);
        addServiceForm.setVgap(10);
        addServiceForm.getStyleClass().add("add-service-form");

        Label nameLabel = new Label("اسم الخدمة:");
        TextField nameField = new TextField();
        nameField.setPromptText("أدخل اسم الخدمة");

        Label descLabel = new Label("الوصف:");
        TextField descField = new TextField();
        descField.setPromptText("أدخل وصف الخدمة");

        Label priceLabel = new Label("السعر الافتراضي:");
        TextField priceField = new TextField();
        priceField.setPromptText("0.00");

        Button addButton = new Button("إضافة خدمة");
        FontAwesomeIconView addIcon = new FontAwesomeIconView(FontAwesomeIcon.PLUS);
        addIcon.setSize("14px");
        addButton.setGraphic(addIcon);
        addButton.getStyleClass().add("add-button");
        addButton.setOnAction(e -> {
            String name = nameField.getText().trim();
            String desc = descField.getText().trim();
            String priceText = priceField.getText().trim();

            if (!name.isEmpty() && !priceText.isEmpty()) {
                try {
                    double price = Double.parseDouble(priceText);
                    String id = String.valueOf(services.size() + 1);
                    Service newService = new Service(id, name, desc, price);
                    services.add(newService);

                    // مسح الحقول
                    nameField.clear();
                    descField.clear();
                    priceField.clear();
                } catch (NumberFormatException ex) {
                    Alert alert = new Alert(Alert.AlertType.ERROR);
                    alert.setTitle("خطأ");
                    alert.setHeaderText(null);
                    alert.setContentText("يرجى إدخال سعر صحيح");
                    alert.showAndWait();
                }
            } else {
                Alert alert = new Alert(Alert.AlertType.WARNING);
                alert.setTitle("تحذير");
                alert.setHeaderText(null);
                alert.setContentText("يرجى ملء جميع الحقول المطلوبة");
                alert.showAndWait();
            }
        });

        addServiceForm.add(nameLabel, 0, 0);
        addServiceForm.add(nameField, 1, 0);
        addServiceForm.add(descLabel, 0, 1);
        addServiceForm.add(descField, 1, 1);
        addServiceForm.add(priceLabel, 0, 2);
        addServiceForm.add(priceField, 1, 2);
        addServiceForm.add(addButton, 1, 3);

        // أزرار الحوار
        HBox buttonBox = new HBox(10);
        buttonBox.setAlignment(Pos.CENTER_RIGHT);

        Button closeButton = new Button("إغلاق");
        closeButton.setOnAction(e -> dialog.close());

        buttonBox.getChildren().add(closeButton);

        mainLayout.getChildren().addAll(titleLabel, servicesTable,
                                       new Separator(), addServiceForm, buttonBox);

        Scene scene = new Scene(mainLayout);
        scene.getStylesheets().add(getClass().getResource("/css/styles.css").toExternalForm());
        dialog.setScene(scene);
        dialog.showAndWait();
    }

    /**
     * عرض نافذة إدارة العملاء
     */
    private void showCustomerManagementDialog() {
        Stage dialog = new Stage();
        dialog.setTitle("إدارة العملاء");
        dialog.initModality(Modality.APPLICATION_MODAL);
        dialog.setWidth(800);
        dialog.setHeight(600);

        VBox mainLayout = new VBox(15);
        mainLayout.setPadding(new Insets(20));

        // العنوان
        Label titleLabel = new Label("إدارة العملاء");
        titleLabel.getStyleClass().add("dialog-title");

        // جدول العملاء
        TableView<Customer> customersTable = new TableView<>();
        customersTable.setItems(customers);
        customersTable.setMinHeight(200);
        VBox.setVgrow(customersTable, Priority.ALWAYS);

        // أعمدة الجدول
        TableColumn<Customer, String> nameCol = new TableColumn<>("الاسم");
        nameCol.setPrefWidth(120);
        nameCol.setCellValueFactory(new PropertyValueFactory<>("name"));
        nameCol.setCellFactory(TextFieldTableCell.forTableColumn());
        nameCol.setOnEditCommit(event -> {
            event.getRowValue().setName(event.getNewValue());
        });

        TableColumn<Customer, String> phoneCol = new TableColumn<>("الهاتف");
        phoneCol.setPrefWidth(100);
        phoneCol.setCellValueFactory(new PropertyValueFactory<>("phone"));
        phoneCol.setCellFactory(TextFieldTableCell.forTableColumn());
        phoneCol.setOnEditCommit(event -> {
            event.getRowValue().setPhone(event.getNewValue());
        });

        TableColumn<Customer, String> emailCol = new TableColumn<>("البريد الإلكتروني");
        emailCol.setPrefWidth(150);
        emailCol.setCellValueFactory(new PropertyValueFactory<>("email"));
        emailCol.setCellFactory(TextFieldTableCell.forTableColumn());
        emailCol.setOnEditCommit(event -> {
            event.getRowValue().setEmail(event.getNewValue());
        });

        TableColumn<Customer, String> companyCol = new TableColumn<>("الشركة");
        companyCol.setPrefWidth(120);
        companyCol.setCellValueFactory(new PropertyValueFactory<>("company"));
        companyCol.setCellFactory(TextFieldTableCell.forTableColumn());
        companyCol.setOnEditCommit(event -> {
            event.getRowValue().setCompany(event.getNewValue());
        });

        TableColumn<Customer, Void> actionsCol = new TableColumn<>("إجراءات");
        actionsCol.setPrefWidth(80);
        actionsCol.setCellFactory(col -> new TableCell<Customer, Void>() {
            private final Button deleteBtn = new Button();

            {
                FontAwesomeIconView deleteIcon = new FontAwesomeIconView(FontAwesomeIcon.TRASH);
                deleteIcon.setSize("12px");
                deleteBtn.setGraphic(deleteIcon);
                deleteBtn.getStyleClass().add("delete-button");
                deleteBtn.setOnAction(e -> {
                    Customer customer = getTableView().getItems().get(getIndex());
                    getTableView().getItems().remove(customer);
                });
            }

            @Override
            protected void updateItem(Void item, boolean empty) {
                super.updateItem(item, empty);
                if (empty) {
                    setGraphic(null);
                } else {
                    setGraphic(deleteBtn);
                }
            }
        });

        customersTable.getColumns().addAll(nameCol, phoneCol, emailCol, companyCol, actionsCol);
        customersTable.setEditable(true);

        // نموذج إضافة عميل جديد
        GridPane addCustomerForm = new GridPane();
        addCustomerForm.setHgap(10);
        addCustomerForm.setVgap(10);
        addCustomerForm.getStyleClass().add("add-service-form");

        Label nameLabel = new Label("الاسم:");
        TextField nameField = new TextField();
        nameField.setPromptText("أدخل اسم العميل");

        Label phoneLabel = new Label("الهاتف:");
        TextField phoneField = new TextField();
        phoneField.setPromptText("أدخل رقم الهاتف");

        Label emailLabel = new Label("البريد الإلكتروني:");
        TextField emailField = new TextField();
        emailField.setPromptText("أدخل البريد الإلكتروني");

        Label companyLabel = new Label("الشركة:");
        TextField companyField = new TextField();
        companyField.setPromptText("أدخل اسم الشركة (اختياري)");

        Button addButton = new Button("إضافة عميل");
        FontAwesomeIconView addIcon = new FontAwesomeIconView(FontAwesomeIcon.USER_PLUS);
        addIcon.setSize("14px");
        addButton.setGraphic(addIcon);
        addButton.getStyleClass().add("add-button");
        addButton.setOnAction(e -> {
            String name = nameField.getText().trim();
            String phone = phoneField.getText().trim();
            String email = emailField.getText().trim();
            String company = companyField.getText().trim();

            if (!name.isEmpty()) {
                String id = String.valueOf(customers.size() + 1);
                Customer newCustomer = new Customer(id, name, phone, email, "", company, "");
                customers.add(newCustomer);

                // مسح الحقول
                nameField.clear();
                phoneField.clear();
                emailField.clear();
                companyField.clear();
            } else {
                Alert alert = new Alert(Alert.AlertType.WARNING);
                alert.setTitle("تحذير");
                alert.setHeaderText(null);
                alert.setContentText("يرجى إدخال اسم العميل على الأقل");
                alert.showAndWait();
            }
        });

        addCustomerForm.add(nameLabel, 0, 0);
        addCustomerForm.add(nameField, 1, 0);
        addCustomerForm.add(phoneLabel, 2, 0);
        addCustomerForm.add(phoneField, 3, 0);
        addCustomerForm.add(emailLabel, 0, 1);
        addCustomerForm.add(emailField, 1, 1);
        addCustomerForm.add(companyLabel, 2, 1);
        addCustomerForm.add(companyField, 3, 1);
        addCustomerForm.add(addButton, 1, 2);

        // أزرار الحوار
        HBox buttonBox = new HBox(10);
        buttonBox.setAlignment(Pos.CENTER_RIGHT);

        Button closeButton = new Button("إغلاق");
        closeButton.setOnAction(e -> dialog.close());

        buttonBox.getChildren().add(closeButton);

        mainLayout.getChildren().addAll(titleLabel, customersTable,
                                       new Separator(), addCustomerForm, buttonBox);

        Scene scene = new Scene(mainLayout);
        scene.getStylesheets().add(getClass().getResource("/css/styles.css").toExternalForm());
        dialog.setScene(scene);
        dialog.showAndWait();
    }

    /**
     * عرض نافذة إضافة عميل جديد
     */
    private void showAddCustomerDialog() {
        Stage dialog = new Stage();
        dialog.setTitle("إضافة عميل جديد");
        dialog.initModality(Modality.APPLICATION_MODAL);
        dialog.setWidth(400);
        dialog.setHeight(300);

        VBox mainLayout = new VBox(15);
        mainLayout.setPadding(new Insets(20));

        // العنوان
        Label titleLabel = new Label("إضافة عميل جديد");
        titleLabel.getStyleClass().add("dialog-title");

        // نموذج البيانات
        GridPane form = new GridPane();
        form.setHgap(10);
        form.setVgap(15);

        Label nameLabel = new Label("الاسم:");
        TextField nameField = new TextField();
        nameField.setPromptText("أدخل اسم العميل");

        Label phoneLabel = new Label("الهاتف:");
        TextField phoneField = new TextField();
        phoneField.setPromptText("أدخل رقم الهاتف");

        Label emailLabel = new Label("البريد الإلكتروني:");
        TextField emailField = new TextField();
        emailField.setPromptText("أدخل البريد الإلكتروني");

        Label companyLabel = new Label("الشركة:");
        TextField companyField = new TextField();
        companyField.setPromptText("أدخل اسم الشركة (اختياري)");

        form.add(nameLabel, 0, 0);
        form.add(nameField, 1, 0);
        form.add(phoneLabel, 0, 1);
        form.add(phoneField, 1, 1);
        form.add(emailLabel, 0, 2);
        form.add(emailField, 1, 2);
        form.add(companyLabel, 0, 3);
        form.add(companyField, 1, 3);

        // أزرار الحوار
        HBox buttonBox = new HBox(10);
        buttonBox.setAlignment(Pos.CENTER_RIGHT);

        Button addButton = new Button("إضافة");
        addButton.getStyleClass().add("add-button");
        addButton.setOnAction(e -> {
            String name = nameField.getText().trim();
            String phone = phoneField.getText().trim();
            String email = emailField.getText().trim();
            String company = companyField.getText().trim();

            if (!name.isEmpty()) {
                String id = String.valueOf(customers.size() + 1);
                Customer newCustomer = new Customer(id, name, phone, email, "", company, "");
                customers.add(newCustomer);
                customerComboBox.getSelectionModel().select(newCustomer);
                dialog.close();
            } else {
                Alert alert = new Alert(Alert.AlertType.WARNING);
                alert.setTitle("تحذير");
                alert.setHeaderText(null);
                alert.setContentText("يرجى إدخال اسم العميل على الأقل");
                alert.showAndWait();
            }
        });

        Button cancelButton = new Button("إلغاء");
        cancelButton.setOnAction(e -> dialog.close());

        buttonBox.getChildren().addAll(addButton, cancelButton);

        mainLayout.getChildren().addAll(titleLabel, form, buttonBox);

        Scene scene = new Scene(mainLayout);
        scene.getStylesheets().add(getClass().getResource("/css/styles.css").toExternalForm());
        dialog.setScene(scene);
        dialog.showAndWait();
    }

    /**
     * عرض تقارير المبيعات
     */
    private void showSalesReportsDialog() {
        Stage dialog = new Stage();
        dialog.setTitle("📊 تقارير المبيعات");
        dialog.initModality(Modality.APPLICATION_MODAL);
        dialog.setWidth(900);
        dialog.setHeight(700);

        VBox mainLayout = new VBox(20);
        mainLayout.setPadding(new Insets(20));

        // العنوان
        Label titleLabel = new Label("📊 تقارير وتحليلات المبيعات");
        titleLabel.getStyleClass().add("dialog-title");

        // شريط أدوات التقارير
        HBox reportsToolbar = new HBox(15);
        reportsToolbar.setAlignment(Pos.CENTER_LEFT);
        reportsToolbar.setPadding(new Insets(10));

        Button dailyReportBtn = new Button("تقرير يومي");
        FontAwesomeIconView dailyIcon = new FontAwesomeIconView(FontAwesomeIcon.CALENDAR);
        dailyIcon.setSize("14px");
        dailyReportBtn.setGraphic(dailyIcon);
        dailyReportBtn.getStyleClass().add("report-button");

        Button monthlyReportBtn = new Button("تقرير شهري");
        FontAwesomeIconView monthlyIcon = new FontAwesomeIconView(FontAwesomeIcon.CALENDAR_ALT);
        monthlyIcon.setSize("14px");
        monthlyReportBtn.setGraphic(monthlyIcon);
        monthlyReportBtn.getStyleClass().add("report-button");

        Button customerReportBtn = new Button("تقرير العملاء");
        FontAwesomeIconView customerIcon = new FontAwesomeIconView(FontAwesomeIcon.USERS);
        customerIcon.setSize("14px");
        customerReportBtn.setGraphic(customerIcon);
        customerReportBtn.getStyleClass().add("report-button");

        Button chartBtn = new Button("الرسوم البيانية");
        FontAwesomeIconView chartIcon = new FontAwesomeIconView(FontAwesomeIcon.PIE_CHART);
        chartIcon.setSize("14px");
        chartBtn.setGraphic(chartIcon);
        chartBtn.getStyleClass().add("chart-button");

        reportsToolbar.getChildren().addAll(dailyReportBtn, monthlyReportBtn, customerReportBtn, chartBtn);

        // منطقة عرض التقارير
        TabPane reportsTabPane = new TabPane();

        // تبويب الإحصائيات السريعة
        Tab statsTab = new Tab("الإحصائيات");
        VBox statsContent = createSalesStatsContent();
        statsTab.setContent(statsContent);
        statsTab.setClosable(false);

        // تبويب الفواتير المحفوظة
        Tab invoicesTab = new Tab("الفواتير المحفوظة");
        VBox invoicesContent = createSavedInvoicesContent();
        invoicesTab.setContent(invoicesContent);
        invoicesTab.setClosable(false);

        // تبويب الرسوم البيانية
        Tab chartsTab = new Tab("الرسوم البيانية");
        VBox chartsContent = createChartsContent();
        chartsTab.setContent(chartsContent);
        chartsTab.setClosable(false);

        reportsTabPane.getTabs().addAll(statsTab, invoicesTab, chartsTab);

        // زر الإغلاق
        Button closeButton = new Button("إغلاق");
        closeButton.setOnAction(e -> dialog.close());

        HBox buttonBox = new HBox();
        buttonBox.setAlignment(Pos.CENTER_RIGHT);
        buttonBox.getChildren().add(closeButton);

        mainLayout.getChildren().addAll(titleLabel, reportsToolbar, reportsTabPane, buttonBox);

        Scene scene = new Scene(mainLayout);
        scene.getStylesheets().add(getClass().getResource("/css/styles.css").toExternalForm());
        dialog.setScene(scene);
        dialog.showAndWait();
    }

    /**
     * إنشاء محتوى الإحصائيات
     */
    private VBox createSalesStatsContent() {
        VBox content = new VBox(20);
        content.setPadding(new Insets(20));

        // إحصائيات سريعة
        HBox statsBox = new HBox(20);
        statsBox.setAlignment(Pos.CENTER);

        // إجمالي الفواتير
        VBox totalInvoicesBox = createStatCard("إجمالي الفواتير", "0", "#3498db");

        // إجمالي المبيعات
        VBox totalSalesBox = createStatCard("إجمالي المبيعات", "0.00 ج.م", "#2ecc71");

        // متوسط الفاتورة
        VBox avgInvoiceBox = createStatCard("متوسط الفاتورة", "0.00 ج.م", "#f39c12");

        // أفضل عميل
        VBox topCustomerBox = createStatCard("أفضل عميل", "غير محدد", "#9b59b6");

        statsBox.getChildren().addAll(totalInvoicesBox, totalSalesBox, avgInvoiceBox, topCustomerBox);

        // جدول أفضل الخدمات
        Label servicesLabel = new Label("أفضل الخدمات:");
        servicesLabel.getStyleClass().add("section-title");

        TableView<Service> topServicesTable = new TableView<>();
        topServicesTable.setItems(services);
        topServicesTable.setMinHeight(200);
        VBox.setVgrow(topServicesTable, Priority.ALWAYS);

        TableColumn<Service, String> serviceNameCol = new TableColumn<>("اسم الخدمة");
        serviceNameCol.setPrefWidth(200);
        serviceNameCol.setCellValueFactory(new PropertyValueFactory<>("name"));

        TableColumn<Service, String> serviceDescCol = new TableColumn<>("الوصف");
        serviceDescCol.setPrefWidth(300);
        serviceDescCol.setCellValueFactory(new PropertyValueFactory<>("description"));

        TableColumn<Service, Double> servicePriceCol = new TableColumn<>("السعر");
        servicePriceCol.setPrefWidth(100);
        servicePriceCol.setCellValueFactory(new PropertyValueFactory<>("price"));
        servicePriceCol.setCellFactory(col -> new TableCell<Service, Double>() {
            @Override
            protected void updateItem(Double item, boolean empty) {
                super.updateItem(item, empty);
                if (empty || item == null) {
                    setText(null);
                } else {
                    setText(decimalFormat.format(item) + " ج.م");
                }
            }
        });

        topServicesTable.getColumns().addAll(serviceNameCol, serviceDescCol, servicePriceCol);

        content.getChildren().addAll(statsBox, servicesLabel, topServicesTable);
        return content;
    }

    /**
     * إنشاء محتوى الفواتير المحفوظة
     */
    private VBox createSavedInvoicesContent() {
        VBox content = new VBox(15);
        content.setPadding(new Insets(20));

        Label titleLabel = new Label("الفواتير المحفوظة:");
        titleLabel.getStyleClass().add("section-title");

        // شريط البحث
        HBox searchBox = new HBox(10);
        searchBox.setAlignment(Pos.CENTER_LEFT);

        TextField searchField = new TextField();
        searchField.setPromptText("ابحث في الفواتير...");
        searchField.setPrefWidth(200);

        Button searchBtn = new Button("بحث");
        FontAwesomeIconView searchIcon = new FontAwesomeIconView(FontAwesomeIcon.SEARCH);
        searchIcon.setSize("12px");
        searchBtn.setGraphic(searchIcon);

        searchBox.getChildren().addAll(new Label("البحث:"), searchField, searchBtn);

        // جدول الفواتير (فارغ حالياً)
        TableView<String> invoicesTable = new TableView<>();
        invoicesTable.setMinHeight(300);
        VBox.setVgrow(invoicesTable, Priority.ALWAYS);

        TableColumn<String, String> invoiceNoCol = new TableColumn<>("رقم الفاتورة");
        invoiceNoCol.setPrefWidth(120);

        TableColumn<String, String> customerCol = new TableColumn<>("العميل");
        customerCol.setPrefWidth(150);

        TableColumn<String, String> dateCol = new TableColumn<>("التاريخ");
        dateCol.setPrefWidth(100);

        TableColumn<String, String> totalCol = new TableColumn<>("الإجمالي");
        totalCol.setPrefWidth(100);

        TableColumn<String, String> statusCol = new TableColumn<>("الحالة");
        statusCol.setPrefWidth(100);

        TableColumn<String, Void> actionsCol = new TableColumn<>("الإجراءات");
        actionsCol.setPrefWidth(150);

        invoicesTable.getColumns().addAll(invoiceNoCol, customerCol, dateCol, totalCol, statusCol, actionsCol);

        // رسالة عدم وجود فواتير
        Label noInvoicesLabel = new Label("لا توجد فواتير محفوظة بعد");
        noInvoicesLabel.getStyleClass().add("table-placeholder");
        invoicesTable.setPlaceholder(noInvoicesLabel);

        content.getChildren().addAll(titleLabel, searchBox, invoicesTable);
        return content;
    }

    /**
     * إنشاء محتوى الرسوم البيانية
     */
    private VBox createChartsContent() {
        VBox content = new VBox(20);
        content.setPadding(new Insets(20));

        Label titleLabel = new Label("الرسوم البيانية والتحليلات:");
        titleLabel.getStyleClass().add("section-title");

        // منطقة الرسوم البيانية
        VBox chartsArea = new VBox(15);
        chartsArea.setAlignment(Pos.CENTER);
        chartsArea.setPadding(new Insets(30));
        chartsArea.setStyle("-fx-background-color: #f8f9fa; -fx-border-color: #dee2e6; -fx-border-width: 1; -fx-border-radius: 5;");

        FontAwesomeIconView chartIcon = new FontAwesomeIconView(FontAwesomeIcon.BAR_CHART);
        chartIcon.setSize("48px");
        chartIcon.setStyle("-fx-fill: #6c757d;");

        Label chartLabel = new Label("الرسوم البيانية");
        chartLabel.setStyle("-fx-font-size: 18px; -fx-font-weight: bold; -fx-text-fill: #495057;");

        Label chartDesc = new Label("سيتم عرض الرسوم البيانية للمبيعات هنا\nبما في ذلك مخططات دائرية وأعمدة بيانية");
        chartDesc.setStyle("-fx-text-alignment: center; -fx-text-fill: #6c757d;");

        chartsArea.getChildren().addAll(chartIcon, chartLabel, chartDesc);

        content.getChildren().addAll(titleLabel, chartsArea);
        return content;
    }

    /**
     * إنشاء بطاقة إحصائية
     */
    private VBox createStatCard(String title, String value, String color) {
        VBox card = new VBox(5);
        card.setAlignment(Pos.CENTER);
        card.setPadding(new Insets(15));
        card.getStyleClass().add("stat-card");
        card.setStyle("-fx-border-color: " + color + "; -fx-border-width: 2;");

        Label titleLabel = new Label(title);
        titleLabel.getStyleClass().add("stat-title");

        Label valueLabel = new Label(value);
        valueLabel.getStyleClass().add("stat-value");
        valueLabel.setStyle("-fx-text-fill: " + color + ";");

        card.getChildren().addAll(titleLabel, valueLabel);
        return card;
    }

    /**
     * توليد رقم فاتورة جديد
     */
    private String generateInvoiceNumber() {
        return "INV-" + System.currentTimeMillis() % 100000;
    }

    /**
     * حفظ الفاتورة في قاعدة البيانات (محاكاة)
     */
    private void saveInvoiceToDatabase(String invoiceNo) {
        // هنا يمكن إضافة كود حفظ الفاتورة في قاعدة البيانات
        System.out.println("تم حفظ الفاتورة رقم: " + invoiceNo);
        System.out.println("العميل: " + (customerComboBox.getValue() != null ? customerComboBox.getValue().getName() : "غير محدد"));
        System.out.println("عدد الأصناف: " + invoiceItems.size());

        // حساب الإجمالي
        double total = invoiceItems.stream()
            .mapToDouble(item -> item.getTotalValue() != null ? item.getTotalValue() : 0.0)
            .sum();
        System.out.println("الإجمالي: " + decimalFormat.format(total) + " ج.م");
    }

    /**
     * عرض معاينة الطباعة
     */
    private void showPrintPreview(String action) {
        Stage printStage = new Stage();
        printStage.setTitle("معاينة " + action);
        printStage.initModality(Modality.APPLICATION_MODAL);
        printStage.setWidth(800);
        printStage.setHeight(900);

        VBox printLayout = new VBox(20);
        printLayout.setPadding(new Insets(30));
        printLayout.setStyle("-fx-background-color: white;");

        // رأس الفاتورة
        VBox header = createPrintHeader();

        // معلومات الفاتورة
        VBox invoiceInfo = createPrintInvoiceInfo();

        // جدول الأصناف للطباعة
        VBox itemsSection = createPrintItemsSection();

        // الإجمالي
        VBox totalSection = createPrintTotalSection();

        // تذييل الفاتورة
        VBox footer = createPrintFooter();

        printLayout.getChildren().addAll(header, invoiceInfo, itemsSection, totalSection, footer);

        // أزرار الطباعة
        HBox buttonBox = new HBox(15);
        buttonBox.setAlignment(Pos.CENTER);
        buttonBox.setPadding(new Insets(20, 0, 0, 0));

        Button printBtn = new Button("طباعة");
        FontAwesomeIconView printIcon = new FontAwesomeIconView(FontAwesomeIcon.PRINT);
        printIcon.setSize("14px");
        printBtn.setGraphic(printIcon);
        printBtn.getStyleClass().add("print-button");
        printBtn.setOnAction(e -> {
            showPlaceholderDialog("طباعة", "تم إرسال الفاتورة للطباعة!");
            printStage.close();
        });

        Button savePdfBtn = new Button("حفظ PDF");
        FontAwesomeIconView pdfIcon = new FontAwesomeIconView(FontAwesomeIcon.FILE_PDF_ALT);
        pdfIcon.setSize("14px");
        savePdfBtn.setGraphic(pdfIcon);
        savePdfBtn.getStyleClass().add("save-button");
        savePdfBtn.setOnAction(e -> {
            showPlaceholderDialog("حفظ PDF", "تم حفظ الفاتورة كملف PDF!");
            printStage.close();
        });

        Button closeBtn = new Button("إغلاق");
        closeBtn.setOnAction(e -> printStage.close());

        buttonBox.getChildren().addAll(printBtn, savePdfBtn, closeBtn);

        ScrollPane scrollPane = new ScrollPane(printLayout);
        scrollPane.setFitToWidth(true);
        scrollPane.setStyle("-fx-background-color: #f5f5f5;");

        VBox mainContainer = new VBox();
        mainContainer.getChildren().addAll(scrollPane, buttonBox);

        Scene scene = new Scene(mainContainer);
        scene.getStylesheets().add(getClass().getResource("/css/styles.css").toExternalForm());
        printStage.setScene(scene);
        printStage.showAndWait();
    }

    /**
     * إنشاء رأس الفاتورة للطباعة
     */
    private VBox createPrintHeader() {
        VBox header = new VBox(10);
        header.setAlignment(Pos.CENTER);
        header.setStyle("-fx-border-color: #000000; -fx-border-width: 0 0 2 0; -fx-padding: 0 0 15 0;");

        Label companyName = new Label("شركة الزجاج والألومنيوم المتقدمة");
        companyName.setStyle("-fx-font-size: 24px; -fx-font-weight: bold;");

        Label companyInfo = new Label("العنوان: شارع الصناعة، المنطقة الصناعية | الهاتف: 01234567890 | البريد: <EMAIL>");
        companyInfo.setStyle("-fx-font-size: 12px; -fx-text-fill: #666666;");

        Label invoiceTitle = new Label("فاتورة مبيعات");
        invoiceTitle.setStyle("-fx-font-size: 20px; -fx-font-weight: bold; -fx-text-fill: #2c3e50;");

        header.getChildren().addAll(companyName, companyInfo, invoiceTitle);
        return header;
    }

    /**
     * إنشاء معلومات الفاتورة للطباعة
     */
    private VBox createPrintInvoiceInfo() {
        VBox infoSection = new VBox(10);

        HBox infoRow = new HBox(50);
        infoRow.setAlignment(Pos.CENTER_LEFT);

        VBox leftInfo = new VBox(5);
        leftInfo.getChildren().addAll(
            new Label("رقم الفاتورة: " + generateInvoiceNumber()),
            new Label("التاريخ: " + java.time.LocalDate.now().toString()),
            new Label("الوقت: " + java.time.LocalTime.now().toString().substring(0, 8))
        );

        VBox rightInfo = new VBox(5);
        String customerName = customerComboBox.getValue() != null ? customerComboBox.getValue().getName() : "عميل نقدي";
        String customerPhone = customerComboBox.getValue() != null ? customerComboBox.getValue().getPhone() : "";
        rightInfo.getChildren().addAll(
            new Label("العميل: " + customerName),
            new Label("الهاتف: " + customerPhone),
            new Label("حالة الفاتورة: مؤكدة")
        );

        infoRow.getChildren().addAll(leftInfo, rightInfo);
        infoSection.getChildren().add(infoRow);

        return infoSection;
    }

    /**
     * إنشاء قسم الأصناف للطباعة
     */
    private VBox createPrintItemsSection() {
        VBox itemsSection = new VBox(10);

        Label itemsTitle = new Label("تفاصيل الأصناف:");
        itemsTitle.setStyle("-fx-font-size: 16px; -fx-font-weight: bold;");

        // رأس الجدول
        HBox tableHeader = new HBox();
        tableHeader.setStyle("-fx-background-color: #ecf0f1; -fx-padding: 10; -fx-border-color: #bdc3c7; -fx-border-width: 1;");

        Label[] headers = {
            new Label("#"),
            new Label("نوع الخدمة"),
            new Label("السمك"),
            new Label("الطول"),
            new Label("العرض"),
            new Label("م²"),
            new Label("العدد"),
            new Label("إجمالي الكمية"),
            new Label("السعر"),
            new Label("الإجمالي")
        };

        double[] widths = {30, 120, 60, 70, 70, 70, 50, 90, 70, 90};

        for (int i = 0; i < headers.length; i++) {
            headers[i].setPrefWidth(widths[i]);
            headers[i].setStyle("-fx-font-weight: bold; -fx-text-alignment: center;");
            tableHeader.getChildren().add(headers[i]);
        }

        VBox tableRows = new VBox();

        // صفوف البيانات
        for (int i = 0; i < invoiceItems.size(); i++) {
            InvoiceItem item = invoiceItems.get(i);
            if (item.getServiceType() != null && !item.getServiceType().trim().isEmpty()) {
                HBox row = createPrintTableRow(i + 1, item, widths);
                tableRows.getChildren().add(row);
            }
        }

        itemsSection.getChildren().addAll(itemsTitle, tableHeader, tableRows);
        return itemsSection;
    }

    /**
     * إنشاء صف في جدول الطباعة
     */
    private HBox createPrintTableRow(int index, InvoiceItem item, double[] widths) {
        HBox row = new HBox();
        row.setStyle("-fx-border-color: #bdc3c7; -fx-border-width: 0 1 1 1; -fx-padding: 8;");

        Label[] cells = {
            new Label(String.valueOf(index)),
            new Label(item.getServiceType() != null ? item.getServiceType() : ""),
            new Label(item.getThickness() != null ? item.getThickness().toString() : ""),
            new Label(item.getLength() != null ? decimalFormat.format(item.getLength()) : ""),
            new Label(item.getWidth() != null ? decimalFormat.format(item.getWidth()) : ""),
            new Label(item.getSquareMeters() != null ? decimalFormat.format(item.getSquareMeters()) : ""),
            new Label(item.getQuantity() != null ? item.getQuantity().toString() : ""),
            new Label(item.getTotalQuantity() != null ? decimalFormat.format(item.getTotalQuantity()) : ""),
            new Label(item.getPrice() != null ? decimalFormat.format(item.getPrice()) : ""),
            new Label(item.getTotalValue() != null ? decimalFormat.format(item.getTotalValue()) + " ج.م" : "")
        };

        for (int i = 0; i < cells.length; i++) {
            cells[i].setPrefWidth(widths[i]);
            cells[i].setStyle("-fx-text-alignment: center;");
            row.getChildren().add(cells[i]);
        }

        return row;
    }

    /**
     * إنشاء قسم الإجمالي للطباعة
     */
    private VBox createPrintTotalSection() {
        VBox totalSection = new VBox(10);
        totalSection.setAlignment(Pos.CENTER_RIGHT);
        totalSection.setStyle("-fx-border-color: #34495e; -fx-border-width: 2 0 0 0; -fx-padding: 15 0 0 0;");

        // حساب الإجماليات
        double subtotal = invoiceItems.stream()
            .mapToDouble(item -> item.getTotalValue() != null ? item.getTotalValue() : 0.0)
            .sum();

        double discount = 0.0; // يمكن إضافة حقل الخصم لاحقاً
        double total = subtotal - discount;

        HBox subtotalRow = new HBox();
        subtotalRow.setAlignment(Pos.CENTER_RIGHT);
        Label subtotalLabel = new Label("المجموع الفرعي: " + decimalFormat.format(subtotal) + " ج.م");
        subtotalLabel.setStyle("-fx-font-size: 14px;");
        subtotalRow.getChildren().add(subtotalLabel);

        HBox discountRow = new HBox();
        discountRow.setAlignment(Pos.CENTER_RIGHT);
        Label discountLabel = new Label("الخصم: " + decimalFormat.format(discount) + " ج.م");
        discountLabel.setStyle("-fx-font-size: 14px;");
        discountRow.getChildren().add(discountLabel);

        HBox totalRow = new HBox();
        totalRow.setAlignment(Pos.CENTER_RIGHT);
        totalRow.setStyle("-fx-background-color: #ecf0f1; -fx-padding: 10; -fx-border-color: #34495e; -fx-border-width: 1;");
        Label totalLabel = new Label("الإجمالي النهائي: " + decimalFormat.format(total) + " ج.م");
        totalLabel.setStyle("-fx-font-size: 18px; -fx-font-weight: bold; -fx-text-fill: #2c3e50;");
        totalRow.getChildren().add(totalLabel);

        totalSection.getChildren().addAll(subtotalRow, discountRow, totalRow);
        return totalSection;
    }

    /**
     * إنشاء تذييل الفاتورة للطباعة
     */
    private VBox createPrintFooter() {
        VBox footer = new VBox(10);
        footer.setAlignment(Pos.CENTER);
        footer.setStyle("-fx-border-color: #000000; -fx-border-width: 2 0 0 0; -fx-padding: 15 0 0 0;");

        Label thanksLabel = new Label("شكراً لتعاملكم معنا");
        thanksLabel.setStyle("-fx-font-size: 16px; -fx-font-weight: bold;");

        Label footerInfo = new Label("هذه الفاتورة صادرة إلكترونياً ولا تحتاج إلى توقيع");
        footerInfo.setStyle("-fx-font-size: 10px; -fx-text-fill: #666666;");

        Label printDate = new Label("تاريخ الطباعة: " + java.time.LocalDateTime.now().toString().substring(0, 19));
        printDate.setStyle("-fx-font-size: 10px; -fx-text-fill: #666666;");

        footer.getChildren().addAll(thanksLabel, footerInfo, printDate);
        return footer;
    }
}
