package com.accounting.controller;

import com.accounting.model.InvoiceItem;
import com.accounting.model.Service;
import com.accounting.model.Customer;
import com.accounting.model.SavedInvoice;
import com.accounting.model.ServiceReport;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.beans.property.SimpleStringProperty;
import java.util.Map;
import java.util.HashMap;
import com.accounting.util.PrintUtils;
import javafx.geometry.Insets;
import javafx.geometry.Pos;
import javafx.scene.Scene;
import javafx.scene.control.*;
import javafx.scene.control.cell.PropertyValueFactory;
import javafx.scene.control.cell.TextFieldTableCell;
import javafx.scene.control.cell.ComboBoxTableCell;
import javafx.scene.layout.*;
import javafx.stage.Modality;
import javafx.stage.Stage;
import javafx.util.converter.DoubleStringConverter;
import javafx.util.converter.IntegerStringConverter;
import de.jensd.fx.glyphs.fontawesome.FontAwesomeIcon;
import de.jensd.fx.glyphs.fontawesome.FontAwesomeIconView;

import java.text.DecimalFormat;

/**
 * متحكم موديول المبيعات
 * Sales Module Controller
 */
public class SalesController {
    
    private ObservableList<Service> services = FXCollections.observableArrayList();
    private ObservableList<Customer> customers = FXCollections.observableArrayList();
    private ObservableList<InvoiceItem> invoiceItems = FXCollections.observableArrayList();
    private ObservableList<SavedInvoice> savedInvoices = FXCollections.observableArrayList();
    private ObservableList<ServiceReport> serviceReports = FXCollections.observableArrayList();
    private DecimalFormat decimalFormat = new DecimalFormat("#,##0.00");
    private static int invoiceCounter = 1; // عداد الفواتير للترقيم التلقائي

    // مراجع للملخص
    private Label subtotalValueLabel;
    private TextField discountField;
    private Label discountValueLabel;
    private Label totalValueLabel;

    // مراجع للفاتورة
    private ComboBox<Customer> customerComboBox;
    
    public SalesController() {
        // إضافة بعض الخدمات الافتراضية
        services.addAll(
            new Service("1", "قص ليزر", "قص المعادن بالليزر", 50.0),
            new Service("2", "ثني معادن", "ثني وتشكيل المعادن", 30.0),
            new Service("3", "لحام", "لحام القطع المعدنية", 40.0),
            new Service("4", "طلاء", "طلاء وتشطيب المعادن", 25.0),
            new Service("5", "تفريز CNC", "تفريز دقيق بالكمبيوتر", 75.0),
            new Service("6", "خراطة", "خراطة القطع المعدنية", 35.0)
        );

        // إضافة بعض العملاء الافتراضيين
        customers.addAll(
            new Customer("1", "أحمد محمد", "01234567890", "<EMAIL>", "القاهرة", "شركة الأمل", "عميل مميز"),
            new Customer("2", "فاطمة علي", "01098765432", "<EMAIL>", "الجيزة", "مؤسسة النور", ""),
            new Customer("3", "محمد حسن", "01555666777", "<EMAIL>", "الإسكندرية", "شركة المستقبل", "عميل جديد"),
            new Customer("4", "سارة أحمد", "01777888999", "<EMAIL>", "المنصورة", "", "عميل فردي")
        );

        // إضافة بعض الفواتير التجريبية
        addSampleInvoices();

        // إضافة صف فارغ للبدء
        addEmptyRow();
    }

    /**
     * إضافة فواتير تجريبية
     */
    private void addSampleInvoices() {
        // فاتورة تجريبية 1
        ObservableList<InvoiceItem> items1 = FXCollections.observableArrayList();
        InvoiceItem item1 = new InvoiceItem(false);
        item1.setServiceType("قص ليزر");
        item1.setThickness(6);
        item1.setLength(1000);
        item1.setWidth(500);
        item1.setQuantity(2);
        item1.setPrice(50.0);
        item1.setDescription("قص ليزر للزجاج");
        items1.add(item1);

        SavedInvoice invoice1 = new SavedInvoice("INV-0001", customers.get(0), items1, 50.0, 0.0, 50.0, "فاتورة تجريبية");
        invoice1.setDateTime(java.time.LocalDateTime.now().minusDays(2));
        savedInvoices.add(invoice1);

        // فاتورة تجريبية 2
        ObservableList<InvoiceItem> items2 = FXCollections.observableArrayList();
        InvoiceItem item2 = new InvoiceItem(false);
        item2.setServiceType("ثني معادن");
        item2.setThickness(8);
        item2.setLength(1200);
        item2.setWidth(600);
        item2.setQuantity(3);
        item2.setPrice(30.0);
        item2.setDescription("ثني الألومنيوم");
        items2.add(item2);

        SavedInvoice invoice2 = new SavedInvoice("INV-0002", customers.get(1), items2, 64.8, 5.0, 59.8, "فاتورة تجريبية 2");
        invoice2.setDateTime(java.time.LocalDateTime.now().minusDays(1));
        savedInvoices.add(invoice2);

        // تحديث العداد
        invoiceCounter = 3;
    }

    /**
     * إضافة صف فارغ للبدء
     */
    private void addEmptyRow() {
        InvoiceItem emptyItem = new InvoiceItem();
        // ترك الصف فارغ ليقوم المستخدم بملئه
        invoiceItems.add(emptyItem);
    }
    
    /**
     * إنشاء واجهة موديول المبيعات
     */
    public VBox createSalesModule() {
        VBox mainContainer = new VBox(20);
        mainContainer.setPadding(new Insets(20));
        mainContainer.getStyleClass().add("sales-module");
        
        // العنوان الرئيسي
        Label titleLabel = new Label("🛒 موديول المبيعات");
        titleLabel.getStyleClass().add("module-title");
        
        // شريط الأدوات
        HBox toolbar = createToolbar();
        
        // منطقة الفاتورة
        VBox invoiceArea = createInvoiceArea();
        
        mainContainer.getChildren().addAll(titleLabel, toolbar, invoiceArea);
        return mainContainer;
    }
    
    /**
     * إنشاء شريط الأدوات
     */
    private HBox createToolbar() {
        HBox toolbar = new HBox(15);
        toolbar.setAlignment(Pos.CENTER_LEFT);
        toolbar.setPadding(new Insets(10));
        toolbar.getStyleClass().add("toolbar");
        
        // زر إدارة الخدمات
        Button servicesBtn = createToolbarButton("الخدمات", FontAwesomeIcon.COG, this::showServicesDialog);
        
        // زر إدارة العملاء
        Button customersBtn = createToolbarButton("العملاء", FontAwesomeIcon.USERS, this::showCustomersDialog);
        
        // زر تقارير المبيعات
        Button reportsBtn = createToolbarButton("التقارير", FontAwesomeIcon.BAR_CHART, this::showReportsDialog);
        
        // فاصل
        Separator separator = new Separator();
        separator.setOrientation(javafx.geometry.Orientation.VERTICAL);
        
        // زر فاتورة جديدة
        Button newInvoiceBtn = createToolbarButton("فاتورة جديدة", FontAwesomeIcon.PLUS, this::newInvoice);
        
        // زر حفظ الفاتورة
        Button saveInvoiceBtn = createToolbarButton("حفظ الفاتورة", FontAwesomeIcon.SAVE, this::saveInvoice);

        // زر حفظ PDF
        Button savePdfBtn = createToolbarButton("حفظ PDF", FontAwesomeIcon.FILE_PDF_ALT, this::savePdf);

        // زر طباعة
        Button printBtn = createToolbarButton("طباعة", FontAwesomeIcon.PRINT, this::printInvoice);

        toolbar.getChildren().addAll(servicesBtn, customersBtn, reportsBtn, separator,
                                   newInvoiceBtn, saveInvoiceBtn, savePdfBtn, printBtn);
        return toolbar;
    }
    
    /**
     * إنشاء زر شريط الأدوات
     */
    private Button createToolbarButton(String text, FontAwesomeIcon icon, Runnable action) {
        Button button = new Button(text);
        FontAwesomeIconView iconView = new FontAwesomeIconView(icon);
        iconView.setSize("16px");
        button.setGraphic(iconView);
        button.getStyleClass().add("toolbar-button");
        button.setOnAction(e -> action.run());
        return button;
    }
    
    /**
     * إنشاء منطقة الفاتورة
     */
    private VBox createInvoiceArea() {
        VBox invoiceArea = new VBox(15);
        invoiceArea.getStyleClass().add("invoice-area");

        // معلومات الفاتورة
        HBox invoiceHeader = createInvoiceHeader();

        // جدول عناصر الفاتورة
        TableView<InvoiceItem> invoiceTable = createInvoiceTable();

        // أزرار إدارة الصفوف
        HBox rowControls = createRowControls(invoiceTable);

        // منطقة الخصم والملاحظات
        VBox discountNotesArea = createDiscountNotesArea();

        // ملخص الفاتورة
        VBox invoiceSummary = createInvoiceSummary();

        // وضع كل شيء في ScrollPane لدعم التمرير
        VBox content = new VBox(15);
        content.getChildren().addAll(invoiceHeader, invoiceTable, rowControls, discountNotesArea, invoiceSummary);

        ScrollPane scrollPane = new ScrollPane(content);
        scrollPane.setFitToWidth(true);
        scrollPane.setVbarPolicy(ScrollPane.ScrollBarPolicy.AS_NEEDED);
        scrollPane.setHbarPolicy(ScrollPane.ScrollBarPolicy.NEVER);
        scrollPane.getStyleClass().add("invoice-scroll-pane");

        invoiceArea.getChildren().add(scrollPane);
        VBox.setVgrow(scrollPane, Priority.ALWAYS);

        return invoiceArea;
    }
    
    /**
     * إنشاء رأس الفاتورة
     */
    private HBox createInvoiceHeader() {
        HBox header = new HBox(25);
        header.setAlignment(Pos.CENTER_LEFT);
        header.setPadding(new Insets(15));
        header.getStyleClass().add("invoice-header");

        // رقم الفاتورة
        VBox invoiceNoBox = new VBox(5);
        Label invoiceNoLabel = new Label("رقم الفاتورة:");
        invoiceNoLabel.getStyleClass().add("field-label");
        TextField invoiceNoField = new TextField("INV-" + System.currentTimeMillis() % 10000);
        invoiceNoField.setPrefWidth(130);
        invoiceNoField.getStyleClass().add("invoice-field");
        invoiceNoBox.getChildren().addAll(invoiceNoLabel, invoiceNoField);

        // تاريخ الفاتورة
        VBox dateBox = new VBox(5);
        Label dateLabel = new Label("التاريخ:");
        dateLabel.getStyleClass().add("field-label");
        DatePicker datePicker = new DatePicker();
        datePicker.setValue(java.time.LocalDate.now());
        datePicker.setPrefWidth(140);
        datePicker.getStyleClass().add("invoice-field");
        dateBox.getChildren().addAll(dateLabel, datePicker);

        // العميل
        VBox customerBox = new VBox(5);
        Label customerLabel = new Label("العميل:");
        customerLabel.getStyleClass().add("field-label");
        customerComboBox = new ComboBox<>();
        customerComboBox.setItems(customers);
        customerComboBox.setPrefWidth(200);
        customerComboBox.setEditable(false);
        customerComboBox.setPromptText("اختر عميل من القائمة");
        customerComboBox.getStyleClass().add("invoice-field");

        // زر إضافة عميل جديد
        Button addCustomerBtn = new Button("إضافة عميل");
        FontAwesomeIconView addCustomerIcon = new FontAwesomeIconView(FontAwesomeIcon.USER_PLUS);
        addCustomerIcon.setSize("12px");
        addCustomerBtn.setGraphic(addCustomerIcon);
        addCustomerBtn.getStyleClass().add("small-button");
        addCustomerBtn.setOnAction(e -> showAddCustomerDialog());

        HBox customerHBox = new HBox(5);
        customerHBox.getChildren().addAll(customerComboBox, addCustomerBtn);
        customerBox.getChildren().addAll(customerLabel, customerHBox);

        // حالة الفاتورة
        VBox statusBox = new VBox(5);
        Label statusLabel = new Label("حالة الفاتورة:");
        statusLabel.getStyleClass().add("field-label");
        ComboBox<String> statusCombo = new ComboBox<>();
        statusCombo.getItems().addAll("مسودة", "مؤكدة", "مدفوعة", "ملغية");
        statusCombo.setValue("مسودة");
        statusCombo.setPrefWidth(120);
        statusCombo.getStyleClass().add("invoice-field");
        statusBox.getChildren().addAll(statusLabel, statusCombo);

        header.getChildren().addAll(invoiceNoBox, dateBox, customerBox, statusBox);

        return header;
    }
    
    /**
     * إنشاء جدول عناصر الفاتورة
     */
    private TableView<InvoiceItem> createInvoiceTable() {
        TableView<InvoiceItem> table = new TableView<>();
        table.setItems(invoiceItems);
        table.setEditable(true);
        table.getStyleClass().add("invoice-table");
        // إزالة قيد الارتفاع ليتوسع الجدول تلقائياً
        table.setMinHeight(200);
        VBox.setVgrow(table, Priority.ALWAYS);
        table.setRowFactory(tv -> {
            TableRow<InvoiceItem> row = new TableRow<>();
            row.setPrefHeight(45); // ارتفاع الصف
            return row;
        });

        // إضافة placeholder عندما يكون الجدول فارغ
        Label placeholder = new Label("انقر على 'إضافة صف يدوي' لبدء إنشاء الفاتورة");
        placeholder.getStyleClass().add("table-placeholder");
        table.setPlaceholder(placeholder);
        
        // العمود: ت
        TableColumn<InvoiceItem, String> indexCol = new TableColumn<>("ت");
        indexCol.setPrefWidth(40);
        indexCol.setCellFactory(col -> {
            TableCell<InvoiceItem, String> cell = new TableCell<>();
            cell.textProperty().bind(cell.indexProperty().add(1).asString());
            return cell;
        });
        
        // العمود: نوع الخدمة
        TableColumn<InvoiceItem, String> serviceCol = new TableColumn<>("نوع الخدمة");
        serviceCol.setPrefWidth(140);
        serviceCol.setCellValueFactory(new PropertyValueFactory<>("serviceType"));

        ObservableList<String> serviceNames = FXCollections.observableArrayList();
        services.forEach(service -> serviceNames.add(service.getName()));
        serviceCol.setCellFactory(ComboBoxTableCell.forTableColumn(serviceNames));
        serviceCol.setOnEditCommit(event -> {
            event.getRowValue().setServiceType(event.getNewValue());
        });
        
        // العمود: السمك
        TableColumn<InvoiceItem, Integer> thicknessCol = new TableColumn<>("السمك (mm)");
        thicknessCol.setPrefWidth(90);
        thicknessCol.setCellValueFactory(new PropertyValueFactory<>("thickness"));

        // إنشاء قائمة السمك من 1 إلى 100
        ObservableList<Integer> thicknessList = FXCollections.observableArrayList();
        for (int i = 1; i <= 100; i++) {
            thicknessList.add(i);
        }
        thicknessCol.setCellFactory(ComboBoxTableCell.forTableColumn(thicknessList));
        thicknessCol.setOnEditCommit(event -> {
            event.getRowValue().setThickness(event.getNewValue());
        });
        
        // العمود: الطول
        TableColumn<InvoiceItem, Double> lengthCol = new TableColumn<>("الطول (mm)");
        lengthCol.setPrefWidth(90);
        lengthCol.setCellValueFactory(new PropertyValueFactory<>("length"));
        lengthCol.setCellFactory(TextFieldTableCell.forTableColumn(new DoubleStringConverter()));
        lengthCol.setOnEditCommit(event -> {
            event.getRowValue().setLength(event.getNewValue());
        });
        
        // العمود: العرض
        TableColumn<InvoiceItem, Double> widthCol = new TableColumn<>("العرض (mm)");
        widthCol.setPrefWidth(90);
        widthCol.setCellValueFactory(new PropertyValueFactory<>("width"));
        widthCol.setCellFactory(TextFieldTableCell.forTableColumn(new DoubleStringConverter()));
        widthCol.setOnEditCommit(event -> {
            event.getRowValue().setWidth(event.getNewValue());
        });
        
        // العمود: المتر المربع (محسوب تلقائياً)
        TableColumn<InvoiceItem, Double> squareMetersCol = new TableColumn<>("المتر المربع");
        squareMetersCol.setPrefWidth(100);
        squareMetersCol.setCellValueFactory(new PropertyValueFactory<>("squareMeters"));
        squareMetersCol.setCellFactory(col -> new TableCell<InvoiceItem, Double>() {
            @Override
            protected void updateItem(Double item, boolean empty) {
                super.updateItem(item, empty);
                getStyleClass().add("calculated-cell");
                if (empty || item == null) {
                    setText(null);
                } else {
                    setText(decimalFormat.format(item));
                }
            }
        });
        
        // العمود: العدد
        TableColumn<InvoiceItem, Integer> quantityCol = new TableColumn<>("العدد");
        quantityCol.setPrefWidth(60);
        quantityCol.setCellValueFactory(new PropertyValueFactory<>("quantity"));
        quantityCol.setCellFactory(TextFieldTableCell.forTableColumn(new IntegerStringConverter()));
        quantityCol.setOnEditCommit(event -> {
            event.getRowValue().setQuantity(event.getNewValue());
        });
        
        // العمود: إجمالي الكمية (محسوب تلقائياً أو يدوي)
        TableColumn<InvoiceItem, Double> totalQuantityCol = new TableColumn<>("إجمالي الكمية");
        totalQuantityCol.setPrefWidth(110);
        totalQuantityCol.setCellValueFactory(new PropertyValueFactory<>("totalQuantity"));
        totalQuantityCol.setCellFactory(col -> new TableCell<InvoiceItem, Double>() {
            @Override
            protected void updateItem(Double item, boolean empty) {
                super.updateItem(item, empty);
                if (empty) {
                    setText(null);
                    setGraphic(null);
                } else {
                    InvoiceItem invoiceItem = getTableView().getItems().get(getIndex());
                    if (invoiceItem.isManualRow()) {
                        // للصف اليدوي: حقل قابل للتعديل
                        TextField textField = new TextField();
                        textField.setText(item != null ? decimalFormat.format(item) : "0.00");
                        textField.setOnAction(e -> {
                            try {
                                double value = Double.parseDouble(textField.getText());
                                invoiceItem.setTotalQuantity(value);
                            } catch (NumberFormatException ex) {
                                textField.setText(decimalFormat.format(item != null ? item : 0.0));
                            }
                        });
                        textField.focusedProperty().addListener((obs, oldVal, newVal) -> {
                            if (!newVal) { // عند فقدان التركيز
                                try {
                                    double value = Double.parseDouble(textField.getText());
                                    invoiceItem.setTotalQuantity(value);
                                } catch (NumberFormatException ex) {
                                    textField.setText(decimalFormat.format(item != null ? item : 0.0));
                                }
                            }
                        });
                        setGraphic(textField);
                        setText(null);
                    } else {
                        // للصف العادي: عرض فقط
                        getStyleClass().add("calculated-cell");
                        setText(item != null ? decimalFormat.format(item) : "0.00");
                        setGraphic(null);
                    }
                }
            }
        });
        
        // العمود: السعر
        TableColumn<InvoiceItem, Double> priceCol = new TableColumn<>("السعر");
        priceCol.setPrefWidth(80);
        priceCol.setCellValueFactory(new PropertyValueFactory<>("price"));
        priceCol.setCellFactory(TextFieldTableCell.forTableColumn(new DoubleStringConverter()));
        priceCol.setOnEditCommit(event -> {
            event.getRowValue().setPrice(event.getNewValue());
        });

        // العمود: التفاصيل
        TableColumn<InvoiceItem, String> descriptionCol = new TableColumn<>("التفاصيل");
        descriptionCol.setPrefWidth(150);
        descriptionCol.setCellValueFactory(new PropertyValueFactory<>("description"));
        descriptionCol.setCellFactory(TextFieldTableCell.forTableColumn());
        descriptionCol.setOnEditCommit(event -> {
            event.getRowValue().setDescription(event.getNewValue());
        });
        
        // العمود: إجمالي القيمة (محسوب تلقائياً)
        TableColumn<InvoiceItem, Double> totalValueCol = new TableColumn<>("إجمالي القيمة");
        totalValueCol.setPrefWidth(110);
        totalValueCol.setCellValueFactory(new PropertyValueFactory<>("totalValue"));
        totalValueCol.setCellFactory(col -> new TableCell<InvoiceItem, Double>() {
            @Override
            protected void updateItem(Double item, boolean empty) {
                super.updateItem(item, empty);
                getStyleClass().add("calculated-cell");
                if (empty || item == null) {
                    setText(null);
                } else {
                    setText(decimalFormat.format(item) + " ج.م");
                }
            }
        });
        
        // العمود: الإجراءات
        TableColumn<InvoiceItem, Void> actionsCol = new TableColumn<>("إجراءات");
        actionsCol.setPrefWidth(120);
        actionsCol.setCellFactory(col -> new TableCell<InvoiceItem, Void>() {
            private final HBox actionBox = new HBox(5);
            private final Button addBtn = new Button();
            private final Button deleteBtn = new Button();

            {
                // زر الإضافة
                FontAwesomeIconView addIcon = new FontAwesomeIconView(FontAwesomeIcon.PLUS);
                addIcon.setSize("12px");
                addBtn.setGraphic(addIcon);
                addBtn.getStyleClass().add("add-row-action-button");
                addBtn.setTooltip(new Tooltip("إضافة صف جديد تحت هذا الصف"));
                addBtn.setOnAction(e -> {
                    int currentIndex = getIndex();
                    addRowAfter(getTableView(), currentIndex);
                });

                // زر الحذف
                FontAwesomeIconView deleteIcon = new FontAwesomeIconView(FontAwesomeIcon.TRASH);
                deleteIcon.setSize("12px");
                deleteBtn.setGraphic(deleteIcon);
                deleteBtn.getStyleClass().add("delete-row-action-button");
                deleteBtn.setTooltip(new Tooltip("حذف هذا الصف"));
                deleteBtn.setOnAction(e -> {
                    int currentIndex = getIndex();
                    deleteRow(getTableView(), currentIndex);
                });

                actionBox.setAlignment(Pos.CENTER);
                actionBox.getChildren().addAll(addBtn, deleteBtn);
            }

            @Override
            protected void updateItem(Void item, boolean empty) {
                super.updateItem(item, empty);
                if (empty) {
                    setGraphic(null);
                } else {
                    setGraphic(actionBox);
                }
            }
        });
        
        table.getColumns().addAll(indexCol, descriptionCol, serviceCol, thicknessCol, lengthCol, widthCol,
                                 squareMetersCol, quantityCol, totalQuantityCol, priceCol,
                                 totalValueCol, actionsCol);
        
        return table;
    }
    
    /**
     * إنشاء أزرار إدارة الصفوف
     */
    private HBox createRowControls(TableView<InvoiceItem> table) {
        HBox controls = new HBox(15);
        controls.setAlignment(Pos.CENTER_LEFT);
        controls.setPadding(new Insets(15));
        controls.getStyleClass().add("row-controls");

        // زر إضافة صف عادي
        Button addNormalRowBtn = new Button("إضافة صف عادي");
        FontAwesomeIconView normalIcon = new FontAwesomeIconView(FontAwesomeIcon.PLUS);
        normalIcon.setSize("16px");
        addNormalRowBtn.setGraphic(normalIcon);
        addNormalRowBtn.getStyleClass().add("add-normal-row-button");
        addNormalRowBtn.setOnAction(e -> addNewRow(table, false));

        // زر إضافة صف يدوي
        Button addManualRowBtn = new Button("إضافة صف يدوي");
        FontAwesomeIconView manualIcon = new FontAwesomeIconView(FontAwesomeIcon.EDIT);
        manualIcon.setSize("16px");
        addManualRowBtn.setGraphic(manualIcon);
        addManualRowBtn.getStyleClass().add("add-manual-row-button");
        addManualRowBtn.setOnAction(e -> addNewRow(table, true));

        // معلومات إضافية
        Label infoLabel = new Label("الصف العادي: حسابات تلقائية | الصف اليدوي: إدخال يدوي للكمية والوصف");
        infoLabel.getStyleClass().add("info-label");

        controls.getChildren().addAll(addNormalRowBtn, addManualRowBtn, new Separator(), infoLabel);
        return controls;
    }

    /**
     * إنشاء منطقة الخصم والملاحظات
     */
    private VBox createDiscountNotesArea() {
        VBox area = new VBox(15);
        area.setPadding(new Insets(20));
        area.getStyleClass().add("discount-notes-area");

        // عنوان القسم
        Label sectionTitle = new Label("تفاصيل إضافية:");
        sectionTitle.getStyleClass().add("section-title");

        // منطقة الخصم
        HBox discountArea = new HBox(15);
        discountArea.setAlignment(Pos.CENTER_LEFT);

        Label discountLabel = new Label("الخصم:");
        discountLabel.getStyleClass().add("field-label");

        discountField = new TextField("0");
        discountField.setPrefWidth(100);
        discountField.setPromptText("0.00");
        discountField.getStyleClass().add("discount-field");
        discountField.textProperty().addListener((obs, oldVal, newVal) -> updateTotals());

        Label discountCurrencyLabel = new Label("ج.م");
        discountCurrencyLabel.getStyleClass().add("currency-label");

        // نوع الخصم
        ComboBox<String> discountTypeCombo = new ComboBox<>();
        discountTypeCombo.getItems().addAll("مبلغ ثابت", "نسبة مئوية");
        discountTypeCombo.setValue("مبلغ ثابت");
        discountTypeCombo.setPrefWidth(120);
        discountTypeCombo.getStyleClass().add("discount-type-combo");

        discountArea.getChildren().addAll(discountLabel, discountField, discountCurrencyLabel,
                                        new Label("نوع الخصم:"), discountTypeCombo);

        // منطقة الملاحظات
        VBox notesArea = new VBox(10);

        Label notesLabel = new Label("ملاحظات الفاتورة:");
        notesLabel.getStyleClass().add("field-label");

        TextArea notesTextArea = new TextArea();
        notesTextArea.setPrefRowCount(4);
        notesTextArea.setPrefWidth(600);
        notesTextArea.setPromptText("أدخل أي ملاحظات أو تفاصيل إضافية للفاتورة...");
        notesTextArea.getStyleClass().add("notes-area");
        notesTextArea.setWrapText(true);

        notesArea.getChildren().addAll(notesLabel, notesTextArea);

        area.getChildren().addAll(sectionTitle, discountArea, notesArea);
        return area;
    }

    /**
     * إضافة صف جديد
     */
    private void addNewRow(TableView<InvoiceItem> table, boolean isManual) {
        InvoiceItem newItem = new InvoiceItem(isManual);
        table.getItems().add(newItem);

        // التركيز على الصف الجديد
        table.getSelectionModel().selectLast();
        table.scrollTo(table.getItems().size() - 1);
    }

    /**
     * إضافة صف جديد بعد صف معين
     */
    private void addRowAfter(TableView<InvoiceItem> table, int index) {
        // تحديد نوع الصف بناءً على الصف الحالي
        boolean isManual = false;
        if (index >= 0 && index < table.getItems().size()) {
            isManual = table.getItems().get(index).isManualRow();
        }

        InvoiceItem newItem = new InvoiceItem(isManual);
        table.getItems().add(index + 1, newItem);

        // التركيز على الصف الجديد
        table.getSelectionModel().select(index + 1);
        table.scrollTo(index + 1);
    }

    /**
     * حذف صف معين
     */
    private void deleteRow(TableView<InvoiceItem> table, int index) {
        if (table.getItems().size() > 0 && index >= 0 && index < table.getItems().size()) {
            table.getItems().remove(index);
        }
    }
    
    /**
     * إنشاء ملخص الفاتورة
     */
    private VBox createInvoiceSummary() {
        VBox summary = new VBox(15);
        summary.setAlignment(Pos.CENTER_RIGHT);
        summary.setPadding(new Insets(20));
        summary.getStyleClass().add("invoice-summary");
        summary.setMaxWidth(400);

        // عنوان الملخص
        Label summaryTitle = new Label("ملخص الفاتورة:");
        summaryTitle.getStyleClass().add("summary-title");

        // الإجمالي الفرعي
        HBox subtotalBox = new HBox();
        subtotalBox.setAlignment(Pos.CENTER_RIGHT);
        subtotalBox.getStyleClass().add("summary-row");
        Label subtotalLabel = new Label("الإجمالي الفرعي:");
        subtotalLabel.getStyleClass().add("summary-label");
        subtotalValueLabel = new Label("0.00 ج.م");
        subtotalValueLabel.getStyleClass().add("summary-value");
        Region spacer1 = new Region();
        HBox.setHgrow(spacer1, Priority.ALWAYS);
        subtotalBox.getChildren().addAll(subtotalLabel, spacer1, subtotalValueLabel);

        // الخصم
        HBox discountSummaryBox = new HBox();
        discountSummaryBox.setAlignment(Pos.CENTER_RIGHT);
        discountSummaryBox.getStyleClass().add("summary-row");
        Label discountSummaryLabel = new Label("الخصم:");
        discountSummaryLabel.getStyleClass().add("summary-label");
        discountValueLabel = new Label("0.00 ج.م");
        discountValueLabel.getStyleClass().add("discount-value");
        Region spacer2 = new Region();
        HBox.setHgrow(spacer2, Priority.ALWAYS);
        discountSummaryBox.getChildren().addAll(discountSummaryLabel, spacer2, discountValueLabel);

        // الإجمالي النهائي
        HBox totalBox = new HBox();
        totalBox.setAlignment(Pos.CENTER_RIGHT);
        totalBox.getStyleClass().add("total-summary-box");
        Label totalLabel = new Label("الإجمالي النهائي:");
        totalLabel.getStyleClass().add("total-label");
        totalValueLabel = new Label("0.00 ج.م");
        totalValueLabel.getStyleClass().add("total-value");
        Region spacer3 = new Region();
        HBox.setHgrow(spacer3, Priority.ALWAYS);
        totalBox.getChildren().addAll(totalLabel, spacer3, totalValueLabel);

        summary.getChildren().addAll(summaryTitle, subtotalBox, discountSummaryBox, totalBox);

        // إضافة مستمع لتحديث الإجماليات عند تغيير البيانات
        invoiceItems.addListener((javafx.collections.ListChangeListener<InvoiceItem>) change -> {
            while (change.next()) {
                if (change.wasAdded()) {
                    for (InvoiceItem item : change.getAddedSubList()) {
                        item.totalValueProperty().addListener((obs, oldVal, newVal) -> updateTotals());
                    }
                }
            }
            updateTotals();
        });

        return summary;
    }

    /**
     * تحديث الإجماليات
     */
    private void updateTotals() {
        double subtotal = invoiceItems.stream()
                .mapToDouble(InvoiceItem::getTotalValue)
                .sum();

        double discount = 0;
        try {
            if (discountField != null && !discountField.getText().trim().isEmpty()) {
                discount = Double.parseDouble(discountField.getText().trim());
            }
        } catch (NumberFormatException e) {
            discount = 0;
        }

        double total = subtotal - discount;

        if (subtotalValueLabel != null) {
            subtotalValueLabel.setText(decimalFormat.format(subtotal) + " ج.م");
        }
        if (discountValueLabel != null) {
            discountValueLabel.setText(decimalFormat.format(discount) + " ج.م");
        }
        if (totalValueLabel != null) {
            totalValueLabel.setText(decimalFormat.format(total) + " ج.م");
        }
    }
    
    // أحداث الأزرار
    private void showServicesDialog() {
        showServiceManagementDialog();
    }

    private void showCustomersDialog() {
        showCustomerManagementDialog();
    }

    private void showReportsDialog() {
        showSalesReportsDialog();
    }

    private void newInvoice() {
        invoiceItems.clear();
        addEmptyRow(); // إضافة صف فارغ للبدء
        customerComboBox.getSelectionModel().clearSelection();
    }

    private void saveInvoice() {
        if (validateInvoice()) {
            // إنشاء رقم فاتورة جديد
            String invoiceNo = generateInvoiceNumber();

            // حفظ الفاتورة (هنا يمكن إضافة قاعدة البيانات لاحقاً)
            saveInvoiceToDatabase(invoiceNo);

            // إظهار رسالة النجاح
            Alert alert = new Alert(Alert.AlertType.INFORMATION);
            alert.setTitle("تم الحفظ");
            alert.setHeaderText("تم حفظ الفاتورة بنجاح");
            alert.setContentText("رقم الفاتورة: " + invoiceNo + "\nيمكنك العثور عليها في تقارير المبيعات");
            alert.showAndWait();

            // مسح الفاتورة الحالية وإنشاء فاتورة جديدة
            newInvoice();
        }
    }

    private void savePdf() {
        if (validateInvoice()) {
            showPrintPreviewForSave();
        }
    }

    private void printInvoice() {
        if (validateInvoice()) {
            showPrintPreviewForPrint();
        }
    }

    private boolean validateInvoice() {
        if (customerComboBox.getSelectionModel().getSelectedItem() == null) {
            showPlaceholderDialog("تحذير", "يرجى اختيار عميل للفاتورة");
            return false;
        }

        if (invoiceItems.isEmpty()) {
            showPlaceholderDialog("تحذير", "يرجى إضافة عناصر للفاتورة");
            return false;
        }

        return true;
    }
    
    private void showPlaceholderDialog(String title, String message) {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle(title);
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }

    /**
     * عرض نافذة إدارة الخدمات
     */
    private void showServiceManagementDialog() {
        Stage dialog = new Stage();
        dialog.setTitle("إدارة الخدمات");
        dialog.initModality(Modality.APPLICATION_MODAL);
        dialog.setWidth(600);
        dialog.setHeight(500);

        VBox mainLayout = new VBox(15);
        mainLayout.setPadding(new Insets(20));

        // العنوان
        Label titleLabel = new Label("إدارة الخدمات");
        titleLabel.getStyleClass().add("dialog-title");

        // جدول الخدمات
        TableView<Service> servicesTable = new TableView<>();
        servicesTable.setItems(services);
        servicesTable.setMinHeight(200);
        VBox.setVgrow(servicesTable, Priority.ALWAYS);

        // أعمدة الجدول
        TableColumn<Service, String> nameCol = new TableColumn<>("اسم الخدمة");
        nameCol.setPrefWidth(150);
        nameCol.setCellValueFactory(new PropertyValueFactory<>("name"));
        nameCol.setCellFactory(TextFieldTableCell.forTableColumn());
        nameCol.setOnEditCommit(event -> {
            event.getRowValue().setName(event.getNewValue());
        });

        TableColumn<Service, String> descCol = new TableColumn<>("الوصف");
        descCol.setPrefWidth(200);
        descCol.setCellValueFactory(new PropertyValueFactory<>("description"));
        descCol.setCellFactory(TextFieldTableCell.forTableColumn());
        descCol.setOnEditCommit(event -> {
            event.getRowValue().setDescription(event.getNewValue());
        });

        TableColumn<Service, Double> priceCol = new TableColumn<>("السعر الافتراضي");
        priceCol.setPrefWidth(120);
        priceCol.setCellValueFactory(new PropertyValueFactory<>("defaultPrice"));
        priceCol.setCellFactory(TextFieldTableCell.forTableColumn(new DoubleStringConverter()));
        priceCol.setOnEditCommit(event -> {
            event.getRowValue().setDefaultPrice(event.getNewValue());
        });

        TableColumn<Service, Void> actionsCol = new TableColumn<>("إجراءات");
        actionsCol.setPrefWidth(80);
        actionsCol.setCellFactory(col -> new TableCell<Service, Void>() {
            private final Button deleteBtn = new Button();

            {
                FontAwesomeIconView deleteIcon = new FontAwesomeIconView(FontAwesomeIcon.TRASH);
                deleteIcon.setSize("12px");
                deleteBtn.setGraphic(deleteIcon);
                deleteBtn.getStyleClass().add("delete-button");
                deleteBtn.setOnAction(e -> {
                    Service service = getTableView().getItems().get(getIndex());
                    getTableView().getItems().remove(service);
                });
            }

            @Override
            protected void updateItem(Void item, boolean empty) {
                super.updateItem(item, empty);
                if (empty) {
                    setGraphic(null);
                } else {
                    setGraphic(deleteBtn);
                }
            }
        });

        servicesTable.getColumns().addAll(nameCol, descCol, priceCol, actionsCol);
        servicesTable.setEditable(true);

        // نموذج إضافة خدمة جديدة
        GridPane addServiceForm = new GridPane();
        addServiceForm.setHgap(10);
        addServiceForm.setVgap(10);
        addServiceForm.getStyleClass().add("add-service-form");

        Label nameLabel = new Label("اسم الخدمة:");
        TextField nameField = new TextField();
        nameField.setPromptText("أدخل اسم الخدمة");

        Label descLabel = new Label("الوصف:");
        TextField descField = new TextField();
        descField.setPromptText("أدخل وصف الخدمة");

        Label priceLabel = new Label("السعر الافتراضي:");
        TextField priceField = new TextField();
        priceField.setPromptText("0.00");

        Button addButton = new Button("إضافة خدمة");
        FontAwesomeIconView addIcon = new FontAwesomeIconView(FontAwesomeIcon.PLUS);
        addIcon.setSize("14px");
        addButton.setGraphic(addIcon);
        addButton.getStyleClass().add("add-button");
        addButton.setOnAction(e -> {
            String name = nameField.getText().trim();
            String desc = descField.getText().trim();
            String priceText = priceField.getText().trim();

            if (!name.isEmpty() && !priceText.isEmpty()) {
                try {
                    double price = Double.parseDouble(priceText);
                    String id = String.valueOf(services.size() + 1);
                    Service newService = new Service(id, name, desc, price);
                    services.add(newService);

                    // مسح الحقول
                    nameField.clear();
                    descField.clear();
                    priceField.clear();
                } catch (NumberFormatException ex) {
                    Alert alert = new Alert(Alert.AlertType.ERROR);
                    alert.setTitle("خطأ");
                    alert.setHeaderText(null);
                    alert.setContentText("يرجى إدخال سعر صحيح");
                    alert.showAndWait();
                }
            } else {
                Alert alert = new Alert(Alert.AlertType.WARNING);
                alert.setTitle("تحذير");
                alert.setHeaderText(null);
                alert.setContentText("يرجى ملء جميع الحقول المطلوبة");
                alert.showAndWait();
            }
        });

        addServiceForm.add(nameLabel, 0, 0);
        addServiceForm.add(nameField, 1, 0);
        addServiceForm.add(descLabel, 0, 1);
        addServiceForm.add(descField, 1, 1);
        addServiceForm.add(priceLabel, 0, 2);
        addServiceForm.add(priceField, 1, 2);
        addServiceForm.add(addButton, 1, 3);

        // أزرار الحوار
        HBox buttonBox = new HBox(10);
        buttonBox.setAlignment(Pos.CENTER_RIGHT);

        Button closeButton = new Button("إغلاق");
        closeButton.setOnAction(e -> dialog.close());

        buttonBox.getChildren().add(closeButton);

        mainLayout.getChildren().addAll(titleLabel, servicesTable,
                                       new Separator(), addServiceForm, buttonBox);

        Scene scene = new Scene(mainLayout);
        scene.getStylesheets().add(getClass().getResource("/css/styles.css").toExternalForm());
        dialog.setScene(scene);
        dialog.showAndWait();
    }

    /**
     * عرض نافذة إدارة العملاء
     */
    private void showCustomerManagementDialog() {
        Stage dialog = new Stage();
        dialog.setTitle("إدارة العملاء");
        dialog.initModality(Modality.APPLICATION_MODAL);
        dialog.setWidth(800);
        dialog.setHeight(600);

        VBox mainLayout = new VBox(15);
        mainLayout.setPadding(new Insets(20));

        // العنوان
        Label titleLabel = new Label("إدارة العملاء");
        titleLabel.getStyleClass().add("dialog-title");

        // جدول العملاء
        TableView<Customer> customersTable = new TableView<>();
        customersTable.setItems(customers);
        customersTable.setMinHeight(200);
        VBox.setVgrow(customersTable, Priority.ALWAYS);

        // أعمدة الجدول
        TableColumn<Customer, String> nameCol = new TableColumn<>("الاسم");
        nameCol.setPrefWidth(120);
        nameCol.setCellValueFactory(new PropertyValueFactory<>("name"));
        nameCol.setCellFactory(TextFieldTableCell.forTableColumn());
        nameCol.setOnEditCommit(event -> {
            event.getRowValue().setName(event.getNewValue());
        });

        TableColumn<Customer, String> phoneCol = new TableColumn<>("الهاتف");
        phoneCol.setPrefWidth(100);
        phoneCol.setCellValueFactory(new PropertyValueFactory<>("phone"));
        phoneCol.setCellFactory(TextFieldTableCell.forTableColumn());
        phoneCol.setOnEditCommit(event -> {
            event.getRowValue().setPhone(event.getNewValue());
        });

        TableColumn<Customer, String> emailCol = new TableColumn<>("البريد الإلكتروني");
        emailCol.setPrefWidth(150);
        emailCol.setCellValueFactory(new PropertyValueFactory<>("email"));
        emailCol.setCellFactory(TextFieldTableCell.forTableColumn());
        emailCol.setOnEditCommit(event -> {
            event.getRowValue().setEmail(event.getNewValue());
        });

        TableColumn<Customer, String> companyCol = new TableColumn<>("الشركة");
        companyCol.setPrefWidth(120);
        companyCol.setCellValueFactory(new PropertyValueFactory<>("company"));
        companyCol.setCellFactory(TextFieldTableCell.forTableColumn());
        companyCol.setOnEditCommit(event -> {
            event.getRowValue().setCompany(event.getNewValue());
        });

        TableColumn<Customer, Void> actionsCol = new TableColumn<>("إجراءات");
        actionsCol.setPrefWidth(80);
        actionsCol.setCellFactory(col -> new TableCell<Customer, Void>() {
            private final Button deleteBtn = new Button();

            {
                FontAwesomeIconView deleteIcon = new FontAwesomeIconView(FontAwesomeIcon.TRASH);
                deleteIcon.setSize("12px");
                deleteBtn.setGraphic(deleteIcon);
                deleteBtn.getStyleClass().add("delete-button");
                deleteBtn.setOnAction(e -> {
                    Customer customer = getTableView().getItems().get(getIndex());
                    getTableView().getItems().remove(customer);
                });
            }

            @Override
            protected void updateItem(Void item, boolean empty) {
                super.updateItem(item, empty);
                if (empty) {
                    setGraphic(null);
                } else {
                    setGraphic(deleteBtn);
                }
            }
        });

        customersTable.getColumns().addAll(nameCol, phoneCol, emailCol, companyCol, actionsCol);
        customersTable.setEditable(true);

        // نموذج إضافة عميل جديد
        GridPane addCustomerForm = new GridPane();
        addCustomerForm.setHgap(10);
        addCustomerForm.setVgap(10);
        addCustomerForm.getStyleClass().add("add-service-form");

        Label nameLabel = new Label("الاسم:");
        TextField nameField = new TextField();
        nameField.setPromptText("أدخل اسم العميل");

        Label phoneLabel = new Label("الهاتف:");
        TextField phoneField = new TextField();
        phoneField.setPromptText("أدخل رقم الهاتف");

        Label emailLabel = new Label("البريد الإلكتروني:");
        TextField emailField = new TextField();
        emailField.setPromptText("أدخل البريد الإلكتروني");

        Label companyLabel = new Label("الشركة:");
        TextField companyField = new TextField();
        companyField.setPromptText("أدخل اسم الشركة (اختياري)");

        Button addButton = new Button("إضافة عميل");
        FontAwesomeIconView addIcon = new FontAwesomeIconView(FontAwesomeIcon.USER_PLUS);
        addIcon.setSize("14px");
        addButton.setGraphic(addIcon);
        addButton.getStyleClass().add("add-button");
        addButton.setOnAction(e -> {
            String name = nameField.getText().trim();
            String phone = phoneField.getText().trim();
            String email = emailField.getText().trim();
            String company = companyField.getText().trim();

            if (!name.isEmpty()) {
                String id = String.valueOf(customers.size() + 1);
                Customer newCustomer = new Customer(id, name, phone, email, "", company, "");
                customers.add(newCustomer);

                // مسح الحقول
                nameField.clear();
                phoneField.clear();
                emailField.clear();
                companyField.clear();
            } else {
                Alert alert = new Alert(Alert.AlertType.WARNING);
                alert.setTitle("تحذير");
                alert.setHeaderText(null);
                alert.setContentText("يرجى إدخال اسم العميل على الأقل");
                alert.showAndWait();
            }
        });

        addCustomerForm.add(nameLabel, 0, 0);
        addCustomerForm.add(nameField, 1, 0);
        addCustomerForm.add(phoneLabel, 2, 0);
        addCustomerForm.add(phoneField, 3, 0);
        addCustomerForm.add(emailLabel, 0, 1);
        addCustomerForm.add(emailField, 1, 1);
        addCustomerForm.add(companyLabel, 2, 1);
        addCustomerForm.add(companyField, 3, 1);
        addCustomerForm.add(addButton, 1, 2);

        // أزرار الحوار
        HBox buttonBox = new HBox(10);
        buttonBox.setAlignment(Pos.CENTER_RIGHT);

        Button closeButton = new Button("إغلاق");
        closeButton.setOnAction(e -> dialog.close());

        buttonBox.getChildren().add(closeButton);

        mainLayout.getChildren().addAll(titleLabel, customersTable,
                                       new Separator(), addCustomerForm, buttonBox);

        Scene scene = new Scene(mainLayout);
        scene.getStylesheets().add(getClass().getResource("/css/styles.css").toExternalForm());
        dialog.setScene(scene);
        dialog.showAndWait();
    }

    /**
     * عرض نافذة إضافة عميل جديد
     */
    private void showAddCustomerDialog() {
        Stage dialog = new Stage();
        dialog.setTitle("إضافة عميل جديد");
        dialog.initModality(Modality.APPLICATION_MODAL);
        dialog.setWidth(400);
        dialog.setHeight(300);

        VBox mainLayout = new VBox(15);
        mainLayout.setPadding(new Insets(20));

        // العنوان
        Label titleLabel = new Label("إضافة عميل جديد");
        titleLabel.getStyleClass().add("dialog-title");

        // نموذج البيانات
        GridPane form = new GridPane();
        form.setHgap(10);
        form.setVgap(15);

        Label nameLabel = new Label("الاسم:");
        TextField nameField = new TextField();
        nameField.setPromptText("أدخل اسم العميل");

        Label phoneLabel = new Label("الهاتف:");
        TextField phoneField = new TextField();
        phoneField.setPromptText("أدخل رقم الهاتف");

        Label emailLabel = new Label("البريد الإلكتروني:");
        TextField emailField = new TextField();
        emailField.setPromptText("أدخل البريد الإلكتروني");

        Label companyLabel = new Label("الشركة:");
        TextField companyField = new TextField();
        companyField.setPromptText("أدخل اسم الشركة (اختياري)");

        form.add(nameLabel, 0, 0);
        form.add(nameField, 1, 0);
        form.add(phoneLabel, 0, 1);
        form.add(phoneField, 1, 1);
        form.add(emailLabel, 0, 2);
        form.add(emailField, 1, 2);
        form.add(companyLabel, 0, 3);
        form.add(companyField, 1, 3);

        // أزرار الحوار
        HBox buttonBox = new HBox(10);
        buttonBox.setAlignment(Pos.CENTER_RIGHT);

        Button addButton = new Button("إضافة");
        addButton.getStyleClass().add("add-button");
        addButton.setOnAction(e -> {
            String name = nameField.getText().trim();
            String phone = phoneField.getText().trim();
            String email = emailField.getText().trim();
            String company = companyField.getText().trim();

            if (!name.isEmpty()) {
                String id = String.valueOf(customers.size() + 1);
                Customer newCustomer = new Customer(id, name, phone, email, "", company, "");
                customers.add(newCustomer);
                customerComboBox.getSelectionModel().select(newCustomer);
                dialog.close();
            } else {
                Alert alert = new Alert(Alert.AlertType.WARNING);
                alert.setTitle("تحذير");
                alert.setHeaderText(null);
                alert.setContentText("يرجى إدخال اسم العميل على الأقل");
                alert.showAndWait();
            }
        });

        Button cancelButton = new Button("إلغاء");
        cancelButton.setOnAction(e -> dialog.close());

        buttonBox.getChildren().addAll(addButton, cancelButton);

        mainLayout.getChildren().addAll(titleLabel, form, buttonBox);

        Scene scene = new Scene(mainLayout);
        scene.getStylesheets().add(getClass().getResource("/css/styles.css").toExternalForm());
        dialog.setScene(scene);
        dialog.showAndWait();
    }

    /**
     * عرض تقارير المبيعات
     */
    private void showSalesReportsDialog() {
        Stage dialog = new Stage();
        dialog.setTitle("📊 تقارير المبيعات");
        dialog.initModality(Modality.APPLICATION_MODAL);
        dialog.setWidth(900);
        dialog.setHeight(700);

        VBox mainLayout = new VBox(20);
        mainLayout.setPadding(new Insets(20));

        // العنوان
        Label titleLabel = new Label("📊 تقارير وتحليلات المبيعات");
        titleLabel.getStyleClass().add("dialog-title");

        // شريط أدوات التقارير
        HBox reportsToolbar = new HBox(15);
        reportsToolbar.setAlignment(Pos.CENTER_LEFT);
        reportsToolbar.setPadding(new Insets(10));

        Button dailyReportBtn = new Button("تقرير يومي");
        FontAwesomeIconView dailyIcon = new FontAwesomeIconView(FontAwesomeIcon.CALENDAR);
        dailyIcon.setSize("14px");
        dailyReportBtn.setGraphic(dailyIcon);
        dailyReportBtn.getStyleClass().add("report-button");

        Button monthlyReportBtn = new Button("تقرير شهري");
        FontAwesomeIconView monthlyIcon = new FontAwesomeIconView(FontAwesomeIcon.CALENDAR_ALT);
        monthlyIcon.setSize("14px");
        monthlyReportBtn.setGraphic(monthlyIcon);
        monthlyReportBtn.getStyleClass().add("report-button");

        Button customerReportBtn = new Button("تقرير العملاء");
        FontAwesomeIconView customerIcon = new FontAwesomeIconView(FontAwesomeIcon.USERS);
        customerIcon.setSize("14px");
        customerReportBtn.setGraphic(customerIcon);
        customerReportBtn.getStyleClass().add("report-button");

        Button chartBtn = new Button("الرسوم البيانية");
        FontAwesomeIconView chartIcon = new FontAwesomeIconView(FontAwesomeIcon.PIE_CHART);
        chartIcon.setSize("14px");
        chartBtn.setGraphic(chartIcon);
        chartBtn.getStyleClass().add("chart-button");

        reportsToolbar.getChildren().addAll(dailyReportBtn, monthlyReportBtn, customerReportBtn, chartBtn);

        // منطقة عرض التقارير
        TabPane reportsTabPane = new TabPane();

        // تبويب الإحصائيات السريعة
        Tab statsTab = new Tab("الإحصائيات");
        VBox statsContent = createSalesStatsContent();
        statsTab.setContent(statsContent);
        statsTab.setClosable(false);

        // تبويب الفواتير المحفوظة
        Tab invoicesTab = new Tab("الفواتير المحفوظة");
        VBox invoicesContent = createSavedInvoicesContent();
        invoicesTab.setContent(invoicesContent);
        invoicesTab.setClosable(false);

        // تبويب تقرير الخدمات المفصل
        Tab servicesReportTab = new Tab("تقرير الخدمات المفصل");
        VBox servicesReportContent = createServicesReportContent();
        servicesReportTab.setContent(servicesReportContent);
        servicesReportTab.setClosable(false);

        // تبويب الرسوم البيانية
        Tab chartsTab = new Tab("الرسوم البيانية");
        VBox chartsContent = createChartsContent();
        chartsTab.setContent(chartsContent);
        chartsTab.setClosable(false);

        reportsTabPane.getTabs().addAll(statsTab, invoicesTab, servicesReportTab, chartsTab);

        // زر الإغلاق
        Button closeButton = new Button("إغلاق");
        closeButton.setOnAction(e -> dialog.close());

        HBox buttonBox = new HBox();
        buttonBox.setAlignment(Pos.CENTER_RIGHT);
        buttonBox.getChildren().add(closeButton);

        mainLayout.getChildren().addAll(titleLabel, reportsToolbar, reportsTabPane, buttonBox);

        Scene scene = new Scene(mainLayout);
        scene.getStylesheets().add(getClass().getResource("/css/styles.css").toExternalForm());
        dialog.setScene(scene);
        dialog.showAndWait();
    }

    /**
     * إنشاء محتوى الإحصائيات
     */
    private VBox createSalesStatsContent() {
        VBox content = new VBox(20);
        content.setPadding(new Insets(20));

        // إحصائيات سريعة
        HBox statsBox = new HBox(20);
        statsBox.setAlignment(Pos.CENTER);

        // حساب الإحصائيات الحقيقية
        int totalInvoices = savedInvoices.size();
        double totalSales = savedInvoices.stream().mapToDouble(SavedInvoice::getTotal).sum();
        double avgInvoice = totalInvoices > 0 ? totalSales / totalInvoices : 0;
        String topCustomer = getTopCustomer();

        // إجمالي الفواتير
        VBox totalInvoicesBox = createStatCard("إجمالي الفواتير", String.valueOf(totalInvoices), "#3498db");

        // إجمالي المبيعات
        VBox totalSalesBox = createStatCard("إجمالي المبيعات", decimalFormat.format(totalSales) + " ج.م", "#2ecc71");

        // متوسط الفاتورة
        VBox avgInvoiceBox = createStatCard("متوسط الفاتورة", decimalFormat.format(avgInvoice) + " ج.م", "#f39c12");

        // أفضل عميل
        VBox topCustomerBox = createStatCard("أفضل عميل", topCustomer, "#9b59b6");

        statsBox.getChildren().addAll(totalInvoicesBox, totalSalesBox, avgInvoiceBox, topCustomerBox);

        // جدول أفضل الخدمات
        Label servicesLabel = new Label("أفضل الخدمات:");
        servicesLabel.getStyleClass().add("section-title");

        TableView<Service> topServicesTable = new TableView<>();
        topServicesTable.setItems(services);
        topServicesTable.setMinHeight(200);
        VBox.setVgrow(topServicesTable, Priority.ALWAYS);

        TableColumn<Service, String> serviceNameCol = new TableColumn<>("اسم الخدمة");
        serviceNameCol.setPrefWidth(200);
        serviceNameCol.setCellValueFactory(new PropertyValueFactory<>("name"));

        TableColumn<Service, String> serviceDescCol = new TableColumn<>("الوصف");
        serviceDescCol.setPrefWidth(300);
        serviceDescCol.setCellValueFactory(new PropertyValueFactory<>("description"));

        TableColumn<Service, Double> servicePriceCol = new TableColumn<>("السعر");
        servicePriceCol.setPrefWidth(100);
        servicePriceCol.setCellValueFactory(new PropertyValueFactory<>("price"));
        servicePriceCol.setCellFactory(col -> new TableCell<Service, Double>() {
            @Override
            protected void updateItem(Double item, boolean empty) {
                super.updateItem(item, empty);
                if (empty || item == null) {
                    setText(null);
                } else {
                    setText(decimalFormat.format(item) + " ج.م");
                }
            }
        });

        topServicesTable.getColumns().addAll(serviceNameCol, serviceDescCol, servicePriceCol);

        content.getChildren().addAll(statsBox, servicesLabel, topServicesTable);
        return content;
    }

    /**
     * إنشاء محتوى الفواتير المحفوظة
     */
    private VBox createSavedInvoicesContent() {
        VBox content = new VBox(15);
        content.setPadding(new Insets(20));

        Label titleLabel = new Label("الفواتير المحفوظة:");
        titleLabel.getStyleClass().add("section-title");

        // شريط الفلاتر المتقدم
        VBox filtersSection = new VBox(10);
        filtersSection.setPadding(new Insets(15));
        filtersSection.setStyle("-fx-background-color: #f8f9fa; -fx-border-color: #dee2e6; -fx-border-width: 1; -fx-border-radius: 5;");

        Label filtersLabel = new Label("فلاتر البحث:");
        filtersLabel.getStyleClass().add("filters-title");

        // الصف الأول من الفلاتر
        HBox firstRowFilters = new HBox(15);
        firstRowFilters.setAlignment(Pos.CENTER_LEFT);

        // فلتر البحث النصي
        Label searchLabel = new Label("البحث:");
        TextField searchField = new TextField();
        searchField.setPromptText("رقم الفاتورة أو اسم العميل...");
        searchField.setPrefWidth(200);

        // فلتر التاريخ من
        Label fromDateLabel = new Label("من تاريخ:");
        DatePicker fromDatePicker = new DatePicker();
        fromDatePicker.setPrefWidth(120);

        // فلتر التاريخ إلى
        Label toDateLabel = new Label("إلى تاريخ:");
        DatePicker toDatePicker = new DatePicker();
        toDatePicker.setPrefWidth(120);

        firstRowFilters.getChildren().addAll(
            searchLabel, searchField,
            fromDateLabel, fromDatePicker,
            toDateLabel, toDatePicker
        );

        // الصف الثاني من الفلاتر
        HBox secondRowFilters = new HBox(15);
        secondRowFilters.setAlignment(Pos.CENTER_LEFT);

        // فلتر العميل
        Label customerFilterLabel = new Label("العميل:");
        ComboBox<String> customerFilterCombo = new ComboBox<>();
        customerFilterCombo.getItems().add("جميع العملاء");
        customerFilterCombo.getItems().addAll(customers.stream().map(Customer::getName).toList());
        customerFilterCombo.setValue("جميع العملاء");
        customerFilterCombo.setPrefWidth(150);

        // فلتر المبلغ من
        Label minAmountLabel = new Label("المبلغ من:");
        TextField minAmountField = new TextField();
        minAmountField.setPromptText("0.00");
        minAmountField.setPrefWidth(100);

        // فلتر المبلغ إلى
        Label maxAmountLabel = new Label("إلى:");
        TextField maxAmountField = new TextField();
        maxAmountField.setPromptText("999999.99");
        maxAmountField.setPrefWidth(100);

        // أزرار الفلتر
        Button applyFilterBtn = new Button("تطبيق الفلتر");
        FontAwesomeIconView filterIcon = new FontAwesomeIconView(FontAwesomeIcon.FILTER);
        filterIcon.setSize("12px");
        applyFilterBtn.setGraphic(filterIcon);
        applyFilterBtn.getStyleClass().add("filter-button");

        Button resetFilterBtn = new Button("إعادة تعيين");
        FontAwesomeIconView resetIcon = new FontAwesomeIconView(FontAwesomeIcon.REFRESH);
        resetIcon.setSize("12px");
        resetFilterBtn.setGraphic(resetIcon);
        resetFilterBtn.getStyleClass().add("reset-button");

        secondRowFilters.getChildren().addAll(
            customerFilterLabel, customerFilterCombo,
            minAmountLabel, minAmountField,
            maxAmountLabel, maxAmountField,
            applyFilterBtn, resetFilterBtn
        );

        filtersSection.getChildren().addAll(filtersLabel, firstRowFilters, secondRowFilters);

        // جدول الفواتير المحفوظة
        TableView<SavedInvoice> invoicesTable = new TableView<>();
        invoicesTable.setItems(savedInvoices);
        invoicesTable.setMinHeight(300);
        VBox.setVgrow(invoicesTable, Priority.ALWAYS);

        TableColumn<SavedInvoice, String> invoiceNoCol = new TableColumn<>("رقم الفاتورة");
        invoiceNoCol.setPrefWidth(120);
        invoiceNoCol.setCellValueFactory(new PropertyValueFactory<>("invoiceNumber"));

        TableColumn<SavedInvoice, String> customerCol = new TableColumn<>("العميل");
        customerCol.setPrefWidth(150);
        customerCol.setCellValueFactory(cellData ->
            new SimpleStringProperty(cellData.getValue().getCustomerName()));

        TableColumn<SavedInvoice, String> dateCol = new TableColumn<>("التاريخ");
        dateCol.setPrefWidth(100);
        dateCol.setCellValueFactory(cellData ->
            new SimpleStringProperty(cellData.getValue().getFormattedDate()));

        TableColumn<SavedInvoice, String> totalCol = new TableColumn<>("الإجمالي");
        totalCol.setPrefWidth(100);
        totalCol.setCellValueFactory(cellData ->
            new SimpleStringProperty(decimalFormat.format(cellData.getValue().getTotal()) + " ج.م"));

        TableColumn<SavedInvoice, String> statusCol = new TableColumn<>("الحالة");
        statusCol.setPrefWidth(100);
        statusCol.setCellValueFactory(new PropertyValueFactory<>("status"));

        TableColumn<SavedInvoice, Void> actionsCol = new TableColumn<>("الإجراءات");
        actionsCol.setPrefWidth(150);
        actionsCol.setCellFactory(col -> new TableCell<SavedInvoice, Void>() {
            private final HBox actionBox = new HBox(5);
            private final Button viewBtn = new Button("عرض");
            private final Button editBtn = new Button("تعديل");
            private final Button printBtn = new Button("طباعة");

            {
                // زر العرض
                FontAwesomeIconView viewIcon = new FontAwesomeIconView(FontAwesomeIcon.EYE);
                viewIcon.setSize("12px");
                viewBtn.setGraphic(viewIcon);
                viewBtn.getStyleClass().add("view-button");
                viewBtn.setOnAction(e -> {
                    SavedInvoice invoice = getTableView().getItems().get(getIndex());
                    viewSavedInvoice(invoice);
                });

                // زر التعديل
                FontAwesomeIconView editIcon = new FontAwesomeIconView(FontAwesomeIcon.EDIT);
                editIcon.setSize("12px");
                editBtn.setGraphic(editIcon);
                editBtn.getStyleClass().add("edit-button");
                editBtn.setOnAction(e -> {
                    SavedInvoice invoice = getTableView().getItems().get(getIndex());
                    editSavedInvoice(invoice);
                });

                // زر الطباعة
                FontAwesomeIconView printIcon = new FontAwesomeIconView(FontAwesomeIcon.PRINT);
                printIcon.setSize("12px");
                printBtn.setGraphic(printIcon);
                printBtn.getStyleClass().add("print-button");
                printBtn.setOnAction(e -> {
                    SavedInvoice invoice = getTableView().getItems().get(getIndex());
                    printSavedInvoice(invoice);
                });

                actionBox.setAlignment(Pos.CENTER);
                actionBox.getChildren().addAll(viewBtn, editBtn, printBtn);
            }

            @Override
            protected void updateItem(Void item, boolean empty) {
                super.updateItem(item, empty);
                if (empty) {
                    setGraphic(null);
                } else {
                    setGraphic(actionBox);
                }
            }
        });

        invoicesTable.getColumns().addAll(invoiceNoCol, customerCol, dateCol, totalCol, statusCol, actionsCol);

        // رسالة عدم وجود فواتير
        Label noInvoicesLabel = new Label("لا توجد فواتير محفوظة بعد");
        noInvoicesLabel.getStyleClass().add("table-placeholder");
        invoicesTable.setPlaceholder(noInvoicesLabel);

        // إعداد فلاتر الفواتير
        setupInvoicesFilters(invoicesTable, searchField, fromDatePicker, toDatePicker,
                           customerFilterCombo, minAmountField, maxAmountField,
                           applyFilterBtn, resetFilterBtn);

        content.getChildren().addAll(titleLabel, filtersSection, invoicesTable);
        return content;
    }

    /**
     * إنشاء محتوى تقرير الخدمات المفصل
     */
    private VBox createServicesReportContent() {
        VBox content = new VBox(15);
        content.setPadding(new Insets(20));

        Label titleLabel = new Label("تقرير الخدمات المفصل:");
        titleLabel.getStyleClass().add("section-title");

        // شريط الفلاتر
        HBox filtersBox = new HBox(15);
        filtersBox.setAlignment(Pos.CENTER_LEFT);
        filtersBox.setPadding(new Insets(10));
        filtersBox.setStyle("-fx-background-color: #f8f9fa; -fx-border-color: #dee2e6; -fx-border-width: 1; -fx-border-radius: 5;");

        // فلتر التاريخ من
        Label fromDateLabel = new Label("من تاريخ:");
        DatePicker fromDatePicker = new DatePicker();
        fromDatePicker.setPrefWidth(120);

        // فلتر التاريخ إلى
        Label toDateLabel = new Label("إلى تاريخ:");
        DatePicker toDatePicker = new DatePicker();
        toDatePicker.setPrefWidth(120);

        // فلتر العميل
        Label customerFilterLabel = new Label("العميل:");
        ComboBox<String> customerFilterCombo = new ComboBox<>();
        customerFilterCombo.getItems().add("جميع العملاء");
        customerFilterCombo.getItems().addAll(customers.stream().map(Customer::getName).toList());
        customerFilterCombo.setValue("جميع العملاء");
        customerFilterCombo.setPrefWidth(150);

        // فلتر الخدمة
        Label serviceFilterLabel = new Label("الخدمة:");
        ComboBox<String> serviceFilterCombo = new ComboBox<>();
        serviceFilterCombo.getItems().add("جميع الخدمات");
        serviceFilterCombo.getItems().addAll(services.stream().map(Service::getName).toList());
        serviceFilterCombo.setValue("جميع الخدمات");
        serviceFilterCombo.setPrefWidth(150);

        // زر تطبيق الفلتر
        Button applyFilterBtn = new Button("تطبيق الفلتر");
        FontAwesomeIconView filterIcon = new FontAwesomeIconView(FontAwesomeIcon.FILTER);
        filterIcon.setSize("12px");
        applyFilterBtn.setGraphic(filterIcon);
        applyFilterBtn.getStyleClass().add("filter-button");

        // زر إعادة تعيين الفلتر
        Button resetFilterBtn = new Button("إعادة تعيين");
        FontAwesomeIconView resetIcon = new FontAwesomeIconView(FontAwesomeIcon.REFRESH);
        resetIcon.setSize("12px");
        resetFilterBtn.setGraphic(resetIcon);
        resetFilterBtn.getStyleClass().add("reset-button");

        filtersBox.getChildren().addAll(
            fromDateLabel, fromDatePicker,
            toDateLabel, toDatePicker,
            customerFilterLabel, customerFilterCombo,
            serviceFilterLabel, serviceFilterCombo,
            applyFilterBtn, resetFilterBtn
        );

        // جدول تقرير الخدمات
        TableView<ServiceReport> servicesTable = new TableView<>();
        servicesTable.setItems(serviceReports);
        servicesTable.setMinHeight(400);
        VBox.setVgrow(servicesTable, Priority.ALWAYS);

        // أعمدة الجدول
        TableColumn<ServiceReport, String> invoiceNoCol = new TableColumn<>("رقم الفاتورة");
        invoiceNoCol.setPrefWidth(100);
        invoiceNoCol.setCellValueFactory(new PropertyValueFactory<>("invoiceNumber"));

        TableColumn<ServiceReport, String> dateCol = new TableColumn<>("التاريخ");
        dateCol.setPrefWidth(100);
        dateCol.setCellValueFactory(cellData ->
            new SimpleStringProperty(cellData.getValue().getFormattedDate()));

        TableColumn<ServiceReport, String> customerCol = new TableColumn<>("العميل");
        customerCol.setPrefWidth(120);
        customerCol.setCellValueFactory(new PropertyValueFactory<>("customerName"));

        TableColumn<ServiceReport, String> serviceCol = new TableColumn<>("الخدمة");
        serviceCol.setPrefWidth(120);
        serviceCol.setCellValueFactory(new PropertyValueFactory<>("serviceName"));

        TableColumn<ServiceReport, Integer> thicknessCol = new TableColumn<>("سمك الزجاج");
        thicknessCol.setPrefWidth(80);
        thicknessCol.setCellValueFactory(new PropertyValueFactory<>("thickness"));

        TableColumn<ServiceReport, String> descriptionCol = new TableColumn<>("التفاصيل");
        descriptionCol.setPrefWidth(150);
        descriptionCol.setCellValueFactory(new PropertyValueFactory<>("description"));

        TableColumn<ServiceReport, Double> quantityCol = new TableColumn<>("إجمالي الكمية");
        quantityCol.setPrefWidth(100);
        quantityCol.setCellValueFactory(new PropertyValueFactory<>("totalQuantity"));
        quantityCol.setCellFactory(col -> new TableCell<ServiceReport, Double>() {
            @Override
            protected void updateItem(Double item, boolean empty) {
                super.updateItem(item, empty);
                if (empty || item == null) {
                    setText(null);
                } else {
                    setText(decimalFormat.format(item));
                }
            }
        });

        TableColumn<ServiceReport, Double> priceCol = new TableColumn<>("السعر");
        priceCol.setPrefWidth(80);
        priceCol.setCellValueFactory(new PropertyValueFactory<>("price"));
        priceCol.setCellFactory(col -> new TableCell<ServiceReport, Double>() {
            @Override
            protected void updateItem(Double item, boolean empty) {
                super.updateItem(item, empty);
                if (empty || item == null) {
                    setText(null);
                } else {
                    setText(decimalFormat.format(item) + " ج.م");
                }
            }
        });

        TableColumn<ServiceReport, Double> totalValueCol = new TableColumn<>("إجمالي القيمة");
        totalValueCol.setPrefWidth(100);
        totalValueCol.setCellValueFactory(new PropertyValueFactory<>("totalValue"));
        totalValueCol.setCellFactory(col -> new TableCell<ServiceReport, Double>() {
            @Override
            protected void updateItem(Double item, boolean empty) {
                super.updateItem(item, empty);
                if (empty || item == null) {
                    setText(null);
                } else {
                    setText(decimalFormat.format(item) + " ج.م");
                }
            }
        });

        servicesTable.getColumns().addAll(
            invoiceNoCol, dateCol, customerCol, serviceCol,
            thicknessCol, descriptionCol, quantityCol, priceCol, totalValueCol
        );

        // شريط أدوات التقرير
        HBox toolbarBox = new HBox(10);
        toolbarBox.setAlignment(Pos.CENTER_LEFT);

        Button exportBtn = new Button("تصدير Excel");
        FontAwesomeIconView exportIcon = new FontAwesomeIconView(FontAwesomeIcon.FILE_EXCEL_ALT);
        exportIcon.setSize("12px");
        exportBtn.setGraphic(exportIcon);
        exportBtn.getStyleClass().add("export-button");

        Button printReportBtn = new Button("طباعة التقرير");
        FontAwesomeIconView printIcon = new FontAwesomeIconView(FontAwesomeIcon.PRINT);
        printIcon.setSize("12px");
        printReportBtn.setGraphic(printIcon);
        printReportBtn.getStyleClass().add("print-button");

        toolbarBox.getChildren().addAll(exportBtn, printReportBtn);

        // رسالة عدم وجود بيانات
        Label noDataLabel = new Label("لا توجد خدمات مسجلة بعد");
        noDataLabel.getStyleClass().add("table-placeholder");
        servicesTable.setPlaceholder(noDataLabel);

        // إعداد الفلاتر
        setupServicesReportFilters(servicesTable, fromDatePicker, toDatePicker,
                                 customerFilterCombo, serviceFilterCombo,
                                 applyFilterBtn, resetFilterBtn);

        content.getChildren().addAll(titleLabel, filtersBox, servicesTable, toolbarBox);
        return content;
    }

    /**
     * إعداد فلاتر تقرير الخدمات
     */
    private void setupServicesReportFilters(TableView<ServiceReport> table,
                                          DatePicker fromDate, DatePicker toDate,
                                          ComboBox<String> customerFilter, ComboBox<String> serviceFilter,
                                          Button applyBtn, Button resetBtn) {

        // قائمة أصلية للبيانات
        ObservableList<ServiceReport> originalData = FXCollections.observableArrayList(serviceReports);

        // تطبيق الفلتر
        applyBtn.setOnAction(e -> {
            ObservableList<ServiceReport> filteredData = FXCollections.observableArrayList();

            for (ServiceReport report : originalData) {
                boolean matches = true;

                // فلتر التاريخ من
                if (fromDate.getValue() != null) {
                    if (report.getDate().toLocalDate().isBefore(fromDate.getValue())) {
                        matches = false;
                    }
                }

                // فلتر التاريخ إلى
                if (toDate.getValue() != null) {
                    if (report.getDate().toLocalDate().isAfter(toDate.getValue())) {
                        matches = false;
                    }
                }

                // فلتر العميل
                if (!customerFilter.getValue().equals("جميع العملاء")) {
                    if (!report.getCustomerName().equals(customerFilter.getValue())) {
                        matches = false;
                    }
                }

                // فلتر الخدمة
                if (!serviceFilter.getValue().equals("جميع الخدمات")) {
                    if (!report.getServiceName().equals(serviceFilter.getValue())) {
                        matches = false;
                    }
                }

                if (matches) {
                    filteredData.add(report);
                }
            }

            table.setItems(filteredData);
        });

        // إعادة تعيين الفلتر
        resetBtn.setOnAction(e -> {
            fromDate.setValue(null);
            toDate.setValue(null);
            customerFilter.setValue("جميع العملاء");
            serviceFilter.setValue("جميع الخدمات");
            table.setItems(serviceReports);
        });
    }

    /**
     * إعداد فلاتر الفواتير المحفوظة
     */
    private void setupInvoicesFilters(TableView<SavedInvoice> table,
                                    TextField searchField, DatePicker fromDate, DatePicker toDate,
                                    ComboBox<String> customerFilter, TextField minAmount, TextField maxAmount,
                                    Button applyBtn, Button resetBtn) {

        // قائمة أصلية للبيانات
        ObservableList<SavedInvoice> originalData = FXCollections.observableArrayList(savedInvoices);

        // تطبيق الفلتر
        applyBtn.setOnAction(e -> {
            ObservableList<SavedInvoice> filteredData = FXCollections.observableArrayList();

            for (SavedInvoice invoice : originalData) {
                boolean matches = true;

                // فلتر البحث النصي
                if (!searchField.getText().trim().isEmpty()) {
                    String searchText = searchField.getText().toLowerCase();
                    boolean textMatch = invoice.getInvoiceNumber().toLowerCase().contains(searchText) ||
                                      invoice.getCustomerName().toLowerCase().contains(searchText);
                    if (!textMatch) {
                        matches = false;
                    }
                }

                // فلتر التاريخ من
                if (fromDate.getValue() != null) {
                    if (invoice.getDateTime().toLocalDate().isBefore(fromDate.getValue())) {
                        matches = false;
                    }
                }

                // فلتر التاريخ إلى
                if (toDate.getValue() != null) {
                    if (invoice.getDateTime().toLocalDate().isAfter(toDate.getValue())) {
                        matches = false;
                    }
                }

                // فلتر العميل
                if (!customerFilter.getValue().equals("جميع العملاء")) {
                    if (!invoice.getCustomerName().equals(customerFilter.getValue())) {
                        matches = false;
                    }
                }

                // فلتر المبلغ الأدنى
                if (!minAmount.getText().trim().isEmpty()) {
                    try {
                        double minValue = Double.parseDouble(minAmount.getText());
                        if (invoice.getTotal() < minValue) {
                            matches = false;
                        }
                    } catch (NumberFormatException ex) {
                        // تجاهل القيمة غير الصحيحة
                    }
                }

                // فلتر المبلغ الأقصى
                if (!maxAmount.getText().trim().isEmpty()) {
                    try {
                        double maxValue = Double.parseDouble(maxAmount.getText());
                        if (invoice.getTotal() > maxValue) {
                            matches = false;
                        }
                    } catch (NumberFormatException ex) {
                        // تجاهل القيمة غير الصحيحة
                    }
                }

                if (matches) {
                    filteredData.add(invoice);
                }
            }

            table.setItems(filteredData);
        });

        // إعادة تعيين الفلتر
        resetBtn.setOnAction(e -> {
            searchField.clear();
            fromDate.setValue(null);
            toDate.setValue(null);
            customerFilter.setValue("جميع العملاء");
            minAmount.clear();
            maxAmount.clear();
            table.setItems(savedInvoices);
        });

        // تطبيق فلتر البحث النصي فوري
        searchField.textProperty().addListener((obs, oldVal, newVal) -> {
            if (newVal.trim().isEmpty()) {
                table.setItems(savedInvoices);
            }
        });
    }

    /**
     * إنشاء محتوى الرسوم البيانية
     */
    private VBox createChartsContent() {
        VBox content = new VBox(20);
        content.setPadding(new Insets(20));

        Label titleLabel = new Label("الرسوم البيانية والتحليلات:");
        titleLabel.getStyleClass().add("section-title");

        // منطقة الرسوم البيانية
        VBox chartsArea = new VBox(15);
        chartsArea.setAlignment(Pos.CENTER);
        chartsArea.setPadding(new Insets(30));
        chartsArea.setStyle("-fx-background-color: #f8f9fa; -fx-border-color: #dee2e6; -fx-border-width: 1; -fx-border-radius: 5;");

        FontAwesomeIconView chartIcon = new FontAwesomeIconView(FontAwesomeIcon.BAR_CHART);
        chartIcon.setSize("48px");
        chartIcon.setStyle("-fx-fill: #6c757d;");

        Label chartLabel = new Label("الرسوم البيانية");
        chartLabel.setStyle("-fx-font-size: 18px; -fx-font-weight: bold; -fx-text-fill: #495057;");

        Label chartDesc = new Label("سيتم عرض الرسوم البيانية للمبيعات هنا\nبما في ذلك مخططات دائرية وأعمدة بيانية");
        chartDesc.setStyle("-fx-text-alignment: center; -fx-text-fill: #6c757d;");

        chartsArea.getChildren().addAll(chartIcon, chartLabel, chartDesc);

        content.getChildren().addAll(titleLabel, chartsArea);
        return content;
    }

    /**
     * إنشاء بطاقة إحصائية
     */
    private VBox createStatCard(String title, String value, String color) {
        VBox card = new VBox(5);
        card.setAlignment(Pos.CENTER);
        card.setPadding(new Insets(15));
        card.getStyleClass().add("stat-card");
        card.setStyle("-fx-border-color: " + color + "; -fx-border-width: 2;");

        Label titleLabel = new Label(title);
        titleLabel.getStyleClass().add("stat-title");

        Label valueLabel = new Label(value);
        valueLabel.getStyleClass().add("stat-value");
        valueLabel.setStyle("-fx-text-fill: " + color + ";");

        card.getChildren().addAll(titleLabel, valueLabel);
        return card;
    }

    /**
     * توليد رقم فاتورة جديد
     */
    private String generateInvoiceNumber() {
        String invoiceNumber = String.format("INV-%04d", invoiceCounter);
        invoiceCounter++;
        return invoiceNumber;
    }

    /**
     * حفظ الفاتورة في قاعدة البيانات (محاكاة)
     */
    private void saveInvoiceToDatabase(String invoiceNo) {
        // حساب الإجماليات
        double subtotal = invoiceItems.stream()
            .mapToDouble(item -> item.getTotalValueObject() != null ? item.getTotalValueObject() : 0.0)
            .sum();

        double discount = 0;
        try {
            discount = Double.parseDouble(discountField.getText());
        } catch (NumberFormatException e) {
            discount = 0;
        }

        double total = subtotal - discount;

        // إنشاء نسخة من عناصر الفاتورة
        ObservableList<InvoiceItem> itemsCopy = FXCollections.observableArrayList();
        for (InvoiceItem item : invoiceItems) {
            if (item.getServiceType() != null && !item.getServiceType().trim().isEmpty()) {
                InvoiceItem copy = new InvoiceItem(item.isManualRow());
                copy.setServiceType(item.getServiceType());
                copy.setThickness(item.getThickness());
                copy.setLength(item.getLength());
                copy.setWidth(item.getWidth());
                copy.setQuantity(item.getQuantity());
                copy.setPrice(item.getPrice());
                copy.setDescription(item.getDescription());
                itemsCopy.add(copy);
            }
        }

        // إنشاء الفاتورة المحفوظة
        SavedInvoice savedInvoice = new SavedInvoice(
            invoiceNo,
            customerComboBox.getValue(),
            itemsCopy,
            subtotal,
            discount,
            total,
            "" // ملاحظات - يمكن إضافة حقل لاحقاً
        );

        // إضافة الفاتورة للقائمة
        savedInvoices.add(savedInvoice);

        // إضافة تقارير الخدمات المفصلة
        addServiceReports(invoiceNo, savedInvoice.getCustomer(), savedInvoice.getDateTime(), itemsCopy);

        System.out.println("تم حفظ الفاتورة رقم: " + invoiceNo);
        System.out.println("العميل: " + savedInvoice.getCustomerName());
        System.out.println("عدد الأصناف: " + itemsCopy.size());
        System.out.println("الإجمالي: " + decimalFormat.format(total) + " ج.م");
    }

    /**
     * إضافة تقارير الخدمات المفصلة
     */
    private void addServiceReports(String invoiceNo, Customer customer, java.time.LocalDateTime dateTime, ObservableList<InvoiceItem> items) {
        for (InvoiceItem item : items) {
            if (item.getServiceType() != null && !item.getServiceType().trim().isEmpty()) {
                ServiceReport report = new ServiceReport(
                    invoiceNo,
                    dateTime,
                    customer.getName(),
                    item.getServiceType(),
                    item.getThickness(),
                    item.getDescription() != null ? item.getDescription() : "",
                    item.getTotalQuantity(),
                    item.getPrice(),
                    item.getTotalValue()
                );
                serviceReports.add(report);
            }
        }
    }

    /**
     * عرض معاينة الطباعة
     */
    private void showPrintPreview(String action) {
        Stage printStage = new Stage();
        printStage.setTitle("معاينة " + action);
        printStage.initModality(Modality.APPLICATION_MODAL);
        printStage.setWidth(800);
        printStage.setHeight(900);

        VBox printLayout = new VBox(20);
        printLayout.setPadding(new Insets(30));
        printLayout.setStyle("-fx-background-color: white;");

        // رأس الفاتورة
        VBox header = createPrintHeader();

        // معلومات الفاتورة
        VBox invoiceInfo = createPrintInvoiceInfo();

        // جدول الأصناف للطباعة
        VBox itemsSection = createPrintItemsSection();

        // الإجمالي
        VBox totalSection = createPrintTotalSection();

        // تذييل الفاتورة
        VBox footer = createPrintFooter();

        printLayout.getChildren().addAll(header, invoiceInfo, itemsSection, totalSection, footer);

        // أزرار الطباعة
        HBox buttonBox = new HBox(15);
        buttonBox.setAlignment(Pos.CENTER);
        buttonBox.setPadding(new Insets(20, 0, 0, 0));

        Button printBtn = new Button("طباعة");
        FontAwesomeIconView printIcon = new FontAwesomeIconView(FontAwesomeIcon.PRINT);
        printIcon.setSize("14px");
        printBtn.setGraphic(printIcon);
        printBtn.getStyleClass().add("print-button");
        printBtn.setOnAction(e -> {
            showPlaceholderDialog("طباعة", "تم إرسال الفاتورة للطباعة!");
            printStage.close();
        });

        Button savePdfBtn = new Button("حفظ PDF");
        FontAwesomeIconView pdfIcon = new FontAwesomeIconView(FontAwesomeIcon.FILE_PDF_ALT);
        pdfIcon.setSize("14px");
        savePdfBtn.setGraphic(pdfIcon);
        savePdfBtn.getStyleClass().add("save-button");
        savePdfBtn.setOnAction(e -> {
            showPlaceholderDialog("حفظ PDF", "تم حفظ الفاتورة كملف PDF!");
            printStage.close();
        });

        Button closeBtn = new Button("إغلاق");
        closeBtn.setOnAction(e -> printStage.close());

        buttonBox.getChildren().addAll(printBtn, savePdfBtn, closeBtn);

        ScrollPane scrollPane = new ScrollPane(printLayout);
        scrollPane.setFitToWidth(true);
        scrollPane.setStyle("-fx-background-color: #f5f5f5;");

        VBox mainContainer = new VBox();
        mainContainer.getChildren().addAll(scrollPane, buttonBox);

        Scene scene = new Scene(mainContainer);
        scene.getStylesheets().add(getClass().getResource("/css/styles.css").toExternalForm());
        printStage.setScene(scene);
        printStage.showAndWait();
    }

    /**
     * إنشاء رأس الفاتورة للطباعة
     */
    private VBox createPrintHeader() {
        VBox header = new VBox(10);
        header.setAlignment(Pos.CENTER);
        header.setStyle("-fx-border-color: #000000; -fx-border-width: 0 0 2 0; -fx-padding: 0 0 15 0;");

        // الحصول على بيانات الشركة
        com.accounting.model.CompanyInfo companyInfo = com.accounting.model.CompanyInfo.getInstance();

        Label companyName = new Label(companyInfo.getCompanyName());
        companyName.setStyle("-fx-font-size: 24px; -fx-font-weight: bold;");

        Label activityLabel = new Label(companyInfo.getActivity());
        activityLabel.setStyle("-fx-font-size: 14px; -fx-text-fill: #333333;");

        Label companyDetails = new Label(companyInfo.getFormattedInfoForPrint());
        companyDetails.setStyle("-fx-font-size: 12px; -fx-text-fill: #666666;");

        Label invoiceTitle = new Label("فاتورة مبيعات");
        invoiceTitle.setStyle("-fx-font-size: 20px; -fx-font-weight: bold; -fx-text-fill: #2c3e50;");

        header.getChildren().addAll(companyName, activityLabel, companyDetails, invoiceTitle);
        return header;
    }

    /**
     * إنشاء معلومات الفاتورة للطباعة
     */
    private VBox createPrintInvoiceInfo() {
        VBox infoSection = new VBox(10);

        HBox infoRow = new HBox(50);
        infoRow.setAlignment(Pos.CENTER_LEFT);

        VBox leftInfo = new VBox(5);
        leftInfo.getChildren().addAll(
            new Label("رقم الفاتورة: " + generateInvoiceNumber()),
            new Label("التاريخ: " + java.time.LocalDate.now().toString()),
            new Label("الوقت: " + java.time.LocalTime.now().toString().substring(0, 8))
        );

        VBox rightInfo = new VBox(5);
        String customerName = customerComboBox.getValue() != null ? customerComboBox.getValue().getName() : "عميل نقدي";
        String customerPhone = customerComboBox.getValue() != null ? customerComboBox.getValue().getPhone() : "";
        rightInfo.getChildren().addAll(
            new Label("العميل: " + customerName),
            new Label("الهاتف: " + customerPhone),
            new Label("حالة الفاتورة: مؤكدة")
        );

        infoRow.getChildren().addAll(leftInfo, rightInfo);
        infoSection.getChildren().add(infoRow);

        return infoSection;
    }

    /**
     * إنشاء قسم الأصناف للطباعة
     */
    private VBox createPrintItemsSection() {
        VBox itemsSection = new VBox(10);

        Label itemsTitle = new Label("تفاصيل الأصناف:");
        itemsTitle.setStyle("-fx-font-size: 16px; -fx-font-weight: bold;");

        // رأس الجدول
        HBox tableHeader = new HBox();
        tableHeader.setStyle("-fx-background-color: #ecf0f1; -fx-padding: 10; -fx-border-color: #bdc3c7; -fx-border-width: 1;");

        Label[] headers = {
            new Label("ت"),
            new Label("التفاصيل"),
            new Label("نوع الخدمة"),
            new Label("السمك"),
            new Label("الطول"),
            new Label("العرض"),
            new Label("م²"),
            new Label("العدد"),
            new Label("إجمالي الكمية"),
            new Label("السعر"),
            new Label("الإجمالي")
        };

        double[] widths = {30, 120, 120, 60, 70, 70, 70, 50, 90, 70, 90};

        for (int i = 0; i < headers.length; i++) {
            headers[i].setPrefWidth(widths[i]);
            headers[i].setStyle("-fx-font-weight: bold; -fx-text-alignment: center;");
            tableHeader.getChildren().add(headers[i]);
        }

        VBox tableRows = new VBox();

        // صفوف البيانات
        for (int i = 0; i < invoiceItems.size(); i++) {
            InvoiceItem item = invoiceItems.get(i);
            if (item.getServiceType() != null && !item.getServiceType().trim().isEmpty()) {
                HBox row = createPrintTableRow(i + 1, item, widths);
                tableRows.getChildren().add(row);
            }
        }

        itemsSection.getChildren().addAll(itemsTitle, tableHeader, tableRows);
        return itemsSection;
    }

    /**
     * إنشاء صف في جدول الطباعة
     */
    private HBox createPrintTableRow(int index, InvoiceItem item, double[] widths) {
        HBox row = new HBox();
        row.setStyle("-fx-border-color: #bdc3c7; -fx-border-width: 0 1 1 1; -fx-padding: 8;");

        Label[] cells = {
            new Label(String.valueOf(index)),
            new Label(item.getDescription() != null ? item.getDescription() : ""),
            new Label(item.getServiceType() != null ? item.getServiceType() : ""),
            new Label(item.getThicknessObject() != null ? item.getThicknessObject().toString() : ""),
            new Label(item.getLengthObject() != null ? decimalFormat.format(item.getLengthObject()) : ""),
            new Label(item.getWidthObject() != null ? decimalFormat.format(item.getWidthObject()) : ""),
            new Label(item.getSquareMetersObject() != null ? decimalFormat.format(item.getSquareMetersObject()) : ""),
            new Label(item.getQuantityObject() != null ? item.getQuantityObject().toString() : ""),
            new Label(item.getTotalQuantityObject() != null ? decimalFormat.format(item.getTotalQuantityObject()) : ""),
            new Label(item.getPriceObject() != null ? decimalFormat.format(item.getPriceObject()) : ""),
            new Label(item.getTotalValueObject() != null ? decimalFormat.format(item.getTotalValueObject()) + " ج.م" : "")
        };

        for (int i = 0; i < cells.length; i++) {
            cells[i].setPrefWidth(widths[i]);
            cells[i].setStyle("-fx-text-alignment: center;");
            row.getChildren().add(cells[i]);
        }

        return row;
    }

    /**
     * إنشاء قسم الإجمالي للطباعة
     */
    private VBox createPrintTotalSection() {
        VBox totalSection = new VBox(10);
        totalSection.setAlignment(Pos.CENTER_RIGHT);
        totalSection.setStyle("-fx-border-color: #34495e; -fx-border-width: 2 0 0 0; -fx-padding: 15 0 0 0;");

        // حساب الإجماليات
        double subtotal = invoiceItems.stream()
            .mapToDouble(item -> item.getTotalValueObject() != null ? item.getTotalValueObject() : 0.0)
            .sum();

        double discount = 0.0; // يمكن إضافة حقل الخصم لاحقاً
        double total = subtotal - discount;

        HBox subtotalRow = new HBox();
        subtotalRow.setAlignment(Pos.CENTER_RIGHT);
        Label subtotalLabel = new Label("المجموع الفرعي: " + decimalFormat.format(subtotal) + " ج.م");
        subtotalLabel.setStyle("-fx-font-size: 14px;");
        subtotalRow.getChildren().add(subtotalLabel);

        HBox discountRow = new HBox();
        discountRow.setAlignment(Pos.CENTER_RIGHT);
        Label discountLabel = new Label("الخصم: " + decimalFormat.format(discount) + " ج.م");
        discountLabel.setStyle("-fx-font-size: 14px;");
        discountRow.getChildren().add(discountLabel);

        HBox totalRow = new HBox();
        totalRow.setAlignment(Pos.CENTER_RIGHT);
        totalRow.setStyle("-fx-background-color: #ecf0f1; -fx-padding: 10; -fx-border-color: #34495e; -fx-border-width: 1;");
        Label totalLabel = new Label("الإجمالي النهائي: " + decimalFormat.format(total) + " ج.م");
        totalLabel.setStyle("-fx-font-size: 18px; -fx-font-weight: bold; -fx-text-fill: #2c3e50;");
        totalRow.getChildren().add(totalLabel);

        totalSection.getChildren().addAll(subtotalRow, discountRow, totalRow);
        return totalSection;
    }

    /**
     * إنشاء تذييل الفاتورة للطباعة
     */
    private VBox createPrintFooter() {
        VBox footer = new VBox(10);
        footer.setAlignment(Pos.CENTER);
        footer.setStyle("-fx-border-color: #000000; -fx-border-width: 2 0 0 0; -fx-padding: 15 0 0 0;");

        Label thanksLabel = new Label("شكراً لتعاملكم معنا");
        thanksLabel.setStyle("-fx-font-size: 16px; -fx-font-weight: bold;");

        Label footerInfo = new Label("هذه الفاتورة صادرة إلكترونياً ولا تحتاج إلى توقيع");
        footerInfo.setStyle("-fx-font-size: 10px; -fx-text-fill: #666666;");

        Label printDate = new Label("تاريخ الطباعة: " + java.time.LocalDateTime.now().toString().substring(0, 19));
        printDate.setStyle("-fx-font-size: 10px; -fx-text-fill: #666666;");

        footer.getChildren().addAll(thanksLabel, footerInfo, printDate);
        return footer;
    }

    /**
     * عرض الفاتورة المحفوظة
     */
    private void viewSavedInvoice(SavedInvoice invoice) {
        Stage dialog = new Stage();
        dialog.setTitle("عرض الفاتورة - " + invoice.getInvoiceNumber());
        dialog.initModality(Modality.APPLICATION_MODAL);
        dialog.setWidth(900);
        dialog.setHeight(700);

        VBox mainLayout = new VBox(20);
        mainLayout.setPadding(new Insets(20));

        // معلومات الفاتورة
        GridPane infoGrid = new GridPane();
        infoGrid.setHgap(20);
        infoGrid.setVgap(10);
        infoGrid.getStyleClass().add("invoice-info");

        infoGrid.add(new Label("رقم الفاتورة:"), 0, 0);
        infoGrid.add(new Label(invoice.getInvoiceNumber()), 1, 0);
        infoGrid.add(new Label("التاريخ:"), 2, 0);
        infoGrid.add(new Label(invoice.getFormattedDateTime()), 3, 0);

        infoGrid.add(new Label("العميل:"), 0, 1);
        infoGrid.add(new Label(invoice.getCustomerName()), 1, 1);
        infoGrid.add(new Label("الهاتف:"), 2, 1);
        infoGrid.add(new Label(invoice.getCustomerPhone()), 3, 1);

        // جدول الأصناف
        TableView<InvoiceItem> itemsTable = new TableView<>();
        itemsTable.setItems(invoice.getItems());
        itemsTable.setMinHeight(300);
        VBox.setVgrow(itemsTable, Priority.ALWAYS);

        // إضافة أعمدة الجدول (نفس أعمدة الفاتورة الأساسية)
        TableColumn<InvoiceItem, String> serviceCol = new TableColumn<>("نوع الخدمة");
        serviceCol.setPrefWidth(150);
        serviceCol.setCellValueFactory(new PropertyValueFactory<>("serviceType"));

        TableColumn<InvoiceItem, String> descCol = new TableColumn<>("الوصف");
        descCol.setPrefWidth(200);
        descCol.setCellValueFactory(new PropertyValueFactory<>("description"));

        TableColumn<InvoiceItem, Double> priceCol = new TableColumn<>("السعر");
        priceCol.setPrefWidth(100);
        priceCol.setCellValueFactory(new PropertyValueFactory<>("price"));
        priceCol.setCellFactory(col -> new TableCell<InvoiceItem, Double>() {
            @Override
            protected void updateItem(Double item, boolean empty) {
                super.updateItem(item, empty);
                if (empty || item == null) {
                    setText(null);
                } else {
                    setText(decimalFormat.format(item));
                }
            }
        });

        TableColumn<InvoiceItem, Double> totalCol = new TableColumn<>("الإجمالي");
        totalCol.setPrefWidth(120);
        totalCol.setCellValueFactory(new PropertyValueFactory<>("totalValue"));
        totalCol.setCellFactory(col -> new TableCell<InvoiceItem, Double>() {
            @Override
            protected void updateItem(Double item, boolean empty) {
                super.updateItem(item, empty);
                if (empty || item == null) {
                    setText(null);
                } else {
                    setText(decimalFormat.format(item) + " ج.م");
                }
            }
        });

        itemsTable.getColumns().addAll(serviceCol, descCol, priceCol, totalCol);

        // ملخص الفاتورة
        VBox summaryBox = new VBox(10);
        summaryBox.setAlignment(Pos.CENTER_RIGHT);
        summaryBox.getStyleClass().add("invoice-summary");

        Label subtotalLabel = new Label("الإجمالي الفرعي: " + decimalFormat.format(invoice.getSubtotal()) + " ج.م");
        Label discountLabel = new Label("الخصم: " + decimalFormat.format(invoice.getDiscount()) + " ج.م");
        Label totalLabel = new Label("الإجمالي النهائي: " + decimalFormat.format(invoice.getTotal()) + " ج.م");
        totalLabel.getStyleClass().add("total-value");

        summaryBox.getChildren().addAll(subtotalLabel, discountLabel, totalLabel);

        // أزرار الحوار
        HBox buttonBox = new HBox(15);
        buttonBox.setAlignment(Pos.CENTER_RIGHT);

        Button printButton = new Button("طباعة");
        FontAwesomeIconView printIcon = new FontAwesomeIconView(FontAwesomeIcon.PRINT);
        printIcon.setSize("14px");
        printButton.setGraphic(printIcon);
        printButton.getStyleClass().add("print-button");
        printButton.setOnAction(e -> {
            printSavedInvoice(invoice);
            dialog.close();
        });

        Button closeButton = new Button("إغلاق");
        closeButton.setOnAction(e -> dialog.close());

        buttonBox.getChildren().addAll(printButton, closeButton);

        mainLayout.getChildren().addAll(
            new Label("تفاصيل الفاتورة:"),
            infoGrid,
            new Separator(),
            new Label("أصناف الفاتورة:"),
            itemsTable,
            summaryBox,
            buttonBox
        );

        Scene scene = new Scene(mainLayout);
        scene.getStylesheets().add(getClass().getResource("/css/styles.css").toExternalForm());
        dialog.setScene(scene);
        dialog.showAndWait();
    }

    /**
     * تعديل الفاتورة المحفوظة
     */
    private void editSavedInvoice(SavedInvoice invoice) {
        // مسح الفاتورة الحالية
        invoiceItems.clear();

        // تحميل بيانات الفاتورة المحفوظة
        customerComboBox.setValue(invoice.getCustomer());
        discountField.setText(String.valueOf(invoice.getDiscount()));

        // تحميل الأصناف
        for (InvoiceItem item : invoice.getItems()) {
            InvoiceItem newItem = new InvoiceItem(item.isManualRow());
            newItem.setServiceType(item.getServiceType());
            newItem.setThickness(item.getThickness());
            newItem.setLength(item.getLength());
            newItem.setWidth(item.getWidth());
            newItem.setQuantity(item.getQuantity());
            newItem.setPrice(item.getPrice());
            newItem.setDescription(item.getDescription());
            invoiceItems.add(newItem);
        }

        // حذف الفاتورة من القائمة المحفوظة
        savedInvoices.remove(invoice);

        // تحديث العداد
        invoiceCounter--;

        showPlaceholderDialog("تم التحميل", "تم تحميل الفاتورة للتعديل. يمكنك الآن تعديلها وحفظها مرة أخرى.");
    }

    /**
     * طباعة الفاتورة المحفوظة
     */
    private void printSavedInvoice(SavedInvoice invoice) {
        showSavedInvoicePrintPreview(invoice);
    }

    /**
     * معاينة طباعة الفاتورة المحفوظة
     */
    private void showSavedInvoicePrintPreview(SavedInvoice invoice) {
        Stage printStage = new Stage();
        printStage.setTitle("معاينة طباعة - " + invoice.getInvoiceNumber());
        printStage.initModality(Modality.APPLICATION_MODAL);
        printStage.setWidth(800);
        printStage.setHeight(900);

        VBox printLayout = new VBox(20);
        printLayout.setPadding(new Insets(30));
        printLayout.setStyle("-fx-background-color: white;");

        // رأس الفاتورة
        VBox header = createPrintHeader();

        // معلومات الفاتورة المحفوظة
        VBox invoiceInfo = createSavedInvoicePrintInfo(invoice);

        // جدول الأصناف للطباعة
        VBox itemsSection = createSavedInvoicePrintItemsSection(invoice);

        // الإجمالي
        VBox totalSection = createSavedInvoicePrintTotalSection(invoice);

        // تذييل الفاتورة
        VBox footer = createPrintFooter();

        printLayout.getChildren().addAll(header, invoiceInfo, itemsSection, totalSection, footer);

        // أزرار الطباعة
        HBox buttonBox = new HBox(15);
        buttonBox.setAlignment(Pos.CENTER);
        buttonBox.setPadding(new Insets(20, 0, 0, 0));

        Button printBtn = new Button("طباعة");
        FontAwesomeIconView printIcon = new FontAwesomeIconView(FontAwesomeIcon.PRINT);
        printIcon.setSize("14px");
        printBtn.setGraphic(printIcon);
        printBtn.getStyleClass().add("print-button");
        printBtn.setOnAction(e -> {
            showPlaceholderDialog("طباعة", "تم إرسال الفاتورة " + invoice.getInvoiceNumber() + " للطباعة!");
            printStage.close();
        });

        Button savePdfBtn = new Button("حفظ PDF");
        FontAwesomeIconView pdfIcon = new FontAwesomeIconView(FontAwesomeIcon.FILE_PDF_ALT);
        pdfIcon.setSize("14px");
        savePdfBtn.setGraphic(pdfIcon);
        savePdfBtn.getStyleClass().add("save-button");
        savePdfBtn.setOnAction(e -> {
            showPlaceholderDialog("حفظ PDF", "تم حفظ الفاتورة " + invoice.getInvoiceNumber() + " كملف PDF!");
            printStage.close();
        });

        Button closeBtn = new Button("إغلاق");
        closeBtn.setOnAction(e -> printStage.close());

        buttonBox.getChildren().addAll(printBtn, savePdfBtn, closeBtn);

        ScrollPane scrollPane = new ScrollPane(printLayout);
        scrollPane.setFitToWidth(true);
        scrollPane.setStyle("-fx-background-color: #f5f5f5;");

        VBox mainContainer = new VBox();
        mainContainer.getChildren().addAll(scrollPane, buttonBox);

        Scene scene = new Scene(mainContainer);
        scene.getStylesheets().add(getClass().getResource("/css/styles.css").toExternalForm());
        printStage.setScene(scene);
        printStage.showAndWait();
    }

    /**
     * إنشاء معلومات الفاتورة المحفوظة للطباعة
     */
    private VBox createSavedInvoicePrintInfo(SavedInvoice invoice) {
        VBox infoSection = new VBox(10);

        HBox infoRow = new HBox(50);
        infoRow.setAlignment(Pos.CENTER_LEFT);

        VBox leftInfo = new VBox(5);
        leftInfo.getChildren().addAll(
            new Label("رقم الفاتورة: " + invoice.getInvoiceNumber()),
            new Label("التاريخ: " + invoice.getFormattedDate()),
            new Label("الوقت: " + invoice.getFormattedTime())
        );

        VBox rightInfo = new VBox(5);
        rightInfo.getChildren().addAll(
            new Label("العميل: " + invoice.getCustomerName()),
            new Label("الهاتف: " + invoice.getCustomerPhone()),
            new Label("حالة الفاتورة: " + invoice.getStatus())
        );

        infoRow.getChildren().addAll(leftInfo, rightInfo);
        infoSection.getChildren().add(infoRow);

        return infoSection;
    }

    /**
     * إنشاء قسم الأصناف للفاتورة المحفوظة للطباعة
     */
    private VBox createSavedInvoicePrintItemsSection(SavedInvoice invoice) {
        VBox itemsSection = new VBox(10);

        Label itemsTitle = new Label("تفاصيل الأصناف:");
        itemsTitle.setStyle("-fx-font-size: 16px; -fx-font-weight: bold;");

        // رأس الجدول
        HBox tableHeader = new HBox();
        tableHeader.setStyle("-fx-background-color: #ecf0f1; -fx-padding: 10; -fx-border-color: #bdc3c7; -fx-border-width: 1;");

        Label[] headers = {
            new Label("#"),
            new Label("نوع الخدمة"),
            new Label("الوصف"),
            new Label("السعر"),
            new Label("الإجمالي")
        };

        double[] widths = {50, 200, 250, 100, 120};

        for (int i = 0; i < headers.length; i++) {
            headers[i].setPrefWidth(widths[i]);
            headers[i].setStyle("-fx-font-weight: bold; -fx-text-alignment: center;");
            tableHeader.getChildren().add(headers[i]);
        }

        VBox tableRows = new VBox();

        // صفوف البيانات
        for (int i = 0; i < invoice.getItems().size(); i++) {
            InvoiceItem item = invoice.getItems().get(i);
            HBox row = createSavedInvoicePrintTableRow(i + 1, item, widths);
            tableRows.getChildren().add(row);
        }

        itemsSection.getChildren().addAll(itemsTitle, tableHeader, tableRows);
        return itemsSection;
    }

    /**
     * إنشاء صف في جدول الطباعة للفاتورة المحفوظة
     */
    private HBox createSavedInvoicePrintTableRow(int index, InvoiceItem item, double[] widths) {
        HBox row = new HBox();
        row.setStyle("-fx-border-color: #bdc3c7; -fx-border-width: 0 1 1 1; -fx-padding: 8;");

        Label[] cells = {
            new Label(String.valueOf(index)),
            new Label(item.getServiceType() != null ? item.getServiceType() : ""),
            new Label(item.getDescription() != null ? item.getDescription() : ""),
            new Label(item.getPriceObject() != null ? decimalFormat.format(item.getPriceObject()) : ""),
            new Label(item.getTotalValueObject() != null ? decimalFormat.format(item.getTotalValueObject()) + " ج.م" : "")
        };

        for (int i = 0; i < cells.length; i++) {
            cells[i].setPrefWidth(widths[i]);
            cells[i].setStyle("-fx-text-alignment: center;");
            row.getChildren().add(cells[i]);
        }

        return row;
    }

    /**
     * إنشاء قسم الإجمالي للفاتورة المحفوظة للطباعة
     */
    private VBox createSavedInvoicePrintTotalSection(SavedInvoice invoice) {
        VBox totalSection = new VBox(10);
        totalSection.setAlignment(Pos.CENTER_RIGHT);
        totalSection.setStyle("-fx-border-color: #34495e; -fx-border-width: 2 0 0 0; -fx-padding: 15 0 0 0;");

        HBox subtotalRow = new HBox();
        subtotalRow.setAlignment(Pos.CENTER_RIGHT);
        Label subtotalLabel = new Label("المجموع الفرعي: " + decimalFormat.format(invoice.getSubtotal()) + " ج.م");
        subtotalLabel.setStyle("-fx-font-size: 14px;");
        subtotalRow.getChildren().add(subtotalLabel);

        HBox discountRow = new HBox();
        discountRow.setAlignment(Pos.CENTER_RIGHT);
        Label discountLabel = new Label("الخصم: " + decimalFormat.format(invoice.getDiscount()) + " ج.م");
        discountLabel.setStyle("-fx-font-size: 14px;");
        discountRow.getChildren().add(discountLabel);

        HBox totalRow = new HBox();
        totalRow.setAlignment(Pos.CENTER_RIGHT);
        totalRow.setStyle("-fx-background-color: #ecf0f1; -fx-padding: 10; -fx-border-color: #34495e; -fx-border-width: 1;");
        Label totalLabel = new Label("الإجمالي النهائي: " + decimalFormat.format(invoice.getTotal()) + " ج.م");
        totalLabel.setStyle("-fx-font-size: 18px; -fx-font-weight: bold; -fx-text-fill: #2c3e50;");
        totalRow.getChildren().add(totalLabel);

        totalSection.getChildren().addAll(subtotalRow, discountRow, totalRow);
        return totalSection;
    }

    /**
     * الحصول على أفضل عميل
     */
    private String getTopCustomer() {
        if (savedInvoices.isEmpty()) {
            return "غير محدد";
        }

        // حساب إجمالي مبيعات كل عميل
        Map<String, Double> customerSales = new HashMap<>();
        for (SavedInvoice invoice : savedInvoices) {
            String customerName = invoice.getCustomerName();
            customerSales.put(customerName,
                customerSales.getOrDefault(customerName, 0.0) + invoice.getTotal());
        }

        // العثور على العميل الأفضل
        return customerSales.entrySet().stream()
            .max(Map.Entry.comparingByValue())
            .map(Map.Entry::getKey)
            .orElse("غير محدد");
    }

    /**
     * معاينة طباعة للحفظ كـ PDF
     */
    private void showPrintPreviewForSave() {
        Stage printStage = new Stage();
        printStage.setTitle("معاينة حفظ PDF - فاتورة مبيعات");
        printStage.initModality(Modality.APPLICATION_MODAL);
        printStage.setWidth(800);
        printStage.setHeight(900);

        VBox printLayout = createPrintLayout();

        // أزرار الحفظ
        HBox buttonBox = new HBox(15);
        buttonBox.setAlignment(Pos.CENTER);
        buttonBox.setPadding(new Insets(20, 0, 0, 0));

        Button savePdfBtn = new Button("حفظ PDF");
        FontAwesomeIconView pdfIcon = new FontAwesomeIconView(FontAwesomeIcon.FILE_PDF_ALT);
        pdfIcon.setSize("14px");
        savePdfBtn.setGraphic(pdfIcon);
        savePdfBtn.getStyleClass().add("save-button");
        savePdfBtn.setOnAction(e -> {
            String fileName = "فاتورة_" + generateInvoiceNumber();
            boolean success = PrintUtils.saveAsPDF(printLayout, fileName, printStage);
            if (success) {
                showPlaceholderDialog("تم الحفظ", "تم حفظ الفاتورة كملف PDF بنجاح!");
            } else {
                showPlaceholderDialog("خطأ", "حدث خطأ أثناء حفظ الملف");
            }
            printStage.close();
        });

        Button closeBtn = new Button("إغلاق");
        closeBtn.setOnAction(e -> printStage.close());

        buttonBox.getChildren().addAll(savePdfBtn, closeBtn);

        ScrollPane scrollPane = new ScrollPane(printLayout);
        scrollPane.setFitToWidth(true);
        scrollPane.setStyle("-fx-background-color: #f5f5f5;");

        VBox mainContainer = new VBox();
        mainContainer.getChildren().addAll(scrollPane, buttonBox);

        Scene scene = new Scene(mainContainer);
        scene.getStylesheets().add(getClass().getResource("/css/styles.css").toExternalForm());
        printStage.setScene(scene);
        printStage.showAndWait();
    }

    /**
     * معاينة طباعة للطباعة الفعلية
     */
    private void showPrintPreviewForPrint() {
        Stage printStage = new Stage();
        printStage.setTitle("معاينة طباعة - فاتورة مبيعات");
        printStage.initModality(Modality.APPLICATION_MODAL);
        printStage.setWidth(800);
        printStage.setHeight(900);

        VBox printLayout = createPrintLayout();

        // أزرار الطباعة
        HBox buttonBox = new HBox(15);
        buttonBox.setAlignment(Pos.CENTER);
        buttonBox.setPadding(new Insets(20, 0, 0, 0));

        Button printBtn = new Button("طباعة");
        FontAwesomeIconView printIcon = new FontAwesomeIconView(FontAwesomeIcon.PRINT);
        printIcon.setSize("14px");
        printBtn.setGraphic(printIcon);
        printBtn.getStyleClass().add("print-button");
        printBtn.setOnAction(e -> {
            if (PrintUtils.isPrinterAvailable()) {
                boolean success = PrintUtils.printNode(printLayout, "فاتورة مبيعات");
                if (success) {
                    showPlaceholderDialog("تم الإرسال", "تم إرسال الفاتورة للطباعة بنجاح!");
                } else {
                    showPlaceholderDialog("خطأ", "حدث خطأ أثناء الطباعة");
                }
            } else {
                showPlaceholderDialog("خطأ", "لا توجد طابعة متاحة");
            }
            printStage.close();
        });

        Button closeBtn = new Button("إغلاق");
        closeBtn.setOnAction(e -> printStage.close());

        buttonBox.getChildren().addAll(printBtn, closeBtn);

        ScrollPane scrollPane = new ScrollPane(printLayout);
        scrollPane.setFitToWidth(true);
        scrollPane.setStyle("-fx-background-color: #f5f5f5;");

        VBox mainContainer = new VBox();
        mainContainer.getChildren().addAll(scrollPane, buttonBox);

        Scene scene = new Scene(mainContainer);
        scene.getStylesheets().add(getClass().getResource("/css/styles.css").toExternalForm());
        printStage.setScene(scene);
        printStage.showAndWait();
    }

    /**
     * إنشاء تخطيط الطباعة
     */
    private VBox createPrintLayout() {
        VBox printLayout = new VBox(20);
        printLayout.setPadding(new Insets(30));
        printLayout.setStyle("-fx-background-color: white;");

        // رأس الفاتورة
        VBox header = createPrintHeader();

        // معلومات الفاتورة
        VBox invoiceInfo = createPrintInvoiceInfo();

        // جدول الأصناف للطباعة
        VBox itemsSection = createPrintItemsSection();

        // الإجمالي
        VBox totalSection = createPrintTotalSection();

        // تذييل الفاتورة
        VBox footer = createPrintFooter();

        printLayout.getChildren().addAll(header, invoiceInfo, itemsSection, totalSection, footer);
        return printLayout;
    }
}
