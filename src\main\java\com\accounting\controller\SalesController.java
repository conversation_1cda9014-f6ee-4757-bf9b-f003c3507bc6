package com.accounting.controller;

import com.accounting.model.InvoiceItem;
import com.accounting.model.Service;
import com.accounting.model.Customer;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.geometry.Insets;
import javafx.geometry.Pos;
import javafx.scene.Scene;
import javafx.scene.control.*;
import javafx.scene.control.cell.PropertyValueFactory;
import javafx.scene.control.cell.TextFieldTableCell;
import javafx.scene.control.cell.ComboBoxTableCell;
import javafx.scene.layout.*;
import javafx.stage.Modality;
import javafx.stage.Stage;
import javafx.util.converter.DoubleStringConverter;
import javafx.util.converter.IntegerStringConverter;
import de.jensd.fx.glyphs.fontawesome.FontAwesomeIcon;
import de.jensd.fx.glyphs.fontawesome.FontAwesomeIconView;

import java.text.DecimalFormat;

/**
 * متحكم موديول المبيعات
 * Sales Module Controller
 */
public class SalesController {
    
    private ObservableList<Service> services = FXCollections.observableArrayList();
    private ObservableList<Customer> customers = FXCollections.observableArrayList();
    private ObservableList<InvoiceItem> invoiceItems = FXCollections.observableArrayList();
    private DecimalFormat decimalFormat = new DecimalFormat("#,##0.00");

    // مراجع للملخص
    private Label subtotalValueLabel;
    private TextField discountField;
    private Label totalValueLabel;

    // مراجع للفاتورة
    private ComboBox<Customer> customerComboBox;
    
    public SalesController() {
        // إضافة بعض الخدمات الافتراضية
        services.addAll(
            new Service("1", "قص ليزر", "قص المعادن بالليزر", 50.0),
            new Service("2", "ثني معادن", "ثني وتشكيل المعادن", 30.0),
            new Service("3", "لحام", "لحام القطع المعدنية", 40.0),
            new Service("4", "طلاء", "طلاء وتشطيب المعادن", 25.0),
            new Service("5", "تفريز CNC", "تفريز دقيق بالكمبيوتر", 75.0),
            new Service("6", "خراطة", "خراطة القطع المعدنية", 35.0)
        );

        // إضافة بعض العملاء الافتراضيين
        customers.addAll(
            new Customer("1", "أحمد محمد", "01234567890", "<EMAIL>", "القاهرة", "شركة الأمل", "عميل مميز"),
            new Customer("2", "فاطمة علي", "01098765432", "<EMAIL>", "الجيزة", "مؤسسة النور", ""),
            new Customer("3", "محمد حسن", "01555666777", "<EMAIL>", "الإسكندرية", "شركة المستقبل", "عميل جديد"),
            new Customer("4", "سارة أحمد", "01777888999", "<EMAIL>", "المنصورة", "", "عميل فردي")
        );

        // إضافة صف فارغ للبدء
        addEmptyRow();
    }

    /**
     * إضافة صف فارغ للبدء
     */
    private void addEmptyRow() {
        InvoiceItem emptyItem = new InvoiceItem();
        // ترك الصف فارغ ليقوم المستخدم بملئه
        invoiceItems.add(emptyItem);
    }
    
    /**
     * إنشاء واجهة موديول المبيعات
     */
    public VBox createSalesModule() {
        VBox mainContainer = new VBox(20);
        mainContainer.setPadding(new Insets(20));
        mainContainer.getStyleClass().add("sales-module");
        
        // العنوان الرئيسي
        Label titleLabel = new Label("🛒 موديول المبيعات");
        titleLabel.getStyleClass().add("module-title");
        
        // شريط الأدوات
        HBox toolbar = createToolbar();
        
        // منطقة الفاتورة
        VBox invoiceArea = createInvoiceArea();
        
        mainContainer.getChildren().addAll(titleLabel, toolbar, invoiceArea);
        return mainContainer;
    }
    
    /**
     * إنشاء شريط الأدوات
     */
    private HBox createToolbar() {
        HBox toolbar = new HBox(15);
        toolbar.setAlignment(Pos.CENTER_LEFT);
        toolbar.setPadding(new Insets(10));
        toolbar.getStyleClass().add("toolbar");
        
        // زر إدارة الخدمات
        Button servicesBtn = createToolbarButton("الخدمات", FontAwesomeIcon.COG, this::showServicesDialog);
        
        // زر إدارة العملاء
        Button customersBtn = createToolbarButton("العملاء", FontAwesomeIcon.USERS, this::showCustomersDialog);
        
        // زر تقارير المبيعات
        Button reportsBtn = createToolbarButton("التقارير", FontAwesomeIcon.BAR_CHART, this::showReportsDialog);
        
        // فاصل
        Separator separator = new Separator();
        separator.setOrientation(javafx.geometry.Orientation.VERTICAL);
        
        // زر فاتورة جديدة
        Button newInvoiceBtn = createToolbarButton("فاتورة جديدة", FontAwesomeIcon.PLUS, this::newInvoice);
        
        // زر حفظ الفاتورة
        Button saveInvoiceBtn = createToolbarButton("حفظ الفاتورة", FontAwesomeIcon.SAVE, this::saveInvoice);

        // زر حفظ PDF
        Button savePdfBtn = createToolbarButton("حفظ PDF", FontAwesomeIcon.FILE_PDF_ALT, this::savePdf);

        // زر طباعة
        Button printBtn = createToolbarButton("طباعة", FontAwesomeIcon.PRINT, this::printInvoice);

        toolbar.getChildren().addAll(servicesBtn, customersBtn, reportsBtn, separator,
                                   newInvoiceBtn, saveInvoiceBtn, savePdfBtn, printBtn);
        return toolbar;
    }
    
    /**
     * إنشاء زر شريط الأدوات
     */
    private Button createToolbarButton(String text, FontAwesomeIcon icon, Runnable action) {
        Button button = new Button(text);
        FontAwesomeIconView iconView = new FontAwesomeIconView(icon);
        iconView.setSize("16px");
        button.setGraphic(iconView);
        button.getStyleClass().add("toolbar-button");
        button.setOnAction(e -> action.run());
        return button;
    }
    
    /**
     * إنشاء منطقة الفاتورة
     */
    private VBox createInvoiceArea() {
        VBox invoiceArea = new VBox(15);
        invoiceArea.getStyleClass().add("invoice-area");
        
        // معلومات الفاتورة
        HBox invoiceHeader = createInvoiceHeader();
        
        // جدول عناصر الفاتورة
        TableView<InvoiceItem> invoiceTable = createInvoiceTable();
        
        // أزرار إدارة الصفوف
        HBox rowControls = createRowControls(invoiceTable);
        
        // ملخص الفاتورة
        VBox invoiceSummary = createInvoiceSummary();
        
        invoiceArea.getChildren().addAll(invoiceHeader, invoiceTable, rowControls, invoiceSummary);
        return invoiceArea;
    }
    
    /**
     * إنشاء رأس الفاتورة
     */
    private HBox createInvoiceHeader() {
        HBox header = new HBox(25);
        header.setAlignment(Pos.CENTER_LEFT);
        header.setPadding(new Insets(15));
        header.getStyleClass().add("invoice-header");

        // رقم الفاتورة
        VBox invoiceNoBox = new VBox(5);
        Label invoiceNoLabel = new Label("رقم الفاتورة:");
        invoiceNoLabel.getStyleClass().add("field-label");
        TextField invoiceNoField = new TextField("INV-" + System.currentTimeMillis() % 10000);
        invoiceNoField.setPrefWidth(130);
        invoiceNoField.getStyleClass().add("invoice-field");
        invoiceNoBox.getChildren().addAll(invoiceNoLabel, invoiceNoField);

        // تاريخ الفاتورة
        VBox dateBox = new VBox(5);
        Label dateLabel = new Label("التاريخ:");
        dateLabel.getStyleClass().add("field-label");
        DatePicker datePicker = new DatePicker();
        datePicker.setValue(java.time.LocalDate.now());
        datePicker.setPrefWidth(140);
        datePicker.getStyleClass().add("invoice-field");
        dateBox.getChildren().addAll(dateLabel, datePicker);

        // العميل
        VBox customerBox = new VBox(5);
        Label customerLabel = new Label("العميل:");
        customerLabel.getStyleClass().add("field-label");
        customerComboBox = new ComboBox<>();
        customerComboBox.setItems(customers);
        customerComboBox.setPrefWidth(200);
        customerComboBox.setEditable(false);
        customerComboBox.setPromptText("اختر عميل من القائمة");
        customerComboBox.getStyleClass().add("invoice-field");

        // زر إضافة عميل جديد
        Button addCustomerBtn = new Button("إضافة عميل");
        FontAwesomeIconView addCustomerIcon = new FontAwesomeIconView(FontAwesomeIcon.USER_PLUS);
        addCustomerIcon.setSize("12px");
        addCustomerBtn.setGraphic(addCustomerIcon);
        addCustomerBtn.getStyleClass().add("small-button");
        addCustomerBtn.setOnAction(e -> showAddCustomerDialog());

        HBox customerHBox = new HBox(5);
        customerHBox.getChildren().addAll(customerComboBox, addCustomerBtn);
        customerBox.getChildren().addAll(customerLabel, customerHBox);

        // حالة الفاتورة
        VBox statusBox = new VBox(5);
        Label statusLabel = new Label("حالة الفاتورة:");
        statusLabel.getStyleClass().add("field-label");
        ComboBox<String> statusCombo = new ComboBox<>();
        statusCombo.getItems().addAll("مسودة", "مؤكدة", "مدفوعة", "ملغية");
        statusCombo.setValue("مسودة");
        statusCombo.setPrefWidth(120);
        statusCombo.getStyleClass().add("invoice-field");
        statusBox.getChildren().addAll(statusLabel, statusCombo);

        header.getChildren().addAll(invoiceNoBox, dateBox, customerBox, statusBox);

        return header;
    }
    
    /**
     * إنشاء جدول عناصر الفاتورة
     */
    private TableView<InvoiceItem> createInvoiceTable() {
        TableView<InvoiceItem> table = new TableView<>();
        table.setItems(invoiceItems);
        table.setEditable(true);
        table.getStyleClass().add("invoice-table");
        table.setPrefHeight(400);
        table.setRowFactory(tv -> {
            TableRow<InvoiceItem> row = new TableRow<>();
            row.setPrefHeight(45); // ارتفاع الصف
            return row;
        });

        // إضافة placeholder عندما يكون الجدول فارغ
        Label placeholder = new Label("انقر على 'إضافة صف يدوي' لبدء إنشاء الفاتورة");
        placeholder.getStyleClass().add("table-placeholder");
        table.setPlaceholder(placeholder);
        
        // العمود: #
        TableColumn<InvoiceItem, String> indexCol = new TableColumn<>("#");
        indexCol.setPrefWidth(40);
        indexCol.setCellFactory(col -> {
            TableCell<InvoiceItem, String> cell = new TableCell<>();
            cell.textProperty().bind(cell.indexProperty().add(1).asString());
            return cell;
        });
        
        // العمود: نوع الخدمة
        TableColumn<InvoiceItem, String> serviceCol = new TableColumn<>("نوع الخدمة");
        serviceCol.setPrefWidth(140);
        serviceCol.setCellValueFactory(new PropertyValueFactory<>("serviceType"));

        ObservableList<String> serviceNames = FXCollections.observableArrayList();
        services.forEach(service -> serviceNames.add(service.getName()));
        serviceCol.setCellFactory(ComboBoxTableCell.forTableColumn(serviceNames));
        serviceCol.setOnEditCommit(event -> {
            event.getRowValue().setServiceType(event.getNewValue());
        });
        
        // العمود: السمك
        TableColumn<InvoiceItem, Integer> thicknessCol = new TableColumn<>("السمك (mm)");
        thicknessCol.setPrefWidth(90);
        thicknessCol.setCellValueFactory(new PropertyValueFactory<>("thickness"));

        // إنشاء قائمة السمك من 1 إلى 100
        ObservableList<Integer> thicknessList = FXCollections.observableArrayList();
        for (int i = 1; i <= 100; i++) {
            thicknessList.add(i);
        }
        thicknessCol.setCellFactory(ComboBoxTableCell.forTableColumn(thicknessList));
        thicknessCol.setOnEditCommit(event -> {
            event.getRowValue().setThickness(event.getNewValue());
        });
        
        // العمود: الطول
        TableColumn<InvoiceItem, Double> lengthCol = new TableColumn<>("الطول (mm)");
        lengthCol.setPrefWidth(90);
        lengthCol.setCellValueFactory(new PropertyValueFactory<>("length"));
        lengthCol.setCellFactory(TextFieldTableCell.forTableColumn(new DoubleStringConverter()));
        lengthCol.setOnEditCommit(event -> {
            event.getRowValue().setLength(event.getNewValue());
        });
        
        // العمود: العرض
        TableColumn<InvoiceItem, Double> widthCol = new TableColumn<>("العرض (mm)");
        widthCol.setPrefWidth(90);
        widthCol.setCellValueFactory(new PropertyValueFactory<>("width"));
        widthCol.setCellFactory(TextFieldTableCell.forTableColumn(new DoubleStringConverter()));
        widthCol.setOnEditCommit(event -> {
            event.getRowValue().setWidth(event.getNewValue());
        });
        
        // العمود: المتر المربع (محسوب تلقائياً)
        TableColumn<InvoiceItem, Double> squareMetersCol = new TableColumn<>("المتر المربع");
        squareMetersCol.setPrefWidth(100);
        squareMetersCol.setCellValueFactory(new PropertyValueFactory<>("squareMeters"));
        squareMetersCol.setCellFactory(col -> new TableCell<InvoiceItem, Double>() {
            @Override
            protected void updateItem(Double item, boolean empty) {
                super.updateItem(item, empty);
                getStyleClass().add("calculated-cell");
                if (empty || item == null) {
                    setText(null);
                } else {
                    setText(decimalFormat.format(item));
                }
            }
        });
        
        // العمود: العدد
        TableColumn<InvoiceItem, Integer> quantityCol = new TableColumn<>("العدد");
        quantityCol.setPrefWidth(60);
        quantityCol.setCellValueFactory(new PropertyValueFactory<>("quantity"));
        quantityCol.setCellFactory(TextFieldTableCell.forTableColumn(new IntegerStringConverter()));
        quantityCol.setOnEditCommit(event -> {
            event.getRowValue().setQuantity(event.getNewValue());
        });
        
        // العمود: إجمالي الكمية (محسوب تلقائياً)
        TableColumn<InvoiceItem, Double> totalQuantityCol = new TableColumn<>("إجمالي الكمية");
        totalQuantityCol.setPrefWidth(110);
        totalQuantityCol.setCellValueFactory(new PropertyValueFactory<>("totalQuantity"));
        totalQuantityCol.setCellFactory(col -> new TableCell<InvoiceItem, Double>() {
            @Override
            protected void updateItem(Double item, boolean empty) {
                super.updateItem(item, empty);
                getStyleClass().add("calculated-cell");
                if (empty || item == null) {
                    setText(null);
                } else {
                    setText(decimalFormat.format(item));
                }
            }
        });
        
        // العمود: السعر
        TableColumn<InvoiceItem, Double> priceCol = new TableColumn<>("السعر");
        priceCol.setPrefWidth(80);
        priceCol.setCellValueFactory(new PropertyValueFactory<>("price"));
        priceCol.setCellFactory(TextFieldTableCell.forTableColumn(new DoubleStringConverter()));
        priceCol.setOnEditCommit(event -> {
            event.getRowValue().setPrice(event.getNewValue());
        });
        
        // العمود: إجمالي القيمة (محسوب تلقائياً)
        TableColumn<InvoiceItem, Double> totalValueCol = new TableColumn<>("إجمالي القيمة");
        totalValueCol.setPrefWidth(110);
        totalValueCol.setCellValueFactory(new PropertyValueFactory<>("totalValue"));
        totalValueCol.setCellFactory(col -> new TableCell<InvoiceItem, Double>() {
            @Override
            protected void updateItem(Double item, boolean empty) {
                super.updateItem(item, empty);
                getStyleClass().add("calculated-cell");
                if (empty || item == null) {
                    setText(null);
                } else {
                    setText(decimalFormat.format(item) + " ج.م");
                }
            }
        });
        
        // العمود: الإجراءات
        TableColumn<InvoiceItem, Void> actionsCol = new TableColumn<>("إجراءات");
        actionsCol.setPrefWidth(120);
        actionsCol.setCellFactory(col -> new TableCell<InvoiceItem, Void>() {
            private final HBox actionBox = new HBox(5);
            private final Button addBtn = new Button();
            private final Button deleteBtn = new Button();

            {
                // زر الإضافة
                FontAwesomeIconView addIcon = new FontAwesomeIconView(FontAwesomeIcon.PLUS);
                addIcon.setSize("12px");
                addBtn.setGraphic(addIcon);
                addBtn.getStyleClass().add("add-row-action-button");
                addBtn.setTooltip(new Tooltip("إضافة صف جديد تحت هذا الصف"));
                addBtn.setOnAction(e -> {
                    int currentIndex = getIndex();
                    addRowAfter(getTableView(), currentIndex);
                });

                // زر الحذف
                FontAwesomeIconView deleteIcon = new FontAwesomeIconView(FontAwesomeIcon.TRASH);
                deleteIcon.setSize("12px");
                deleteBtn.setGraphic(deleteIcon);
                deleteBtn.getStyleClass().add("delete-row-action-button");
                deleteBtn.setTooltip(new Tooltip("حذف هذا الصف"));
                deleteBtn.setOnAction(e -> {
                    int currentIndex = getIndex();
                    deleteRow(getTableView(), currentIndex);
                });

                actionBox.setAlignment(Pos.CENTER);
                actionBox.getChildren().addAll(addBtn, deleteBtn);
            }

            @Override
            protected void updateItem(Void item, boolean empty) {
                super.updateItem(item, empty);
                if (empty) {
                    setGraphic(null);
                } else {
                    setGraphic(actionBox);
                }
            }
        });
        
        table.getColumns().addAll(indexCol, serviceCol, thicknessCol, lengthCol, widthCol,
                                 squareMetersCol, quantityCol, totalQuantityCol, priceCol,
                                 totalValueCol, actionsCol);
        
        return table;
    }
    
    /**
     * إنشاء أزرار إدارة الصفوف
     */
    private HBox createRowControls(TableView<InvoiceItem> table) {
        HBox controls = new HBox(15);
        controls.setAlignment(Pos.CENTER_LEFT);
        controls.setPadding(new Insets(15));
        controls.getStyleClass().add("row-controls");

        // زر إضافة صف عادي
        Button addNormalRowBtn = new Button("إضافة صف عادي");
        FontAwesomeIconView normalIcon = new FontAwesomeIconView(FontAwesomeIcon.PLUS);
        normalIcon.setSize("16px");
        addNormalRowBtn.setGraphic(normalIcon);
        addNormalRowBtn.getStyleClass().add("add-normal-row-button");
        addNormalRowBtn.setOnAction(e -> addNewRow(table, false));

        // زر إضافة صف يدوي
        Button addManualRowBtn = new Button("إضافة صف يدوي");
        FontAwesomeIconView manualIcon = new FontAwesomeIconView(FontAwesomeIcon.EDIT);
        manualIcon.setSize("16px");
        addManualRowBtn.setGraphic(manualIcon);
        addManualRowBtn.getStyleClass().add("add-manual-row-button");
        addManualRowBtn.setOnAction(e -> addNewRow(table, true));

        // معلومات إضافية
        Label infoLabel = new Label("الصف العادي: حسابات تلقائية | الصف اليدوي: إدخال يدوي للكمية والوصف");
        infoLabel.getStyleClass().add("info-label");

        controls.getChildren().addAll(addNormalRowBtn, addManualRowBtn, new Separator(), infoLabel);
        return controls;
    }

    /**
     * إضافة صف جديد
     */
    private void addNewRow(TableView<InvoiceItem> table, boolean isManual) {
        InvoiceItem newItem = new InvoiceItem(isManual);
        table.getItems().add(newItem);

        // التركيز على الصف الجديد
        table.getSelectionModel().selectLast();
        table.scrollTo(table.getItems().size() - 1);
    }

    /**
     * إضافة صف جديد بعد صف معين
     */
    private void addRowAfter(TableView<InvoiceItem> table, int index) {
        // تحديد نوع الصف بناءً على الصف الحالي
        boolean isManual = false;
        if (index >= 0 && index < table.getItems().size()) {
            isManual = table.getItems().get(index).isManualRow();
        }

        InvoiceItem newItem = new InvoiceItem(isManual);
        table.getItems().add(index + 1, newItem);

        // التركيز على الصف الجديد
        table.getSelectionModel().select(index + 1);
        table.scrollTo(index + 1);
    }

    /**
     * حذف صف معين
     */
    private void deleteRow(TableView<InvoiceItem> table, int index) {
        if (table.getItems().size() > 0 && index >= 0 && index < table.getItems().size()) {
            table.getItems().remove(index);
        }
    }
    
    /**
     * إنشاء ملخص الفاتورة
     */
    private VBox createInvoiceSummary() {
        VBox summary = new VBox(10);
        summary.setAlignment(Pos.CENTER_RIGHT);
        summary.setPadding(new Insets(15));
        summary.getStyleClass().add("invoice-summary");
        summary.setMaxWidth(300);
        
        // الإجمالي الفرعي
        HBox subtotalBox = new HBox();
        subtotalBox.setAlignment(Pos.CENTER_RIGHT);
        Label subtotalLabel = new Label("الإجمالي الفرعي:");
        subtotalValueLabel = new Label("0.00");
        subtotalValueLabel.getStyleClass().add("summary-value");
        subtotalBox.getChildren().addAll(subtotalLabel, new Region(), subtotalValueLabel);
        HBox.setHgrow(subtotalBox.getChildren().get(1), Priority.ALWAYS);

        // الخصم
        HBox discountBox = new HBox();
        discountBox.setAlignment(Pos.CENTER_RIGHT);
        Label discountLabel = new Label("الخصم:");
        discountField = new TextField("0");
        discountField.setPrefWidth(80);
        discountField.textProperty().addListener((obs, oldVal, newVal) -> updateTotals());
        discountBox.getChildren().addAll(discountLabel, new Region(), discountField);
        HBox.setHgrow(discountBox.getChildren().get(1), Priority.ALWAYS);

        // الإجمالي النهائي
        HBox totalBox = new HBox();
        totalBox.setAlignment(Pos.CENTER_RIGHT);
        totalBox.getStyleClass().add("total-box");
        Label totalLabel = new Label("الإجمالي النهائي:");
        totalLabel.getStyleClass().add("total-label");
        totalValueLabel = new Label("0.00");
        totalValueLabel.getStyleClass().add("total-value");
        totalBox.getChildren().addAll(totalLabel, new Region(), totalValueLabel);
        HBox.setHgrow(totalBox.getChildren().get(1), Priority.ALWAYS);
        
        // ملاحظات
        Label notesLabel = new Label("ملاحظات:");
        TextArea notesArea = new TextArea();
        notesArea.setPrefRowCount(3);
        notesArea.setPromptText("أدخل أي ملاحظات إضافية...");
        
        summary.getChildren().addAll(subtotalBox, discountBox, totalBox, notesLabel, notesArea);

        // إضافة مستمع لتحديث الإجماليات عند تغيير البيانات
        invoiceItems.addListener((javafx.collections.ListChangeListener<InvoiceItem>) change -> {
            while (change.next()) {
                if (change.wasAdded()) {
                    for (InvoiceItem item : change.getAddedSubList()) {
                        item.totalValueProperty().addListener((obs, oldVal, newVal) -> updateTotals());
                    }
                }
            }
            updateTotals();
        });

        return summary;
    }

    /**
     * تحديث الإجماليات
     */
    private void updateTotals() {
        double subtotal = invoiceItems.stream()
                .mapToDouble(InvoiceItem::getTotalValue)
                .sum();

        double discount = 0;
        try {
            discount = Double.parseDouble(discountField.getText());
        } catch (NumberFormatException e) {
            discount = 0;
        }

        double total = subtotal - discount;

        subtotalValueLabel.setText(decimalFormat.format(subtotal));
        totalValueLabel.setText(decimalFormat.format(total));
    }
    
    // أحداث الأزرار
    private void showServicesDialog() {
        showServiceManagementDialog();
    }

    private void showCustomersDialog() {
        showCustomerManagementDialog();
    }

    private void showReportsDialog() {
        showPlaceholderDialog("تقارير المبيعات", "سيتم تطوير هذه الميزة قريباً");
    }

    private void newInvoice() {
        invoiceItems.clear();
        addEmptyRow(); // إضافة صف فارغ للبدء
        customerComboBox.getSelectionModel().clearSelection();
    }

    private void saveInvoice() {
        if (validateInvoice()) {
            showPlaceholderDialog("حفظ الفاتورة", "تم حفظ الفاتورة في تقرير الفواتير بنجاح!");
        }
    }

    private void savePdf() {
        if (validateInvoice()) {
            showPlaceholderDialog("حفظ PDF", "تم حفظ الفاتورة كملف PDF بنجاح!");
        }
    }

    private void printInvoice() {
        if (validateInvoice()) {
            showPlaceholderDialog("طباعة", "تم إرسال الفاتورة للطباعة!");
        }
    }

    private boolean validateInvoice() {
        if (customerComboBox.getSelectionModel().getSelectedItem() == null) {
            showPlaceholderDialog("تحذير", "يرجى اختيار عميل للفاتورة");
            return false;
        }

        if (invoiceItems.isEmpty()) {
            showPlaceholderDialog("تحذير", "يرجى إضافة عناصر للفاتورة");
            return false;
        }

        return true;
    }
    
    private void showPlaceholderDialog(String title, String message) {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle(title);
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }

    /**
     * عرض نافذة إدارة الخدمات
     */
    private void showServiceManagementDialog() {
        Stage dialog = new Stage();
        dialog.setTitle("إدارة الخدمات");
        dialog.initModality(Modality.APPLICATION_MODAL);
        dialog.setWidth(600);
        dialog.setHeight(500);

        VBox mainLayout = new VBox(15);
        mainLayout.setPadding(new Insets(20));

        // العنوان
        Label titleLabel = new Label("إدارة الخدمات");
        titleLabel.getStyleClass().add("dialog-title");

        // جدول الخدمات
        TableView<Service> servicesTable = new TableView<>();
        servicesTable.setItems(services);
        servicesTable.setPrefHeight(300);

        // أعمدة الجدول
        TableColumn<Service, String> nameCol = new TableColumn<>("اسم الخدمة");
        nameCol.setPrefWidth(150);
        nameCol.setCellValueFactory(new PropertyValueFactory<>("name"));
        nameCol.setCellFactory(TextFieldTableCell.forTableColumn());
        nameCol.setOnEditCommit(event -> {
            event.getRowValue().setName(event.getNewValue());
        });

        TableColumn<Service, String> descCol = new TableColumn<>("الوصف");
        descCol.setPrefWidth(200);
        descCol.setCellValueFactory(new PropertyValueFactory<>("description"));
        descCol.setCellFactory(TextFieldTableCell.forTableColumn());
        descCol.setOnEditCommit(event -> {
            event.getRowValue().setDescription(event.getNewValue());
        });

        TableColumn<Service, Double> priceCol = new TableColumn<>("السعر الافتراضي");
        priceCol.setPrefWidth(120);
        priceCol.setCellValueFactory(new PropertyValueFactory<>("defaultPrice"));
        priceCol.setCellFactory(TextFieldTableCell.forTableColumn(new DoubleStringConverter()));
        priceCol.setOnEditCommit(event -> {
            event.getRowValue().setDefaultPrice(event.getNewValue());
        });

        TableColumn<Service, Void> actionsCol = new TableColumn<>("إجراءات");
        actionsCol.setPrefWidth(80);
        actionsCol.setCellFactory(col -> new TableCell<Service, Void>() {
            private final Button deleteBtn = new Button();

            {
                FontAwesomeIconView deleteIcon = new FontAwesomeIconView(FontAwesomeIcon.TRASH);
                deleteIcon.setSize("12px");
                deleteBtn.setGraphic(deleteIcon);
                deleteBtn.getStyleClass().add("delete-button");
                deleteBtn.setOnAction(e -> {
                    Service service = getTableView().getItems().get(getIndex());
                    getTableView().getItems().remove(service);
                });
            }

            @Override
            protected void updateItem(Void item, boolean empty) {
                super.updateItem(item, empty);
                if (empty) {
                    setGraphic(null);
                } else {
                    setGraphic(deleteBtn);
                }
            }
        });

        servicesTable.getColumns().addAll(nameCol, descCol, priceCol, actionsCol);
        servicesTable.setEditable(true);

        // نموذج إضافة خدمة جديدة
        GridPane addServiceForm = new GridPane();
        addServiceForm.setHgap(10);
        addServiceForm.setVgap(10);
        addServiceForm.getStyleClass().add("add-service-form");

        Label nameLabel = new Label("اسم الخدمة:");
        TextField nameField = new TextField();
        nameField.setPromptText("أدخل اسم الخدمة");

        Label descLabel = new Label("الوصف:");
        TextField descField = new TextField();
        descField.setPromptText("أدخل وصف الخدمة");

        Label priceLabel = new Label("السعر الافتراضي:");
        TextField priceField = new TextField();
        priceField.setPromptText("0.00");

        Button addButton = new Button("إضافة خدمة");
        FontAwesomeIconView addIcon = new FontAwesomeIconView(FontAwesomeIcon.PLUS);
        addIcon.setSize("14px");
        addButton.setGraphic(addIcon);
        addButton.getStyleClass().add("add-button");
        addButton.setOnAction(e -> {
            String name = nameField.getText().trim();
            String desc = descField.getText().trim();
            String priceText = priceField.getText().trim();

            if (!name.isEmpty() && !priceText.isEmpty()) {
                try {
                    double price = Double.parseDouble(priceText);
                    String id = String.valueOf(services.size() + 1);
                    Service newService = new Service(id, name, desc, price);
                    services.add(newService);

                    // مسح الحقول
                    nameField.clear();
                    descField.clear();
                    priceField.clear();
                } catch (NumberFormatException ex) {
                    Alert alert = new Alert(Alert.AlertType.ERROR);
                    alert.setTitle("خطأ");
                    alert.setHeaderText(null);
                    alert.setContentText("يرجى إدخال سعر صحيح");
                    alert.showAndWait();
                }
            } else {
                Alert alert = new Alert(Alert.AlertType.WARNING);
                alert.setTitle("تحذير");
                alert.setHeaderText(null);
                alert.setContentText("يرجى ملء جميع الحقول المطلوبة");
                alert.showAndWait();
            }
        });

        addServiceForm.add(nameLabel, 0, 0);
        addServiceForm.add(nameField, 1, 0);
        addServiceForm.add(descLabel, 0, 1);
        addServiceForm.add(descField, 1, 1);
        addServiceForm.add(priceLabel, 0, 2);
        addServiceForm.add(priceField, 1, 2);
        addServiceForm.add(addButton, 1, 3);

        // أزرار الحوار
        HBox buttonBox = new HBox(10);
        buttonBox.setAlignment(Pos.CENTER_RIGHT);

        Button closeButton = new Button("إغلاق");
        closeButton.setOnAction(e -> dialog.close());

        buttonBox.getChildren().add(closeButton);

        mainLayout.getChildren().addAll(titleLabel, servicesTable,
                                       new Separator(), addServiceForm, buttonBox);

        Scene scene = new Scene(mainLayout);
        scene.getStylesheets().add(getClass().getResource("/css/styles.css").toExternalForm());
        dialog.setScene(scene);
        dialog.showAndWait();
    }

    /**
     * عرض نافذة إدارة العملاء
     */
    private void showCustomerManagementDialog() {
        Stage dialog = new Stage();
        dialog.setTitle("إدارة العملاء");
        dialog.initModality(Modality.APPLICATION_MODAL);
        dialog.setWidth(800);
        dialog.setHeight(600);

        VBox mainLayout = new VBox(15);
        mainLayout.setPadding(new Insets(20));

        // العنوان
        Label titleLabel = new Label("إدارة العملاء");
        titleLabel.getStyleClass().add("dialog-title");

        // جدول العملاء
        TableView<Customer> customersTable = new TableView<>();
        customersTable.setItems(customers);
        customersTable.setPrefHeight(350);

        // أعمدة الجدول
        TableColumn<Customer, String> nameCol = new TableColumn<>("الاسم");
        nameCol.setPrefWidth(120);
        nameCol.setCellValueFactory(new PropertyValueFactory<>("name"));
        nameCol.setCellFactory(TextFieldTableCell.forTableColumn());
        nameCol.setOnEditCommit(event -> {
            event.getRowValue().setName(event.getNewValue());
        });

        TableColumn<Customer, String> phoneCol = new TableColumn<>("الهاتف");
        phoneCol.setPrefWidth(100);
        phoneCol.setCellValueFactory(new PropertyValueFactory<>("phone"));
        phoneCol.setCellFactory(TextFieldTableCell.forTableColumn());
        phoneCol.setOnEditCommit(event -> {
            event.getRowValue().setPhone(event.getNewValue());
        });

        TableColumn<Customer, String> emailCol = new TableColumn<>("البريد الإلكتروني");
        emailCol.setPrefWidth(150);
        emailCol.setCellValueFactory(new PropertyValueFactory<>("email"));
        emailCol.setCellFactory(TextFieldTableCell.forTableColumn());
        emailCol.setOnEditCommit(event -> {
            event.getRowValue().setEmail(event.getNewValue());
        });

        TableColumn<Customer, String> companyCol = new TableColumn<>("الشركة");
        companyCol.setPrefWidth(120);
        companyCol.setCellValueFactory(new PropertyValueFactory<>("company"));
        companyCol.setCellFactory(TextFieldTableCell.forTableColumn());
        companyCol.setOnEditCommit(event -> {
            event.getRowValue().setCompany(event.getNewValue());
        });

        TableColumn<Customer, Void> actionsCol = new TableColumn<>("إجراءات");
        actionsCol.setPrefWidth(80);
        actionsCol.setCellFactory(col -> new TableCell<Customer, Void>() {
            private final Button deleteBtn = new Button();

            {
                FontAwesomeIconView deleteIcon = new FontAwesomeIconView(FontAwesomeIcon.TRASH);
                deleteIcon.setSize("12px");
                deleteBtn.setGraphic(deleteIcon);
                deleteBtn.getStyleClass().add("delete-button");
                deleteBtn.setOnAction(e -> {
                    Customer customer = getTableView().getItems().get(getIndex());
                    getTableView().getItems().remove(customer);
                });
            }

            @Override
            protected void updateItem(Void item, boolean empty) {
                super.updateItem(item, empty);
                if (empty) {
                    setGraphic(null);
                } else {
                    setGraphic(deleteBtn);
                }
            }
        });

        customersTable.getColumns().addAll(nameCol, phoneCol, emailCol, companyCol, actionsCol);
        customersTable.setEditable(true);

        // نموذج إضافة عميل جديد
        GridPane addCustomerForm = new GridPane();
        addCustomerForm.setHgap(10);
        addCustomerForm.setVgap(10);
        addCustomerForm.getStyleClass().add("add-service-form");

        Label nameLabel = new Label("الاسم:");
        TextField nameField = new TextField();
        nameField.setPromptText("أدخل اسم العميل");

        Label phoneLabel = new Label("الهاتف:");
        TextField phoneField = new TextField();
        phoneField.setPromptText("أدخل رقم الهاتف");

        Label emailLabel = new Label("البريد الإلكتروني:");
        TextField emailField = new TextField();
        emailField.setPromptText("أدخل البريد الإلكتروني");

        Label companyLabel = new Label("الشركة:");
        TextField companyField = new TextField();
        companyField.setPromptText("أدخل اسم الشركة (اختياري)");

        Button addButton = new Button("إضافة عميل");
        FontAwesomeIconView addIcon = new FontAwesomeIconView(FontAwesomeIcon.USER_PLUS);
        addIcon.setSize("14px");
        addButton.setGraphic(addIcon);
        addButton.getStyleClass().add("add-button");
        addButton.setOnAction(e -> {
            String name = nameField.getText().trim();
            String phone = phoneField.getText().trim();
            String email = emailField.getText().trim();
            String company = companyField.getText().trim();

            if (!name.isEmpty()) {
                String id = String.valueOf(customers.size() + 1);
                Customer newCustomer = new Customer(id, name, phone, email, "", company, "");
                customers.add(newCustomer);

                // مسح الحقول
                nameField.clear();
                phoneField.clear();
                emailField.clear();
                companyField.clear();
            } else {
                Alert alert = new Alert(Alert.AlertType.WARNING);
                alert.setTitle("تحذير");
                alert.setHeaderText(null);
                alert.setContentText("يرجى إدخال اسم العميل على الأقل");
                alert.showAndWait();
            }
        });

        addCustomerForm.add(nameLabel, 0, 0);
        addCustomerForm.add(nameField, 1, 0);
        addCustomerForm.add(phoneLabel, 2, 0);
        addCustomerForm.add(phoneField, 3, 0);
        addCustomerForm.add(emailLabel, 0, 1);
        addCustomerForm.add(emailField, 1, 1);
        addCustomerForm.add(companyLabel, 2, 1);
        addCustomerForm.add(companyField, 3, 1);
        addCustomerForm.add(addButton, 1, 2);

        // أزرار الحوار
        HBox buttonBox = new HBox(10);
        buttonBox.setAlignment(Pos.CENTER_RIGHT);

        Button closeButton = new Button("إغلاق");
        closeButton.setOnAction(e -> dialog.close());

        buttonBox.getChildren().add(closeButton);

        mainLayout.getChildren().addAll(titleLabel, customersTable,
                                       new Separator(), addCustomerForm, buttonBox);

        Scene scene = new Scene(mainLayout);
        scene.getStylesheets().add(getClass().getResource("/css/styles.css").toExternalForm());
        dialog.setScene(scene);
        dialog.showAndWait();
    }

    /**
     * عرض نافذة إضافة عميل جديد
     */
    private void showAddCustomerDialog() {
        Stage dialog = new Stage();
        dialog.setTitle("إضافة عميل جديد");
        dialog.initModality(Modality.APPLICATION_MODAL);
        dialog.setWidth(400);
        dialog.setHeight(300);

        VBox mainLayout = new VBox(15);
        mainLayout.setPadding(new Insets(20));

        // العنوان
        Label titleLabel = new Label("إضافة عميل جديد");
        titleLabel.getStyleClass().add("dialog-title");

        // نموذج البيانات
        GridPane form = new GridPane();
        form.setHgap(10);
        form.setVgap(15);

        Label nameLabel = new Label("الاسم:");
        TextField nameField = new TextField();
        nameField.setPromptText("أدخل اسم العميل");

        Label phoneLabel = new Label("الهاتف:");
        TextField phoneField = new TextField();
        phoneField.setPromptText("أدخل رقم الهاتف");

        Label emailLabel = new Label("البريد الإلكتروني:");
        TextField emailField = new TextField();
        emailField.setPromptText("أدخل البريد الإلكتروني");

        Label companyLabel = new Label("الشركة:");
        TextField companyField = new TextField();
        companyField.setPromptText("أدخل اسم الشركة (اختياري)");

        form.add(nameLabel, 0, 0);
        form.add(nameField, 1, 0);
        form.add(phoneLabel, 0, 1);
        form.add(phoneField, 1, 1);
        form.add(emailLabel, 0, 2);
        form.add(emailField, 1, 2);
        form.add(companyLabel, 0, 3);
        form.add(companyField, 1, 3);

        // أزرار الحوار
        HBox buttonBox = new HBox(10);
        buttonBox.setAlignment(Pos.CENTER_RIGHT);

        Button addButton = new Button("إضافة");
        addButton.getStyleClass().add("add-button");
        addButton.setOnAction(e -> {
            String name = nameField.getText().trim();
            String phone = phoneField.getText().trim();
            String email = emailField.getText().trim();
            String company = companyField.getText().trim();

            if (!name.isEmpty()) {
                String id = String.valueOf(customers.size() + 1);
                Customer newCustomer = new Customer(id, name, phone, email, "", company, "");
                customers.add(newCustomer);
                customerComboBox.getSelectionModel().select(newCustomer);
                dialog.close();
            } else {
                Alert alert = new Alert(Alert.AlertType.WARNING);
                alert.setTitle("تحذير");
                alert.setHeaderText(null);
                alert.setContentText("يرجى إدخال اسم العميل على الأقل");
                alert.showAndWait();
            }
        });

        Button cancelButton = new Button("إلغاء");
        cancelButton.setOnAction(e -> dialog.close());

        buttonBox.getChildren().addAll(addButton, cancelButton);

        mainLayout.getChildren().addAll(titleLabel, form, buttonBox);

        Scene scene = new Scene(mainLayout);
        scene.getStylesheets().add(getClass().getResource("/css/styles.css").toExternalForm());
        dialog.setScene(scene);
        dialog.showAndWait();
    }
}
