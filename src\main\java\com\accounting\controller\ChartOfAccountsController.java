package com.accounting.controller;

import com.accounting.model.Account;
import com.accounting.service.AccountService;
import javafx.geometry.Insets;
import javafx.geometry.Pos;
import javafx.scene.Scene;
import javafx.scene.control.*;
import javafx.scene.control.cell.PropertyValueFactory;
import javafx.scene.layout.*;
import javafx.stage.Modality;
import javafx.stage.Stage;
import de.jensd.fx.glyphs.fontawesome.FontAwesomeIcon;
import de.jensd.fx.glyphs.fontawesome.FontAwesomeIconView;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import java.text.DecimalFormat;
import java.util.Optional;

/**
 * كنترولر دليل الحسابات
 */
public class ChartOfAccountsController {
    
    private final AccountService accountService;
    private final DecimalFormat decimalFormat = new DecimalFormat("#,##0.00");
    private TableView<Account> accountsTable;
    private TextField searchField;
    
    public ChartOfAccountsController() {
        this.accountService = AccountService.getInstance();
    }
    
    /**
     * عرض نافذة دليل الحسابات
     */
    public void showChartOfAccountsDialog() {
        Stage dialog = new Stage();
        dialog.setTitle("📋 دليل الحسابات");
        dialog.initModality(Modality.APPLICATION_MODAL);
        
        // تحديد حجم النافذة
        javafx.stage.Screen screen = javafx.stage.Screen.getPrimary();
        double screenHeight = screen.getVisualBounds().getHeight();
        double screenWidth = screen.getVisualBounds().getWidth();
        
        dialog.setWidth(Math.min(1200, screenWidth * 0.9));
        dialog.setHeight(Math.min(800, screenHeight * 0.9));
        dialog.setResizable(true);
        
        VBox mainLayout = new VBox(15);
        mainLayout.setPadding(new Insets(20));
        
        // العنوان والوصف
        Label titleLabel = new Label("📋 دليل الحسابات");
        titleLabel.getStyleClass().add("dialog-title");
        
        Label descLabel = new Label("إدارة شجرة الحسابات والتفرع الهرمي");
        descLabel.getStyleClass().add("dialog-description");
        
        // شريط الأدوات
        HBox toolbarBox = createToolbar();
        
        // جدول الحسابات
        accountsTable = createAccountsTable();
        
        // تحميل البيانات
        refreshAccountsTable();
        
        // وضع الجدول في ScrollPane
        ScrollPane scrollPane = new ScrollPane(accountsTable);
        scrollPane.setFitToWidth(true);
        scrollPane.setVbarPolicy(ScrollPane.ScrollBarPolicy.AS_NEEDED);
        scrollPane.setHbarPolicy(ScrollPane.ScrollBarPolicy.NEVER);
        scrollPane.getStyleClass().add("accounts-scroll-pane");
        VBox.setVgrow(scrollPane, Priority.ALWAYS);
        
        // أزرار الإجراءات
        HBox buttonBox = createButtonBox(dialog);
        
        mainLayout.getChildren().addAll(
            titleLabel, descLabel, toolbarBox, scrollPane, buttonBox
        );
        
        Scene scene = new Scene(mainLayout);
        scene.getStylesheets().add(getClass().getResource("/css/styles.css").toExternalForm());
        dialog.setScene(scene);
        dialog.showAndWait();
    }
    
    /**
     * إنشاء شريط الأدوات
     */
    private HBox createToolbar() {
        HBox toolbar = new HBox(15);
        toolbar.setAlignment(Pos.CENTER_LEFT);
        toolbar.setPadding(new Insets(10));
        toolbar.getStyleClass().add("toolbar");
        
        // البحث
        Label searchLabel = new Label("البحث:");
        searchField = new TextField();
        searchField.setPromptText("ابحث في الحسابات...");
        searchField.setPrefWidth(200);
        searchField.textProperty().addListener((obs, oldVal, newVal) -> filterAccounts(newVal));
        
        // أزرار الإجراءات
        Button addBtn = new Button("إضافة حساب");
        FontAwesomeIconView addIcon = new FontAwesomeIconView(FontAwesomeIcon.PLUS);
        addIcon.setSize("12px");
        addBtn.setGraphic(addIcon);
        addBtn.getStyleClass().add("add-button");
        addBtn.setOnAction(e -> showAddAccountDialog());
        
        Button editBtn = new Button("تعديل");
        FontAwesomeIconView editIcon = new FontAwesomeIconView(FontAwesomeIcon.EDIT);
        editIcon.setSize("12px");
        editBtn.setGraphic(editIcon);
        editBtn.getStyleClass().add("edit-button");
        editBtn.setOnAction(e -> editSelectedAccount());
        
        Button deleteBtn = new Button("حذف");
        FontAwesomeIconView deleteIcon = new FontAwesomeIconView(FontAwesomeIcon.TRASH);
        deleteIcon.setSize("12px");
        deleteBtn.setGraphic(deleteIcon);
        deleteBtn.getStyleClass().add("delete-button");
        deleteBtn.setOnAction(e -> deleteSelectedAccount());
        
        Button refreshBtn = new Button("تحديث");
        FontAwesomeIconView refreshIcon = new FontAwesomeIconView(FontAwesomeIcon.REFRESH);
        refreshIcon.setSize("12px");
        refreshBtn.setGraphic(refreshIcon);
        refreshBtn.getStyleClass().add("refresh-button");
        refreshBtn.setOnAction(e -> refreshAccountsTable());
        
        toolbar.getChildren().addAll(
            searchLabel, searchField,
            new Separator(),
            addBtn, editBtn, deleteBtn, refreshBtn
        );
        
        return toolbar;
    }
    
    /**
     * إنشاء جدول الحسابات
     */
    private TableView<Account> createAccountsTable() {
        TableView<Account> table = new TableView<>();
        table.getStyleClass().add("accounts-table");
        
        // الأعمدة
        TableColumn<Account, String> codeCol = new TableColumn<>("رمز الحساب");
        codeCol.setCellValueFactory(new PropertyValueFactory<>("accountCode"));
        codeCol.setPrefWidth(100);
        
        TableColumn<Account, String> nameCol = new TableColumn<>("اسم الحساب");
        nameCol.setCellValueFactory(new PropertyValueFactory<>("accountName"));
        nameCol.setPrefWidth(250);
        
        TableColumn<Account, String> typeCol = new TableColumn<>("نوع الحساب");
        typeCol.setCellValueFactory(cellData -> 
            cellData.getValue().accountTypeProperty().asString());
        typeCol.setPrefWidth(150);
        
        TableColumn<Account, String> natureCol = new TableColumn<>("طبيعة الحساب");
        natureCol.setCellValueFactory(cellData -> 
            cellData.getValue().accountNatureProperty().asString());
        natureCol.setPrefWidth(120);
        
        TableColumn<Account, Integer> levelCol = new TableColumn<>("المستوى");
        levelCol.setCellValueFactory(new PropertyValueFactory<>("level"));
        levelCol.setPrefWidth(80);
        
        TableColumn<Account, String> balanceCol = new TableColumn<>("الرصيد الحالي");
        balanceCol.setCellValueFactory(cellData -> {
            double balance = cellData.getValue().getCurrentBalance();
            return new javafx.beans.property.SimpleStringProperty(
                decimalFormat.format(balance) + " ج.م"
            );
        });
        balanceCol.setPrefWidth(120);
        
        TableColumn<Account, Boolean> activeCol = new TableColumn<>("نشط");
        activeCol.setCellValueFactory(new PropertyValueFactory<>("active"));
        activeCol.setPrefWidth(60);
        
        // إضافة الأعمدة
        table.getColumns().addAll(codeCol, nameCol, typeCol, natureCol, levelCol, balanceCol, activeCol);
        
        // تخصيص عرض الصفوف حسب المستوى
        table.setRowFactory(tv -> {
            TableRow<Account> row = new TableRow<>();
            row.itemProperty().addListener((obs, oldAccount, newAccount) -> {
                if (newAccount != null) {
                    // تطبيق تنسيق مختلف حسب مستوى الحساب
                    String style = "";
                    switch (newAccount.getLevel()) {
                        case 1:
                            style = "-fx-font-weight: bold; -fx-background-color: #e3f2fd;";
                            break;
                        case 2:
                            style = "-fx-font-weight: bold; -fx-background-color: #f3e5f5;";
                            break;
                        case 3:
                            style = "-fx-background-color: #f1f8e9;";
                            break;
                        default:
                            style = "";
                    }
                    row.setStyle(style);
                }
            });
            return row;
        });
        
        return table;
    }
    
    /**
     * إنشاء صندوق الأزرار
     */
    private HBox createButtonBox(Stage dialog) {
        HBox buttonBox = new HBox(15);
        buttonBox.setAlignment(Pos.CENTER_RIGHT);
        buttonBox.setPadding(new Insets(10, 0, 0, 0));
        
        Button exportBtn = new Button("تصدير");
        FontAwesomeIconView exportIcon = new FontAwesomeIconView(FontAwesomeIcon.DOWNLOAD);
        exportIcon.setSize("12px");
        exportBtn.setGraphic(exportIcon);
        exportBtn.getStyleClass().add("export-button");
        exportBtn.setOnAction(e -> exportAccounts());
        
        Button printBtn = new Button("طباعة");
        FontAwesomeIconView printIcon = new FontAwesomeIconView(FontAwesomeIcon.PRINT);
        printIcon.setSize("12px");
        printBtn.setGraphic(printIcon);
        printBtn.getStyleClass().add("print-button");
        printBtn.setOnAction(e -> printAccounts());
        
        Button closeBtn = new Button("إغلاق");
        closeBtn.setOnAction(e -> dialog.close());
        
        buttonBox.getChildren().addAll(exportBtn, printBtn, closeBtn);
        return buttonBox;
    }
    
    /**
     * تحديث جدول الحسابات
     */
    private void refreshAccountsTable() {
        ObservableList<Account> accounts = FXCollections.observableArrayList(
            accountService.getAllAccounts()
        );
        accountsTable.setItems(accounts);
    }
    
    /**
     * فلترة الحسابات
     */
    private void filterAccounts(String searchText) {
        if (searchText == null || searchText.trim().isEmpty()) {
            refreshAccountsTable();
        } else {
            ObservableList<Account> filteredAccounts = FXCollections.observableArrayList(
                accountService.searchAccounts(searchText)
            );
            accountsTable.setItems(filteredAccounts);
        }
    }
    
    /**
     * عرض نافذة إضافة حساب جديد
     */
    private void showAddAccountDialog() {
        AccountFormController formController = new AccountFormController(accountService);
        Optional<Account> result = formController.showAccountForm(null);
        
        if (result.isPresent()) {
            Account newAccount = result.get();
            if (accountService.addAccount(newAccount)) {
                refreshAccountsTable();
                showSuccessAlert("تم إضافة الحساب بنجاح!");
            } else {
                showErrorAlert("فشل في إضافة الحساب. تأكد من عدم تكرار رمز الحساب.");
            }
        }
    }
    
    /**
     * تعديل الحساب المحدد
     */
    private void editSelectedAccount() {
        Account selectedAccount = accountsTable.getSelectionModel().getSelectedItem();
        if (selectedAccount == null) {
            showWarningAlert("يرجى اختيار حساب للتعديل.");
            return;
        }
        
        AccountFormController formController = new AccountFormController(accountService);
        Optional<Account> result = formController.showAccountForm(selectedAccount);
        
        if (result.isPresent()) {
            Account updatedAccount = result.get();
            if (accountService.updateAccount(updatedAccount)) {
                refreshAccountsTable();
                showSuccessAlert("تم تحديث الحساب بنجاح!");
            } else {
                showErrorAlert("فشل في تحديث الحساب.");
            }
        }
    }
    
    /**
     * حذف الحساب المحدد
     */
    private void deleteSelectedAccount() {
        Account selectedAccount = accountsTable.getSelectionModel().getSelectedItem();
        if (selectedAccount == null) {
            showWarningAlert("يرجى اختيار حساب للحذف.");
            return;
        }
        
        if (!selectedAccount.canBeDeleted()) {
            showErrorAlert("لا يمكن حذف هذا الحساب لأنه يحتوي على حركة أو حسابات فرعية.");
            return;
        }
        
        Alert confirmAlert = new Alert(Alert.AlertType.CONFIRMATION);
        confirmAlert.setTitle("تأكيد الحذف");
        confirmAlert.setHeaderText("حذف الحساب");
        confirmAlert.setContentText("هل أنت متأكد من حذف الحساب: " + selectedAccount.getAccountName() + "؟");
        
        Optional<ButtonType> result = confirmAlert.showAndWait();
        if (result.isPresent() && result.get() == ButtonType.OK) {
            if (accountService.deleteAccount(selectedAccount.getAccountCode())) {
                refreshAccountsTable();
                showSuccessAlert("تم حذف الحساب بنجاح!");
            } else {
                showErrorAlert("فشل في حذف الحساب.");
            }
        }
    }
    
    /**
     * تصدير الحسابات
     */
    private void exportAccounts() {
        String exportText = accountService.exportAccountsToText();
        
        // عرض النص في نافذة
        Stage exportStage = new Stage();
        exportStage.setTitle("تصدير دليل الحسابات");
        exportStage.initModality(Modality.APPLICATION_MODAL);
        
        TextArea textArea = new TextArea(exportText);
        textArea.setEditable(false);
        textArea.setWrapText(true);
        
        VBox layout = new VBox(10);
        layout.setPadding(new Insets(20));
        layout.getChildren().add(textArea);
        VBox.setVgrow(textArea, Priority.ALWAYS);
        
        Scene scene = new Scene(layout, 800, 600);
        exportStage.setScene(scene);
        exportStage.showAndWait();
    }
    
    /**
     * طباعة الحسابات
     */
    private void printAccounts() {
        showInfoAlert("ميزة الطباعة ستكون متاحة قريباً.");
    }
    
    // رسائل التنبيه
    private void showSuccessAlert(String message) {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle("نجح");
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }
    
    private void showErrorAlert(String message) {
        Alert alert = new Alert(Alert.AlertType.ERROR);
        alert.setTitle("خطأ");
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }
    
    private void showWarningAlert(String message) {
        Alert alert = new Alert(Alert.AlertType.WARNING);
        alert.setTitle("تحذير");
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }
    
    private void showInfoAlert(String message) {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle("معلومات");
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }
}
