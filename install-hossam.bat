@echo off
setlocal enabledelayedexpansion

echo.
echo ========================================
echo Installing Hossam Accounting System
echo ========================================
echo.

:: Check if running as administrator
net session >nul 2>&1
if %errorlevel% neq 0 (
    echo This installer needs administrator privileges.
    echo Please run as administrator.
    echo.
    echo Right-click on this file and select "Run as administrator"
    pause
    exit /b 1
)

echo ✅ Running with administrator privileges

:: Set installation directory
set INSTALL_DIR=C:\Program Files\Hossam
set DESKTOP_SHORTCUT=%USERPROFILE%\Desktop\Hossam.lnk
set START_MENU_DIR=%PROGRAMDATA%\Microsoft\Windows\Start Menu\Programs

echo.
echo Installation directory: %INSTALL_DIR%
echo.

:: Create installation directory
if exist "%INSTALL_DIR%" (
    echo Removing existing installation...
    rmdir /s /q "%INSTALL_DIR%"
)

echo Creating installation directory...
mkdir "%INSTALL_DIR%"

:: Copy application files
echo Copying application files...
if exist "Hossam-Simple\accounting-system-1.0.0.jar" (
    copy "Hossam-Simple\accounting-system-1.0.0.jar" "%INSTALL_DIR%\"
) else if exist "target\accounting-system-1.0.0.jar" (
    copy "target\accounting-system-1.0.0.jar" "%INSTALL_DIR%\"
) else (
    echo ERROR: Application JAR file not found!
    echo Please make sure the application is built first.
    pause
    exit /b 1
)

:: Create launcher script
echo Creating launcher...
echo @echo off > "%INSTALL_DIR%\Hossam.bat"
echo setlocal >> "%INSTALL_DIR%\Hossam.bat"
echo cd /d "%%~dp0" >> "%INSTALL_DIR%\Hossam.bat"
echo echo Starting Hossam Accounting System... >> "%INSTALL_DIR%\Hossam.bat"
echo java -Dfile.encoding=UTF-8 -jar accounting-system-1.0.0.jar >> "%INSTALL_DIR%\Hossam.bat"
echo if %%errorlevel%% neq 0 ^( >> "%INSTALL_DIR%\Hossam.bat"
echo     echo. >> "%INSTALL_DIR%\Hossam.bat"
echo     echo Java not found or application failed to start! >> "%INSTALL_DIR%\Hossam.bat"
echo     echo Please install Java 17 from: https://adoptium.net/ >> "%INSTALL_DIR%\Hossam.bat"
echo     echo. >> "%INSTALL_DIR%\Hossam.bat"
echo     pause >> "%INSTALL_DIR%\Hossam.bat"
echo ^) >> "%INSTALL_DIR%\Hossam.bat"

:: Create silent launcher
echo Creating silent launcher...
echo Set objShell = CreateObject("WScript.Shell") > "%INSTALL_DIR%\Hossam-Silent.vbs"
echo objShell.Run """" ^& objShell.CurrentDirectory ^& "\Hossam.bat""", 0, False >> "%INSTALL_DIR%\Hossam-Silent.vbs"

:: Create uninstaller
echo Creating uninstaller...
echo @echo off > "%INSTALL_DIR%\Uninstall.bat"
echo setlocal >> "%INSTALL_DIR%\Uninstall.bat"
echo echo Uninstalling Hossam Accounting System... >> "%INSTALL_DIR%\Uninstall.bat"
echo echo. >> "%INSTALL_DIR%\Uninstall.bat"
echo set /p confirm="Are you sure you want to uninstall? (y/n): " >> "%INSTALL_DIR%\Uninstall.bat"
echo if /i "%%confirm%%" neq "y" exit /b 0 >> "%INSTALL_DIR%\Uninstall.bat"
echo echo. >> "%INSTALL_DIR%\Uninstall.bat"
echo echo Removing files... >> "%INSTALL_DIR%\Uninstall.bat"
echo del "%%USERPROFILE%%\Desktop\Hossam.lnk" 2^>nul >> "%INSTALL_DIR%\Uninstall.bat"
echo del "%%PROGRAMDATA%%\Microsoft\Windows\Start Menu\Programs\Hossam.lnk" 2^>nul >> "%INSTALL_DIR%\Uninstall.bat"
echo cd /d C:\ >> "%INSTALL_DIR%\Uninstall.bat"
echo rmdir /s /q "%INSTALL_DIR%" >> "%INSTALL_DIR%\Uninstall.bat"
echo echo Uninstallation completed! >> "%INSTALL_DIR%\Uninstall.bat"
echo pause >> "%INSTALL_DIR%\Uninstall.bat"

:: Create desktop shortcut using PowerShell
echo Creating desktop shortcut...
powershell -command "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%DESKTOP_SHORTCUT%'); $Shortcut.TargetPath = '%INSTALL_DIR%\Hossam.bat'; $Shortcut.WorkingDirectory = '%INSTALL_DIR%'; $Shortcut.Description = 'Hossam Accounting System'; $Shortcut.Save()"

:: Create start menu shortcut
echo Creating start menu shortcut...
powershell -command "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%START_MENU_DIR%\Hossam.lnk'); $Shortcut.TargetPath = '%INSTALL_DIR%\Hossam.bat'; $Shortcut.WorkingDirectory = '%INSTALL_DIR%'; $Shortcut.Description = 'Hossam Accounting System'; $Shortcut.Save()"

:: Create README file
echo Creating documentation...
echo Hossam Accounting System > "%INSTALL_DIR%\README.txt"
echo ======================= >> "%INSTALL_DIR%\README.txt"
echo. >> "%INSTALL_DIR%\README.txt"
echo Installation completed successfully! >> "%INSTALL_DIR%\README.txt"
echo. >> "%INSTALL_DIR%\README.txt"
echo How to run: >> "%INSTALL_DIR%\README.txt"
echo 1. Double-click desktop shortcut "Hossam" >> "%INSTALL_DIR%\README.txt"
echo 2. Or go to Start Menu and search "Hossam" >> "%INSTALL_DIR%\README.txt"
echo 3. Or run: %INSTALL_DIR%\Hossam.bat >> "%INSTALL_DIR%\README.txt"
echo. >> "%INSTALL_DIR%\README.txt"
echo To uninstall: >> "%INSTALL_DIR%\README.txt"
echo Run: %INSTALL_DIR%\Uninstall.bat >> "%INSTALL_DIR%\README.txt"
echo. >> "%INSTALL_DIR%\README.txt"
echo Requirements: >> "%INSTALL_DIR%\README.txt"
echo - Java 17 or newer >> "%INSTALL_DIR%\README.txt"
echo - Download from: https://adoptium.net/ >> "%INSTALL_DIR%\README.txt"
echo. >> "%INSTALL_DIR%\README.txt"
echo Support: <EMAIL> >> "%INSTALL_DIR%\README.txt"

:: Register in Add/Remove Programs (optional)
echo Registering in Add/Remove Programs...
reg add "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\Hossam" /v "DisplayName" /t REG_SZ /d "Hossam Accounting System" /f >nul 2>&1
reg add "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\Hossam" /v "UninstallString" /t REG_SZ /d "%INSTALL_DIR%\Uninstall.bat" /f >nul 2>&1
reg add "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\Hossam" /v "DisplayVersion" /t REG_SZ /d "1.0.0" /f >nul 2>&1
reg add "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\Hossam" /v "Publisher" /t REG_SZ /d "Advanced Glass and Aluminum Company" /f >nul 2>&1
reg add "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\Hossam" /v "InstallLocation" /t REG_SZ /d "%INSTALL_DIR%" /f >nul 2>&1

echo.
echo ========================================
echo Installation Completed Successfully!
echo ========================================
echo.
echo ✅ Application installed to: %INSTALL_DIR%
echo ✅ Desktop shortcut created: Hossam
echo ✅ Start menu entry created: Hossam
echo ✅ Registered in Add/Remove Programs
echo.
echo 🚀 How to run:
echo   1. Double-click "Hossam" on desktop
echo   2. Search "Hossam" in Start Menu
echo   3. Run: %INSTALL_DIR%\Hossam.bat
echo.
echo 📁 Installation files:
dir /b "%INSTALL_DIR%"
echo.
echo 🗑️ To uninstall:
echo   - Go to Add/Remove Programs and uninstall "Hossam Accounting System"
echo   - Or run: %INSTALL_DIR%\Uninstall.bat
echo.

:: Ask if user wants to run the application now
set /p run_now="Do you want to run Hossam now? (y/n): "
if /i "%run_now%"=="y" (
    echo.
    echo Starting Hossam Accounting System...
    start "" "%INSTALL_DIR%\Hossam.bat"
)

echo.
echo Installation completed! You can close this window.
pause
