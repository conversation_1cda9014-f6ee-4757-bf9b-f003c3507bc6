package com.accounting.controller;

import com.accounting.model.DeliveryOrder;
import com.accounting.model.ManufacturingOrder;
import com.accounting.service.DeliveryOrderService;
import com.accounting.service.ManufacturingService;
import com.accounting.util.PrintUtils;
import javafx.geometry.Insets;
import javafx.geometry.Pos;
import javafx.scene.control.*;
import javafx.scene.control.cell.PropertyValueFactory;
import javafx.scene.layout.*;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import de.jensd.fx.glyphs.fontawesome.FontAwesomeIcon;
import de.jensd.fx.glyphs.fontawesome.FontAwesomeIconView;
import javafx.stage.Modality;
import javafx.stage.Stage;
import javafx.scene.Scene;
import java.time.LocalDate;
import java.text.DecimalFormat;
import java.util.function.Consumer;
import java.util.Map;

/**
 * كنترولر أوامر التسليم
 */
public class DeliveryOrderController {

    private final DeliveryOrderService deliveryOrderService;
    private final ManufacturingService manufacturingService;
    private final DecimalFormat decimalFormat = new DecimalFormat("#,##0.00");
    private TableView<DeliveryOrder> ordersTable;
    private TextField searchField;
    private DatePicker fromDatePicker;
    private DatePicker toDatePicker;
    private ObservableList<DeliveryOrder> deliveryOrdersList;
    private ComboBox<DeliveryOrder.DeliveryStatus> statusFilter;
    private ComboBox<String> customerFilter;
    
    public DeliveryOrderController(DeliveryOrderService deliveryOrderService) {
        this.deliveryOrderService = deliveryOrderService;
        this.manufacturingService = ManufacturingService.getInstance();
        this.deliveryOrdersList = FXCollections.observableArrayList();
        // تحميل جميع أوامر التسليم مرة واحدة عند الإنشاء
        loadAllDeliveryOrders();
    }

    /**
     * تحميل جميع أوامر التسليم وتحديث القائمة المحلية
     */
    private void loadAllDeliveryOrders() {
        deliveryOrdersList.setAll(deliveryOrderService.getAllDeliveryOrders());
    }
    
    /**
     * إنشاء صفحة أوامر التسليم
     */
    public VBox createDeliveryOrdersPage(Consumer<DeliveryOrder> onEditOrder, Consumer<DeliveryOrder> onPrintOrder) {
        VBox container = new VBox(15);
        container.setPadding(new Insets(20));
        container.setFillWidth(true);
        VBox.setVgrow(container, Priority.ALWAYS);
        
        // العنوان والوصف
        Label titleLabel = new Label("🚚 إدارة أوامر التسليم");
        titleLabel.getStyleClass().add("section-title");
        
        Label descLabel = new Label("إدارة وتتبع أوامر التسليم للعملاء");
        descLabel.getStyleClass().add("section-description");
        
        // شريط الأدوات
        HBox toolbar = createToolbar(onEditOrder);
        
        // الإحصائيات السريعة
        HBox statsBox = createStatsBox();
        
        // الفلاتر
        VBox filtersBox = createFiltersBox();
        
        // الجدول
        ordersTable = createOrdersTable(onEditOrder, onPrintOrder);
        ordersTable.setMinHeight(350);
        ordersTable.setPrefHeight(600);
        ordersTable.setColumnResizePolicy(TableView.CONSTRAINED_RESIZE_POLICY);
        VBox.setVgrow(ordersTable, Priority.ALWAYS);
        // تحميل البيانات
        refreshOrdersTable();
        // وضع الجدول في ScrollPane
        ScrollPane scrollPane = new ScrollPane(ordersTable);
        scrollPane.setFitToWidth(true);
        scrollPane.setVbarPolicy(ScrollPane.ScrollBarPolicy.AS_NEEDED);
        scrollPane.setHbarPolicy(ScrollPane.ScrollBarPolicy.NEVER);
        scrollPane.setPrefHeight(600);
        VBox.setVgrow(scrollPane, Priority.ALWAYS);
        
        container.getChildren().addAll(titleLabel, descLabel, toolbar, statsBox, filtersBox, scrollPane);
        return container;
    }
    
    /**
     * إنشاء شريط الأدوات
     */
    private HBox createToolbar(Consumer<DeliveryOrder> onEditOrder) {
        HBox toolbar = new HBox(15);
        toolbar.setAlignment(Pos.CENTER_LEFT);
        toolbar.setPadding(new Insets(10));
        toolbar.getStyleClass().add("toolbar");
        
        Button newOrderBtn = new Button("أمر تسليم جديد");
        FontAwesomeIconView newIcon = new FontAwesomeIconView(FontAwesomeIcon.PLUS_CIRCLE);
        newIcon.setSize("18px");
        newIcon.setStyle("-fx-fill: #27ae60;");
        newOrderBtn.setGraphic(newIcon);
        newOrderBtn.getStyleClass().add("add-button");
        newOrderBtn.setStyle("-fx-background-radius: 20; -fx-font-weight: bold; -fx-background-color: #eafaf1;");
        newOrderBtn.setOnAction(e -> onEditOrder.accept(null));
        
        Button refreshBtn = new Button("تحديث");
        FontAwesomeIconView refreshIcon = new FontAwesomeIconView(FontAwesomeIcon.REFRESH);
        refreshIcon.setSize("16px");
        refreshIcon.setStyle("-fx-fill: #2980b9;");
        refreshBtn.setGraphic(refreshIcon);
        refreshBtn.getStyleClass().add("refresh-button");
        refreshBtn.setStyle("-fx-background-radius: 20; -fx-background-color: #eaf2fb;");
        refreshBtn.setOnAction(e -> refreshOrdersTable());

        Button fromManufacturingBtn = new Button("من أمر تصنيع");
        FontAwesomeIconView manufacturingIcon = new FontAwesomeIconView(FontAwesomeIcon.INDUSTRY);
        manufacturingIcon.setSize("16px");
        manufacturingIcon.setStyle("-fx-fill: #f39c12;");
        fromManufacturingBtn.setGraphic(manufacturingIcon);
        fromManufacturingBtn.getStyleClass().add("manufacturing-button");
        fromManufacturingBtn.setStyle("-fx-background-radius: 20; -fx-background-color: #fff6e3;");
        fromManufacturingBtn.setOnAction(e -> createFromManufacturingOrder());

        toolbar.getChildren().addAll(newOrderBtn, fromManufacturingBtn, refreshBtn);
        return toolbar;
    }
    
    /**
     * إنشاء صندوق الإحصائيات
     */
    private HBox createStatsBox() {
        HBox statsBox = new HBox(20);
        statsBox.setAlignment(Pos.CENTER);
        statsBox.setPadding(new Insets(15));
        statsBox.getStyleClass().add("stats-container");
        
        Map<String, Integer> stats = deliveryOrderService.getDeliveryOrderStatistics();
        
        VBox totalStats = createStatCard(
            "إجمالي الأوامر", 
            String.valueOf(stats.get("إجمالي الأوامر")),
            FontAwesomeIcon.TRUCK,
            "#3498db"
        );
        
        VBox confirmedStats = createStatCard(
            "مؤكدة", 
            String.valueOf(stats.get("أوامر مؤكدة")),
            FontAwesomeIcon.CHECK_CIRCLE,
            "#f39c12"
        );
        
        VBox deliveredStats = createStatCard(
            "مسلمة", 
            String.valueOf(stats.get("أوامر مسلمة")),
            FontAwesomeIcon.CHECK,
            "#2ecc71"
        );
        
        VBox thisMonthStats = createStatCard(
            "هذا الشهر", 
            String.valueOf(stats.get("أوامر الشهر الحالي")),
            FontAwesomeIcon.CALENDAR,
            "#e74c3c"
        );
        
        statsBox.getChildren().addAll(totalStats, confirmedStats, deliveredStats, thisMonthStats);
        return statsBox;
    }
    
    /**
     * إنشاء بطاقة إحصائية
     */
    private VBox createStatCard(String title, String value, FontAwesomeIcon icon, String color) {
        VBox card = new VBox(10);
        card.setAlignment(Pos.CENTER);
        card.setPrefSize(150, 80);
        card.getStyleClass().add("stat-card");
        
        FontAwesomeIconView iconView = new FontAwesomeIconView(icon);
        iconView.setSize("20px");
        iconView.setStyle("-fx-fill: " + color + ";");
        
        Label valueLabel = new Label(value);
        valueLabel.getStyleClass().add("stat-value");
        valueLabel.setStyle("-fx-text-fill: " + color + ";");
        
        Label titleLabel = new Label(title);
        titleLabel.getStyleClass().add("stat-title");
        
        card.getChildren().addAll(iconView, valueLabel, titleLabel);
        return card;
    }
    
    /**
     * إنشاء صندوق الفلاتر
     */
    private VBox createFiltersBox() {
        VBox filtersBox = new VBox(10);
        filtersBox.setPadding(new Insets(15));
        filtersBox.getStyleClass().add("filters-container");
        
        Label filtersTitle = new Label("🔍 فلاتر البحث");
        filtersTitle.getStyleClass().add("filters-title");
        
        // الصف الأول من الفلاتر
        HBox firstRow = new HBox(15);
        firstRow.setAlignment(Pos.CENTER_LEFT);
        
        // البحث
        Label searchLabel = new Label("البحث:");
        searchField = new TextField();
        searchField.setPromptText("ابحث في الأوامر...");
        searchField.setPrefWidth(200);
        searchField.textProperty().addListener((obs, oldVal, newVal) -> applyFilters());
        
        // فلتر التاريخ من
        Label fromLabel = new Label("من تاريخ:");
        fromDatePicker = new DatePicker();
        fromDatePicker.setValue(LocalDate.now().withDayOfMonth(1));
        fromDatePicker.valueProperty().addListener((obs, oldVal, newVal) -> applyFilters());
        
        // فلتر التاريخ إلى
        Label toLabel = new Label("إلى تاريخ:");
        toDatePicker = new DatePicker();
        toDatePicker.setValue(LocalDate.now());
        toDatePicker.valueProperty().addListener((obs, oldVal, newVal) -> applyFilters());
        
        firstRow.getChildren().addAll(
            searchLabel, searchField,
            new Separator(),
            fromLabel, fromDatePicker,
            toLabel, toDatePicker
        );
        
        // الصف الثاني من الفلاتر
        HBox secondRow = new HBox(15);
        secondRow.setAlignment(Pos.CENTER_LEFT);
        
        // فلتر الحالة
        Label statusLabel = new Label("الحالة:");
        statusFilter = new ComboBox<>();
        statusFilter.getItems().add(null); // جميع الحالات
        statusFilter.getItems().addAll(DeliveryOrder.DeliveryStatus.values());
        statusFilter.setPromptText("جميع الحالات");
        statusFilter.valueProperty().addListener((obs, oldVal, newVal) -> applyFilters());
        
        // فلتر العميل
        Label customerLabel = new Label("العميل:");
        customerFilter = new ComboBox<>();
        customerFilter.getItems().add(null); // جميع العملاء
        loadCustomers();
        customerFilter.setPromptText("جميع العملاء");
        customerFilter.valueProperty().addListener((obs, oldVal, newVal) -> applyFilters());
        
        // أزرار الفلاتر
        Button applyBtn = new Button("تطبيق");
        FontAwesomeIconView applyIcon = new FontAwesomeIconView(FontAwesomeIcon.SEARCH);
        applyIcon.setSize("12px");
        applyBtn.setGraphic(applyIcon);
        applyBtn.getStyleClass().add("filter-button");
        applyBtn.setOnAction(e -> applyFilters());
        
        Button resetBtn = new Button("إعادة تعيين");
        FontAwesomeIconView resetIcon = new FontAwesomeIconView(FontAwesomeIcon.REFRESH);
        resetIcon.setSize("12px");
        resetBtn.setGraphic(resetIcon);
        resetBtn.getStyleClass().add("reset-button");
        resetBtn.setOnAction(e -> resetFilters());
        
        secondRow.getChildren().addAll(
            statusLabel, statusFilter,
            customerLabel, customerFilter,
            new Separator(),
            applyBtn, resetBtn
        );
        
        filtersBox.getChildren().addAll(filtersTitle, firstRow, secondRow);
        return filtersBox;
    }
    
    /**
     * إنشاء جدول الأوامر
     */
    private TableView<DeliveryOrder> createOrdersTable(Consumer<DeliveryOrder> onEditOrder, Consumer<DeliveryOrder> onPrintOrder) {
        TableView<DeliveryOrder> table = new TableView<>();
        table.getStyleClass().add("orders-table");
        table.setColumnResizePolicy(TableView.CONSTRAINED_RESIZE_POLICY);
        table.setMinHeight(350);
        table.setPrefHeight(600);
        VBox.setVgrow(table, Priority.ALWAYS);
        
        // الأعمدة
        TableColumn<DeliveryOrder, String> numberCol = new TableColumn<>("رقم أمر التسليم");
        numberCol.setCellValueFactory(new PropertyValueFactory<>("deliveryOrderNumber"));
        numberCol.setPrefWidth(120);
        
        TableColumn<DeliveryOrder, String> customerCol = new TableColumn<>("العميل");
        customerCol.setCellValueFactory(new PropertyValueFactory<>("customerName"));
        customerCol.setPrefWidth(150);
        
        TableColumn<DeliveryOrder, String> manufacturingOrderCol = new TableColumn<>("رقم أمر العمل");
        manufacturingOrderCol.setCellValueFactory(new PropertyValueFactory<>("manufacturingOrderNumber"));
        manufacturingOrderCol.setPrefWidth(120);
        
        TableColumn<DeliveryOrder, String> invoiceCol = new TableColumn<>("رقم الفاتورة");
        invoiceCol.setCellValueFactory(new PropertyValueFactory<>("invoiceNumber"));
        invoiceCol.setPrefWidth(120);
        
        TableColumn<DeliveryOrder, LocalDate> dateCol = new TableColumn<>("تاريخ التسليم");
        dateCol.setCellValueFactory(new PropertyValueFactory<>("deliveryDate"));
        dateCol.setPrefWidth(100);
        
        TableColumn<DeliveryOrder, String> statusCol = new TableColumn<>("الحالة");
        statusCol.setCellValueFactory(cellData -> 
            cellData.getValue().statusProperty().asString());
        statusCol.setPrefWidth(100);
        
        TableColumn<DeliveryOrder, Integer> itemsCountCol = new TableColumn<>("عدد العناصر");
        itemsCountCol.setCellValueFactory(cellData -> 
            new javafx.beans.property.SimpleIntegerProperty(cellData.getValue().getDeliveryItems().size()).asObject());
        itemsCountCol.setPrefWidth(100);
        
        // عمود الإجراءات
        TableColumn<DeliveryOrder, Void> actionsCol = new TableColumn<>("الإجراءات");
        actionsCol.setCellFactory(col -> new TableCell<DeliveryOrder, Void>() {
            private final Button editBtn = new Button();
            private final Button printBtn = new Button();
            private final HBox actionsBox = new HBox(8);
            {
                FontAwesomeIconView editIcon = new FontAwesomeIconView(FontAwesomeIcon.PENCIL_SQUARE);
                editIcon.setSize("14px");
                editIcon.setStyle("-fx-fill: #2980b9;");
                editBtn.setGraphic(editIcon);
                editBtn.setTooltip(new Tooltip("تعديل"));
                editBtn.getStyleClass().add("edit-button");
                editBtn.setStyle("-fx-background-radius: 15; -fx-background-color: #eaf2fb;");

                FontAwesomeIconView printIcon = new FontAwesomeIconView(FontAwesomeIcon.PRINT);
                printIcon.setSize("14px");
                printIcon.setStyle("-fx-fill: #16a085;");
                printBtn.setGraphic(printIcon);
                printBtn.getStyleClass().add("print-button");
                printBtn.setStyle("-fx-background-radius: 15; -fx-background-color: #eafaf1;");
                actionsBox.getChildren().addAll(editBtn, printBtn);
                actionsBox.setAlignment(Pos.CENTER);
            }
            @Override
            protected void updateItem(Void item, boolean empty) {
                super.updateItem(item, empty);
                if (empty) {
                    setGraphic(null);
                } else {
                    DeliveryOrder order = getTableView().getItems().get(getIndex());
                    editBtn.setOnAction(e -> onEditOrder.accept(order));
                    // تحسين زر الطباعة: تعطيل إذا كان الأمر DRAFT أو غير قابل للطباعة
                    boolean canPrint = order != null && order.getStatus() != DeliveryOrder.DeliveryStatus.DRAFT;
                    printBtn.setDisable(!canPrint);
                    if (!canPrint) {
                        printBtn.setTooltip(new Tooltip("لا يمكن طباعة أمر التسليم قبل تأكيده"));
                    } else {
                        printBtn.setTooltip(new Tooltip("طباعة أمر التسليم"));
                        printBtn.setOnAction(e -> showPrintDialogForDeliveryOrder(order));
                    }
                    setGraphic(actionsBox);
                }
            }
        });
        actionsCol.setPrefWidth(120);
        
        // إضافة الأعمدة
        table.getColumns().addAll(numberCol, customerCol, manufacturingOrderCol, invoiceCol, 
                                 dateCol, statusCol, itemsCountCol, actionsCol);
        
        // تخصيص عرض الصفوف حسب الحالة
        table.setRowFactory(tv -> {
            TableRow<DeliveryOrder> row = new TableRow<>();
            row.itemProperty().addListener((obs, oldOrder, newOrder) -> {
                if (newOrder != null) {
                    String style = "";
                    switch (newOrder.getStatus()) {
                        case DRAFT:
                            style = "-fx-background-color: #fff3cd;";
                            break;
                        case CONFIRMED:
                            style = "-fx-background-color: #d1ecf1;";
                            break;
                        case DELIVERED:
                            style = "-fx-background-color: #d4edda;";
                            break;
                        case CANCELLED:
                            style = "-fx-background-color: #f5c6cb;";
                            break;
                    }
                    row.setStyle(style);
                }
            });
            return row;
        });
        
        return table;
    }
    
    /**
     * تحميل قائمة العملاء
     */
    private void loadCustomers() {
        customerFilter.getItems().addAll(
            deliveryOrdersList.stream()
                .map(DeliveryOrder::getCustomerName)
                .filter(name -> name != null && !name.trim().isEmpty())
                .distinct()
                .sorted()
                .toArray(String[]::new)
        );
    }
    
    /**
     * تحديث جدول الأوامر
     */
    private void refreshOrdersTable() {
        loadAllDeliveryOrders(); // تحديث القائمة المحلية
        ordersTable.setItems(FXCollections.observableArrayList(deliveryOrdersList));
        // تحديث قائمة العملاء
        customerFilter.getItems().clear();
        customerFilter.getItems().add(null);
        loadCustomers();
    }
    
    /**
     * تطبيق الفلاتر
     */
    private void applyFilters() {
        ObservableList<DeliveryOrder> filteredOrders = FXCollections.observableArrayList();
        for (DeliveryOrder order : deliveryOrdersList) {
            boolean matches = true;
            // فلتر البحث النصي
            String searchText = searchField.getText();
            if (searchText != null && !searchText.trim().isEmpty()) {
                String searchLower = searchText.toLowerCase();
                boolean textMatch = (order.getDeliveryOrderNumber() != null && order.getDeliveryOrderNumber().toLowerCase().contains(searchLower)) ||
                                    (order.getCustomerName() != null && order.getCustomerName().toLowerCase().contains(searchLower)) ||
                                    (order.getManufacturingOrderNumber() != null && order.getManufacturingOrderNumber().toLowerCase().contains(searchLower)) ||
                                    (order.getInvoiceNumber() != null && order.getInvoiceNumber().toLowerCase().contains(searchLower));
                if (!textMatch) {
                    matches = false;
                }
            }
            // فلتر التاريخ
            LocalDate fromDate = fromDatePicker.getValue();
            LocalDate toDate = toDatePicker.getValue();
            if (fromDate != null && order.getDeliveryDate() != null && order.getDeliveryDate().isBefore(fromDate)) {
                matches = false;
            }
            if (toDate != null && order.getDeliveryDate() != null && order.getDeliveryDate().isAfter(toDate)) {
                matches = false;
            }
            // فلتر الحالة
            DeliveryOrder.DeliveryStatus selectedStatus = statusFilter.getValue();
            if (selectedStatus != null && order.getStatus() != selectedStatus) {
                matches = false;
            }
            // فلتر العميل
            String selectedCustomer = customerFilter.getValue();
            if (selectedCustomer != null && !selectedCustomer.equals(order.getCustomerName())) {
                matches = false;
            }
            if (matches) {
                filteredOrders.add(order);
            }
        }
        ordersTable.setItems(filteredOrders);
    }
    
    /**
     * إعادة تعيين الفلاتر
     */
    private void resetFilters() {
        searchField.clear();
        fromDatePicker.setValue(LocalDate.now().withDayOfMonth(1));
        toDatePicker.setValue(LocalDate.now());
        statusFilter.setValue(null);
        customerFilter.setValue(null);
        refreshOrdersTable();
    }

    /**
     * إنشاء أمر تسليم من أمر تصنيع
     */
    private void createFromManufacturingOrder() {
        // عرض قائمة أوامر التصنيع المكتملة
        var completedOrders = manufacturingService.getManufacturingOrdersByStatus(ManufacturingOrder.OrderStatus.COMPLETED);

        if (completedOrders.isEmpty()) {
            showInfoAlert("لا توجد أوامر تصنيع مكتملة لإنشاء أمر تسليم منها.");
            return;
        }

        // إنشاء نافذة اختيار أمر التصنيع
        ChoiceDialog<ManufacturingOrder> dialog = new ChoiceDialog<>(completedOrders.get(0), completedOrders);
        dialog.setTitle("اختيار أمر التصنيع");
        dialog.setHeaderText("اختر أمر التصنيع لإنشاء أمر تسليم");
        dialog.setContentText("أمر التصنيع:");

        dialog.showAndWait().ifPresent(selectedOrder -> {
            DeliveryOrder newDeliveryOrder = deliveryOrderService.createFromManufacturingOrder(selectedOrder);
            if (newDeliveryOrder != null) {
                refreshOrdersTable();
                showInfoAlert("تم إنشاء أمر التسليم بنجاح من أمر التصنيع رقم: " + selectedOrder.getOrderNumber());
            } else {
                showErrorAlert("فشل في إنشاء أمر التسليم.");
            }
        });
    }

    /**
     * عرض رسالة معلومات
     */
    private void showInfoAlert(String message) {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle("معلومات");
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }

    /**
     * عرض رسالة خطأ
     */
    private void showErrorAlert(String message) {
        Alert alert = new Alert(Alert.AlertType.ERROR);
        alert.setTitle("خطأ");
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }

    /**
     * عرض نافذة طباعة أمر التسليم
     */
    private void showPrintDialogForDeliveryOrder(DeliveryOrder order) {
        if (order == null) {
            showErrorAlert("لا يوجد أمر محدد للطباعة.");
            return;
        }
        DeliveryOrderFormController formController = new DeliveryOrderFormController(deliveryOrderService);
    VBox form = formController.createDeliveryOrderPrintPage(order); // نموذج عرض فقط للطباعة

        Stage printStage = new Stage();
        printStage.setTitle("طباعة أمر التسليم رقم: " + order.getDeliveryOrderNumber());
        printStage.initModality(Modality.APPLICATION_MODAL);
        printStage.setMinWidth(700);
        printStage.setMinHeight(600);
        
        Button printBtn = new Button("طباعة");
        printBtn.getStyleClass().add("print-button");
        printBtn.setOnAction(e -> {
            boolean success = PrintUtils.printNode(form, "أمر تسليم رقم " + order.getDeliveryOrderNumber());
            if (success) {
                showInfoAlert("تم إرسال أمر التسليم للطباعة بنجاح!");
            } else {
                showErrorAlert("حدث خطأ أثناء الطباعة أو تم الإلغاء.");
            }
        });
        Button closeBtn = new Button("إغلاق");
        closeBtn.setOnAction(e -> printStage.close());
        HBox btnBox = new HBox(15, printBtn, closeBtn);
        btnBox.setAlignment(Pos.CENTER);
        btnBox.setPadding(new Insets(20, 0, 0, 0));
        VBox root = new VBox(form, btnBox);
        Scene scene = new Scene(root);
        printStage.setScene(scene);
        printStage.showAndWait();
    }

    /**
     * إنشاء صفحة للطباعة
     */
    private Node createPrintPage(String content) {
        VBox page = new VBox();
        page.setPadding(new Insets(20));
        page.setSpacing(10);
        
        TextArea textArea = new TextArea(content);
        textArea.setEditable(false);
        textArea.setWrapText(true);
        textArea.setStyle("-fx-font-family: 'Courier New'; -fx-font-size: 12px;");
        
        page.getChildren().add(textArea);
        return page;
    }
}
