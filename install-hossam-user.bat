@echo off
setlocal enabledelayedexpansion

echo.
echo ========================================
echo Installing Hossam Accounting System
echo ========================================
echo.

:: Set user installation directory (no admin needed)
set INSTALL_DIR=%USERPROFILE%\Hossam
set DESKTOP_SHORTCUT=%USERPROFILE%\Desktop\Hossam.lnk

echo Installation directory: %INSTALL_DIR%
echo.

:: Create installation directory
if exist "%INSTALL_DIR%" (
    echo Removing existing installation...
    rmdir /s /q "%INSTALL_DIR%"
)

echo Creating installation directory...
mkdir "%INSTALL_DIR%"

:: Copy application files
echo Copying application files...
if exist "Hossam-Simple\accounting-system-1.0.0.jar" (
    copy "Hossam-Simple\accounting-system-1.0.0.jar" "%INSTALL_DIR%\"
    echo ✅ Application copied from Hossam-Simple
) else if exist "target\accounting-system-1.0.0.jar" (
    copy "target\accounting-system-1.0.0.jar" "%INSTALL_DIR%\"
    echo ✅ Application copied from target
) else (
    echo ❌ ERROR: Application JAR file not found!
    echo Please make sure the application is built first.
    pause
    exit /b 1
)

:: Create launcher script
echo Creating launcher...
echo @echo off > "%INSTALL_DIR%\Hossam.bat"
echo setlocal >> "%INSTALL_DIR%\Hossam.bat"
echo cd /d "%%~dp0" >> "%INSTALL_DIR%\Hossam.bat"
echo title Hossam Accounting System >> "%INSTALL_DIR%\Hossam.bat"
echo echo ======================================== >> "%INSTALL_DIR%\Hossam.bat"
echo echo       Hossam Accounting System >> "%INSTALL_DIR%\Hossam.bat"
echo echo ======================================== >> "%INSTALL_DIR%\Hossam.bat"
echo echo Starting application... >> "%INSTALL_DIR%\Hossam.bat"
echo echo. >> "%INSTALL_DIR%\Hossam.bat"
echo java -Dfile.encoding=UTF-8 -jar accounting-system-1.0.0.jar >> "%INSTALL_DIR%\Hossam.bat"
echo if %%errorlevel%% neq 0 ^( >> "%INSTALL_DIR%\Hossam.bat"
echo     echo. >> "%INSTALL_DIR%\Hossam.bat"
echo     echo ❌ Java not found or application failed to start! >> "%INSTALL_DIR%\Hossam.bat"
echo     echo. >> "%INSTALL_DIR%\Hossam.bat"
echo     echo Please install Java 17 from: https://adoptium.net/ >> "%INSTALL_DIR%\Hossam.bat"
echo     echo. >> "%INSTALL_DIR%\Hossam.bat"
echo     pause >> "%INSTALL_DIR%\Hossam.bat"
echo ^) >> "%INSTALL_DIR%\Hossam.bat"

:: Create silent launcher
echo Creating silent launcher...
echo Set objShell = CreateObject("WScript.Shell") > "%INSTALL_DIR%\Hossam-Silent.vbs"
echo objShell.CurrentDirectory = "%INSTALL_DIR%" >> "%INSTALL_DIR%\Hossam-Silent.vbs"
echo objShell.Run "Hossam.bat", 0, False >> "%INSTALL_DIR%\Hossam-Silent.vbs"

:: Create uninstaller
echo Creating uninstaller...
echo @echo off > "%INSTALL_DIR%\Uninstall.bat"
echo setlocal >> "%INSTALL_DIR%\Uninstall.bat"
echo echo ======================================== >> "%INSTALL_DIR%\Uninstall.bat"
echo echo Uninstalling Hossam Accounting System >> "%INSTALL_DIR%\Uninstall.bat"
echo echo ======================================== >> "%INSTALL_DIR%\Uninstall.bat"
echo echo. >> "%INSTALL_DIR%\Uninstall.bat"
echo set /p confirm="Are you sure you want to uninstall? (y/n): " >> "%INSTALL_DIR%\Uninstall.bat"
echo if /i "%%confirm%%" neq "y" exit /b 0 >> "%INSTALL_DIR%\Uninstall.bat"
echo echo. >> "%INSTALL_DIR%\Uninstall.bat"
echo echo Removing files... >> "%INSTALL_DIR%\Uninstall.bat"
echo del "%DESKTOP_SHORTCUT%" 2^>nul >> "%INSTALL_DIR%\Uninstall.bat"
echo cd /d "%USERPROFILE%" >> "%INSTALL_DIR%\Uninstall.bat"
echo rmdir /s /q "%INSTALL_DIR%" >> "%INSTALL_DIR%\Uninstall.bat"
echo echo ✅ Uninstallation completed! >> "%INSTALL_DIR%\Uninstall.bat"
echo pause >> "%INSTALL_DIR%\Uninstall.bat"

:: Create desktop shortcut using PowerShell
echo Creating desktop shortcut...
powershell -command "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%DESKTOP_SHORTCUT%'); $Shortcut.TargetPath = '%INSTALL_DIR%\Hossam.bat'; $Shortcut.WorkingDirectory = '%INSTALL_DIR%'; $Shortcut.Description = 'Hossam Accounting System'; $Shortcut.Save()" 2>nul

if exist "%DESKTOP_SHORTCUT%" (
    echo ✅ Desktop shortcut created
) else (
    echo ⚠️ Desktop shortcut creation failed
)

:: Create README file
echo Creating documentation...
echo Hossam Accounting System > "%INSTALL_DIR%\README.txt"
echo ======================= >> "%INSTALL_DIR%\README.txt"
echo. >> "%INSTALL_DIR%\README.txt"
echo Installation completed successfully! >> "%INSTALL_DIR%\README.txt"
echo. >> "%INSTALL_DIR%\README.txt"
echo Installation location: %INSTALL_DIR% >> "%INSTALL_DIR%\README.txt"
echo. >> "%INSTALL_DIR%\README.txt"
echo How to run: >> "%INSTALL_DIR%\README.txt"
echo 1. Double-click desktop shortcut "Hossam" >> "%INSTALL_DIR%\README.txt"
echo 2. Or run: %INSTALL_DIR%\Hossam.bat >> "%INSTALL_DIR%\README.txt"
echo 3. Silent mode: %INSTALL_DIR%\Hossam-Silent.vbs >> "%INSTALL_DIR%\README.txt"
echo. >> "%INSTALL_DIR%\README.txt"
echo To uninstall: >> "%INSTALL_DIR%\README.txt"
echo Run: %INSTALL_DIR%\Uninstall.bat >> "%INSTALL_DIR%\README.txt"
echo. >> "%INSTALL_DIR%\README.txt"
echo Requirements: >> "%INSTALL_DIR%\README.txt"
echo - Java 17 or newer >> "%INSTALL_DIR%\README.txt"
echo - Download from: https://adoptium.net/ >> "%INSTALL_DIR%\README.txt"
echo. >> "%INSTALL_DIR%\README.txt"
echo Support: <EMAIL> >> "%INSTALL_DIR%\README.txt"

echo.
echo ========================================
echo Installation Completed Successfully!
echo ========================================
echo.
echo ✅ Application installed to: %INSTALL_DIR%
if exist "%DESKTOP_SHORTCUT%" echo ✅ Desktop shortcut created: Hossam
echo ✅ Uninstaller created
echo.
echo 🚀 How to run:
if exist "%DESKTOP_SHORTCUT%" echo   1. Double-click "Hossam" on desktop
echo   2. Run: %INSTALL_DIR%\Hossam.bat
echo   3. Silent: %INSTALL_DIR%\Hossam-Silent.vbs
echo.
echo 📁 Installation files:
dir /b "%INSTALL_DIR%"
echo.
echo 🗑️ To uninstall:
echo   Run: %INSTALL_DIR%\Uninstall.bat
echo.

:: Ask if user wants to run the application now
set /p run_now="Do you want to run Hossam now? (y/n): "
if /i "%run_now%"=="y" (
    echo.
    echo Starting Hossam Accounting System...
    start "" "%INSTALL_DIR%\Hossam.bat"
)

echo.
echo ✅ Installation completed! You can close this window.
pause
