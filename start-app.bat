@echo off
setlocal

echo.
echo ========================================
echo Starting Accounting System
echo ========================================
echo.

:: Set Java environment
set JAVA_HOME=C:\Program Files\Eclipse Adoptium\jdk-********-hotspot
set PATH=%JAVA_HOME%\bin;%PATH%

:: Check Java
java -version > nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Java not found
    echo Please install Java 17 or newer from:
    echo https://adoptium.net/
    echo.
    pause
    exit /b 1
)

echo Java is available
echo.

:: Check for built JAR
if exist "target\accounting-system-1.0.0.jar" (
    echo Running from built JAR...
    java -Dfile.encoding=UTF-8 -Djava.awt.headless=false --add-opens javafx.graphics/javafx.scene=ALL-UNNAMED --add-opens javafx.controls/javafx.scene.control=ALL-UNNAMED --add-opens javafx.base/javafx.beans.property=ALL-UNNAMED -jar target\accounting-system-1.0.0.jar
) else (
    echo Building and running project...
    call mvnw.cmd javafx:run
)

if %errorlevel% neq 0 (
    echo.
    echo ERROR: Failed to start application
    echo.
    echo Troubleshooting:
    echo 1. Make sure Java 17+ is installed
    echo 2. Check internet connection for dependencies
    echo 3. Run build-installer.bat first
    echo.
    pause
    exit /b 1
)

echo.
echo Application closed successfully
pause
