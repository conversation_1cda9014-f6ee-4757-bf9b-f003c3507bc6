package com.accounting.model;

import javafx.beans.property.*;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.UUID;

/**
 * نموذج أمر التصنيع
 */
public class ManufacturingOrder {
    
    // الخصائص الأساسية
    private final StringProperty orderId = new SimpleStringProperty();
    private final StringProperty orderNumber = new SimpleStringProperty();
    private final StringProperty customerName = new SimpleStringProperty();
    private final StringProperty invoiceNumber = new SimpleStringProperty();
    private final ObjectProperty<LocalDate> orderDate = new SimpleObjectProperty<>();
    private final ObjectProperty<LocalDateTime> createdDate = new SimpleObjectProperty<>();
    private final ObjectProperty<OrderStatus> status = new SimpleObjectProperty<>();
    private final StringProperty notes = new SimpleStringProperty();
    
    // الخدمات المختارة
    private final BooleanProperty glassCuttingSelected = new SimpleBooleanProperty();
    private final BooleanProperty thermalGlassSelected = new SimpleBooleanProperty();
    private final BooleanProperty filmSelected = new SimpleBooleanProperty();
    private final BooleanProperty bulletProofFilmSelected = new SimpleBooleanProperty();
    private final BooleanProperty doubleGlassSelected = new SimpleBooleanProperty();
    private final BooleanProperty polishSelected = new SimpleBooleanProperty();
    private final BooleanProperty holeSelected = new SimpleBooleanProperty();
    private final BooleanProperty cncSelected = new SimpleBooleanProperty();
    private final BooleanProperty drawSelected = new SimpleBooleanProperty();
    private final BooleanProperty otherServicesSelected = new SimpleBooleanProperty();
    
    // قوائم التفاصيل
    private final ObservableList<GlassCuttingItem> glassCuttingItems = FXCollections.observableArrayList();
    private final ObservableList<FilmItem> filmItems = FXCollections.observableArrayList();
    private final ObservableList<DoubleGlassItem> doubleGlassItems = FXCollections.observableArrayList();
    private final ObservableList<RequiredMeasurement> requiredMeasurements = FXCollections.observableArrayList();
    
    // خاصية الزيادة
    private final BooleanProperty hasIncrease = new SimpleBooleanProperty();
    private final DoubleProperty increaseValue = new SimpleDoubleProperty();
    private final ObservableList<RequiredMeasurement> increasedMeasurements = FXCollections.observableArrayList();
    
    // معلومات إضافية
    private final StringProperty createdBy = new SimpleStringProperty();
    private final StringProperty modifiedBy = new SimpleStringProperty();
    private final ObjectProperty<LocalDateTime> modifiedDate = new SimpleObjectProperty<>();
    
    /**
     * حالة أمر التصنيع
     */
    public enum OrderStatus {
        DRAFT("مسودة"),
        CONFIRMED("مؤكد"),
        IN_PROGRESS("قيد التنفيذ"),
        COMPLETED("مكتمل"),
        CANCELLED("ملغي");
        
        private final String arabicName;
        
        OrderStatus(String arabicName) {
            this.arabicName = arabicName;
        }
        
        public String getArabicName() {
            return arabicName;
        }
        
        @Override
        public String toString() {
            return arabicName;
        }
    }
    
    /**
     * المنشئ الافتراضي
     */
    public ManufacturingOrder() {
        this.orderId.set(UUID.randomUUID().toString());
        this.orderDate.set(LocalDate.now());
        this.createdDate.set(LocalDateTime.now());
        this.status.set(OrderStatus.DRAFT);
    }
    
    /**
     * منشئ مع المعاملات الأساسية
     */
    public ManufacturingOrder(String customerName, String invoiceNumber) {
        this();
        this.customerName.set(customerName);
        this.invoiceNumber.set(invoiceNumber);
    }
    
    // Getters and Setters
    public String getOrderId() { return orderId.get(); }
    public void setOrderId(String orderId) { this.orderId.set(orderId); }
    public StringProperty orderIdProperty() { return orderId; }
    
    public String getOrderNumber() { return orderNumber.get(); }
    public void setOrderNumber(String orderNumber) { this.orderNumber.set(orderNumber); }
    public StringProperty orderNumberProperty() { return orderNumber; }
    
    public String getCustomerName() { return customerName.get(); }
    public void setCustomerName(String customerName) { this.customerName.set(customerName); }
    public StringProperty customerNameProperty() { return customerName; }
    
    public String getInvoiceNumber() { return invoiceNumber.get(); }
    public void setInvoiceNumber(String invoiceNumber) { this.invoiceNumber.set(invoiceNumber); }
    public StringProperty invoiceNumberProperty() { return invoiceNumber; }
    
    public LocalDate getOrderDate() { return orderDate.get(); }
    public void setOrderDate(LocalDate orderDate) { this.orderDate.set(orderDate); }
    public ObjectProperty<LocalDate> orderDateProperty() { return orderDate; }
    
    public LocalDateTime getCreatedDate() { return createdDate.get(); }
    public void setCreatedDate(LocalDateTime createdDate) { this.createdDate.set(createdDate); }
    public ObjectProperty<LocalDateTime> createdDateProperty() { return createdDate; }
    
    public OrderStatus getStatus() { return status.get(); }
    public void setStatus(OrderStatus status) { this.status.set(status); }
    public ObjectProperty<OrderStatus> statusProperty() { return status; }
    
    public String getNotes() { return notes.get(); }
    public void setNotes(String notes) { this.notes.set(notes); }
    public StringProperty notesProperty() { return notes; }
    
    // خصائص الخدمات
    public boolean isGlassCuttingSelected() { return glassCuttingSelected.get(); }
    public void setGlassCuttingSelected(boolean selected) { this.glassCuttingSelected.set(selected); }
    public BooleanProperty glassCuttingSelectedProperty() { return glassCuttingSelected; }
    
    public boolean isThermalGlassSelected() { return thermalGlassSelected.get(); }
    public void setThermalGlassSelected(boolean selected) { this.thermalGlassSelected.set(selected); }
    public BooleanProperty thermalGlassSelectedProperty() { return thermalGlassSelected; }
    
    public boolean isFilmSelected() { return filmSelected.get(); }
    public void setFilmSelected(boolean selected) { this.filmSelected.set(selected); }
    public BooleanProperty filmSelectedProperty() { return filmSelected; }
    
    public boolean isBulletProofFilmSelected() { return bulletProofFilmSelected.get(); }
    public void setBulletProofFilmSelected(boolean selected) { this.bulletProofFilmSelected.set(selected); }
    public BooleanProperty bulletProofFilmSelectedProperty() { return bulletProofFilmSelected; }
    
    public boolean isDoubleGlassSelected() { return doubleGlassSelected.get(); }
    public void setDoubleGlassSelected(boolean selected) { this.doubleGlassSelected.set(selected); }
    public BooleanProperty doubleGlassSelectedProperty() { return doubleGlassSelected; }
    
    public boolean isPolishSelected() { return polishSelected.get(); }
    public void setPolishSelected(boolean selected) { this.polishSelected.set(selected); }
    public BooleanProperty polishSelectedProperty() { return polishSelected; }
    
    public boolean isHoleSelected() { return holeSelected.get(); }
    public void setHoleSelected(boolean selected) { this.holeSelected.set(selected); }
    public BooleanProperty holeSelectedProperty() { return holeSelected; }
    
    public boolean isCncSelected() { return cncSelected.get(); }
    public void setCncSelected(boolean selected) { this.cncSelected.set(selected); }
    public BooleanProperty cncSelectedProperty() { return cncSelected; }
    
    public boolean isDrawSelected() { return drawSelected.get(); }
    public void setDrawSelected(boolean selected) { this.drawSelected.set(selected); }
    public BooleanProperty drawSelectedProperty() { return drawSelected; }
    
    public boolean isOtherServicesSelected() { return otherServicesSelected.get(); }
    public void setOtherServicesSelected(boolean selected) { this.otherServicesSelected.set(selected); }
    public BooleanProperty otherServicesSelectedProperty() { return otherServicesSelected; }
    
    // قوائم التفاصيل
    public ObservableList<GlassCuttingItem> getGlassCuttingItems() { return glassCuttingItems; }
    public ObservableList<FilmItem> getFilmItems() { return filmItems; }
    public ObservableList<DoubleGlassItem> getDoubleGlassItems() { return doubleGlassItems; }
    public ObservableList<RequiredMeasurement> getRequiredMeasurements() { return requiredMeasurements; }
    
    // خاصية الزيادة
    public boolean isHasIncrease() { return hasIncrease.get(); }
    public void setHasIncrease(boolean hasIncrease) { this.hasIncrease.set(hasIncrease); }
    public BooleanProperty hasIncreaseProperty() { return hasIncrease; }
    
    public double getIncreaseValue() { return increaseValue.get(); }
    public void setIncreaseValue(double increaseValue) { this.increaseValue.set(increaseValue); }
    public DoubleProperty increaseValueProperty() { return increaseValue; }
    
    public ObservableList<RequiredMeasurement> getIncreasedMeasurements() { return increasedMeasurements; }
    
    // معلومات إضافية
    public String getCreatedBy() { return createdBy.get(); }
    public void setCreatedBy(String createdBy) { this.createdBy.set(createdBy); }
    public StringProperty createdByProperty() { return createdBy; }
    
    public String getModifiedBy() { return modifiedBy.get(); }
    public void setModifiedBy(String modifiedBy) { this.modifiedBy.set(modifiedBy); }
    public StringProperty modifiedByProperty() { return modifiedBy; }
    
    public LocalDateTime getModifiedDate() { return modifiedDate.get(); }
    public void setModifiedDate(LocalDateTime modifiedDate) { this.modifiedDate.set(modifiedDate); }
    public ObjectProperty<LocalDateTime> modifiedDateProperty() { return modifiedDate; }
    
    /**
     * حساب إجمالي المتر المربع لقص الزجاج
     */
    public double getTotalGlassCuttingSquareMeters() {
        return glassCuttingItems.stream()
                .mapToDouble(GlassCuttingItem::getTotalSquareMeters)
                .sum();
    }
    
    /**
     * حساب إجمالي عدد قطع قص الزجاج
     */
    public int getTotalGlassCuttingCount() {
        return glassCuttingItems.stream()
                .mapToInt(GlassCuttingItem::getCount)
                .sum();
    }
    
    /**
     * حساب إجمالي المتر المربع للفيلم
     */
    public double getTotalFilmSquareMeters() {
        return filmItems.stream()
                .mapToDouble(FilmItem::getTotalSquareMeters)
                .sum();
    }
    
    /**
     * حساب إجمالي الأطوال للفيلم بالمتر
     */
    public double getTotalFilmLengthMeters() {
        return filmItems.stream()
                .mapToDouble(item -> item.getLength() / 1000.0 * item.getCount())
                .sum();
    }
    
    /**
     * حساب إجمالي المتر المربع للمقاسات المطلوبة
     */
    public double getTotalRequiredSquareMeters() {
        return requiredMeasurements.stream()
                .mapToDouble(RequiredMeasurement::getTotalSquareMeters)
                .sum();
    }
    
    /**
     * حساب إجمالي المتر الطولي للمقاسات المطلوبة
     */
    public double getTotalRequiredLinearMeters() {
        return requiredMeasurements.stream()
                .mapToDouble(RequiredMeasurement::getTotalLinearMeters)
                .sum();
    }
    
    /**
     * حساب إجمالي عدد القطع للمقاسات المطلوبة
     */
    public int getTotalRequiredCount() {
        return requiredMeasurements.stream()
                .mapToInt(RequiredMeasurement::getCount)
                .sum();
    }
    
    /**
     * تحديث المقاسات مع الزيادة
     */
    public void updateIncreasedMeasurements() {
        increasedMeasurements.clear();
        if (hasIncrease.get() && increaseValue.get() > 0) {
            for (RequiredMeasurement original : requiredMeasurements) {
                RequiredMeasurement increased = new RequiredMeasurement();
                increased.setDescription(original.getDescription());
                increased.setLength(original.getLength() + increaseValue.get());
                increased.setWidth(original.getWidth() + increaseValue.get());
                increased.setCount(original.getCount());
                increasedMeasurements.add(increased);
            }
        }
    }
    
    /**
     * التحقق من صحة البيانات
     */
    public boolean isValid() {
        return customerName.get() != null && !customerName.get().trim().isEmpty() &&
               orderNumber.get() != null && !orderNumber.get().trim().isEmpty() &&
               orderDate.get() != null;
    }
    
    /**
     * نسخ أمر التصنيع
     */
    public ManufacturingOrder copy() {
        ManufacturingOrder copy = new ManufacturingOrder();
        copy.setCustomerName(this.getCustomerName());
        copy.setInvoiceNumber(this.getInvoiceNumber());
        copy.setOrderDate(this.getOrderDate());
        copy.setNotes(this.getNotes());
        
        // نسخ الخدمات المختارة
        copy.setGlassCuttingSelected(this.isGlassCuttingSelected());
        copy.setThermalGlassSelected(this.isThermalGlassSelected());
        copy.setFilmSelected(this.isFilmSelected());
        copy.setBulletProofFilmSelected(this.isBulletProofFilmSelected());
        copy.setDoubleGlassSelected(this.isDoubleGlassSelected());
        copy.setPolishSelected(this.isPolishSelected());
        copy.setHoleSelected(this.isHoleSelected());
        copy.setCncSelected(this.isCncSelected());
        copy.setDrawSelected(this.isDrawSelected());
        copy.setOtherServicesSelected(this.isOtherServicesSelected());
        
        // نسخ التفاصيل
        for (GlassCuttingItem item : this.glassCuttingItems) {
            copy.glassCuttingItems.add(item.copy());
        }
        for (FilmItem item : this.filmItems) {
            copy.filmItems.add(item.copy());
        }
        for (DoubleGlassItem item : this.doubleGlassItems) {
            copy.doubleGlassItems.add(item.copy());
        }
        for (RequiredMeasurement item : this.requiredMeasurements) {
            copy.requiredMeasurements.add(item.copy());
        }
        
        // نسخ خاصية الزيادة
        copy.setHasIncrease(this.isHasIncrease());
        copy.setIncreaseValue(this.getIncreaseValue());
        
        return copy;
    }
}
