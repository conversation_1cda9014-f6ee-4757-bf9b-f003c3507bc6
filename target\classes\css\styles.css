/* ===== الألوان الأساسية ===== */
.root {
    -fx-primary-color: #2c3e50;
    -fx-secondary-color: #3498db;
    -fx-accent-color: #e74c3c;
    -fx-success-color: #27ae60;
    -fx-warning-color: #f39c12;
    -fx-background-color: #ecf0f1;
    -fx-text-color: #2c3e50;
    -fx-light-gray: #bdc3c7;
    -fx-dark-gray: #7f8c8d;
}

/* ===== الخط الأساسي ===== */
.root {
    -fx-font-family: "Segoe UI", "Tahoma", "Arial", sans-serif;
    -fx-font-size: 14px;
}

/* ===== شريط القوائم الرئيسي ===== */
.main-menu-bar {
    -fx-background-color: linear-gradient(to bottom, #34495e, #2c3e50);
    -fx-border-color: #1a252f;
    -fx-border-width: 0 0 1 0;
}

.main-menu-bar .menu {
    -fx-text-fill: white;
    -fx-font-weight: bold;
    -fx-padding: 8 15 8 15;
}

.main-menu-bar .menu:hover {
    -fx-background-color: #3498db;
}

.main-menu-bar .menu:showing {
    -fx-background-color: #2980b9;
}

.main-menu-bar .menu .label {
    -fx-text-fill: white;
}

/* ===== قوائم فرعية ===== */
.context-menu {
    -fx-background-color: white;
    -fx-border-color: #bdc3c7;
    -fx-border-width: 1;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.2), 10, 0, 0, 2);
}

.menu-item {
    -fx-padding: 8 20 8 20;
    -fx-text-fill: #2c3e50;
}

.menu-item:hover {
    -fx-background-color: #ecf0f1;
}

.menu-item:focused {
    -fx-background-color: #3498db;
    -fx-text-fill: white;
}

/* ===== شريط الأدوات ===== */
.main-toolbar {
    -fx-background-color: linear-gradient(to bottom, #ffffff, #f8f9fa);
    -fx-border-color: #dee2e6;
    -fx-border-width: 0 0 1 0;
    -fx-padding: 5;
}

.toolbar-button {
    -fx-background-color: transparent;
    -fx-text-fill: #495057;
    -fx-font-size: 12px;
    -fx-padding: 8 12 8 12;
    -fx-border-radius: 4;
    -fx-background-radius: 4;
}

.toolbar-button:hover {
    -fx-background-color: #e9ecef;
    -fx-text-fill: #212529;
}

.toolbar-button:pressed {
    -fx-background-color: #dee2e6;
}

/* ===== منطقة المحتوى ===== */
.content-scroll-pane {
    -fx-background-color: transparent;
    -fx-border-color: transparent;
}

.content-area {
    -fx-background-color: #ffffff;
    -fx-min-height: 600px;
}

/* ===== شاشة الترحيب ===== */
.welcome-title {
    -fx-font-size: 32px;
    -fx-font-weight: bold;
    -fx-text-fill: #2c3e50;
    -fx-text-alignment: center;
}

.welcome-description {
    -fx-font-size: 16px;
    -fx-text-fill: #7f8c8d;
    -fx-text-alignment: center;
}

/* ===== أزرار الوصول السريع ===== */
.quick-access-button {
    -fx-background-color: linear-gradient(to bottom, #3498db, #2980b9);
    -fx-text-fill: white;
    -fx-font-size: 14px;
    -fx-font-weight: bold;
    -fx-padding: 15 25 15 25;
    -fx-border-radius: 8;
    -fx-background-radius: 8;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.2), 5, 0, 0, 2);
    -fx-cursor: hand;
    -fx-min-width: 120px;
    -fx-alignment: center;
    -fx-content-display: top;
    -fx-graphic-text-gap: 8;
}

.quick-access-button:hover {
    -fx-background-color: linear-gradient(to bottom, #5dade2, #3498db);
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.3), 8, 0, 0, 3);
    -fx-scale-y: 1.05;
    -fx-scale-x: 1.05;
}

.quick-access-button:pressed {
    -fx-background-color: linear-gradient(to bottom, #2980b9, #21618c);
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.4), 3, 0, 0, 1);
}

/* ===== عناوين الموديولات ===== */
.module-title {
    -fx-font-size: 28px;
    -fx-font-weight: bold;
    -fx-text-fill: #2c3e50;
    -fx-text-alignment: center;
}

.module-message {
    -fx-font-size: 16px;
    -fx-text-fill: #7f8c8d;
    -fx-text-alignment: center;
}

/* ===== زر العودة ===== */
.back-button {
    -fx-background-color: linear-gradient(to bottom, #95a5a6, #7f8c8d);
    -fx-text-fill: white;
    -fx-font-size: 14px;
    -fx-padding: 10 20 10 20;
    -fx-border-radius: 6;
    -fx-background-radius: 6;
    -fx-cursor: hand;
}

.back-button:hover {
    -fx-background-color: linear-gradient(to bottom, #a6b5b6, #95a5a6);
}

/* ===== شريط الحالة ===== */
.status-bar {
    -fx-background-color: linear-gradient(to bottom, #f8f9fa, #e9ecef);
    -fx-border-color: #dee2e6;
    -fx-border-width: 1 0 0 0;
    -fx-padding: 0;
    -fx-min-height: 25px;
    -fx-max-height: 25px;
}

.status-label {
    -fx-text-fill: #495057;
    -fx-font-size: 12px;
}

.version-label {
    -fx-text-fill: #6c757d;
    -fx-font-size: 11px;
    -fx-font-style: italic;
}

/* ===== أيقونات شريط الأدوات ===== */
.icon-new, .icon-save, .icon-print, .icon-undo, .icon-redo, .icon-search {
    -fx-min-width: 16px;
    -fx-min-height: 16px;
    -fx-max-width: 16px;
    -fx-max-height: 16px;
}

.icon-new {
    -fx-background-color: #27ae60;
}

.icon-save {
    -fx-background-color: #3498db;
}

.icon-print {
    -fx-background-color: #9b59b6;
}

.icon-undo {
    -fx-background-color: #f39c12;
}

.icon-redo {
    -fx-background-color: #e67e22;
}

.icon-search {
    -fx-background-color: #1abc9c;
}

/* ===== تأثيرات الحركة ===== */
.quick-access-button {
    -fx-transition: all 0.3s ease;
}

.toolbar-button {
    -fx-transition: all 0.2s ease;
}

/* ===== تحسينات إضافية ===== */
.scroll-pane > .viewport {
    -fx-background-color: transparent;
}

.scroll-pane {
    -fx-background-color: transparent;
}

.scroll-bar:horizontal, .scroll-bar:vertical {
    -fx-background-color: #f8f9fa;
}

.scroll-bar .thumb {
    -fx-background-color: #ced4da;
    -fx-background-radius: 5;
}

.scroll-bar .thumb:hover {
    -fx-background-color: #adb5bd;
}

/* ===== تخصيص للنصوص العربية ===== */
.arabic-text {
    -fx-font-family: "Segoe UI", "Tahoma", "Arial Unicode MS", sans-serif;
    -fx-text-alignment: right;
}

/* ===== موديول المبيعات ===== */
.sales-module {
    -fx-background-color: #ffffff;
    -fx-padding: 20;
}

.toolbar {
    -fx-background-color: linear-gradient(to bottom, #f8f9fa, #e9ecef);
    -fx-border-color: #dee2e6;
    -fx-border-width: 0 0 1 0;
    -fx-border-radius: 5 5 0 0;
    -fx-background-radius: 5 5 0 0;
}

.invoice-area {
    -fx-background-color: #ffffff;
    -fx-border-color: #dee2e6;
    -fx-border-width: 1;
    -fx-border-radius: 5;
    -fx-background-radius: 5;
    -fx-padding: 15;
}

.invoice-header {
    -fx-background-color: linear-gradient(to bottom, #ffffff, #f8f9fa);
    -fx-border-color: #dee2e6;
    -fx-border-width: 0 0 2 0;
    -fx-border-radius: 8 8 0 0;
    -fx-background-radius: 8 8 0 0;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 3, 0, 0, 1);
}

.field-label {
    -fx-text-fill: #495057;
    -fx-font-weight: bold;
    -fx-font-size: 12px;
}

.invoice-field {
    -fx-background-color: white;
    -fx-border-color: #ced4da;
    -fx-border-width: 1;
    -fx-border-radius: 4;
    -fx-background-radius: 4;
    -fx-padding: 6 10 6 10;
    -fx-font-size: 13px;
}

.invoice-field:focused {
    -fx-border-color: #3498db;
    -fx-effect: dropshadow(gaussian, rgba(52, 152, 219, 0.3), 3, 0, 0, 0);
}

.invoice-table {
    -fx-background-color: #ffffff;
    -fx-border-color: #dee2e6;
    -fx-border-width: 1;
    -fx-border-radius: 6;
    -fx-background-radius: 6;
}

.invoice-table .column-header-background {
    -fx-background-color: linear-gradient(to bottom, #3498db, #2980b9);
}

.invoice-table .column-header {
    -fx-background-color: transparent;
    -fx-text-fill: white;
    -fx-font-weight: bold;
    -fx-font-size: 13px;
    -fx-padding: 10 8 10 8;
    -fx-alignment: center;
}

.invoice-table .table-cell {
    -fx-border-color: #e9ecef;
    -fx-border-width: 0 1 1 0;
    -fx-padding: 12 8 12 8;
    -fx-alignment: center;
    -fx-font-size: 13px;
    -fx-min-height: 45px;
    -fx-pref-height: 45px;
    -fx-max-height: 45px;
}

.invoice-table .table-row-cell {
    -fx-background-color: white;
    -fx-min-height: 45px;
    -fx-pref-height: 45px;
    -fx-max-height: 45px;
    -fx-border-color: #dee2e6;
    -fx-border-width: 0 0 1 0;
}

.invoice-table .table-row-cell:odd {
    -fx-background-color: #f8f9fa;
}

.invoice-table .table-row-cell:selected {
    -fx-background-color: #e3f2fd;
    -fx-text-fill: #1976d2;
    -fx-border-color: #3498db;
    -fx-border-width: 2;
}

.invoice-table .table-row-cell:hover {
    -fx-background-color: #f0f8ff;
    -fx-effect: dropshadow(gaussian, rgba(52, 152, 219, 0.2), 3, 0, 0, 0);
}

.invoice-table .table-row-cell:empty {
    -fx-background-color: #fafafa;
    -fx-border-color: #e0e0e0;
    -fx-border-style: dashed;
}

/* Placeholder للجدول الفارغ */
.table-placeholder {
    -fx-text-fill: #6c757d;
    -fx-font-size: 16px;
    -fx-font-style: italic;
    -fx-padding: 50;
}

/* تحسين ComboBox في الجدول */
.invoice-table .combo-box {
    -fx-background-color: white;
    -fx-border-color: #ced4da;
    -fx-border-width: 1;
    -fx-border-radius: 4;
    -fx-padding: 6 8 6 8;
    -fx-font-size: 12px;
    -fx-min-height: 35px;
    -fx-pref-height: 35px;
}

.invoice-table .combo-box:focused {
    -fx-border-color: #3498db;
    -fx-border-width: 2;
    -fx-effect: dropshadow(gaussian, rgba(52, 152, 219, 0.3), 3, 0, 0, 0);
}

.invoice-table .combo-box:hover {
    -fx-border-color: #80bdff;
}

/* تحسين TextField في الجدول */
.invoice-table .text-field {
    -fx-background-color: white;
    -fx-border-color: #ced4da;
    -fx-border-width: 1;
    -fx-border-radius: 4;
    -fx-padding: 6 8 6 8;
    -fx-alignment: center;
    -fx-font-size: 12px;
    -fx-min-height: 35px;
    -fx-pref-height: 35px;
}

.invoice-table .text-field:focused {
    -fx-border-color: #3498db;
    -fx-border-width: 2;
    -fx-effect: dropshadow(gaussian, rgba(52, 152, 219, 0.3), 3, 0, 0, 0);
}

.invoice-table .text-field:hover {
    -fx-border-color: #80bdff;
}

/* تحسين الخلايا المحسوبة */
.invoice-table .calculated-cell {
    -fx-background-color: #f8f9fa;
    -fx-text-fill: #495057;
    -fx-font-weight: bold;
    -fx-border-color: #e9ecef;
    -fx-border-width: 1;
    -fx-border-radius: 3;
}

.invoice-summary {
    -fx-background-color: #f8f9fa;
    -fx-border-color: #dee2e6;
    -fx-border-width: 1;
    -fx-border-radius: 5;
    -fx-background-radius: 5;
}

.summary-value {
    -fx-font-weight: bold;
    -fx-text-fill: #2c3e50;
}

.total-box {
    -fx-background-color: #3498db;
    -fx-padding: 10;
    -fx-border-radius: 5;
    -fx-background-radius: 5;
}

.total-label {
    -fx-text-fill: white;
    -fx-font-weight: bold;
    -fx-font-size: 16px;
}

.total-value {
    -fx-text-fill: white;
    -fx-font-weight: bold;
    -fx-font-size: 18px;
}

/* ===== أزرار إدارة الصفوف ===== */
.row-controls {
    -fx-background-color: #f8f9fa;
    -fx-border-color: #dee2e6;
    -fx-border-width: 1 0 0 0;
    -fx-padding: 15;
}

/* زر إضافة صف عادي */
.add-normal-row-button {
    -fx-background-color: linear-gradient(to bottom, #007bff, #0056b3);
    -fx-text-fill: white;
    -fx-font-weight: bold;
    -fx-font-size: 14px;
    -fx-padding: 12 20 12 20;
    -fx-border-radius: 6;
    -fx-background-radius: 6;
    -fx-cursor: hand;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.2), 3, 0, 0, 1);
}

.add-normal-row-button:hover {
    -fx-background-color: linear-gradient(to bottom, #0056b3, #004085);
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.3), 5, 0, 0, 2);
    -fx-scale-x: 1.02;
    -fx-scale-y: 1.02;
}

/* زر إضافة صف يدوي */
.add-manual-row-button {
    -fx-background-color: linear-gradient(to bottom, #28a745, #218838);
    -fx-text-fill: white;
    -fx-font-weight: bold;
    -fx-font-size: 14px;
    -fx-padding: 12 20 12 20;
    -fx-border-radius: 6;
    -fx-background-radius: 6;
    -fx-cursor: hand;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.2), 3, 0, 0, 1);
}

.add-manual-row-button:hover {
    -fx-background-color: linear-gradient(to bottom, #34ce57, #28a745);
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.3), 5, 0, 0, 2);
    -fx-scale-x: 1.02;
    -fx-scale-y: 1.02;
}

.add-manual-row-button:pressed {
    -fx-background-color: linear-gradient(to bottom, #218838, #1e7e34);
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.4), 2, 0, 0, 1);
}

/* زر صغير */
.small-button {
    -fx-background-color: linear-gradient(to bottom, #6c757d, #545b62);
    -fx-text-fill: white;
    -fx-font-size: 11px;
    -fx-padding: 4 8 4 8;
    -fx-border-radius: 3;
    -fx-background-radius: 3;
    -fx-cursor: hand;
}

.small-button:hover {
    -fx-background-color: linear-gradient(to bottom, #545b62, #495057);
}

.info-label {
    -fx-text-fill: #6c757d;
    -fx-font-size: 12px;
    -fx-font-style: italic;
}

/* ===== أزرار الإجراءات في الجدول ===== */
.add-row-action-button {
    -fx-background-color: linear-gradient(to bottom, #17a2b8, #138496);
    -fx-text-fill: white;
    -fx-padding: 4 6 4 6;
    -fx-border-radius: 3;
    -fx-background-radius: 3;
    -fx-cursor: hand;
    -fx-min-width: 25px;
    -fx-min-height: 25px;
}

.add-row-action-button:hover {
    -fx-background-color: linear-gradient(to bottom, #20c0db, #17a2b8);
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.2), 3, 0, 0, 1);
}

.delete-row-action-button {
    -fx-background-color: linear-gradient(to bottom, #dc3545, #c82333);
    -fx-text-fill: white;
    -fx-padding: 4 6 4 6;
    -fx-border-radius: 3;
    -fx-background-radius: 3;
    -fx-cursor: hand;
    -fx-min-width: 25px;
    -fx-min-height: 25px;
}

.delete-row-action-button:hover {
    -fx-background-color: linear-gradient(to bottom, #e4606d, #dc3545);
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.2), 3, 0, 0, 1);
}

/* ===== أزرار قديمة للتوافق ===== */
.add-row-button {
    -fx-background-color: linear-gradient(to bottom, #27ae60, #229954);
    -fx-text-fill: white;
    -fx-font-weight: bold;
    -fx-padding: 8 15 8 15;
    -fx-border-radius: 5;
    -fx-background-radius: 5;
    -fx-cursor: hand;
}

.add-row-button:hover {
    -fx-background-color: linear-gradient(to bottom, #2ecc71, #27ae60);
}

.delete-button {
    -fx-background-color: linear-gradient(to bottom, #e74c3c, #c0392b);
    -fx-text-fill: white;
    -fx-padding: 5 8 5 8;
    -fx-border-radius: 3;
    -fx-background-radius: 3;
    -fx-cursor: hand;
}

.delete-button:hover {
    -fx-background-color: linear-gradient(to bottom, #ec7063, #e74c3c);
}

/* ===== نافذة إدارة الخدمات ===== */
.dialog-title {
    -fx-font-size: 18px;
    -fx-font-weight: bold;
    -fx-text-fill: #2c3e50;
}

.add-service-form {
    -fx-background-color: #f8f9fa;
    -fx-padding: 15;
    -fx-border-color: #dee2e6;
    -fx-border-width: 1;
    -fx-border-radius: 5;
    -fx-background-radius: 5;
}

.add-button {
    -fx-background-color: linear-gradient(to bottom, #3498db, #2980b9);
    -fx-text-fill: white;
    -fx-font-weight: bold;
    -fx-padding: 8 15 8 15;
    -fx-border-radius: 5;
    -fx-background-radius: 5;
    -fx-cursor: hand;
}

.add-button:hover {
    -fx-background-color: linear-gradient(to bottom, #5dade2, #3498db);
}

/* ===== موديول الرواتب ===== */
.payroll-module {
    -fx-background-color: #f8f9fa;
}

.main-tabs .tab-pane {
    -fx-background-color: white;
}

.main-tabs .tab {
    -fx-background-color: #e9ecef;
    -fx-text-fill: #495057;
    -fx-font-weight: bold;
    -fx-padding: 10 20 10 20;
}

.main-tabs .tab:selected {
    -fx-background-color: #3498db;
    -fx-text-fill: white;
}

.data-table {
    -fx-background-color: white;
    -fx-border-color: #dee2e6;
    -fx-border-width: 1;
    -fx-border-radius: 5;
    -fx-background-radius: 5;
}

.data-table .column-header-background {
    -fx-background-color: linear-gradient(to bottom, #3498db, #2980b9);
}

.data-table .column-header {
    -fx-background-color: transparent;
    -fx-text-fill: white;
    -fx-font-weight: bold;
    -fx-font-size: 12px;
    -fx-padding: 8 6 8 6;
    -fx-alignment: center;
}

.data-table .table-cell {
    -fx-border-color: #e9ecef;
    -fx-border-width: 0 1 1 0;
    -fx-padding: 8 6 8 6;
    -fx-alignment: center;
    -fx-font-size: 11px;
}

.data-table .table-row-cell {
    -fx-background-color: white;
}

.data-table .table-row-cell:odd {
    -fx-background-color: #f8f9fa;
}

.data-table .table-row-cell:selected {
    -fx-background-color: #e3f2fd;
    -fx-text-fill: #1976d2;
}

.data-table .table-row-cell:hover {
    -fx-background-color: #f0f8ff;
}

/* أزرار التقرير والتعديل */
.report-button {
    -fx-background-color: linear-gradient(to bottom, #17a2b8, #138496);
    -fx-text-fill: white;
    -fx-padding: 4 8 4 8;
    -fx-border-radius: 3;
    -fx-background-radius: 3;
    -fx-cursor: hand;
    -fx-font-size: 10px;
}

.report-button:hover {
    -fx-background-color: linear-gradient(to bottom, #20c0d7, #17a2b8);
}

.edit-button {
    -fx-background-color: linear-gradient(to bottom, #ffc107, #e0a800);
    -fx-text-fill: #212529;
    -fx-padding: 4 8 4 8;
    -fx-border-radius: 3;
    -fx-background-radius: 3;
    -fx-cursor: hand;
    -fx-font-size: 10px;
}

.edit-button:hover {
    -fx-background-color: linear-gradient(to bottom, #ffcd39, #ffc107);
}

/* حقل محسوب */
.calculated-field {
    -fx-background-color: #f8f9fa;
    -fx-text-fill: #495057;
    -fx-font-weight: bold;
    -fx-border-color: #e9ecef;
    -fx-border-width: 1;
    -fx-border-radius: 3;
}

/* معلومات الموظف */
.employee-info {
    -fx-background-color: #e3f2fd;
    -fx-padding: 15;
    -fx-border-color: #2196f3;
    -fx-border-width: 1;
    -fx-border-radius: 5;
    -fx-background-radius: 5;
}

/* الملخص المالي */
.financial-summary {
    -fx-background-color: #f1f8e9;
    -fx-padding: 15;
    -fx-border-color: #4caf50;
    -fx-border-width: 1;
    -fx-border-radius: 5;
    -fx-background-radius: 5;
}

.positive-balance {
    -fx-text-fill: #2e7d32;
    -fx-font-weight: bold;
}

.negative-balance {
    -fx-text-fill: #d32f2f;
    -fx-font-weight: bold;
}

/* أزرار PDF والطباعة */
.pdf-button {
    -fx-background-color: linear-gradient(to bottom, #dc3545, #c82333);
    -fx-text-fill: white;
    -fx-padding: 8 15 8 15;
    -fx-border-radius: 5;
    -fx-background-radius: 5;
    -fx-cursor: hand;
    -fx-font-weight: bold;
}

.pdf-button:hover {
    -fx-background-color: linear-gradient(to bottom, #e4606d, #dc3545);
}

.print-button {
    -fx-background-color: linear-gradient(to bottom, #6c757d, #545b62);
    -fx-text-fill: white;
    -fx-padding: 8 15 8 15;
    -fx-border-radius: 5;
    -fx-background-radius: 5;
    -fx-cursor: hand;
    -fx-font-weight: bold;
}

.print-button:hover {
    -fx-background-color: linear-gradient(to bottom, #828a91, #6c757d);
}

/* ===== موديول المخازن ===== */
.inventory-module {
    -fx-background-color: #f8f9fa;
}

.filter-section {
    -fx-background-color: white;
    -fx-border-color: #dee2e6;
    -fx-border-width: 1;
    -fx-border-radius: 5;
    -fx-background-radius: 5;
}

.section-title {
    -fx-font-size: 14px;
    -fx-font-weight: bold;
    -fx-text-fill: #495057;
}

.stats-box {
    -fx-background-color: white;
    -fx-border-color: #dee2e6;
    -fx-border-width: 1;
    -fx-border-radius: 8;
    -fx-background-radius: 8;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 3, 0, 0, 1);
}

.stat-card {
    -fx-background-color: white;
    -fx-border-radius: 8;
    -fx-background-radius: 8;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 2, 0, 0, 1);
    -fx-min-width: 150px;
}

.stat-title {
    -fx-font-size: 12px;
    -fx-text-fill: #6c757d;
    -fx-font-weight: normal;
}

.stat-value {
    -fx-font-size: 24px;
    -fx-font-weight: bold;
}

/* أزرار الإجراءات في المخازن */
.receive-button {
    -fx-background-color: linear-gradient(to bottom, #28a745, #218838);
    -fx-text-fill: white;
    -fx-padding: 3 6 3 6;
    -fx-border-radius: 3;
    -fx-background-radius: 3;
    -fx-cursor: hand;
    -fx-font-size: 9px;
}

.receive-button:hover {
    -fx-background-color: linear-gradient(to bottom, #34ce57, #28a745);
}

.issue-button {
    -fx-background-color: linear-gradient(to bottom, #dc3545, #c82333);
    -fx-text-fill: white;
    -fx-padding: 3 6 3 6;
    -fx-border-radius: 3;
    -fx-background-radius: 3;
    -fx-cursor: hand;
    -fx-font-size: 9px;
}

.issue-button:hover {
    -fx-background-color: linear-gradient(to bottom, #e4606d, #dc3545);
}

.history-button {
    -fx-background-color: linear-gradient(to bottom, #6f42c1, #5a32a3);
    -fx-text-fill: white;
    -fx-padding: 3 6 3 6;
    -fx-border-radius: 3;
    -fx-background-radius: 3;
    -fx-cursor: hand;
    -fx-font-size: 9px;
}

.history-button:hover {
    -fx-background-color: linear-gradient(to bottom, #8a63d2, #6f42c1);
}

.view-button {
    -fx-background-color: linear-gradient(to bottom, #17a2b8, #138496);
    -fx-text-fill: white;
    -fx-padding: 3 6 3 6;
    -fx-border-radius: 3;
    -fx-background-radius: 3;
    -fx-cursor: hand;
    -fx-font-size: 9px;
}

.view-button:hover {
    -fx-background-color: linear-gradient(to bottom, #20c0d7, #17a2b8);
}

.clear-button {
    -fx-background-color: linear-gradient(to bottom, #6c757d, #545b62);
    -fx-text-fill: white;
    -fx-padding: 4 8 4 8;
    -fx-border-radius: 3;
    -fx-background-radius: 3;
    -fx-cursor: hand;
    -fx-font-size: 11px;
}

.clear-button:hover {
    -fx-background-color: linear-gradient(to bottom, #828a91, #6c757d);
}

/* خصائص الأبعاد */
.dimensions-checkbox {
    -fx-font-size: 12px;
    -fx-font-weight: bold;
    -fx-text-fill: #007bff;
}

.calculated-field {
    -fx-background-color: #f8f9fa;
    -fx-border-color: #dee2e6;
    -fx-border-width: 1;
    -fx-text-fill: #495057;
    -fx-font-weight: bold;
}

.dimensions-form {
    -fx-background-color: #f0f8ff;
    -fx-border-color: #007bff;
    -fx-border-width: 1;
    -fx-border-radius: 5;
    -fx-background-radius: 5;
    -fx-padding: 10;
}

/* تحسين القوائم المنسدلة */
.menu-bar {
    -fx-background-color: linear-gradient(to bottom, #ffffff, #f8f9fa);
    -fx-border-color: #dee2e6;
    -fx-border-width: 0 0 1 0;
}

.menu-bar .menu {
    -fx-background-color: #f8f9fa;
    -fx-text-fill: #495057;
    -fx-font-weight: bold;
    -fx-padding: 10 18 10 18;
    -fx-border-radius: 6;
    -fx-background-radius: 6;
    -fx-border-color: #dee2e6;
    -fx-border-width: 1;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.05), 2, 0, 0, 1);
}

.menu-bar .menu:hover {
    -fx-background-color: #e3f2fd;
    -fx-text-fill: #1976d2;
    -fx-border-color: #2196f3;
    -fx-effect: dropshadow(gaussian, rgba(33, 150, 243, 0.3), 4, 0, 0, 2);
}

.menu-bar .menu:showing {
    -fx-background-color: #1976d2;
    -fx-text-fill: white;
    -fx-border-color: #1565c0;
    -fx-effect: dropshadow(gaussian, rgba(25, 118, 210, 0.4), 6, 0, 0, 3);
}

.menu-bar .menu .label {
    -fx-text-fill: inherit;
}

.context-menu {
    -fx-background-color: white;
    -fx-border-color: #dee2e6;
    -fx-border-width: 1;
    -fx-border-radius: 6;
    -fx-background-radius: 6;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.15), 8, 0, 0, 2);
    -fx-padding: 5;
}

.menu-item {
    -fx-background-color: white;
    -fx-text-fill: #495057;
    -fx-padding: 10 20 10 20;
    -fx-border-radius: 4;
    -fx-background-radius: 4;
    -fx-font-size: 14px;
    -fx-font-weight: normal;
    -fx-border-color: transparent;
    -fx-border-width: 1;
}

.menu-item:hover {
    -fx-background-color: #e3f2fd;
    -fx-text-fill: #1976d2;
    -fx-border-color: #bbdefb;
    -fx-effect: dropshadow(gaussian, rgba(33, 150, 243, 0.2), 3, 0, 0, 1);
}

.menu-item:focused {
    -fx-background-color: #1976d2;
    -fx-text-fill: white;
    -fx-border-color: #1565c0;
    -fx-effect: dropshadow(gaussian, rgba(25, 118, 210, 0.3), 4, 0, 0, 2);
}

.menu-item .label {
    -fx-text-fill: inherit;
}

.separator .line {
    -fx-border-color: #dee2e6;
    -fx-border-width: 1 0 0 0;
    -fx-padding: 2 0 2 0;
}

/* أزرار التقارير والرسوم البيانية */
.report-button {
    -fx-background-color: linear-gradient(to bottom, #17a2b8, #138496);
    -fx-text-fill: white;
    -fx-padding: 8 15 8 15;
    -fx-border-radius: 4;
    -fx-background-radius: 4;
    -fx-cursor: hand;
    -fx-font-weight: bold;
}

.report-button:hover {
    -fx-background-color: linear-gradient(to bottom, #20c0d7, #17a2b8);
}

.chart-button {
    -fx-background-color: linear-gradient(to bottom, #6f42c1, #5a32a3);
    -fx-text-fill: white;
    -fx-padding: 8 15 8 15;
    -fx-border-radius: 4;
    -fx-background-radius: 4;
    -fx-cursor: hand;
    -fx-font-weight: bold;
}

.chart-button:hover {
    -fx-background-color: linear-gradient(to bottom, #8a63d2, #6f42c1);
}

.export-button {
    -fx-background-color: linear-gradient(to bottom, #28a745, #218838);
    -fx-text-fill: white;
    -fx-padding: 6 12 6 12;
    -fx-border-radius: 3;
    -fx-background-radius: 3;
    -fx-cursor: hand;
    -fx-font-size: 11px;
}

.export-button:hover {
    -fx-background-color: linear-gradient(to bottom, #34ce57, #28a745);
}

/* أزرار الفواتير المحفوظة */
.view-button {
    -fx-background-color: linear-gradient(to bottom, #17a2b8, #138496);
    -fx-text-fill: white;
    -fx-padding: 4 8 4 8;
    -fx-border-radius: 3;
    -fx-background-radius: 3;
    -fx-cursor: hand;
    -fx-font-size: 10px;
}

.view-button:hover {
    -fx-background-color: linear-gradient(to bottom, #20c0d7, #17a2b8);
}

.edit-button {
    -fx-background-color: linear-gradient(to bottom, #ffc107, #e0a800);
    -fx-text-fill: white;
    -fx-padding: 4 8 4 8;
    -fx-border-radius: 3;
    -fx-background-radius: 3;
    -fx-cursor: hand;
    -fx-font-size: 10px;
}

.edit-button:hover {
    -fx-background-color: linear-gradient(to bottom, #ffcd39, #ffc107);
}

/* تحسين بطاقات الإحصائيات */
.stat-card {
    -fx-background-color: white;
    -fx-border-radius: 8;
    -fx-background-radius: 8;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 6, 0, 0, 2);
    -fx-padding: 20;
    -fx-alignment: center;
    -fx-min-width: 150;
}

.stat-title {
    -fx-font-size: 12px;
    -fx-text-fill: #6c757d;
    -fx-font-weight: normal;
}

.stat-value {
    -fx-font-size: 24px;
    -fx-font-weight: bold;
    -fx-padding: 5 0 0 0;
}

/* تحسين معلومات الفاتورة */
.invoice-info {
    -fx-background-color: #f8f9fa;
    -fx-border-color: #dee2e6;
    -fx-border-width: 1;
    -fx-border-radius: 5;
    -fx-background-radius: 5;
    -fx-padding: 15;
}

.invoice-summary {
    -fx-background-color: #f8f9fa;
    -fx-border-color: #dee2e6;
    -fx-border-width: 1;
    -fx-border-radius: 5;
    -fx-background-radius: 5;
    -fx-padding: 15;
}

.total-value {
    -fx-font-size: 18px;
    -fx-font-weight: bold;
    -fx-text-fill: #28a745;
}

/* منطقة الخصم والملاحظات */
.discount-notes-area {
    -fx-background-color: #f8f9fa;
    -fx-border-color: #dee2e6;
    -fx-border-width: 1;
    -fx-border-radius: 8;
    -fx-background-radius: 8;
}

.discount-field {
    -fx-font-size: 14px;
    -fx-padding: 8;
    -fx-border-color: #ced4da;
    -fx-border-width: 1;
    -fx-border-radius: 4;
    -fx-background-radius: 4;
}

.discount-type-combo {
    -fx-font-size: 12px;
    -fx-padding: 6;
}

.notes-area {
    -fx-font-size: 13px;
    -fx-padding: 10;
    -fx-border-color: #ced4da;
    -fx-border-width: 1;
    -fx-border-radius: 4;
    -fx-background-radius: 4;
}

.currency-label {
    -fx-font-size: 12px;
    -fx-text-fill: #6c757d;
    -fx-padding: 0 0 0 5;
}

/* ملخص الفاتورة المحسن */
.summary-title {
    -fx-font-size: 16px;
    -fx-font-weight: bold;
    -fx-text-fill: #495057;
    -fx-padding: 0 0 10 0;
}

.summary-row {
    -fx-padding: 8 0 8 0;
    -fx-border-color: #dee2e6;
    -fx-border-width: 0 0 1 0;
}

.summary-label {
    -fx-font-size: 14px;
    -fx-text-fill: #495057;
}

.summary-value {
    -fx-font-size: 14px;
    -fx-font-weight: bold;
    -fx-text-fill: #495057;
}

.discount-value {
    -fx-font-size: 14px;
    -fx-font-weight: bold;
    -fx-text-fill: #dc3545;
}

.total-summary-box {
    -fx-background-color: #e8f5e8;
    -fx-border-color: #28a745;
    -fx-border-width: 2;
    -fx-border-radius: 6;
    -fx-background-radius: 6;
    -fx-padding: 15;
}

.total-label {
    -fx-font-size: 16px;
    -fx-font-weight: bold;
    -fx-text-fill: #155724;
}

/* تحسين ScrollPane للفاتورة */
.invoice-scroll-pane {
    -fx-background-color: transparent;
    -fx-border-color: transparent;
}

.invoice-scroll-pane .viewport {
    -fx-background-color: transparent;
}

.invoice-scroll-pane .scroll-bar:vertical {
    -fx-background-color: #f8f9fa;
    -fx-border-color: #dee2e6;
    -fx-border-width: 1;
    -fx-border-radius: 5;
    -fx-background-radius: 5;
}

.invoice-scroll-pane .scroll-bar:vertical .track {
    -fx-background-color: #f8f9fa;
    -fx-border-radius: 5;
    -fx-background-radius: 5;
}

.invoice-scroll-pane .scroll-bar:vertical .thumb {
    -fx-background-color: #6c757d;
    -fx-border-radius: 5;
    -fx-background-radius: 5;
}

.invoice-scroll-pane .scroll-bar:vertical .thumb:hover {
    -fx-background-color: #495057;
}

/* فلاتر التقارير */
.filters-title {
    -fx-font-size: 14px;
    -fx-font-weight: bold;
    -fx-text-fill: #495057;
    -fx-padding: 0 0 10 0;
}

.filter-button {
    -fx-background-color: linear-gradient(to bottom, #007bff, #0056b3);
    -fx-text-fill: white;
    -fx-padding: 8 15 8 15;
    -fx-border-radius: 4;
    -fx-background-radius: 4;
    -fx-cursor: hand;
    -fx-font-size: 12px;
    -fx-font-weight: bold;
}

.filter-button:hover {
    -fx-background-color: linear-gradient(to bottom, #0056b3, #004085);
}

.reset-button {
    -fx-background-color: linear-gradient(to bottom, #6c757d, #545b62);
    -fx-text-fill: white;
    -fx-padding: 8 15 8 15;
    -fx-border-radius: 4;
    -fx-background-radius: 4;
    -fx-cursor: hand;
    -fx-font-size: 12px;
}

.reset-button:hover {
    -fx-background-color: linear-gradient(to bottom, #545b62, #3d4142);
}

/* تحسين حقول النماذج */
.field-label {
    -fx-font-size: 13px;
    -fx-font-weight: bold;
    -fx-text-fill: #495057;
    -fx-padding: 0 0 5 0;
}

.dialog-title {
    -fx-font-size: 18px;
    -fx-font-weight: bold;
    -fx-text-fill: #2c3e50;
    -fx-padding: 0 0 20 0;
}

/* تحسين أزرار الحفظ */
.save-button {
    -fx-background-color: linear-gradient(to bottom, #28a745, #1e7e34);
    -fx-text-fill: white;
    -fx-padding: 10 20 10 20;
    -fx-border-radius: 5;
    -fx-background-radius: 5;
    -fx-cursor: hand;
    -fx-font-size: 14px;
    -fx-font-weight: bold;
}

.save-button:hover {
    -fx-background-color: linear-gradient(to bottom, #1e7e34, #155724);
}

/* تحسين DatePicker */
.date-picker {
    -fx-font-size: 12px;
    -fx-padding: 6;
    -fx-border-color: #ced4da;
    -fx-border-width: 1;
    -fx-border-radius: 4;
    -fx-background-radius: 4;
}

.date-picker:focused {
    -fx-border-color: #007bff;
    -fx-border-width: 2;
}

/* تحسين ComboBox للفلاتر */
.combo-box {
    -fx-font-size: 12px;
    -fx-padding: 6;
    -fx-border-color: #ced4da;
    -fx-border-width: 1;
    -fx-border-radius: 4;
    -fx-background-radius: 4;
}

.combo-box:focused {
    -fx-border-color: #007bff;
    -fx-border-width: 2;
}

.combo-box .list-view {
    -fx-background-color: white;
    -fx-border-color: #dee2e6;
    -fx-border-width: 1;
    -fx-border-radius: 4;
    -fx-background-radius: 4;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.15), 8, 0, 0, 2);
}

.combo-box .list-cell {
    -fx-padding: 8 12 8 12;
    -fx-font-size: 12px;
}

.combo-box .list-cell:hover {
    -fx-background-color: #e9ecef;
}

.combo-box .list-cell:selected {
    -fx-background-color: #007bff;
    -fx-text-fill: white;
}

/* تحسين TextField للفلاتر */
.text-field {
    -fx-font-size: 12px;
    -fx-padding: 8;
    -fx-border-color: #ced4da;
    -fx-border-width: 1;
    -fx-border-radius: 4;
    -fx-background-radius: 4;
}

.text-field:focused {
    -fx-border-color: #007bff;
    -fx-border-width: 2;
    -fx-effect: dropshadow(gaussian, rgba(0,123,255,0.25), 4, 0, 0, 0);
}

/* تحسين TextArea */
.text-area {
    -fx-font-size: 12px;
    -fx-padding: 8;
    -fx-border-color: #ced4da;
    -fx-border-width: 1;
    -fx-border-radius: 4;
    -fx-background-radius: 4;
}

.text-area:focused {
    -fx-border-color: #007bff;
    -fx-border-width: 2;
}

.text-area .content {
    -fx-background-color: white;
    -fx-background-radius: 4;
}

/* تحسين الجداول */
.table-view {
    -fx-border-color: #dee2e6;
    -fx-border-width: 1;
    -fx-border-radius: 5;
    -fx-background-radius: 5;
}

.table-view .column-header {
    -fx-background-color: #f8f9fa;
    -fx-border-color: #dee2e6;
    -fx-border-width: 0 1 1 0;
    -fx-padding: 10;
    -fx-font-weight: bold;
    -fx-text-fill: #495057;
}

.table-view .table-cell {
    -fx-padding: 8;
    -fx-border-color: #dee2e6;
    -fx-border-width: 0 1 1 0;
}

.table-view .table-row-cell:selected {
    -fx-background-color: #e3f2fd;
    -fx-text-fill: #1976d2;
}

.table-view .table-row-cell:hover {
    -fx-background-color: #f5f5f5;
}

/* تنسيق الموديول المحاسبي */
.module-title {
    -fx-font-size: 24px;
    -fx-font-weight: bold;
    -fx-text-fill: #2c3e50;
    -fx-padding: 0 0 10 0;
}

.module-description {
    -fx-font-size: 14px;
    -fx-text-fill: #7f8c8d;
    -fx-padding: 0 0 20 0;
}

.module-button {
    -fx-background-color: white;
    -fx-border-color: #bdc3c7;
    -fx-border-width: 1;
    -fx-border-radius: 8;
    -fx-background-radius: 8;
    -fx-cursor: hand;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 5, 0, 0, 2);
}

.module-button:hover {
    -fx-background-color: #ecf0f1;
    -fx-border-color: #3498db;
    -fx-effect: dropshadow(gaussian, rgba(52,152,219,0.3), 8, 0, 0, 3);
}

.module-button-title {
    -fx-font-size: 14px;
    -fx-font-weight: bold;
    -fx-text-fill: #2c3e50;
    -fx-text-alignment: center;
}

.module-button-description {
    -fx-font-size: 11px;
    -fx-text-fill: #7f8c8d;
    -fx-text-alignment: center;
}

.module-icon {
    -fx-fill: #3498db;
}

/* تنسيق بطاقات الإحصائيات */
.stats-container {
    -fx-background-color: #f8f9fa;
    -fx-border-color: #dee2e6;
    -fx-border-width: 1;
    -fx-border-radius: 8;
    -fx-background-radius: 8;
}

.stat-card {
    -fx-background-color: white;
    -fx-border-color: #e9ecef;
    -fx-border-width: 1;
    -fx-border-radius: 6;
    -fx-background-radius: 6;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.05), 3, 0, 0, 1);
}

.stat-card:hover {
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.15), 6, 0, 0, 2);
}

.stat-value {
    -fx-font-size: 18px;
    -fx-font-weight: bold;
}

.stat-title {
    -fx-font-size: 12px;
    -fx-text-fill: #6c757d;
}

/* تنسيق منطقة الوصول السريع */
.quick-access-container {
    -fx-background-color: #f8f9fa;
    -fx-border-color: #dee2e6;
    -fx-border-width: 1;
    -fx-border-radius: 8;
    -fx-background-radius: 8;
}

.quick-access-button {
    -fx-background-color: linear-gradient(to bottom, #3498db, #2980b9);
    -fx-text-fill: white;
    -fx-padding: 8 15 8 15;
    -fx-border-radius: 4;
    -fx-background-radius: 4;
    -fx-cursor: hand;
    -fx-font-size: 12px;
}

.quick-access-button:hover {
    -fx-background-color: linear-gradient(to bottom, #2980b9, #21618c);
}

/* تنسيق جداول الحسابات */
.accounts-table {
    -fx-border-color: #dee2e6;
    -fx-border-width: 1;
    -fx-border-radius: 5;
    -fx-background-radius: 5;
}

.accounts-scroll-pane {
    -fx-background-color: transparent;
    -fx-border-color: transparent;
}

/* تنسيق جداول القيود */
.entries-table {
    -fx-border-color: #dee2e6;
    -fx-border-width: 1;
    -fx-border-radius: 5;
    -fx-background-radius: 5;
}

.entries-scroll-pane {
    -fx-background-color: transparent;
    -fx-border-color: transparent;
}

/* تنسيق شريط الأدوات */
.toolbar {
    -fx-background-color: #f8f9fa;
    -fx-border-color: #dee2e6;
    -fx-border-width: 0 0 1 0;
}

/* تنسيق أزرار الإجراءات المحاسبية */
.approve-button {
    -fx-background-color: linear-gradient(to bottom, #28a745, #1e7e34);
    -fx-text-fill: white;
    -fx-padding: 6 12 6 12;
    -fx-border-radius: 3;
    -fx-background-radius: 3;
    -fx-cursor: hand;
    -fx-font-size: 11px;
}

.approve-button:hover {
    -fx-background-color: linear-gradient(to bottom, #1e7e34, #155724);
}

.post-button {
    -fx-background-color: linear-gradient(to bottom, #17a2b8, #117a8b);
    -fx-text-fill: white;
    -fx-padding: 6 12 6 12;
    -fx-border-radius: 3;
    -fx-background-radius: 3;
    -fx-cursor: hand;
    -fx-font-size: 11px;
}

.post-button:hover {
    -fx-background-color: linear-gradient(to bottom, #117a8b, #0c5460);
}

.unpost-button {
    -fx-background-color: linear-gradient(to bottom, #fd7e14, #e55a00);
    -fx-text-fill: white;
    -fx-padding: 6 12 6 12;
    -fx-border-radius: 3;
    -fx-background-radius: 3;
    -fx-cursor: hand;
    -fx-font-size: 11px;
}

.unpost-button:hover {
    -fx-background-color: linear-gradient(to bottom, #e55a00, #cc5200);
}

.view-button {
    -fx-background-color: linear-gradient(to bottom, #6f42c1, #5a2d91);
    -fx-text-fill: white;
    -fx-padding: 6 12 6 12;
    -fx-border-radius: 3;
    -fx-background-radius: 3;
    -fx-cursor: hand;
    -fx-font-size: 11px;
}

.view-button:hover {
    -fx-background-color: linear-gradient(to bottom, #5a2d91, #4c2a85);
}

.edit-button {
    -fx-background-color: linear-gradient(to bottom, #ffc107, #e0a800);
    -fx-text-fill: #212529;
    -fx-padding: 6 12 6 12;
    -fx-border-radius: 3;
    -fx-background-radius: 3;
    -fx-cursor: hand;
    -fx-font-size: 11px;
}

.edit-button:hover {
    -fx-background-color: linear-gradient(to bottom, #e0a800, #d39e00);
}

.generate-button {
    -fx-background-color: linear-gradient(to bottom, #6610f2, #520dc2);
    -fx-text-fill: white;
    -fx-padding: 6 12 6 12;
    -fx-border-radius: 3;
    -fx-background-radius: 3;
    -fx-cursor: hand;
    -fx-font-size: 11px;
}

.generate-button:hover {
    -fx-background-color: linear-gradient(to bottom, #520dc2, #4709ac);
}

/* تنسيق حاويات الفلاتر */
.filters-container {
    -fx-background-color: #f8f9fa;
    -fx-border-color: #dee2e6;
    -fx-border-width: 1;
    -fx-border-radius: 5;
    -fx-background-radius: 5;
}

/* تنسيق عناوين الأقسام */
.section-title {
    -fx-font-size: 16px;
    -fx-font-weight: bold;
    -fx-text-fill: #495057;
}

.dialog-description {
    -fx-font-size: 13px;
    -fx-text-fill: #6c757d;
    -fx-wrap-text: true;
}

/* تنسيق موديول التصنيع */
.orders-table {
    -fx-border-color: #dee2e6;
    -fx-border-width: 1;
    -fx-border-radius: 5;
    -fx-background-radius: 5;
}

.orders-scroll-pane {
    -fx-background-color: transparent;
    -fx-border-color: transparent;
}

.reports-table {
    -fx-border-color: #dee2e6;
    -fx-border-width: 1;
    -fx-border-radius: 5;
    -fx-background-radius: 5;
}

.reports-scroll-pane {
    -fx-background-color: transparent;
    -fx-border-color: transparent;
}

.filters-title {
    -fx-font-size: 14px;
    -fx-font-weight: bold;
    -fx-text-fill: #495057;
}

.placeholder-text {
    -fx-font-size: 14px;
    -fx-text-fill: #6c757d;
    -fx-font-style: italic;
}

/* تنسيق أزرار الفلاتر */
.filter-button {
    -fx-background-color: linear-gradient(to bottom, #007bff, #0056b3);
    -fx-text-fill: white;
    -fx-padding: 6 12 6 12;
    -fx-border-radius: 3;
    -fx-background-radius: 3;
    -fx-cursor: hand;
    -fx-font-size: 11px;
}

.filter-button:hover {
    -fx-background-color: linear-gradient(to bottom, #0056b3, #004085);
}

.reset-button {
    -fx-background-color: linear-gradient(to bottom, #6c757d, #545b62);
    -fx-text-fill: white;
    -fx-padding: 6 12 6 12;
    -fx-border-radius: 3;
    -fx-background-radius: 3;
    -fx-cursor: hand;
    -fx-font-size: 11px;
}

.reset-button:hover {
    -fx-background-color: linear-gradient(to bottom, #545b62, #3d4449);
}

/* تحسين ScrollPane للنوافذ المختلفة */
.services-scroll-pane,
.customers-scroll-pane,
.company-info-scroll-pane,
.charts-scroll-pane,
.print-scroll-pane {
    -fx-background-color: transparent;
    -fx-border-color: transparent;
}

.services-scroll-pane .viewport,
.customers-scroll-pane .viewport,
.company-info-scroll-pane .viewport,
.charts-scroll-pane .viewport,
.print-scroll-pane .viewport {
    -fx-background-color: transparent;
}

.services-scroll-pane .scroll-bar:vertical,
.customers-scroll-pane .scroll-bar:vertical,
.company-info-scroll-pane .scroll-bar:vertical,
.charts-scroll-pane .scroll-bar:vertical,
.print-scroll-pane .scroll-bar:vertical {
    -fx-background-color: #f8f9fa;
    -fx-border-color: #dee2e6;
    -fx-border-width: 1;
    -fx-border-radius: 5;
    -fx-background-radius: 5;
}

.services-scroll-pane .scroll-bar:vertical .thumb,
.customers-scroll-pane .scroll-bar:vertical .thumb,
.company-info-scroll-pane .scroll-bar:vertical .thumb,
.charts-scroll-pane .scroll-bar:vertical .thumb,
.print-scroll-pane .scroll-bar:vertical .thumb {
    -fx-background-color: #6c757d;
    -fx-background-radius: 5;
    -fx-border-radius: 5;
}

.services-scroll-pane .scroll-bar:vertical .thumb:hover,
.customers-scroll-pane .scroll-bar:vertical .thumb:hover,
.company-info-scroll-pane .scroll-bar:vertical .thumb:hover,
.charts-scroll-pane .scroll-bar:vertical .thumb:hover,
.print-scroll-pane .scroll-bar:vertical .thumb:hover {
    -fx-background-color: #495057;
}

/* تحسين الرسوم البيانية */
.charts-scroll-pane {
    -fx-background-color: #f8f9fa;
}

.chart-container {
    -fx-background-color: white;
    -fx-border-color: #dee2e6;
    -fx-border-width: 1;
    -fx-border-radius: 8;
    -fx-background-radius: 8;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 5, 0, 0, 2);
}

.chart-title {
    -fx-font-size: 16px;
    -fx-font-weight: bold;
    -fx-text-fill: #2c3e50;
}

/* تحسين أزرار التقارير */
.export-button {
    -fx-background-color: linear-gradient(to bottom, #28a745, #1e7e34);
    -fx-text-fill: white;
    -fx-padding: 8 15 8 15;
    -fx-border-radius: 4;
    -fx-background-radius: 4;
    -fx-cursor: hand;
    -fx-font-size: 12px;
    -fx-font-weight: bold;
}

.export-button:hover {
    -fx-background-color: linear-gradient(to bottom, #1e7e34, #155724);
}

.print-button {
    -fx-background-color: linear-gradient(to bottom, #17a2b8, #117a8b);
    -fx-text-fill: white;
    -fx-padding: 8 15 8 15;
    -fx-border-radius: 4;
    -fx-background-radius: 4;
    -fx-cursor: hand;
    -fx-font-size: 12px;
    -fx-font-weight: bold;
}

.print-button:hover {
    -fx-background-color: linear-gradient(to bottom, #117a8b, #0c5460);
}

/* تحسين نوافذ المعاينة */
.print-scroll-pane {
    -fx-background-color: #f5f5f5;
    -fx-border-color: #dee2e6;
    -fx-border-width: 1;
    -fx-border-radius: 5;
}

/* تحسين الجداول في النوافذ */
.services-scroll-pane .table-view,
.customers-scroll-pane .table-view {
    -fx-border-color: #dee2e6;
    -fx-border-width: 1;
    -fx-border-radius: 5;
    -fx-background-radius: 5;
}

/* تحسين النماذج */
.add-service-form {
    -fx-background-color: #f8f9fa;
    -fx-border-color: #dee2e6;
    -fx-border-width: 1;
    -fx-border-radius: 5;
    -fx-background-radius: 5;
    -fx-padding: 15;
}

/* تحسين أزرار الحذف */
.delete-button {
    -fx-background-color: linear-gradient(to bottom, #dc3545, #c82333);
    -fx-text-fill: white;
    -fx-padding: 5 8 5 8;
    -fx-border-radius: 3;
    -fx-background-radius: 3;
    -fx-cursor: hand;
    -fx-font-size: 10px;
}

.delete-button:hover {
    -fx-background-color: linear-gradient(to bottom, #c82333, #a71e2a);
}

/* تحسين أزرار الإضافة */
.add-button {
    -fx-background-color: linear-gradient(to bottom, #007bff, #0056b3);
    -fx-text-fill: white;
    -fx-padding: 8 15 8 15;
    -fx-border-radius: 4;
    -fx-background-radius: 4;
    -fx-cursor: hand;
    -fx-font-size: 12px;
    -fx-font-weight: bold;
}

.add-button:hover {
    -fx-background-color: linear-gradient(to bottom, #0056b3, #004085);
}

/* ===== تحسين المظهر العام ===== */
.button {
    -fx-cursor: hand;
}

.label {
    -fx-text-fill: #2c3e50;
}

/* ===== تأثيرات الظلال ===== */
.elevated {
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 5, 0, 0, 2);
}

.elevated-high {
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.2), 10, 0, 0, 4);
}
