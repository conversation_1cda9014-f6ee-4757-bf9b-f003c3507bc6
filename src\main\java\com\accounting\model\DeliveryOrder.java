package com.accounting.model;

import javafx.beans.property.*;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * نموذج أمر التسليم
 */
public class DeliveryOrder {
    
    // المعلومات الأساسية
    private final StringProperty deliveryOrderId = new SimpleStringProperty();
    private final StringProperty deliveryOrderNumber = new SimpleStringProperty();
    private final StringProperty customerName = new SimpleStringProperty();
    private final ObjectProperty<LocalDate> deliveryDate = new SimpleObjectProperty<>();
    private final StringProperty manufacturingOrderNumber = new SimpleStringProperty();
    private final StringProperty invoiceNumber = new SimpleStringProperty();
    private final StringProperty notes = new SimpleStringProperty();
    private final ObjectProperty<LocalDateTime> createdDate = new SimpleObjectProperty<>();
    private final ObjectProperty<DeliveryStatus> status = new SimpleObjectProperty<>(DeliveryStatus.DRAFT);
    
    // عناصر التسليم
    private final ObservableList<DeliveryItem> deliveryItems = FXCollections.observableArrayList();
    
    /**
     * حالات أمر التسليم
     */
    public enum DeliveryStatus {
        DRAFT("مسودة"),
        CONFIRMED("مؤكد"),
        DELIVERED("تم التسليم"),
        CANCELLED("ملغي");
        
        private final String displayName;
        
        DeliveryStatus(String displayName) {
            this.displayName = displayName;
        }
        
        public String getDisplayName() {
            return displayName;
        }
        
        @Override
        public String toString() {
            return displayName;
        }
    }
    
    /**
     * المنشئ الافتراضي
     */
    public DeliveryOrder() {
        this.deliveryOrderId.set(generateDeliveryOrderId());
        this.deliveryOrderNumber.set(generateDeliveryOrderNumber());
        this.deliveryDate.set(LocalDate.now());
        this.createdDate.set(LocalDateTime.now());
    }
    
    /**
     * منشئ مع معلومات أساسية
     */
    public DeliveryOrder(String customerName, String manufacturingOrderNumber, String invoiceNumber) {
        this();
        this.customerName.set(customerName);
        this.manufacturingOrderNumber.set(manufacturingOrderNumber);
        this.invoiceNumber.set(invoiceNumber);
    }
    
    /**
     * توليد معرف أمر التسليم
     */
    private String generateDeliveryOrderId() {
        return "DO" + System.currentTimeMillis();
    }
    
    /**
     * توليد رقم أمر التسليم
     */
    private String generateDeliveryOrderNumber() {
        // يمكن تحسين هذا لاحقاً ليكون متسلسل
        return "DEL" + String.format("%06d", (int)(Math.random() * 999999) + 1);
    }
    
    // Getters and Setters
    
    public String getDeliveryOrderId() {
        return deliveryOrderId.get();
    }
    
    public StringProperty deliveryOrderIdProperty() {
        return deliveryOrderId;
    }
    
    public void setDeliveryOrderId(String deliveryOrderId) {
        this.deliveryOrderId.set(deliveryOrderId);
    }
    
    public String getDeliveryOrderNumber() {
        return deliveryOrderNumber.get();
    }
    
    public StringProperty deliveryOrderNumberProperty() {
        return deliveryOrderNumber;
    }
    
    public void setDeliveryOrderNumber(String deliveryOrderNumber) {
        this.deliveryOrderNumber.set(deliveryOrderNumber);
    }
    
    public String getCustomerName() {
        return customerName.get();
    }
    
    public StringProperty customerNameProperty() {
        return customerName;
    }
    
    public void setCustomerName(String customerName) {
        this.customerName.set(customerName);
    }
    
    public LocalDate getDeliveryDate() {
        return deliveryDate.get();
    }
    
    public ObjectProperty<LocalDate> deliveryDateProperty() {
        return deliveryDate;
    }
    
    public void setDeliveryDate(LocalDate deliveryDate) {
        this.deliveryDate.set(deliveryDate);
    }
    
    public String getManufacturingOrderNumber() {
        return manufacturingOrderNumber.get();
    }
    
    public StringProperty manufacturingOrderNumberProperty() {
        return manufacturingOrderNumber;
    }
    
    public void setManufacturingOrderNumber(String manufacturingOrderNumber) {
        this.manufacturingOrderNumber.set(manufacturingOrderNumber);
    }
    
    public String getInvoiceNumber() {
        return invoiceNumber.get();
    }
    
    public StringProperty invoiceNumberProperty() {
        return invoiceNumber;
    }
    
    public void setInvoiceNumber(String invoiceNumber) {
        this.invoiceNumber.set(invoiceNumber);
    }
    
    public String getNotes() {
        return notes.get();
    }
    
    public StringProperty notesProperty() {
        return notes;
    }
    
    public void setNotes(String notes) {
        this.notes.set(notes);
    }
    
    public LocalDateTime getCreatedDate() {
        return createdDate.get();
    }
    
    public ObjectProperty<LocalDateTime> createdDateProperty() {
        return createdDate;
    }
    
    public void setCreatedDate(LocalDateTime createdDate) {
        this.createdDate.set(createdDate);
    }
    
    public DeliveryStatus getStatus() {
        return status.get();
    }
    
    public ObjectProperty<DeliveryStatus> statusProperty() {
        return status;
    }
    
    public void setStatus(DeliveryStatus status) {
        this.status.set(status);
    }
    
    public ObservableList<DeliveryItem> getDeliveryItems() {
        return deliveryItems;
    }
    
    /**
     * إضافة عنصر تسليم
     */
    public void addDeliveryItem(DeliveryItem item) {
        deliveryItems.add(item);
    }
    
    /**
     * حذف عنصر تسليم
     */
    public void removeDeliveryItem(DeliveryItem item) {
        deliveryItems.remove(item);
    }
    
    /**
     * مسح جميع عناصر التسليم
     */
    public void clearDeliveryItems() {
        deliveryItems.clear();
    }
    
    /**
     * الحصول على إجمالي العدد
     */
    public int getTotalCount() {
        return deliveryItems.stream()
                .mapToInt(DeliveryItem::getCount)
                .sum();
    }
    
    /**
     * التحقق من صحة البيانات
     */
    public boolean isValid() {
        return customerName.get() != null && !customerName.get().trim().isEmpty() &&
               deliveryDate.get() != null &&
               !deliveryItems.isEmpty();
    }
    
    /**
     * نسخ البيانات من أمر تصنيع
     */
    public void copyFromManufacturingOrder(ManufacturingOrder manufacturingOrder) {
        setCustomerName(manufacturingOrder.getCustomerName());
        setManufacturingOrderNumber(manufacturingOrder.getOrderNumber());
        setInvoiceNumber(manufacturingOrder.getInvoiceNumber());
        
        // نسخ المقاسات المطلوبة كعناصر تسليم
        for (RequiredMeasurement measurement : manufacturingOrder.getRequiredMeasurements()) {
            DeliveryItem deliveryItem = new DeliveryItem();
            deliveryItem.setDescription(measurement.getDescription());
            deliveryItem.setLength(measurement.getLength());
            deliveryItem.setWidth(measurement.getWidth());
            deliveryItem.setCount(measurement.getCount());
            deliveryItem.setSourceMeasurementId(measurement.getDescription()); // استخدام الوصف كمعرف مؤقت
            addDeliveryItem(deliveryItem);
        }
        
        // نسخ المقاسات مع الزيادة إذا كانت متوفرة
        if (manufacturingOrder.isHasIncrease() && !manufacturingOrder.getIncreasedMeasurements().isEmpty()) {
            for (RequiredMeasurement measurement : manufacturingOrder.getIncreasedMeasurements()) {
                DeliveryItem deliveryItem = new DeliveryItem();
                deliveryItem.setDescription(measurement.getDescription() + " (مع زيادة)");
                deliveryItem.setLength(measurement.getLength());
                deliveryItem.setWidth(measurement.getWidth());
                deliveryItem.setCount(measurement.getCount());
                deliveryItem.setSourceMeasurementId(measurement.getDescription() + "_increased");
                addDeliveryItem(deliveryItem);
            }
        }
    }
    
    @Override
    public String toString() {
        return "DeliveryOrder{" +
                "deliveryOrderNumber=" + deliveryOrderNumber.get() +
                ", customerName=" + customerName.get() +
                ", deliveryDate=" + deliveryDate.get() +
                ", status=" + status.get() +
                '}';
    }
}
