package com.accounting.model;

import javafx.beans.property.*;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.UUID;

/**
 * نموذج قيد دفتر الأستاذ
 */
public class LedgerEntry {
    
    // الخصائص الأساسية
    private final StringProperty ledgerEntryId = new SimpleStringProperty();
    private final StringProperty accountCode = new SimpleStringProperty();
    private final StringProperty accountName = new SimpleStringProperty();
    private final ObjectProperty<LocalDate> transactionDate = new SimpleObjectProperty<>();
    
    // ربط مع القيد الأصلي
    private final StringProperty journalEntryId = new SimpleStringProperty();
    private final StringProperty journalEntryNumber = new SimpleStringProperty();
    private final StringProperty journalEntryDetailId = new SimpleStringProperty();
    
    // المبالغ والأرصدة
    private final DoubleProperty debitAmount = new SimpleDoubleProperty(0.0);
    private final DoubleProperty creditAmount = new SimpleDoubleProperty(0.0);
    private final DoubleProperty runningBalance = new SimpleDoubleProperty(0.0);
    
    // الوصف والمرجع
    private final StringProperty description = new SimpleStringProperty();
    private final StringProperty reference = new SimpleStringProperty();
    private final StringProperty costCenter = new SimpleStringProperty();
    
    // معلومات إضافية
    private final StringProperty currency = new SimpleStringProperty("EGP");
    private final DoubleProperty exchangeRate = new SimpleDoubleProperty(1.0);
    private final DoubleProperty amountInBaseCurrency = new SimpleDoubleProperty(0.0);
    
    // نوع الحركة
    private final ObjectProperty<MovementType> movementType = new SimpleObjectProperty<>();
    private final ObjectProperty<JournalEntry.EntryType> entryType = new SimpleObjectProperty<>();
    
    // معلومات التدقيق
    private final ObjectProperty<LocalDateTime> createdDate = new SimpleObjectProperty<>(LocalDateTime.now());
    private final StringProperty createdBy = new SimpleStringProperty();
    
    // المراجع
    private Account account;
    private JournalEntry journalEntry;
    private JournalEntryDetail journalEntryDetail;
    
    /**
     * نوع الحركة في دفتر الأستاذ
     */
    public enum MovementType {
        DEBIT("مدين", "Debit"),
        CREDIT("دائن", "Credit");
        
        private final String arabicName;
        private final String englishName;
        
        MovementType(String arabicName, String englishName) {
            this.arabicName = arabicName;
            this.englishName = englishName;
        }
        
        public String getArabicName() { return arabicName; }
        public String getEnglishName() { return englishName; }
        
        @Override
        public String toString() { return arabicName; }
    }
    
    // Constructors
    public LedgerEntry() {
        setLedgerEntryId(UUID.randomUUID().toString());
        setTransactionDate(LocalDate.now());
    }
    
    public LedgerEntry(Account account, JournalEntryDetail journalEntryDetail) {
        this();
        setAccount(account);
        setJournalEntryDetail(journalEntryDetail);
        
        // نسخ البيانات من تفاصيل القيد
        setAccountCode(account.getAccountCode());
        setAccountName(account.getAccountName());
        setDebitAmount(journalEntryDetail.getDebitAmount());
        setCreditAmount(journalEntryDetail.getCreditAmount());
        setDescription(journalEntryDetail.getDescription());
        setReference(journalEntryDetail.getReference());
        setCostCenter(journalEntryDetail.getCostCenter());
        setCurrency(journalEntryDetail.getCurrency());
        setExchangeRate(journalEntryDetail.getExchangeRate());
        setAmountInBaseCurrency(journalEntryDetail.getAmountInBaseCurrency());
        
        // تحديد نوع الحركة
        if (journalEntryDetail.getDebitAmount() > 0) {
            setMovementType(MovementType.DEBIT);
        } else {
            setMovementType(MovementType.CREDIT);
        }
        
        // ربط مع القيد الأصلي
        if (journalEntryDetail.getJournalEntry() != null) {
            setJournalEntry(journalEntryDetail.getJournalEntry());
            setJournalEntryId(journalEntryDetail.getJournalEntry().getEntryId());
            setJournalEntryNumber(journalEntryDetail.getJournalEntry().getEntryNumber());
            setTransactionDate(journalEntryDetail.getJournalEntry().getEntryDate());
            setEntryType(journalEntryDetail.getJournalEntry().getEntryType());
        }
        
        setJournalEntryDetailId(journalEntryDetail.getDetailId());
    }
    
    // Property getters
    public StringProperty ledgerEntryIdProperty() { return ledgerEntryId; }
    public StringProperty accountCodeProperty() { return accountCode; }
    public StringProperty accountNameProperty() { return accountName; }
    public ObjectProperty<LocalDate> transactionDateProperty() { return transactionDate; }
    public StringProperty journalEntryIdProperty() { return journalEntryId; }
    public StringProperty journalEntryNumberProperty() { return journalEntryNumber; }
    public StringProperty journalEntryDetailIdProperty() { return journalEntryDetailId; }
    public DoubleProperty debitAmountProperty() { return debitAmount; }
    public DoubleProperty creditAmountProperty() { return creditAmount; }
    public DoubleProperty runningBalanceProperty() { return runningBalance; }
    public StringProperty descriptionProperty() { return description; }
    public StringProperty referenceProperty() { return reference; }
    public StringProperty costCenterProperty() { return costCenter; }
    public StringProperty currencyProperty() { return currency; }
    public DoubleProperty exchangeRateProperty() { return exchangeRate; }
    public DoubleProperty amountInBaseCurrencyProperty() { return amountInBaseCurrency; }
    public ObjectProperty<MovementType> movementTypeProperty() { return movementType; }
    public ObjectProperty<JournalEntry.EntryType> entryTypeProperty() { return entryType; }
    public ObjectProperty<LocalDateTime> createdDateProperty() { return createdDate; }
    public StringProperty createdByProperty() { return createdBy; }
    
    // Value getters and setters
    public String getLedgerEntryId() { return ledgerEntryId.get(); }
    public void setLedgerEntryId(String ledgerEntryId) { this.ledgerEntryId.set(ledgerEntryId); }
    
    public String getAccountCode() { return accountCode.get(); }
    public void setAccountCode(String accountCode) { this.accountCode.set(accountCode); }
    
    public String getAccountName() { return accountName.get(); }
    public void setAccountName(String accountName) { this.accountName.set(accountName); }
    
    public LocalDate getTransactionDate() { return transactionDate.get(); }
    public void setTransactionDate(LocalDate transactionDate) { this.transactionDate.set(transactionDate); }
    
    public String getJournalEntryId() { return journalEntryId.get(); }
    public void setJournalEntryId(String journalEntryId) { this.journalEntryId.set(journalEntryId); }
    
    public String getJournalEntryNumber() { return journalEntryNumber.get(); }
    public void setJournalEntryNumber(String journalEntryNumber) { this.journalEntryNumber.set(journalEntryNumber); }
    
    public String getJournalEntryDetailId() { return journalEntryDetailId.get(); }
    public void setJournalEntryDetailId(String journalEntryDetailId) { this.journalEntryDetailId.set(journalEntryDetailId); }
    
    public double getDebitAmount() { return debitAmount.get(); }
    public void setDebitAmount(double debitAmount) { this.debitAmount.set(debitAmount); }
    
    public double getCreditAmount() { return creditAmount.get(); }
    public void setCreditAmount(double creditAmount) { this.creditAmount.set(creditAmount); }
    
    public double getRunningBalance() { return runningBalance.get(); }
    public void setRunningBalance(double runningBalance) { this.runningBalance.set(runningBalance); }
    
    public String getDescription() { return description.get(); }
    public void setDescription(String description) { this.description.set(description); }
    
    public String getReference() { return reference.get(); }
    public void setReference(String reference) { this.reference.set(reference); }
    
    public String getCostCenter() { return costCenter.get(); }
    public void setCostCenter(String costCenter) { this.costCenter.set(costCenter); }
    
    public String getCurrency() { return currency.get(); }
    public void setCurrency(String currency) { this.currency.set(currency); }
    
    public double getExchangeRate() { return exchangeRate.get(); }
    public void setExchangeRate(double exchangeRate) { this.exchangeRate.set(exchangeRate); }
    
    public double getAmountInBaseCurrency() { return amountInBaseCurrency.get(); }
    public void setAmountInBaseCurrency(double amountInBaseCurrency) { this.amountInBaseCurrency.set(amountInBaseCurrency); }
    
    public MovementType getMovementType() { return movementType.get(); }
    public void setMovementType(MovementType movementType) { this.movementType.set(movementType); }
    
    public JournalEntry.EntryType getEntryType() { return entryType.get(); }
    public void setEntryType(JournalEntry.EntryType entryType) { this.entryType.set(entryType); }
    
    public LocalDateTime getCreatedDate() { return createdDate.get(); }
    public void setCreatedDate(LocalDateTime createdDate) { this.createdDate.set(createdDate); }
    
    public String getCreatedBy() { return createdBy.get(); }
    public void setCreatedBy(String createdBy) { this.createdBy.set(createdBy); }
    
    // Object references
    public Account getAccount() { return account; }
    public void setAccount(Account account) { this.account = account; }
    
    public JournalEntry getJournalEntry() { return journalEntry; }
    public void setJournalEntry(JournalEntry journalEntry) { this.journalEntry = journalEntry; }
    
    public JournalEntryDetail getJournalEntryDetail() { return journalEntryDetail; }
    public void setJournalEntryDetail(JournalEntryDetail journalEntryDetail) { this.journalEntryDetail = journalEntryDetail; }
    
    /**
     * الحصول على المبلغ المطلق
     */
    public double getAbsoluteAmount() {
        return Math.max(getDebitAmount(), getCreditAmount());
    }
    
    /**
     * الحصول على المبلغ الصافي (مدين موجب، دائن سالب)
     */
    public double getNetAmount() {
        return getDebitAmount() - getCreditAmount();
    }
    
    /**
     * التحقق من كون الحركة مدينة
     */
    public boolean isDebit() {
        return getMovementType() == MovementType.DEBIT;
    }
    
    /**
     * التحقق من كون الحركة دائنة
     */
    public boolean isCredit() {
        return getMovementType() == MovementType.CREDIT;
    }
    
    /**
     * حساب الرصيد الجاري بناءً على طبيعة الحساب
     */
    public void calculateRunningBalance(double previousBalance, Account.AccountNature accountNature) {
        double newBalance = previousBalance;
        
        if (accountNature == Account.AccountNature.DEBIT) {
            // الحسابات المدينة: المدين يزيد الرصيد، الدائن ينقص الرصيد
            newBalance += getDebitAmount() - getCreditAmount();
        } else {
            // الحسابات الدائنة: الدائن يزيد الرصيد، المدين ينقص الرصيد
            newBalance += getCreditAmount() - getDebitAmount();
        }
        
        setRunningBalance(newBalance);
    }
    
    /**
     * تحويل إلى نص منسق
     */
    public String getFormattedAmount() {
        if (isDebit()) {
            return String.format("%.2f مدين", getDebitAmount());
        } else {
            return String.format("%.2f دائن", getCreditAmount());
        }
    }
    
    /**
     * الحصول على تاريخ منسق
     */
    public String getFormattedDate() {
        if (getTransactionDate() != null) {
            return getTransactionDate().format(java.time.format.DateTimeFormatter.ofPattern("dd/MM/yyyy"));
        }
        return "";
    }
    
    @Override
    public String toString() {
        return getAccountCode() + " - " + getFormattedDate() + " - " + getFormattedAmount();
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        LedgerEntry entry = (LedgerEntry) obj;
        return getLedgerEntryId() != null && getLedgerEntryId().equals(entry.getLedgerEntryId());
    }
    
    @Override
    public int hashCode() {
        return getLedgerEntryId() != null ? getLedgerEntryId().hashCode() : 0;
    }
}
