# إعدادات التطبيق - Application Settings
app.name=نظام الحسابات المتكامل
app.name.en=Integrated Accounting System
app.version=1.0.0
app.author=Accounting System Developer
app.description=نظام حسابات ديسك توب متكامل باستخدام JavaFX

# إعدادات قاعدة البيانات - Database Settings
db.type=sqlite
db.file=accounting.db
db.backup.enabled=true
db.backup.interval=24

# إعدادات الواجهة - UI Settings
ui.theme=modern
ui.language=ar
ui.font.family=Segoe UI
ui.font.size=14
ui.window.width=1200
ui.window.height=800
ui.window.maximized=true

# إعدادات الأمان - Security Settings
security.enabled=false
security.session.timeout=30
security.password.min.length=6

# إعدادات التقارير - Reports Settings
reports.format=pdf
reports.template.path=templates/
reports.output.path=reports/

# إعدادات النسخ الاحتياطي - Backup Settings
backup.auto.enabled=true
backup.path=backup/
backup.keep.days=30

# إعدادات التطوير - Development Settings
dev.mode=true
dev.show.debug=false
dev.auto.save=true
