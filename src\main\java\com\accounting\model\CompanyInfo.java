package com.accounting.model;

import javafx.beans.property.*;

/**
 * نموذج بيانات الشركة
 */
public class CompanyInfo {
    private final StringProperty companyName = new SimpleStringProperty();
    private final StringProperty activity = new SimpleStringProperty();
    private final StringProperty address = new SimpleStringProperty();
    private final StringProperty phone = new SimpleStringProperty();
    private final StringProperty website = new SimpleStringProperty();
    private final StringProperty email = new SimpleStringProperty();
    
    // مثيل واحد للشركة (Singleton)
    private static CompanyInfo instance;
    
    private CompanyInfo() {
        // تعيين قيم افتراضية
        setCompanyName("شركة الزجاج والألومنيوم المتقدمة");
        setActivity("تصنيع وتركيب الزجاج والألومنيوم");
        setAddress("شارع الصناعة، المنطقة الصناعية");
        setPhone("01234567890");
        setWebsite("www.glass-aluminum.com");
        setEmail("<EMAIL>");
    }
    
    public static CompanyInfo getInstance() {
        if (instance == null) {
            instance = new CompanyInfo();
        }
        return instance;
    }
    
    // Property getters
    public StringProperty companyNameProperty() { return companyName; }
    public StringProperty activityProperty() { return activity; }
    public StringProperty addressProperty() { return address; }
    public StringProperty phoneProperty() { return phone; }
    public StringProperty websiteProperty() { return website; }
    public StringProperty emailProperty() { return email; }
    
    // Value getters and setters
    public String getCompanyName() { return companyName.get(); }
    public void setCompanyName(String companyName) { this.companyName.set(companyName); }
    
    public String getActivity() { return activity.get(); }
    public void setActivity(String activity) { this.activity.set(activity); }
    
    public String getAddress() { return address.get(); }
    public void setAddress(String address) { this.address.set(address); }
    
    public String getPhone() { return phone.get(); }
    public void setPhone(String phone) { this.phone.set(phone); }
    
    public String getWebsite() { return website.get(); }
    public void setWebsite(String website) { this.website.set(website); }
    
    public String getEmail() { return email.get(); }
    public void setEmail(String email) { this.email.set(email); }
    
    /**
     * تحديث جميع بيانات الشركة
     */
    public void updateCompanyInfo(String name, String activity, String address, 
                                String phone, String website, String email) {
        setCompanyName(name);
        setActivity(activity);
        setAddress(address);
        setPhone(phone);
        setWebsite(website);
        setEmail(email);
    }
    
    /**
     * الحصول على معلومات الشركة كنص منسق
     */
    public String getFormattedInfo() {
        StringBuilder info = new StringBuilder();
        info.append(getCompanyName()).append("\n");
        info.append(getActivity()).append("\n");
        info.append("العنوان: ").append(getAddress()).append("\n");
        info.append("الهاتف: ").append(getPhone()).append("\n");
        if (!getWebsite().trim().isEmpty()) {
            info.append("الموقع: ").append(getWebsite()).append("\n");
        }
        if (!getEmail().trim().isEmpty()) {
            info.append("البريد: ").append(getEmail());
        }
        return info.toString();
    }
    
    /**
     * الحصول على معلومات الشركة للطباعة
     */
    public String getFormattedInfoForPrint() {
        StringBuilder info = new StringBuilder();
        info.append(getCompanyName()).append(" | ");
        info.append("العنوان: ").append(getAddress()).append(" | ");
        info.append("الهاتف: ").append(getPhone());
        if (!getEmail().trim().isEmpty()) {
            info.append(" | البريد: ").append(getEmail());
        }
        return info.toString();
    }
    
    /**
     * التحقق من اكتمال البيانات الأساسية
     */
    public boolean isBasicInfoComplete() {
        return !getCompanyName().trim().isEmpty() && 
               !getAddress().trim().isEmpty() && 
               !getPhone().trim().isEmpty();
    }
    
    /**
     * حفظ البيانات (محاكاة)
     */
    public void saveToFile() {
        // هنا يمكن إضافة كود حفظ البيانات في ملف
        System.out.println("تم حفظ بيانات الشركة:");
        System.out.println(getFormattedInfo());
    }
    
    /**
     * تحميل البيانات (محاكاة)
     */
    public void loadFromFile() {
        // هنا يمكن إضافة كود تحميل البيانات من ملف
        System.out.println("تم تحميل بيانات الشركة");
    }
}
