package com.accounting.test;

import com.accounting.controller.InventoryController;
import javafx.application.Application;
import javafx.scene.Scene;
import javafx.scene.layout.VBox;
import javafx.stage.Stage;

/**
 * اختبار واجهة موديول المخازن المحسنة
 */
public class EnhancedInventoryTest extends Application {

    @Override
    public void start(Stage primaryStage) {
        try {
            // إنشاء المتحكم المحسن
            InventoryController controller = new InventoryController();

            // إنشاء الواجهة المحسنة
            VBox root = controller.createInventoryModule();

            // إنشاء المشهد
            Scene scene = new Scene(root, 1400, 900);

            // تحميل ملف CSS المحسن
            try {
                scene.getStylesheets().add(getClass().getResource("/css/enhanced-inventory.css").toExternalForm());
                scene.getStylesheets().add(getClass().getResource("/css/styles.css").toExternalForm());
            } catch (Exception e) {
                System.out.println("تعذر تحميل ملف CSS: " + e.getMessage());
            }

            // إعداد النافذة
            primaryStage.setTitle("🏭 موديول المخازن المتقدم - نظام إدارة المصنع");
            primaryStage.setScene(scene);
            primaryStage.setMaximized(true);
            primaryStage.show();

            System.out.println("✅ تم تشغيل موديول المخازن المحسن بنجاح!");
            System.out.println("🔔 نظام الإشعارات مفعل ويعمل!");

        } catch (Exception e) {
            e.printStackTrace();
            System.err.println("❌ خطأ في تشغيل موديول المخازن: " + e.getMessage());
        }
    }

    public static void main(String[] args) {
        System.out.println("🚀 بدء تشغيل موديول المخازن المحسن...");
        launch(args);
    }
}
