package com.accounting.model;

import javafx.beans.property.*;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * نموذج أمر الاستلام
 */
public class ReceiptOrder {
    
    // الخصائص الأساسية
    private final StringProperty receiptOrderId = new SimpleStringProperty();
    private final StringProperty orderNumber = new SimpleStringProperty();
    private final StringProperty supplierName = new SimpleStringProperty();
    private final StringProperty manufacturingOrderNumber = new SimpleStringProperty();
    private final StringProperty invoiceNumber = new SimpleStringProperty();
    private final ObjectProperty<LocalDate> receiptDate = new SimpleObjectProperty<>();
    private final StringProperty notes = new SimpleStringProperty();
    private final ObjectProperty<ReceiptStatus> status = new SimpleObjectProperty<>();
    
    // عناصر الاستلام
    private final ObservableList<ReceiptItem> receiptItems = FXCollections.observableArrayList();
    
    // حالات أمر الاستلام
    public enum ReceiptStatus {
        DRAFT("مسودة"),
        CONFIRMED("مؤكد"),
        IN_RECEIPT("قيد الاستلام"),
        RECEIVED("مستلم"),
        CANCELLED("ملغي");
        
        private final String displayName;
        
        ReceiptStatus(String displayName) {
            this.displayName = displayName;
        }
        
        @Override
        public String toString() {
            return displayName;
        }
    }
    
    /**
     * المنشئ الافتراضي
     */
    public ReceiptOrder() {
        this.receiptOrderId.set(UUID.randomUUID().toString());
        this.receiptDate.set(LocalDate.now());
        this.status.set(ReceiptStatus.DRAFT);
    }
    
    /**
     * منشئ مع المعاملات
     */
    public ReceiptOrder(String orderNumber, String supplierName, LocalDate receiptDate) {
        this();
        this.orderNumber.set(orderNumber);
        this.supplierName.set(supplierName);
        this.receiptDate.set(receiptDate);
    }
    
    // Getters and Setters
    public String getReceiptOrderId() {
        return receiptOrderId.get();
    }
    
    public void setReceiptOrderId(String receiptOrderId) {
        this.receiptOrderId.set(receiptOrderId);
    }
    
    public StringProperty receiptOrderIdProperty() {
        return receiptOrderId;
    }
    
    public String getOrderNumber() {
        return orderNumber.get();
    }
    
    public void setOrderNumber(String orderNumber) {
        this.orderNumber.set(orderNumber);
    }
    
    public StringProperty orderNumberProperty() {
        return orderNumber;
    }
    
    public String getSupplierName() {
        return supplierName.get();
    }
    
    public void setSupplierName(String supplierName) {
        this.supplierName.set(supplierName);
    }
    
    public StringProperty supplierNameProperty() {
        return supplierName;
    }
    
    public String getManufacturingOrderNumber() {
        return manufacturingOrderNumber.get();
    }
    
    public void setManufacturingOrderNumber(String manufacturingOrderNumber) {
        this.manufacturingOrderNumber.set(manufacturingOrderNumber);
    }
    
    public StringProperty manufacturingOrderNumberProperty() {
        return manufacturingOrderNumber;
    }
    
    public String getInvoiceNumber() {
        return invoiceNumber.get();
    }
    
    public void setInvoiceNumber(String invoiceNumber) {
        this.invoiceNumber.set(invoiceNumber);
    }
    
    public StringProperty invoiceNumberProperty() {
        return invoiceNumber;
    }
    
    public LocalDate getReceiptDate() {
        return receiptDate.get();
    }
    
    public void setReceiptDate(LocalDate receiptDate) {
        this.receiptDate.set(receiptDate);
    }
    
    public ObjectProperty<LocalDate> receiptDateProperty() {
        return receiptDate;
    }
    
    public String getNotes() {
        return notes.get();
    }
    
    public void setNotes(String notes) {
        this.notes.set(notes);
    }
    
    public StringProperty notesProperty() {
        return notes;
    }
    
    public ReceiptStatus getStatus() {
        return status.get();
    }
    
    public void setStatus(ReceiptStatus status) {
        this.status.set(status);
    }
    
    public ObjectProperty<ReceiptStatus> statusProperty() {
        return status;
    }
    
    // إدارة عناصر الاستلام
    public ObservableList<ReceiptItem> getReceiptItems() {
        return receiptItems;
    }
    
    public void addReceiptItem(ReceiptItem item) {
        receiptItems.add(item);
    }
    
    public void removeReceiptItem(ReceiptItem item) {
        receiptItems.remove(item);
    }
    
    public void clearReceiptItems() {
        receiptItems.clear();
    }
    
    // حسابات الإجماليات
    public int getTotalItems() {
        return receiptItems.size();
    }
    
    public int getTotalRequiredQuantity() {
        return receiptItems.stream()
                .mapToInt(ReceiptItem::getRequiredQuantity)
                .sum();
    }
    
    public int getTotalReceivedQuantity() {
        return receiptItems.stream()
                .mapToInt(ReceiptItem::getReceivedQuantity)
                .sum();
    }
    
    public boolean isFullyReceived() {
        return receiptItems.stream()
                .allMatch(item -> item.getReceivedQuantity() >= item.getRequiredQuantity());
    }
    
    public boolean isPartiallyReceived() {
        return receiptItems.stream()
                .anyMatch(item -> item.getReceivedQuantity() > 0 && 
                                item.getReceivedQuantity() < item.getRequiredQuantity());
    }
    
    @Override
    public String toString() {
        return orderNumber.get() + " - " + supplierName.get();
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        ReceiptOrder that = (ReceiptOrder) obj;
        return receiptOrderId.get().equals(that.receiptOrderId.get());
    }
    
    @Override
    public int hashCode() {
        return receiptOrderId.get().hashCode();
    }
}
