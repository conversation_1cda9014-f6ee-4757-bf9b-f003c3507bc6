package com.accounting.controller;

import com.accounting.model.*;
import com.accounting.service.ReceiptOrderService;
import com.accounting.service.ManufacturingService;
import javafx.geometry.Insets;
import javafx.geometry.Pos;
import javafx.scene.control.*;
import javafx.scene.control.cell.PropertyValueFactory;
import javafx.scene.layout.*;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import de.jensd.fx.glyphs.fontawesome.FontAwesomeIcon;
import de.jensd.fx.glyphs.fontawesome.FontAwesomeIconView;
import java.time.LocalDate;
import java.util.function.Consumer;

/**
 * كنترولر أوامر الاستلام
 */
public class ReceiptOrderController {
    private ReceiptOrderService receiptOrderService;
    private ManufacturingService manufacturingService;
    private TableView<ReceiptOrder> receiptOrdersTable;
    private ObservableList<ReceiptOrder> receiptOrdersList;

    public ReceiptOrderController(ReceiptOrderService receiptOrderService) {
        this.receiptOrderService = receiptOrderService;
        this.manufacturingService = ManufacturingService.getInstance();
        this.receiptOrdersList = FXCollections.observableArrayList();
    }

    /**
     * إنشاء صفحة أوامر الاستلام للنافذة الواحدة
     */
    public VBox createReceiptOrdersPage(Consumer<ReceiptOrder> onEditOrder, 
                                       Consumer<ReceiptOrder> onPrintOrder) {
        VBox container = new VBox(15);
        container.setPadding(new Insets(20));
        
        // العنوان والوصف
        Label titleLabel = new Label("📥 إدارة أوامر الاستلام");
        titleLabel.getStyleClass().add("section-title");
        
        Label descLabel = new Label("إدارة أوامر استلام المواد والمنتجات");
        descLabel.getStyleClass().add("section-description");
        
        // شريط الأدوات
        HBox toolbar = createPageToolbar(onEditOrder);
        
        // الفلاتر
        VBox filtersBox = createFiltersBox();
        
        // الجدول
        receiptOrdersTable = createReceiptOrdersTableForPage(onEditOrder, onPrintOrder);
        
        // تحميل البيانات
        refreshReceiptOrdersTable();
        
        // وضع الجدول في ScrollPane
        ScrollPane scrollPane = new ScrollPane(receiptOrdersTable);
        scrollPane.setFitToWidth(true);
        scrollPane.setVbarPolicy(ScrollPane.ScrollBarPolicy.AS_NEEDED);
        scrollPane.setHbarPolicy(ScrollPane.ScrollBarPolicy.NEVER);
        scrollPane.setPrefHeight(400);
        VBox.setVgrow(scrollPane, Priority.ALWAYS);
        
        container.getChildren().addAll(titleLabel, descLabel, toolbar, filtersBox, scrollPane);
        return container;
    }

    /**
     * إنشاء شريط أدوات الصفحة
     */
    private HBox createPageToolbar(Consumer<ReceiptOrder> onEditOrder) {
        HBox toolbar = new HBox(15);
        toolbar.setAlignment(Pos.CENTER_LEFT);
        toolbar.setPadding(new Insets(10));
        toolbar.getStyleClass().add("toolbar");
        
        Button newOrderBtn = new Button("أمر استلام جديد");
        FontAwesomeIconView newIcon = new FontAwesomeIconView(FontAwesomeIcon.PLUS);
        newIcon.setSize("12px");
        newOrderBtn.setGraphic(newIcon);
        newOrderBtn.getStyleClass().add("add-button");
        newOrderBtn.setOnAction(e -> onEditOrder.accept(null));
        
        Button refreshBtn = new Button("تحديث");
        FontAwesomeIconView refreshIcon = new FontAwesomeIconView(FontAwesomeIcon.REFRESH);
        refreshIcon.setSize("12px");
        refreshBtn.setGraphic(refreshIcon);
        refreshBtn.getStyleClass().add("refresh-button");
        refreshBtn.setOnAction(e -> refreshReceiptOrdersTable());
        
        Button fromManufacturingBtn = new Button("من أمر تصنيع");
        FontAwesomeIconView manufacturingIcon = new FontAwesomeIconView(FontAwesomeIcon.INDUSTRY);
        manufacturingIcon.setSize("12px");
        fromManufacturingBtn.setGraphic(manufacturingIcon);
        fromManufacturingBtn.getStyleClass().add("manufacturing-button");
        fromManufacturingBtn.setOnAction(e -> createFromManufacturingOrder());
        
        toolbar.getChildren().addAll(newOrderBtn, fromManufacturingBtn, refreshBtn);
        return toolbar;
    }

    /**
     * إنشاء صندوق الفلاتر
     */
    private VBox createFiltersBox() {
        VBox filtersBox = new VBox(10);
        filtersBox.setPadding(new Insets(15));
        filtersBox.setStyle("-fx-background-color: #f8f9fa; -fx-border-color: #dee2e6; -fx-border-width: 1; -fx-border-radius: 8;");
        
        Label filtersLabel = new Label("🔍 الفلاتر");
        filtersLabel.getStyleClass().add("filters-title");
        
        HBox filtersRow = new HBox(15);
        filtersRow.setAlignment(Pos.CENTER_LEFT);
        
        // فلتر الحالة
        ComboBox<String> statusFilter = new ComboBox<>();
        statusFilter.getItems().addAll("جميع الحالات", "مسودة", "مؤكد", "قيد الاستلام", "مستلم", "ملغي");
        statusFilter.setValue("جميع الحالات");
        statusFilter.setPrefWidth(150);
        
        // فلتر التاريخ
        DatePicker fromDatePicker = new DatePicker();
        fromDatePicker.setPromptText("من تاريخ");
        fromDatePicker.setPrefWidth(130);
        
        DatePicker toDatePicker = new DatePicker();
        toDatePicker.setPromptText("إلى تاريخ");
        toDatePicker.setPrefWidth(130);
        
        // فلتر المورد
        TextField supplierFilter = new TextField();
        supplierFilter.setPromptText("اسم المورد");
        supplierFilter.setPrefWidth(150);
        
        Button applyFiltersBtn = new Button("تطبيق");
        FontAwesomeIconView filterIcon = new FontAwesomeIconView(FontAwesomeIcon.FILTER);
        filterIcon.setSize("12px");
        applyFiltersBtn.setGraphic(filterIcon);
        applyFiltersBtn.getStyleClass().add("filter-button");
        
        filtersRow.getChildren().addAll(
            new Label("الحالة:"), statusFilter,
            new Label("من:"), fromDatePicker,
            new Label("إلى:"), toDatePicker,
            new Label("المورد:"), supplierFilter,
            applyFiltersBtn
        );
        
        filtersBox.getChildren().addAll(filtersLabel, filtersRow);
        return filtersBox;
    }

    /**
     * إنشاء جدول أوامر الاستلام للصفحة
     */
    private TableView<ReceiptOrder> createReceiptOrdersTableForPage(Consumer<ReceiptOrder> onEditOrder, 
                                                                   Consumer<ReceiptOrder> onPrintOrder) {
        TableView<ReceiptOrder> table = new TableView<>();
        table.getStyleClass().add("receipt-orders-table");
        
        // الأعمدة الأساسية
        TableColumn<ReceiptOrder, String> numberCol = new TableColumn<>("رقم الأمر");
        numberCol.setCellValueFactory(new PropertyValueFactory<>("orderNumber"));
        numberCol.setPrefWidth(100);
        
        TableColumn<ReceiptOrder, String> supplierCol = new TableColumn<>("المورد");
        supplierCol.setCellValueFactory(new PropertyValueFactory<>("supplierName"));
        supplierCol.setPrefWidth(150);
        
        TableColumn<ReceiptOrder, String> manufacturingOrderCol = new TableColumn<>("أمر التصنيع");
        manufacturingOrderCol.setCellValueFactory(new PropertyValueFactory<>("manufacturingOrderNumber"));
        manufacturingOrderCol.setPrefWidth(120);
        
        TableColumn<ReceiptOrder, LocalDate> dateCol = new TableColumn<>("التاريخ");
        dateCol.setCellValueFactory(new PropertyValueFactory<>("receiptDate"));
        dateCol.setPrefWidth(100);
        
        TableColumn<ReceiptOrder, String> statusCol = new TableColumn<>("الحالة");
        statusCol.setCellValueFactory(cellData -> 
            cellData.getValue().statusProperty().asString());
        statusCol.setPrefWidth(100);
        
        // عمود الإجراءات
        TableColumn<ReceiptOrder, Void> actionsCol = new TableColumn<>("الإجراءات");
        actionsCol.setCellFactory(col -> new TableCell<ReceiptOrder, Void>() {
            private final Button editBtn = new Button("تعديل");
            private final Button printBtn = new Button("طباعة");
            private final Button confirmBtn = new Button("تأكيد");
            private final HBox actionsBox = new HBox(5);
            
            {
                FontAwesomeIconView editIcon = new FontAwesomeIconView(FontAwesomeIcon.EDIT);
                editIcon.setSize("10px");
                editBtn.setGraphic(editIcon);
                editBtn.getStyleClass().add("edit-button");
                
                FontAwesomeIconView printIcon = new FontAwesomeIconView(FontAwesomeIcon.PRINT);
                printIcon.setSize("10px");
                printBtn.setGraphic(printIcon);
                printBtn.getStyleClass().add("print-button");
                
                FontAwesomeIconView confirmIcon = new FontAwesomeIconView(FontAwesomeIcon.CHECK);
                confirmIcon.setSize("10px");
                confirmBtn.setGraphic(confirmIcon);
                confirmBtn.getStyleClass().add("confirm-button");
                
                actionsBox.getChildren().addAll(editBtn, printBtn, confirmBtn);
                actionsBox.setAlignment(Pos.CENTER);
            }
            
            @Override
            protected void updateItem(Void item, boolean empty) {
                super.updateItem(item, empty);
                if (empty) {
                    setGraphic(null);
                } else {
                    ReceiptOrder order = getTableView().getItems().get(getIndex());
                    editBtn.setOnAction(e -> onEditOrder.accept(order));
                    printBtn.setOnAction(e -> onPrintOrder.accept(order));
                    confirmBtn.setOnAction(e -> confirmReceiptOrder(order));
                    setGraphic(actionsBox);
                }
            }
        });
        actionsCol.setPrefWidth(200);
        
        // إضافة الأعمدة
        table.getColumns().addAll(numberCol, supplierCol, manufacturingOrderCol, dateCol, statusCol, actionsCol);
        
        // تخصيص عرض الصفوف حسب الحالة
        table.setRowFactory(tv -> {
            TableRow<ReceiptOrder> row = new TableRow<>();
            row.itemProperty().addListener((obs, oldOrder, newOrder) -> {
                if (newOrder != null) {
                    String style = "";
                    switch (newOrder.getStatus()) {
                        case DRAFT:
                            style = "-fx-background-color: #fff3cd;";
                            break;
                        case CONFIRMED:
                            style = "-fx-background-color: #d1ecf1;";
                            break;
                        case IN_RECEIPT:
                            style = "-fx-background-color: #f8d7da;";
                            break;
                        case RECEIVED:
                            style = "-fx-background-color: #d4edda;";
                            break;
                        case CANCELLED:
                            style = "-fx-background-color: #f5c6cb;";
                            break;
                    }
                    row.setStyle(style);
                }
            });
            return row;
        });
        
        table.setItems(receiptOrdersList);
        return table;
    }

    /**
     * تحديث جدول أوامر الاستلام
     */
    private void refreshReceiptOrdersTable() {
        receiptOrdersList.clear();
        receiptOrdersList.addAll(receiptOrderService.getAllReceiptOrders());
    }

    /**
     * تأكيد أمر الاستلام
     */
    private void confirmReceiptOrder(ReceiptOrder order) {
        if (receiptOrderService.confirmReceiptOrder(order.getOrderId())) {
            refreshReceiptOrdersTable();
            showInfoAlert("تم تأكيد أمر الاستلام بنجاح.");
        } else {
            showErrorAlert("فشل في تأكيد أمر الاستلام.");
        }
    }

    /**
     * إنشاء أمر استلام من أمر تصنيع
     */
    private void createFromManufacturingOrder() {
        // عرض قائمة أوامر التصنيع المؤكدة
        var confirmedOrders = manufacturingService.getManufacturingOrdersByStatus(ManufacturingOrder.OrderStatus.CONFIRMED);
        
        if (confirmedOrders.isEmpty()) {
            showInfoAlert("لا توجد أوامر تصنيع مؤكدة لإنشاء أمر استلام منها.");
            return;
        }
        
        // إنشاء نافذة اختيار أمر التصنيع
        ChoiceDialog<ManufacturingOrder> dialog = new ChoiceDialog<>(confirmedOrders.get(0), confirmedOrders);
        dialog.setTitle("اختيار أمر التصنيع");
        dialog.setHeaderText("اختر أمر التصنيع لإنشاء أمر استلام");
        dialog.setContentText("أمر التصنيع:");
        
        dialog.showAndWait().ifPresent(selectedOrder -> {
            ReceiptOrder newReceiptOrder = receiptOrderService.createFromManufacturingOrder(selectedOrder);
            if (newReceiptOrder != null) {
                refreshReceiptOrdersTable();
                showInfoAlert("تم إنشاء أمر الاستلام بنجاح من أمر التصنيع رقم: " + selectedOrder.getOrderNumber());
            } else {
                showErrorAlert("فشل في إنشاء أمر الاستلام.");
            }
        });
    }

    /**
     * عرض رسالة معلومات
     */
    private void showInfoAlert(String message) {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle("معلومات");
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }

    /**
     * عرض رسالة خطأ
     */
    private void showErrorAlert(String message) {
        Alert alert = new Alert(Alert.AlertType.ERROR);
        alert.setTitle("خطأ");
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }
}
