# دليل التثبيت والتشغيل - Installation Guide

## متطلبات النظام - System Requirements

### الحد الأدنى:
- **نظام التشغيل:** Windows 10/11, macOS 10.14+, Linux Ubuntu 18.04+
- **Java:** OpenJDK 17 أو أحدث
- **الذاكرة:** 4 GB RAM
- **مساحة القرص:** 500 MB مساحة فارغة
- **الاتصال:** اتصال إنترنت (للتحميل الأولي فقط)

### الموصى به:
- **Java:** OpenJDK 21 LTS
- **الذاكرة:** 8 GB RAM أو أكثر
- **مساحة القرص:** 2 GB مساحة فارغة

## خطوات التثبيت

### 1. تثبيت Java
إذا لم يكن Java مثبتاً على نظامك:

#### Windows:
1. اذهب إلى [Adoptium](https://adoptium.net/)
2. حمل OpenJDK 17 أو 21 (LTS)
3. شغ<PERSON> ملف التثبيت واتبع التعليمات
4. تأكد من إضافة Java إلى PATH

#### macOS:
```bash
# باستخدام Homebrew
brew install openjdk@17
```

#### Linux (Ubuntu/Debian):
```bash
sudo apt update
sudo apt install openjdk-17-jdk
```

### 2. التحقق من تثبيت Java
افتح Command Prompt أو Terminal واكتب:
```bash
java -version
```

يجب أن ترى إصدار Java 17 أو أحدث.

## تشغيل البرنامج

### الطريقة الأولى: استخدام ملف التشغيل (Windows)
1. انقر نقراً مزدوجاً على `run.bat`
2. انتظر حتى يتم تحميل التبعيات (في المرة الأولى فقط)
3. سيفتح البرنامج تلقائياً

### الطريقة الثانية: استخدام Command Line
```bash
# Windows
mvnw.cmd clean javafx:run

# macOS/Linux
./mvnw clean javafx:run
```

### الطريقة الثالثة: بناء ملف JAR
```bash
# بناء المشروع
mvnw.cmd clean package

# تشغيل الملف المبني
java -jar target/accounting-system-1.0.0.jar
```

## حل المشاكل الشائعة

### مشكلة: "java command not found"
**الحل:** تأكد من تثبيت Java وإضافته إلى PATH

### مشكلة: "JavaFX runtime components are missing"
**الحل:** استخدم الأمر التالي:
```bash
mvnw.cmd clean javafx:run
```

### مشكلة: بطء في التحميل الأولي
**السبب:** تحميل التبعيات من الإنترنت
**الحل:** انتظر حتى اكتمال التحميل (مرة واحدة فقط)

### مشكلة: خطأ في الاتصال بالإنترنت
**الحل:** تأكد من اتصالك بالإنترنت أو استخدم proxy إذا لزم الأمر

## الملفات المهمة

- `run.bat` - ملف التشغيل الرئيسي (Windows)
- `run-simple.bat` - ملف تشغيل بديل
- `mvnw.cmd` - Maven Wrapper (Windows)
- `mvnw` - Maven Wrapper (macOS/Linux)
- `pom.xml` - ملف إعدادات Maven
- `src/` - مجلد الكود المصدري

## الدعم الفني

إذا واجهت أي مشاكل:
1. تأكد من تثبيت Java 17+
2. تأكد من اتصالك بالإنترنت
3. جرب إعادة تشغيل Command Prompt كمدير
4. تأكد من عدم وجود برامج مكافحة فيروسات تحجب التطبيق

## ملاحظات إضافية

- البرنامج يعمل بدون اتصال إنترنت بعد التحميل الأولي
- جميع البيانات تُحفظ محلياً على جهازك
- يمكن نسخ المجلد كاملاً لعمل نسخة احتياطية

---
**نظام الحسابات المتكامل v1.0**
