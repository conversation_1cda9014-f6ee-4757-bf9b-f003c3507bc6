package com.accounting;

import javafx.application.Application;
import javafx.fxml.FXMLLoader;
import javafx.scene.Scene;
import javafx.scene.image.Image;
import javafx.stage.Stage;

import java.io.IOException;

/**
 * نظام الحسابات الرئيسي
 * Main Accounting System Application
 */
public class AccountingApplication extends Application {

    @Override
    public void start(Stage stage) throws IOException {
        FXMLLoader fxmlLoader = new FXMLLoader(AccountingApplication.class.getResource("/fxml/main-view.fxml"));
        Scene scene = new Scene(fxmlLoader.load(), 1200, 800);
        
        // إضافة ملف CSS للتصميم
        scene.getStylesheets().add(getClass().getResource("/css/styles.css").toExternalForm());
        
        stage.setTitle("نظام الحسابات المتكامل - Integrated Accounting System");
        stage.setScene(scene);
        stage.setMaximized(true);
        stage.setMinWidth(1000);
        stage.setMinHeight(700);
        
        // إضافة أيقونة للتطبيق
        try {
            stage.getIcons().add(new Image(getClass().getResourceAsStream("/images/app-icon.png")));
        } catch (Exception e) {
            System.out.println("Could not load application icon");
        }
        
        stage.show();
    }

    public static void main(String[] args) {
        launch();
    }
}
