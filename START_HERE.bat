@echo off
chcp 65001 >nul

:: عرض اللوجو
if exist "logo.txt" (
    type logo.txt
    echo.
) else (
    echo ========================================
    echo    نظام الحسابات المتكامل
    echo    Integrated Accounting System
    echo ========================================
    echo.
)

echo 🎯 مرحباً بك في نظام الحسابات المتكامل!
echo 🎯 Welcome to the Integrated Accounting System!
echo.

echo 📋 اختر ما تريد فعله:
echo 📋 Choose what you want to do:
echo.

echo    1️⃣  تشغيل البرنامج (Run Application)
echo    2️⃣  فحص متطلبات النظام (Check System Requirements)
echo    3️⃣  اختبار Java (Test Java)
echo    4️⃣  تشغيل وضع المطور (Developer Mode)
echo    5️⃣  عرض المساعدة (Show Help)
echo    6️⃣  خروج (Exit)
echo.

set /p choice="اختر رقماً (Choose a number): "

if "%choice%"=="1" goto run_app
if "%choice%"=="2" goto check_system
if "%choice%"=="3" goto test_java
if "%choice%"=="4" goto dev_mode
if "%choice%"=="5" goto show_help
if "%choice%"=="6" goto exit
goto invalid_choice

:run_app
echo.
echo 🚀 تشغيل البرنامج...
call run.bat
goto end

:check_system
echo.
echo 🔍 فحص متطلبات النظام...
call check-system.bat
goto end

:test_java
echo.
echo 🧪 اختبار Java...
call test-java.bat
goto end

:dev_mode
echo.
echo 🔧 تشغيل وضع المطور...
call run-dev.bat
goto end

:show_help
echo.
echo 📖 المساعدة والوثائق:
echo.
echo 📘 ملفات المساعدة المتاحة:
echo    - README.md (الوثائق الرئيسية)
echo    - INSTALL.md (دليل التثبيت)
echo    - QUICK_START.md (البدء السريع)
echo.
echo 🔗 روابط مفيدة:
echo    - تحميل Java: https://adoptium.net/
echo    - وثائق JavaFX: https://openjfx.io/
echo.
echo 🛠️ ملفات التشغيل:
echo    - run.bat (التشغيل العادي)
echo    - run-dev.bat (وضع المطور)
echo    - check-system.bat (فحص النظام)
echo    - test-java.bat (اختبار Java)
echo.
pause
goto end

:invalid_choice
echo.
echo ❌ اختيار غير صحيح، يرجى المحاولة مرة أخرى
echo ❌ Invalid choice, please try again
echo.
pause
goto start

:exit
echo.
echo 👋 شكراً لاستخدام نظام الحسابات المتكامل
echo 👋 Thank you for using the Integrated Accounting System
goto end

:end
echo.
echo اضغط أي مفتاح للخروج...
pause >nul
