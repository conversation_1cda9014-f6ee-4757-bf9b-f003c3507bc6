package com.accounting.model;

/**
 * نموذج بند قائمة الدخل
 * Income Statement Item Model
 */
public class IncomeStatementItem {
    
    private String accountCode;
    private String accountName;
    private double amount;
    private String description;
    private ItemType type;
    
    /**
     * أنواع بنود قائمة الدخل
     */
    public enum ItemType {
        REVENUE("إيراد", "Revenue"),
        COST_OF_GOODS_SOLD("تكلفة البضاعة المباعة", "Cost of Goods Sold"),
        OPERATING_EXPENSE("مصروف تشغيلي", "Operating Expense"),
        OTHER_INCOME("إيراد آخر", "Other Income"),
        OTHER_EXPENSE("مصروف آخر", "Other Expense"),
        TAX("ضريبة", "Tax");
        
        private final String arabicName;
        private final String englishName;
        
        ItemType(String arabicName, String englishName) {
            this.arabicName = arabicName;
            this.englishName = englishName;
        }
        
        public String getArabicName() { return arabicName; }
        public String getEnglishName() { return englishName; }
    }
    
    /**
     * كونستركتور افتراضي
     */
    public IncomeStatementItem() {
    }
    
    /**
     * كونستركتور مع المعاملات الأساسية
     */
    public IncomeStatementItem(String accountCode, String accountName, double amount, ItemType type) {
        this.accountCode = accountCode;
        this.accountName = accountName;
        this.amount = amount;
        this.type = type;
    }
    
    /**
     * كونستركتور كامل
     */
    public IncomeStatementItem(String accountCode, String accountName, double amount, 
                              String description, ItemType type) {
        this.accountCode = accountCode;
        this.accountName = accountName;
        this.amount = amount;
        this.description = description;
        this.type = type;
    }
    
    /**
     * التحقق من صحة البيانات
     */
    public boolean isValid() {
        return accountCode != null && !accountCode.trim().isEmpty() &&
               accountName != null && !accountName.trim().isEmpty() &&
               type != null;
    }
    
    /**
     * التحقق من كون البند إيراد
     */
    public boolean isRevenue() {
        return type == ItemType.REVENUE || type == ItemType.OTHER_INCOME;
    }
    
    /**
     * التحقق من كون البند مصروف
     */
    public boolean isExpense() {
        return type == ItemType.COST_OF_GOODS_SOLD || 
               type == ItemType.OPERATING_EXPENSE || 
               type == ItemType.OTHER_EXPENSE ||
               type == ItemType.TAX;
    }
    
    /**
     * الحصول على المبلغ المطلق
     */
    public double getAbsoluteAmount() {
        return Math.abs(amount);
    }
    
    /**
     * تنسيق المبلغ للعرض
     */
    public String getFormattedAmount() {
        return String.format("%,.2f", amount);
    }
    
    /**
     * نسخ البند
     */
    public IncomeStatementItem copy() {
        return new IncomeStatementItem(accountCode, accountName, amount, description, type);
    }
    
    /**
     * مقارنة البنود
     */
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        
        IncomeStatementItem that = (IncomeStatementItem) obj;
        return accountCode != null ? accountCode.equals(that.accountCode) : that.accountCode == null;
    }
    
    @Override
    public int hashCode() {
        return accountCode != null ? accountCode.hashCode() : 0;
    }
    
    @Override
    public String toString() {
        return String.format("%s - %s: %,.2f", accountCode, accountName, amount);
    }
    
    // Getters and Setters
    public String getAccountCode() {
        return accountCode;
    }
    
    public void setAccountCode(String accountCode) {
        this.accountCode = accountCode;
    }
    
    public String getAccountName() {
        return accountName;
    }
    
    public void setAccountName(String accountName) {
        this.accountName = accountName;
    }
    
    public double getAmount() {
        return amount;
    }
    
    public void setAmount(double amount) {
        this.amount = amount;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public ItemType getType() {
        return type;
    }
    
    public void setType(ItemType type) {
        this.type = type;
    }
}
