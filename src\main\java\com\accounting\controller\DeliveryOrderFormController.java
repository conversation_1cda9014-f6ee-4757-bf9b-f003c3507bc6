package com.accounting.controller;

import com.accounting.model.DeliveryOrder;
import com.accounting.model.DeliveryItem;
import com.accounting.service.DeliveryOrderService;
import javafx.geometry.Insets;
import javafx.geometry.Pos;
import javafx.scene.control.*;
import javafx.scene.control.cell.PropertyValueFactory;
import javafx.scene.control.cell.TextFieldTableCell;
import javafx.scene.layout.*;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.util.converter.DoubleStringConverter;
import javafx.util.converter.IntegerStringConverter;
import de.jensd.fx.glyphs.fontawesome.FontAwesomeIcon;
import de.jensd.fx.glyphs.fontawesome.FontAwesomeIconView;
import java.time.LocalDate;
import java.text.DecimalFormat;
import java.util.function.Consumer;

/**
 * كنترولر نموذج أمر التسليم
 */
public class DeliveryOrderFormController {
    
    private final DeliveryOrderService deliveryOrderService;
    private final DecimalFormat decimalFormat = new DecimalFormat("#,##0.00");
    
    // مكونات النموذج
    private TextField customerNameField;
    private DatePicker deliveryDatePicker;
    private TextField manufacturingOrderNumberField;
    private TextField invoiceNumberField;
    private TextArea notesArea;
    private ComboBox<DeliveryOrder.DeliveryStatus> statusComboBox;
    
    // جدول عناصر التسليم
    private TableView<DeliveryItem> deliveryItemsTable;
    private ObservableList<DeliveryItem> deliveryItems;
    
    // الأمر الحالي
    private DeliveryOrder currentOrder;
    
    public DeliveryOrderFormController(DeliveryOrderService deliveryOrderService) {
        this.deliveryOrderService = deliveryOrderService;
    }
    
    /**
     * إنشاء صفحة نموذج أمر التسليم
     */
    public VBox createDeliveryOrderFormPage(DeliveryOrder order, Consumer<DeliveryOrder> onSave, Consumer<DeliveryOrder> onPrint) {
        currentOrder = order != null ? order : new DeliveryOrder();
        deliveryItems = FXCollections.observableArrayList(currentOrder.getDeliveryItems());
        
        VBox container = new VBox(20);
        container.setPadding(new Insets(20));
        
        // العنوان
        Label titleLabel = new Label(order == null ? "📝 أمر تسليم جديد" : "📝 تعديل أمر التسليم");
        titleLabel.getStyleClass().add("section-title");
        
        // معلومات الأمر الأساسية
        VBox basicInfoBox = createBasicInfoSection();
        
        // جدول عناصر التسليم
        VBox itemsTableBox = createDeliveryItemsSection();
        
        // أزرار الإجراءات
        HBox buttonsBox = createButtonsSection(onSave, onPrint);
        
        container.getChildren().addAll(titleLabel, basicInfoBox, itemsTableBox, buttonsBox);
        return container;
    }
    
    /**
     * إنشاء قسم المعلومات الأساسية
     */
    private VBox createBasicInfoSection() {
        VBox section = new VBox(15);
        section.setPadding(new Insets(15));
        section.getStyleClass().add("form-section");
        
        Label sectionTitle = new Label("📋 معلومات أمر التسليم");
        sectionTitle.getStyleClass().add("section-subtitle");
        
        GridPane grid = new GridPane();
        grid.setHgap(15);
        grid.setVgap(15);
        grid.setPadding(new Insets(10));
        
        // الصف الأول
        Label customerLabel = new Label("اسم العميل:");
        customerLabel.getStyleClass().add("field-label");
        customerNameField = new TextField();
        customerNameField.setPromptText("أدخل اسم العميل");
        customerNameField.setPrefWidth(200);
        
        Label dateLabel = new Label("تاريخ التسليم:");
        dateLabel.getStyleClass().add("field-label");
        deliveryDatePicker = new DatePicker();
        deliveryDatePicker.setValue(LocalDate.now());
        deliveryDatePicker.setPrefWidth(150);
        
        // الصف الثاني
        Label manufacturingOrderLabel = new Label("رقم أمر العمل:");
        manufacturingOrderLabel.getStyleClass().add("field-label");
        manufacturingOrderNumberField = new TextField();
        manufacturingOrderNumberField.setPromptText("رقم أمر العمل");
        manufacturingOrderNumberField.setPrefWidth(200);
        
        Label invoiceLabel = new Label("رقم الفاتورة:");
        invoiceLabel.getStyleClass().add("field-label");
        invoiceNumberField = new TextField();
        invoiceNumberField.setPromptText("رقم الفاتورة");
        invoiceNumberField.setPrefWidth(200);
        
        // الصف الثالث
        Label statusLabel = new Label("الحالة:");
        statusLabel.getStyleClass().add("field-label");
        statusComboBox = new ComboBox<>();
        statusComboBox.getItems().addAll(DeliveryOrder.DeliveryStatus.values());
        statusComboBox.setValue(DeliveryOrder.DeliveryStatus.DRAFT);
        statusComboBox.setPrefWidth(150);
        
        // ترتيب العناصر في الشبكة
        grid.add(customerLabel, 0, 0);
        grid.add(customerNameField, 1, 0);
        grid.add(dateLabel, 2, 0);
        grid.add(deliveryDatePicker, 3, 0);
        
        grid.add(manufacturingOrderLabel, 0, 1);
        grid.add(manufacturingOrderNumberField, 1, 1);
        grid.add(invoiceLabel, 2, 1);
        grid.add(invoiceNumberField, 3, 1);
        
        grid.add(statusLabel, 0, 2);
        grid.add(statusComboBox, 1, 2);
        
        // الملاحظات
        Label notesLabel = new Label("ملاحظات:");
        notesLabel.getStyleClass().add("field-label");
        notesArea = new TextArea();
        notesArea.setPromptText("ملاحظات إضافية...");
        notesArea.setPrefRowCount(3);
        notesArea.setWrapText(true);
        
        VBox notesBox = new VBox(5);
        notesBox.getChildren().addAll(notesLabel, notesArea);
        
        section.getChildren().addAll(sectionTitle, grid, notesBox);
        
        // تحميل البيانات إذا كان الأمر موجود
        if (currentOrder != null) {
            loadOrderData();
        }
        
        return section;
    }
    
    /**
     * إنشاء قسم جدول عناصر التسليم
     */
    private VBox createDeliveryItemsSection() {
        VBox section = new VBox(15);
        section.setPadding(new Insets(15));
        section.getStyleClass().add("form-section");
        
        Label sectionTitle = new Label("📦 عناصر التسليم");
        sectionTitle.getStyleClass().add("section-subtitle");
        
        // شريط أدوات الجدول
        HBox toolbar = createTableToolbar();
        
        // الجدول
        deliveryItemsTable = createDeliveryItemsTable();
        
        // وضع الجدول في ScrollPane
        ScrollPane scrollPane = new ScrollPane(deliveryItemsTable);
        scrollPane.setFitToWidth(true);
        scrollPane.setVbarPolicy(ScrollPane.ScrollBarPolicy.AS_NEEDED);
        scrollPane.setHbarPolicy(ScrollPane.ScrollBarPolicy.NEVER);
        scrollPane.setPrefHeight(300);
        VBox.setVgrow(scrollPane, Priority.ALWAYS);
        
        // الإجماليات
        HBox totalsBox = createTotalsBox();
        
        section.getChildren().addAll(sectionTitle, toolbar, scrollPane, totalsBox);
        return section;
    }
    
    /**
     * إنشاء شريط أدوات الجدول
     */
    private HBox createTableToolbar() {
        HBox toolbar = new HBox(10);
        toolbar.setAlignment(Pos.CENTER_LEFT);
        toolbar.setPadding(new Insets(10));
        toolbar.getStyleClass().add("toolbar");
        
        Button addItemBtn = new Button("إضافة عنصر");
        FontAwesomeIconView addIcon = new FontAwesomeIconView(FontAwesomeIcon.PLUS);
        addIcon.setSize("12px");
        addItemBtn.setGraphic(addIcon);
        addItemBtn.getStyleClass().add("add-button");
        addItemBtn.setOnAction(e -> addNewItem());
        
        Button deleteItemBtn = new Button("حذف عنصر");
        FontAwesomeIconView deleteIcon = new FontAwesomeIconView(FontAwesomeIcon.MINUS);
        deleteIcon.setSize("12px");
        deleteItemBtn.setGraphic(deleteIcon);
        deleteItemBtn.getStyleClass().add("delete-button");
        deleteItemBtn.setOnAction(e -> deleteSelectedItem());
        
        Button clearAllBtn = new Button("مسح الكل");
        FontAwesomeIconView clearIcon = new FontAwesomeIconView(FontAwesomeIcon.TRASH);
        clearIcon.setSize("12px");
        clearAllBtn.setGraphic(clearIcon);
        clearAllBtn.getStyleClass().add("unpost-button");
        clearAllBtn.setOnAction(e -> clearAllItems());
        
        toolbar.getChildren().addAll(addItemBtn, deleteItemBtn, clearAllBtn);
        return toolbar;
    }
    
    /**
     * إنشاء جدول عناصر التسليم
     */
    private TableView<DeliveryItem> createDeliveryItemsTable() {
        TableView<DeliveryItem> table = new TableView<>();
        table.setEditable(true);
        table.getStyleClass().add("delivery-items-table");
        
        // عمود الترقيم
        TableColumn<DeliveryItem, Integer> indexCol = new TableColumn<>("ت");
        indexCol.setCellValueFactory(cellData -> {
            int index = table.getItems().indexOf(cellData.getValue()) + 1;
            return new javafx.beans.property.SimpleIntegerProperty(index).asObject();
        });
        indexCol.setPrefWidth(40);
        indexCol.setSortable(false);
        
        // عمود التفاصيل
        TableColumn<DeliveryItem, String> descCol = new TableColumn<>("التفاصيل");
        descCol.setCellValueFactory(new PropertyValueFactory<>("description"));
        descCol.setCellFactory(TextFieldTableCell.forTableColumn());
        descCol.setOnEditCommit(event -> {
            event.getRowValue().setDescription(event.getNewValue());
            updateTotals();
        });
        descCol.setPrefWidth(200);
        
        // عمود الطول
        TableColumn<DeliveryItem, Double> lengthCol = new TableColumn<>("الطول (مم)");
        lengthCol.setCellValueFactory(new PropertyValueFactory<>("length"));
        lengthCol.setCellFactory(TextFieldTableCell.forTableColumn(new DoubleStringConverter()));
        lengthCol.setOnEditCommit(event -> {
            event.getRowValue().setLength(event.getNewValue());
            updateTotals();
        });
        lengthCol.setPrefWidth(100);
        
        // عمود العرض
        TableColumn<DeliveryItem, Double> widthCol = new TableColumn<>("العرض (مم)");
        widthCol.setCellValueFactory(new PropertyValueFactory<>("width"));
        widthCol.setCellFactory(TextFieldTableCell.forTableColumn(new DoubleStringConverter()));
        widthCol.setOnEditCommit(event -> {
            event.getRowValue().setWidth(event.getNewValue());
            updateTotals();
        });
        widthCol.setPrefWidth(100);
        
        // عمود العدد
        TableColumn<DeliveryItem, Integer> countCol = new TableColumn<>("العدد");
        countCol.setCellValueFactory(new PropertyValueFactory<>("count"));
        countCol.setCellFactory(TextFieldTableCell.forTableColumn(new IntegerStringConverter()));
        countCol.setOnEditCommit(event -> {
            event.getRowValue().setCount(event.getNewValue());
            updateTotals();
        });
        countCol.setPrefWidth(80);
        
        // عمود الملاحظة
        TableColumn<DeliveryItem, String> notesCol = new TableColumn<>("ملاحظة");
        notesCol.setCellValueFactory(new PropertyValueFactory<>("notes"));
        notesCol.setCellFactory(TextFieldTableCell.forTableColumn());
        notesCol.setOnEditCommit(event -> {
            event.getRowValue().setNotes(event.getNewValue());
        });
        notesCol.setPrefWidth(150);
        
        // عمود حالة التسليم
        TableColumn<DeliveryItem, String> statusCol = new TableColumn<>("حالة التسليم");
        statusCol.setCellValueFactory(cellData -> 
            cellData.getValue().deliveryStatusProperty().asString());
        statusCol.setPrefWidth(100);
        
        // إضافة الأعمدة
        table.getColumns().addAll(indexCol, descCol, lengthCol, widthCol, countCol, notesCol, statusCol);
        
        // ربط البيانات
        table.setItems(deliveryItems);
        
        // إضافة مستمع للتحديث التلقائي
        deliveryItems.addListener((javafx.collections.ListChangeListener<DeliveryItem>) change -> {
            table.refresh();
            updateTotals();
        });
        
        return table;
    }
    
    /**
     * إنشاء صندوق الإجماليات
     */
    private HBox createTotalsBox() {
        HBox totalsBox = new HBox(30);
        totalsBox.setAlignment(Pos.CENTER);
        totalsBox.setPadding(new Insets(15));
        totalsBox.getStyleClass().add("totals-container");
        
        // إجمالي العدد
        VBox totalCountBox = new VBox(5);
        totalCountBox.setAlignment(Pos.CENTER);
        Label totalCountTitleLabel = new Label("إجمالي العدد");
        totalCountTitleLabel.getStyleClass().add("totals-title");
        Label totalCountLabel = new Label("0");
        totalCountLabel.getStyleClass().add("totals-value");
        totalCountBox.getChildren().addAll(totalCountTitleLabel, totalCountLabel);
        
        // إجمالي العناصر
        VBox totalItemsBox = new VBox(5);
        totalItemsBox.setAlignment(Pos.CENTER);
        Label totalItemsTitleLabel = new Label("إجمالي العناصر");
        totalItemsTitleLabel.getStyleClass().add("totals-title");
        Label totalItemsLabel = new Label("0");
        totalItemsLabel.getStyleClass().add("totals-value");
        totalItemsBox.getChildren().addAll(totalItemsTitleLabel, totalItemsLabel);
        
        totalsBox.getChildren().addAll(totalCountBox, totalItemsBox);
        return totalsBox;
    }
    
    /**
     * إنشاء قسم الأزرار
     */
    private HBox createButtonsSection(Consumer<DeliveryOrder> onSave, Consumer<DeliveryOrder> onPrint) {
        HBox buttonsBox = new HBox(15);
        buttonsBox.setAlignment(Pos.CENTER);
        buttonsBox.setPadding(new Insets(20, 0, 0, 0));
        
        Button saveBtn = new Button("حفظ");
        FontAwesomeIconView saveIcon = new FontAwesomeIconView(FontAwesomeIcon.SAVE);
        saveIcon.setSize("12px");
        saveBtn.setGraphic(saveIcon);
        saveBtn.getStyleClass().add("save-button");
        saveBtn.setOnAction(e -> saveOrder(onSave));
        
        Button printBtn = new Button("طباعة");
        FontAwesomeIconView printIcon = new FontAwesomeIconView(FontAwesomeIcon.PRINT);
        printIcon.setSize("12px");
        printBtn.setGraphic(printIcon);
        printBtn.getStyleClass().add("print-button");
        printBtn.setOnAction(e -> {
            if (saveOrder(onSave)) {
                onPrint.accept(currentOrder);
            }
        });
        
        Button saveAndPrintBtn = new Button("حفظ وطباعة");
        FontAwesomeIconView saveAndPrintIcon = new FontAwesomeIconView(FontAwesomeIcon.PRINT);
        saveAndPrintIcon.setSize("12px");
        saveAndPrintBtn.setGraphic(saveAndPrintIcon);
        saveAndPrintBtn.getStyleClass().add("save-print-button");
        saveAndPrintBtn.setOnAction(e -> {
            if (saveOrder(onSave)) {
                onPrint.accept(currentOrder);
            }
        });
        
        buttonsBox.getChildren().addAll(saveBtn, printBtn, saveAndPrintBtn);
        return buttonsBox;
    }
    
    /**
     * تحميل بيانات الأمر
     */
    private void loadOrderData() {
        customerNameField.setText(currentOrder.getCustomerName());
        deliveryDatePicker.setValue(currentOrder.getDeliveryDate());
        manufacturingOrderNumberField.setText(currentOrder.getManufacturingOrderNumber());
        invoiceNumberField.setText(currentOrder.getInvoiceNumber());
        statusComboBox.setValue(currentOrder.getStatus());
        notesArea.setText(currentOrder.getNotes());
    }
    
    /**
     * إضافة عنصر جديد
     */
    private void addNewItem() {
        DeliveryItem newItem = new DeliveryItem();
        newItem.setDescription("عنصر جديد");
        newItem.setLength(1000.0);
        newItem.setWidth(1000.0);
        newItem.setCount(1);
        deliveryItems.add(newItem);
        
        // تحديد الصف الجديد
        deliveryItemsTable.getSelectionModel().selectLast();
        deliveryItemsTable.scrollTo(deliveryItems.size() - 1);
    }
    
    /**
     * حذف العنصر المحدد
     */
    private void deleteSelectedItem() {
        DeliveryItem selectedItem = deliveryItemsTable.getSelectionModel().getSelectedItem();
        if (selectedItem != null) {
            Alert confirmAlert = new Alert(Alert.AlertType.CONFIRMATION);
            confirmAlert.setTitle("تأكيد الحذف");
            confirmAlert.setHeaderText("حذف عنصر");
            confirmAlert.setContentText("هل أنت متأكد من حذف هذا العنصر؟");
            
            confirmAlert.showAndWait().ifPresent(response -> {
                if (response == ButtonType.OK) {
                    deliveryItems.remove(selectedItem);
                }
            });
        } else {
            showWarningAlert("يرجى اختيار عنصر للحذف.");
        }
    }
    
    /**
     * مسح جميع العناصر
     */
    private void clearAllItems() {
        if (!deliveryItems.isEmpty()) {
            Alert confirmAlert = new Alert(Alert.AlertType.CONFIRMATION);
            confirmAlert.setTitle("تأكيد المسح");
            confirmAlert.setHeaderText("مسح جميع العناصر");
            confirmAlert.setContentText("هل أنت متأكد من مسح جميع العناصر؟");
            
            confirmAlert.showAndWait().ifPresent(response -> {
                if (response == ButtonType.OK) {
                    deliveryItems.clear();
                }
            });
        }
    }
    
    /**
     * تحديث الإجماليات
     */
    private void updateTotals() {
        // سيتم تحديث الإجماليات هنا
    }
    
    /**
     * حفظ الأمر
     */
    private boolean saveOrder(Consumer<DeliveryOrder> onSave) {
        if (!validateForm()) {
            return false;
        }
        
        // تحديث بيانات الأمر
        currentOrder.setCustomerName(customerNameField.getText().trim());
        currentOrder.setDeliveryDate(deliveryDatePicker.getValue());
        currentOrder.setManufacturingOrderNumber(manufacturingOrderNumberField.getText().trim());
        currentOrder.setInvoiceNumber(invoiceNumberField.getText().trim());
        currentOrder.setStatus(statusComboBox.getValue());
        currentOrder.setNotes(notesArea.getText().trim());
        
        // تحديث عناصر التسليم
        currentOrder.clearDeliveryItems();
        for (DeliveryItem item : deliveryItems) {
            currentOrder.addDeliveryItem(item);
        }
        
        // حفظ في الخدمة
        boolean success;
        if (deliveryOrderService.findDeliveryOrderById(currentOrder.getDeliveryOrderId()) != null) {
            success = deliveryOrderService.updateDeliveryOrder(currentOrder);
        } else {
            success = deliveryOrderService.addDeliveryOrder(currentOrder);
        }
        
        if (success) {
            onSave.accept(currentOrder);
            return true;
        } else {
            showErrorAlert("فشل في حفظ أمر التسليم.");
            return false;
        }
    }
    
    /**
     * التحقق من صحة النموذج
     */
    private boolean validateForm() {
        if (customerNameField.getText() == null || customerNameField.getText().trim().isEmpty()) {
            showErrorAlert("يرجى إدخال اسم العميل.");
            customerNameField.requestFocus();
            return false;
        }
        
        if (deliveryDatePicker.getValue() == null) {
            showErrorAlert("يرجى تحديد تاريخ التسليم.");
            deliveryDatePicker.requestFocus();
            return false;
        }
        
        if (deliveryItems.isEmpty()) {
            showErrorAlert("يرجى إضافة عنصر واحد على الأقل للتسليم.");
            return false;
        }
        
        return true;
    }
    
    // رسائل التنبيه
    private void showErrorAlert(String message) {
        Alert alert = new Alert(Alert.AlertType.ERROR);
        alert.setTitle("خطأ");
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }
    
    private void showWarningAlert(String message) {
        Alert alert = new Alert(Alert.AlertType.WARNING);
        alert.setTitle("تحذير");
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }
}
