@echo off
chcp 65001 >nul
echo ========================================
echo    نظام الحسابات المتكامل - وضع المطور
echo    Accounting System - Developer Mode
echo ========================================
echo.

echo 🔧 وضع المطور مفعل - سيتم عرض معلومات تفصيلية
echo.

echo 📋 معلومات النظام:
echo OS: %OS%
echo Processor: %PROCESSOR_ARCHITECTURE%
echo User: %USERNAME%
echo Date: %DATE% %TIME%
echo.

echo 🔍 فحص Java...
java -version
if %errorlevel% neq 0 (
    echo ❌ خطأ: Java غير متاح
    echo.
    echo 📥 تحميل Java:
    echo https://adoptium.net/temurin/releases/?version=17
    echo.
    pause
    exit /b 1
)

echo.
echo 🔍 فحص متغيرات البيئة...
echo JAVA_HOME: %JAVA_HOME%
echo PATH: %PATH%
echo.

echo 📁 فحص ملفات المشروع...
dir /b *.xml *.bat *.md 2>nul
echo.

echo 📂 فحص هيكل المشروع...
if exist "src\main\java" (
    echo ✅ src\main\java
) else (
    echo ❌ src\main\java
)

if exist "src\main\resources" (
    echo ✅ src\main\resources  
) else (
    echo ❌ src\main\resources
)

echo.
echo 🚀 بدء التشغيل مع معلومات تفصيلية...
echo.

echo 📦 تنظيف وبناء المشروع...
call mvnw.cmd clean compile -X

if %errorlevel% neq 0 (
    echo ❌ فشل في بناء المشروع
    echo.
    echo 🔍 تحقق من:
    echo - اتصال الإنترنت
    echo - إعدادات Firewall/Antivirus
    echo - صحة ملف pom.xml
    echo.
    pause
    exit /b 1
)

echo.
echo ✅ تم بناء المشروع بنجاح
echo.

echo 🎯 تشغيل التطبيق...
call mvnw.cmd javafx:run -X

echo.
echo 📊 انتهى التشغيل - كود الخروج: %errorlevel%
echo.

if %errorlevel% neq 0 (
    echo ❌ حدث خطأ أثناء التشغيل
    echo.
    echo 🔧 حلول مقترحة:
    echo 1. تأكد من Java 17+
    echo 2. تأكد من JavaFX متاح
    echo 3. جرب: mvnw.cmd clean javafx:run
    echo 4. تحقق من logs أعلاه
    echo.
) else (
    echo ✅ تم إغلاق التطبيق بنجاح
)

echo.
echo 📝 معلومات إضافية:
echo - ملفات Log: target\
echo - ملفات Build: target\classes\
echo - Dependencies: .m2\repository\
echo.

pause
