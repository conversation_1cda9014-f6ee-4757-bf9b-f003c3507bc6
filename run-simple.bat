@echo off
chcp 65001 >nul
echo ========================================
echo    نظام الحسابات المتكامل
echo    Integrated Accounting System
echo    (تشغيل مبسط)
echo ========================================
echo.

echo التحقق من وجود Java...
java -version
if %errorlevel% neq 0 (
    echo.
    echo خطأ: Java غير مثبت على النظام
    echo يرجى تثبيت Java 17 أو أحدث
    pause
    exit /b 1
)

echo.
echo بناء المشروع...
call mvnw.cmd clean compile

if %errorlevel% neq 0 (
    echo خطأ في بناء المشروع
    pause
    exit /b 1
)

echo.
echo تشغيل البرنامج...
call mvnw.cmd exec:java -Dexec.mainClass="com.accounting.AccountingApplication"

pause
