@echo off
chcp 65001 >nul
echo ========================================
echo    اختبار Java - Java Test
echo ========================================
echo.

echo 🔍 اختبار Java...
java -version
if %errorlevel% neq 0 (
    echo ❌ Java غير متاح
    pause
    exit /b 1
)

echo.
echo 🔨 تجميع ملف الاختبار...
javac -d target\test-classes -cp src\main\java src\main\java\com\accounting\TestApp.java

if %errorlevel% neq 0 (
    echo ❌ فشل في التجميع
    pause
    exit /b 1
)

echo ✅ تم التجميع بنجاح
echo.

echo 🚀 تشغيل اختبار Java...
echo.
java -cp target\test-classes com.accounting.TestApp

echo.
echo ✅ انتهى الاختبار
pause
