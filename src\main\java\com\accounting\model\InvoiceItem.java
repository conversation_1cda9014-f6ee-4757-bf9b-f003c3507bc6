package com.accounting.model;

import javafx.beans.property.*;

/**
 * عنصر الفاتورة
 * Invoice Item Model
 */
public class InvoiceItem {
    private final StringProperty serviceType = new SimpleStringProperty();
    private final IntegerProperty thickness = new SimpleIntegerProperty();
    private final DoubleProperty length = new SimpleDoubleProperty();
    private final DoubleProperty width = new SimpleDoubleProperty();
    private final DoubleProperty squareMeters = new SimpleDoubleProperty();
    private final IntegerProperty quantity = new SimpleIntegerProperty();
    private final DoubleProperty totalQuantity = new SimpleDoubleProperty();
    private final DoubleProperty price = new SimpleDoubleProperty();
    private final DoubleProperty totalValue = new SimpleDoubleProperty();
    private final BooleanProperty isManualRow = new SimpleBooleanProperty(false);
    private final StringProperty description = new SimpleStringProperty(); // للصف اليدوي
    
    public InvoiceItem() {
        this(false); // افتراضياً صف عادي
    }

    public InvoiceItem(boolean isManual) {
        this.isManualRow.set(isManual);

        if (!isManual) {
            // للصف العادي: حساب المتر المربع تلقائياً
            length.addListener((obs, oldVal, newVal) -> calculateSquareMeters());
            width.addListener((obs, oldVal, newVal) -> calculateSquareMeters());

            // حساب إجمالي الكمية تلقائياً
            squareMeters.addListener((obs, oldVal, newVal) -> calculateTotalQuantity());
            quantity.addListener((obs, oldVal, newVal) -> calculateTotalQuantity());
        }

        // حساب إجمالي القيمة تلقائياً (للصفين العادي واليدوي)
        totalQuantity.addListener((obs, oldVal, newVal) -> calculateTotalValue());
        price.addListener((obs, oldVal, newVal) -> calculateTotalValue());
    }
    
    private void calculateSquareMeters() {
        double l = length.get();
        double w = width.get();
        if (l > 0 && w > 0) {
            squareMeters.set((l * w) / 1_000_000.0);
        } else {
            squareMeters.set(0);
        }
    }
    
    private void calculateTotalQuantity() {
        double sm = squareMeters.get();
        int qty = quantity.get();
        totalQuantity.set(sm * qty);
    }
    
    private void calculateTotalValue() {
        double tq = totalQuantity.get();
        double p = price.get();
        totalValue.set(tq * p);
    }
    
    // Property getters
    public StringProperty serviceTypeProperty() { return serviceType; }
    public IntegerProperty thicknessProperty() { return thickness; }
    public DoubleProperty lengthProperty() { return length; }
    public DoubleProperty widthProperty() { return width; }
    public DoubleProperty squareMetersProperty() { return squareMeters; }
    public IntegerProperty quantityProperty() { return quantity; }
    public DoubleProperty totalQuantityProperty() { return totalQuantity; }
    public DoubleProperty priceProperty() { return price; }
    public DoubleProperty totalValueProperty() { return totalValue; }
    public BooleanProperty isManualRowProperty() { return isManualRow; }
    public StringProperty descriptionProperty() { return description; }
    
    // Value getters and setters
    public String getServiceType() { return serviceType.get(); }
    public void setServiceType(String serviceType) { this.serviceType.set(serviceType); }
    
    public int getThickness() { return thickness.get(); }
    public void setThickness(int thickness) { this.thickness.set(thickness); }
    
    public double getLength() { return length.get(); }
    public void setLength(double length) { this.length.set(length); }
    
    public double getWidth() { return width.get(); }
    public void setWidth(double width) { this.width.set(width); }
    
    public double getSquareMeters() { return squareMeters.get(); }
    
    public int getQuantity() { return quantity.get(); }
    public void setQuantity(int quantity) { this.quantity.set(quantity); }
    
    public double getTotalQuantity() { return totalQuantity.get(); }
    
    public double getPrice() { return price.get(); }
    public void setPrice(double price) { this.price.set(price); }

    public double getTotalValue() { return totalValue.get(); }

    public boolean isManualRow() { return isManualRow.get(); }
    public void setManualRow(boolean isManual) { this.isManualRow.set(isManual); }

    public String getDescription() { return description.get(); }
    public void setDescription(String description) { this.description.set(description); }
}
