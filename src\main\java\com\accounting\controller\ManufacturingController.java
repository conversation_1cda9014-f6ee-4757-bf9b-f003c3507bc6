package com.accounting.controller;

import com.accounting.model.ManufacturingOrder;
import com.accounting.model.DeliveryOrder;
import com.accounting.service.ManufacturingService;
import com.accounting.service.DeliveryOrderService;
import com.accounting.service.ManufacturingPrintService;
import javafx.geometry.Insets;
import javafx.geometry.Pos;
import javafx.scene.control.*;
import javafx.scene.layout.*;
import de.jensd.fx.glyphs.fontawesome.FontAwesomeIcon;
import de.jensd.fx.glyphs.fontawesome.FontAwesomeIconView;
import javafx.stage.Stage;
import java.text.DecimalFormat;
import java.util.Map;

/**
 * كنترولر موديول التصنيع الرئيسي - نافذة واحدة متكاملة
 */
public class ManufacturingController {

    private final ManufacturingService manufacturingService;
    private final DeliveryOrderService deliveryOrderService;
    private final ManufacturingPrintService printService;
    private final DecimalFormat decimalFormat = new DecimalFormat("#,##0.00");

    // مكونات الواجهة الرئيسية
    private VBox mainContainer;
    private VBox contentArea;
    private HBox navigationBar;
    private Label currentPageLabel;

    // الصفحات المختلفة
    private enum Page {
        MAIN_DASHBOARD,
        MANUFACTURING_ORDERS,
        MANUFACTURING_ORDER_FORM,
        DELIVERY_ORDERS,
        DELIVERY_ORDER_FORM,
        MANUFACTURING_REPORTS
    }

    private Page currentPage = Page.MAIN_DASHBOARD;
    private ManufacturingOrder currentManufacturingOrder;
    private DeliveryOrder currentDeliveryOrder;

    public ManufacturingController() {
        this.manufacturingService = ManufacturingService.getInstance();
        this.deliveryOrderService = DeliveryOrderService.getInstance();
        this.printService = new ManufacturingPrintService();
    }
    
    /**
     * إنشاء موديول التصنيع - نافذة واحدة متكاملة
     */
    public VBox createManufacturingModule() {
        mainContainer = new VBox();
        mainContainer.getStyleClass().add("manufacturing-module");

        // شريط التنقل
        navigationBar = createNavigationBar();

        // منطقة المحتوى
        contentArea = new VBox();
        contentArea.setPadding(new Insets(20));
        VBox.setVgrow(contentArea, Priority.ALWAYS);

        mainContainer.getChildren().addAll(navigationBar, contentArea);

        // عرض الصفحة الرئيسية
        showMainDashboard();

        return mainContainer;
    }

    /**
     * إنشاء شريط التنقل
     */
    private HBox createNavigationBar() {
        HBox navBar = new HBox(15);
        navBar.setPadding(new Insets(10, 20, 10, 20));
        navBar.setAlignment(Pos.CENTER_LEFT);
        navBar.getStyleClass().add("navigation-bar");
        navBar.setStyle("-fx-background-color: #f8f9fa; -fx-border-color: #dee2e6; -fx-border-width: 0 0 1 0;");

        // زر العودة
        Button backButton = new Button();
        FontAwesomeIconView backIcon = new FontAwesomeIconView(FontAwesomeIcon.ARROW_LEFT);
        backIcon.setSize("16px");
        backButton.setGraphic(backIcon);
        backButton.getStyleClass().add("nav-button");
        backButton.setOnAction(e -> navigateBack());

        // عنوان الصفحة الحالية
        currentPageLabel = new Label("🔧 موديول التصنيع");
        currentPageLabel.getStyleClass().add("page-title");
        currentPageLabel.setStyle("-fx-font-size: 16px; -fx-font-weight: bold;");

        // مساحة فارغة للتوسيط
        Region spacer = new Region();
        HBox.setHgrow(spacer, Priority.ALWAYS);

        navBar.getChildren().addAll(backButton, currentPageLabel, spacer);
        return navBar;
    }

    /**
     * التنقل للخلف
     */
    private void navigateBack() {
        switch (currentPage) {
            case MANUFACTURING_ORDER_FORM:
            case MANUFACTURING_REPORTS:
                showManufacturingOrdersPage();
                break;
            case DELIVERY_ORDER_FORM:
                showDeliveryOrdersPage();
                break;
            case MANUFACTURING_ORDERS:
            case DELIVERY_ORDERS:
                showMainDashboard();
                break;
            default:
                showMainDashboard();
                break;
        }
    }

    /**
     * عرض الصفحة الرئيسية
     */
    private void showMainDashboard() {
        currentPage = Page.MAIN_DASHBOARD;
        currentPageLabel.setText("🔧 موديول التصنيع");

        contentArea.getChildren().clear();

        // العنوان والوصف
        Label titleLabel = new Label("🔧 موديول التصنيع");
        titleLabel.getStyleClass().add("module-title");

        Label descriptionLabel = new Label("نظام إدارة أوامر التصنيع والإنتاج للزجاج والألومنيوم");
        descriptionLabel.getStyleClass().add("module-description");
        descriptionLabel.setWrapText(true);

        // الإحصائيات السريعة
        HBox statsBox = createQuickStatsBox();

        // أزرار الوظائف الرئيسية
        GridPane buttonsGrid = createMainButtonsGrid();

        contentArea.getChildren().addAll(titleLabel, descriptionLabel, statsBox, buttonsGrid);
    }

    /**
     * إنشاء صندوق الإحصائيات السريعة
     */
    private HBox createQuickStatsBox() {
        HBox statsBox = new HBox(20);
        statsBox.setAlignment(Pos.CENTER);
        statsBox.setPadding(new Insets(20));
        statsBox.getStyleClass().add("stats-container");

        // إحصائيات أوامر التصنيع
        Map<String, Object> manufacturingStats = manufacturingService.getManufacturingStatistics();

        VBox totalOrdersCard = createStatCard(
            "إجمالي أوامر التصنيع",
            String.valueOf(manufacturingStats.getOrDefault("إجمالي الأوامر", 0)),
            FontAwesomeIcon.INDUSTRY,
            "#3498db"
        );

        VBox inProgressCard = createStatCard(
            "قيد التنفيذ",
            String.valueOf(manufacturingStats.getOrDefault("أوامر قيد التنفيذ", 0)),
            FontAwesomeIcon.COG,
            "#e74c3c"
        );

        VBox completedCard = createStatCard(
            "مكتملة",
            String.valueOf(manufacturingStats.getOrDefault("أوامر مكتمل", 0)),
            FontAwesomeIcon.CHECK_CIRCLE,
            "#2ecc71"
        );

        // إحصائيات أوامر التسليم
        Map<String, Integer> deliveryStats = deliveryOrderService.getDeliveryOrderStatistics();

        VBox deliveryCard = createStatCard(
            "أوامر التسليم",
            String.valueOf(deliveryStats.getOrDefault("إجمالي الأوامر", 0)),
            FontAwesomeIcon.TRUCK,
            "#f39c12"
        );

        statsBox.getChildren().addAll(totalOrdersCard, inProgressCard, completedCard, deliveryCard);
        return statsBox;
    }

    /**
     * إنشاء بطاقة إحصائية
     */
    private VBox createStatCard(String title, String value, FontAwesomeIcon icon, String color) {
        VBox card = new VBox(10);
        card.setAlignment(Pos.CENTER);
        card.setPrefSize(180, 100);
        card.getStyleClass().add("stat-card");
        card.setStyle("-fx-background-color: white; -fx-border-color: " + color + "; -fx-border-width: 2; -fx-border-radius: 8; -fx-background-radius: 8; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 5, 0, 0, 2);");

        FontAwesomeIconView iconView = new FontAwesomeIconView(icon);
        iconView.setSize("24px");
        iconView.setStyle("-fx-fill: " + color + ";");

        Label valueLabel = new Label(value);
        valueLabel.getStyleClass().add("stat-value");
        valueLabel.setStyle("-fx-text-fill: " + color + "; -fx-font-size: 20px; -fx-font-weight: bold;");

        Label titleLabel = new Label(title);
        titleLabel.getStyleClass().add("stat-title");
        titleLabel.setStyle("-fx-font-size: 12px; -fx-text-fill: #495057;");
        titleLabel.setWrapText(true);
        titleLabel.setTextAlignment(javafx.scene.text.TextAlignment.CENTER);

        card.getChildren().addAll(iconView, valueLabel, titleLabel);
        return card;
    }

    /**
     * إنشاء شبكة الأزرار الرئيسية
     */
    private GridPane createMainButtonsGrid() {
        GridPane grid = new GridPane();
        grid.setHgap(20);
        grid.setVgap(20);
        grid.setAlignment(Pos.CENTER);
        grid.setPadding(new Insets(30));

        // الصف الأول
        Button manufacturingOrdersBtn = createMainButton(
            "📋 أوامر التصنيع",
            "إدارة وإنشاء أوامر التصنيع",
            FontAwesomeIcon.CLIPBOARD,
            "#007bff"
        );
        manufacturingOrdersBtn.setOnAction(e -> showManufacturingOrdersPage());

        Button deliveryOrdersBtn = createMainButton(
            "🚚 أوامر التسليم",
            "إدارة وإنشاء أوامر التسليم",
            FontAwesomeIcon.TRUCK,
            "#28a745"
        );
        deliveryOrdersBtn.setOnAction(e -> showDeliveryOrdersPage());

        Button reportsBtn = createMainButton(
            "📊 التقارير",
            "تقارير شاملة لأوامر التصنيع",
            FontAwesomeIcon.BAR_CHART,
            "#17a2b8"
        );
        reportsBtn.setOnAction(e -> showManufacturingReportsPage());

        // الصف الثاني
        Button settingsBtn = createMainButton(
            "⚙️ الإعدادات",
            "إعدادات موديول التصنيع",
            FontAwesomeIcon.COG,
            "#6c757d"
        );
        settingsBtn.setOnAction(e -> showSettingsPage());

        Button helpBtn = createMainButton(
            "❓ المساعدة",
            "دليل الاستخدام والمساعدة",
            FontAwesomeIcon.QUESTION_CIRCLE,
            "#ffc107"
        );
        helpBtn.setOnAction(e -> showHelpPage());

        Button backupBtn = createMainButton(
            "💾 النسخ الاحتياطي",
            "إنشاء واستعادة النسخ الاحتياطية",
            FontAwesomeIcon.DATABASE,
            "#dc3545"
        );
        backupBtn.setOnAction(e -> showBackupPage());

        // ترتيب الأزرار في الشبكة
        grid.add(manufacturingOrdersBtn, 0, 0);
        grid.add(deliveryOrdersBtn, 1, 0);
        grid.add(reportsBtn, 2, 0);
        grid.add(settingsBtn, 0, 1);
        grid.add(helpBtn, 1, 1);
        grid.add(backupBtn, 2, 1);

        return grid;
    }

    /**
     * إنشاء زر رئيسي
     */
    private Button createMainButton(String title, String description, FontAwesomeIcon icon, String color) {
        Button button = new Button();
        button.setPrefSize(200, 120);
        button.getStyleClass().add("main-function-button");

        VBox content = new VBox(8);
        content.setAlignment(Pos.CENTER);

        FontAwesomeIconView iconView = new FontAwesomeIconView(icon);
        iconView.setSize("32px");
        iconView.setStyle("-fx-fill: " + color + ";");

        Label titleLabel = new Label(title);
        titleLabel.setStyle("-fx-font-size: 14px; -fx-font-weight: bold; -fx-text-fill: #2c3e50;");
        titleLabel.setWrapText(true);
        titleLabel.setTextAlignment(javafx.scene.text.TextAlignment.CENTER);

        Label descLabel = new Label(description);
        descLabel.setStyle("-fx-font-size: 10px; -fx-text-fill: #6c757d;");
        descLabel.setWrapText(true);
        descLabel.setTextAlignment(javafx.scene.text.TextAlignment.CENTER);

        content.getChildren().addAll(iconView, titleLabel, descLabel);
        button.setGraphic(content);

        // تأثيرات التفاعل
        button.setOnMouseEntered(e -> button.setStyle("-fx-background-color: " + color + "20; -fx-border-color: " + color + "; -fx-border-width: 2; -fx-border-radius: 8; -fx-background-radius: 8;"));
        button.setOnMouseExited(e -> button.setStyle("-fx-background-color: white; -fx-border-color: #dee2e6; -fx-border-width: 1; -fx-border-radius: 8; -fx-background-radius: 8;"));

        return button;
    }

    /**
     * عرض صفحة أوامر التصنيع
     */
    private void showManufacturingOrdersPage() {
        currentPage = Page.MANUFACTURING_ORDERS;
        currentPageLabel.setText("📋 أوامر التصنيع");

        contentArea.getChildren().clear();

        ManufacturingOrderController orderController = new ManufacturingOrderController();
        VBox orderPage = orderController.createManufacturingOrdersPage(
            this::showManufacturingOrderForm,
            this::showManufacturingReportsPage,
            this::printManufacturingOrder
        );

        contentArea.getChildren().add(orderPage);
    }

    /**
     * عرض نموذج أمر التصنيع
     */
    private void showManufacturingOrderForm(ManufacturingOrder order) {
        currentPage = Page.MANUFACTURING_ORDER_FORM;
        currentPageLabel.setText(order == null ? "📝 أمر تصنيع جديد" : "📝 تعديل أمر التصنيع");
        currentManufacturingOrder = order;

        contentArea.getChildren().clear();

        ManufacturingOrderFormController formController = new ManufacturingOrderFormController(manufacturingService);
        VBox formPage = new VBox();
        Label placeholder = new Label("نموذج أمر التصنيع قيد التطوير");
        formPage.getChildren().add(placeholder);

        contentArea.getChildren().add(formPage);
    }

    /**
     * عرض صفحة أوامر التسليم
     */
    private void showDeliveryOrdersPage() {
        currentPage = Page.DELIVERY_ORDERS;
        currentPageLabel.setText("🚚 أوامر التسليم");

        contentArea.getChildren().clear();

        DeliveryOrderController deliveryController = new DeliveryOrderController(deliveryOrderService);
        VBox deliveryPage = new VBox();
        Label placeholder = new Label("صفحة أوامر التسليم قيد التطوير");
        deliveryPage.getChildren().add(placeholder);

        contentArea.getChildren().add(deliveryPage);
    }

    /**
     * عرض نموذج أمر التسليم
     */
    private void showDeliveryOrderForm(DeliveryOrder order) {
        currentPage = Page.DELIVERY_ORDER_FORM;
        currentPageLabel.setText(order == null ? "📝 أمر تسليم جديد" : "📝 تعديل أمر التسليم");
        currentDeliveryOrder = order;

        contentArea.getChildren().clear();

        DeliveryOrderFormController formController = new DeliveryOrderFormController(deliveryOrderService);
        VBox formPage = new VBox();
        Label placeholder = new Label("نموذج أمر التسليم قيد التطوير");
        formPage.getChildren().add(placeholder);

        contentArea.getChildren().add(formPage);
    }

    /**
     * عرض صفحة تقارير التصنيع
     */
    private void showManufacturingReportsPage() {
        currentPage = Page.MANUFACTURING_REPORTS;
        currentPageLabel.setText("📊 تقارير التصنيع");

        contentArea.getChildren().clear();

        ManufacturingReportsController reportsController = new ManufacturingReportsController();
        VBox reportsPage = new VBox();
        Label placeholder = new Label("صفحة التقارير قيد التطوير");
        reportsPage.getChildren().add(placeholder);

        contentArea.getChildren().add(reportsPage);
    }

    /**
     * عند حفظ أمر التصنيع
     */
    private void onManufacturingOrderSaved(ManufacturingOrder order) {
        showManufacturingOrdersPage();
        showSuccessAlert("تم حفظ أمر التصنيع بنجاح!");
    }

    /**
     * عند حفظ أمر التسليم
     */
    private void onDeliveryOrderSaved(DeliveryOrder order) {
        showDeliveryOrdersPage();
        showSuccessAlert("تم حفظ أمر التسليم بنجاح!");
    }

    /**
     * طباعة أمر التصنيع
     */
    private void printManufacturingOrder(ManufacturingOrder order) {
        if (order == null) {
            showErrorAlert("لا يوجد أمر للطباعة.");
            return;
        }

        Stage stage = (Stage) mainContainer.getScene().getWindow();
        boolean success = printService.printManufacturingOrder(order, stage);

        if (success) {
            showSuccessAlert("تم حفظ التقرير بنجاح!");
        }
    }

    /**
     * طباعة أمر التسليم
     */
    private void printDeliveryOrder(DeliveryOrder order) {
        if (order == null) {
            showErrorAlert("لا يوجد أمر للطباعة.");
            return;
        }

        // سيتم تطوير خدمة طباعة أوامر التسليم لاحقاً
        showInfoAlert("ميزة طباعة أوامر التسليم ستكون متاحة قريباً.");
    }

    /**
     * عرض صفحة الإعدادات
     */
    private void showSettingsPage() {
        showInfoAlert("صفحة الإعدادات قيد التطوير.");
    }

    /**
     * عرض صفحة المساعدة
     */
    private void showHelpPage() {
        showInfoAlert("دليل المساعدة قيد التطوير.");
    }

    /**
     * عرض صفحة النسخ الاحتياطي
     */
    private void showBackupPage() {
        showInfoAlert("ميزة النسخ الاحتياطي قيد التطوير.");
    }

    // رسائل التنبيه
    private void showSuccessAlert(String message) {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle("نجح");
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }

    private void showErrorAlert(String message) {
        Alert alert = new Alert(Alert.AlertType.ERROR);
        alert.setTitle("خطأ");
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }

    private void showInfoAlert(String message) {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle("معلومات");
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }
}
