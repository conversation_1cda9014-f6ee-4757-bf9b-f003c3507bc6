package com.accounting.controller;

import com.accounting.model.ManufacturingOrder;
import com.accounting.service.ManufacturingService;
import javafx.geometry.Insets;
import javafx.geometry.Pos;
import javafx.scene.control.*;
import javafx.scene.layout.*;
import de.jensd.fx.glyphs.fontawesome.FontAwesomeIcon;
import de.jensd.fx.glyphs.fontawesome.FontAwesomeIconView;
import java.text.DecimalFormat;

/**
 * كنترولر موديول التصنيع الرئيسي
 */
public class ManufacturingController {
    
    private final ManufacturingService manufacturingService;
    private final DecimalFormat decimalFormat = new DecimalFormat("#,##0.00");
    
    public ManufacturingController() {
        this.manufacturingService = ManufacturingService.getInstance();
    }
    
    /**
     * إنشاء واجهة موديول التصنيع الرئيسية
     */
    public VBox createManufacturingModule() {
        VBox mainContainer = new VBox(20);
        mainContainer.setPadding(new Insets(20));
        
        // العنوان الرئيسي
        Label titleLabel = new Label("🔧 موديول التصنيع");
        titleLabel.getStyleClass().add("module-title");
        
        // وصف الموديول
        Label descriptionLabel = new Label("نظام إدارة أوامر التصنيع والإنتاج للزجاج والألومنيوم");
        descriptionLabel.getStyleClass().add("module-description");
        descriptionLabel.setWrapText(true);
        
        // منطقة الأزرار الرئيسية
        GridPane buttonsGrid = createMainButtonsGrid();
        
        // منطقة الإحصائيات السريعة
        HBox statsBox = createQuickStatsBox();
        
        // منطقة الوصول السريع
        VBox quickAccessBox = createQuickAccessBox();
        
        mainContainer.getChildren().addAll(
            titleLabel, 
            descriptionLabel, 
            new Separator(),
            buttonsGrid,
            new Separator(),
            statsBox,
            new Separator(),
            quickAccessBox
        );
        
        return mainContainer;
    }
    
    /**
     * إنشاء شبكة الأزرار الرئيسية
     */
    private GridPane createMainButtonsGrid() {
        GridPane grid = new GridPane();
        grid.setHgap(20);
        grid.setVgap(20);
        grid.setAlignment(Pos.CENTER);
        
        // الصف الأول
        Button manufacturingOrderBtn = createModuleButton(
            "📋 Manufacturing Order", 
            "إنشاء وإدارة أوامر التصنيع",
            FontAwesomeIcon.INDUSTRY,
            this::showManufacturingOrder
        );
        
        Button costCalculationBtn = createModuleButton(
            "💰 تكلفة تصنيع", 
            "حساب تكاليف التصنيع والمواد",
            FontAwesomeIcon.CALCULATOR,
            this::showCostCalculation
        );
        
        Button deliveryOrderBtn = createModuleButton(
            "📤 أمر تسليم", 
            "إدارة أوامر التسليم للعملاء",
            FontAwesomeIcon.TRUCK,
            this::showDeliveryOrder
        );
        
        // الصف الثاني
        Button receiptOrderBtn = createModuleButton(
            "📥 أمر استلام", 
            "إدارة أوامر استلام المواد",
            FontAwesomeIcon.DOWNLOAD,
            this::showReceiptOrder
        );
        
        Button reportsBtn = createModuleButton(
            "📊 تقرير أوامر التصنيع", 
            "تقارير شاملة لأوامر التصنيع",
            FontAwesomeIcon.BAR_CHART,
            this::showManufacturingReports
        );
        
        Button settingsBtn = createModuleButton(
            "⚙️ إعدادات التصنيع", 
            "إعدادات الخدمات والمواد",
            FontAwesomeIcon.COG,
            this::showManufacturingSettings
        );
        
        // ترتيب الأزرار في الشبكة
        grid.add(manufacturingOrderBtn, 0, 0);
        grid.add(costCalculationBtn, 1, 0);
        grid.add(deliveryOrderBtn, 2, 0);
        grid.add(receiptOrderBtn, 0, 1);
        grid.add(reportsBtn, 1, 1);
        grid.add(settingsBtn, 2, 1);
        
        return grid;
    }
    
    /**
     * إنشاء زر موديول
     */
    private Button createModuleButton(String title, String description, FontAwesomeIcon icon, Runnable action) {
        VBox buttonContent = new VBox(10);
        buttonContent.setAlignment(Pos.CENTER);
        buttonContent.setPrefSize(200, 120);
        
        FontAwesomeIconView iconView = new FontAwesomeIconView(icon);
        iconView.setSize("32px");
        iconView.getStyleClass().add("module-icon");
        
        Label titleLabel = new Label(title);
        titleLabel.getStyleClass().add("module-button-title");
        titleLabel.setWrapText(true);
        
        Label descLabel = new Label(description);
        descLabel.getStyleClass().add("module-button-description");
        descLabel.setWrapText(true);
        
        buttonContent.getChildren().addAll(iconView, titleLabel, descLabel);
        
        Button button = new Button();
        button.setGraphic(buttonContent);
        button.getStyleClass().add("module-button");
        button.setOnAction(e -> action.run());
        
        return button;
    }
    
    /**
     * إنشاء صندوق الإحصائيات السريعة
     */
    private HBox createQuickStatsBox() {
        HBox statsBox = new HBox(20);
        statsBox.setAlignment(Pos.CENTER);
        statsBox.setPadding(new Insets(20));
        statsBox.getStyleClass().add("stats-container");
        
        // إحصائيات أوامر التصنيع
        var stats = manufacturingService.getManufacturingStatistics();
        
        VBox totalOrdersStats = createStatCard(
            "إجمالي الأوامر", 
            String.valueOf(stats.get("إجمالي الأوامر")),
            FontAwesomeIcon.INDUSTRY,
            "#3498db"
        );
        
        VBox draftOrdersStats = createStatCard(
            "أوامر مسودة", 
            String.valueOf(stats.get("أوامر مسودة")),
            FontAwesomeIcon.EDIT,
            "#f39c12"
        );
        
        VBox inProgressStats = createStatCard(
            "قيد التنفيذ", 
            String.valueOf(stats.get("أوامر قيد التنفيذ")),
            FontAwesomeIcon.COG,
            "#e74c3c"
        );
        
        VBox completedStats = createStatCard(
            "مكتملة", 
            String.valueOf(stats.get("أوامر مكتمل")),
            FontAwesomeIcon.CHECK_CIRCLE,
            "#2ecc71"
        );
        
        statsBox.getChildren().addAll(totalOrdersStats, draftOrdersStats, inProgressStats, completedStats);
        return statsBox;
    }
    
    /**
     * إنشاء بطاقة إحصائية
     */
    private VBox createStatCard(String title, String value, FontAwesomeIcon icon, String color) {
        VBox card = new VBox(10);
        card.setAlignment(Pos.CENTER);
        card.setPrefSize(150, 100);
        card.getStyleClass().add("stat-card");
        
        FontAwesomeIconView iconView = new FontAwesomeIconView(icon);
        iconView.setSize("24px");
        iconView.setStyle("-fx-fill: " + color + ";");
        
        Label valueLabel = new Label(value);
        valueLabel.getStyleClass().add("stat-value");
        valueLabel.setStyle("-fx-text-fill: " + color + ";");
        
        Label titleLabel = new Label(title);
        titleLabel.getStyleClass().add("stat-title");
        
        card.getChildren().addAll(iconView, valueLabel, titleLabel);
        return card;
    }
    
    /**
     * إنشاء صندوق الوصول السريع
     */
    private VBox createQuickAccessBox() {
        VBox quickAccessBox = new VBox(15);
        quickAccessBox.setPadding(new Insets(20));
        quickAccessBox.getStyleClass().add("quick-access-container");
        
        Label titleLabel = new Label("🚀 الوصول السريع");
        titleLabel.getStyleClass().add("section-title");
        
        HBox buttonsBox = new HBox(15);
        buttonsBox.setAlignment(Pos.CENTER);
        
        Button newOrderBtn = createQuickButton("أمر جديد", FontAwesomeIcon.PLUS, this::createNewManufacturingOrder);
        Button todayOrdersBtn = createQuickButton("أوامر اليوم", FontAwesomeIcon.CALENDAR, this::showTodayOrders);
        Button urgentOrdersBtn = createQuickButton("أوامر عاجلة", FontAwesomeIcon.EXCLAMATION, this::showUrgentOrders);
        Button backupBtn = createQuickButton("نسخ احتياطي", FontAwesomeIcon.DOWNLOAD, this::createBackup);
        
        buttonsBox.getChildren().addAll(newOrderBtn, todayOrdersBtn, urgentOrdersBtn, backupBtn);
        
        quickAccessBox.getChildren().addAll(titleLabel, buttonsBox);
        return quickAccessBox;
    }
    
    /**
     * إنشاء زر وصول سريع
     */
    private Button createQuickButton(String text, FontAwesomeIcon icon, Runnable action) {
        Button button = new Button(text);
        FontAwesomeIconView iconView = new FontAwesomeIconView(icon);
        iconView.setSize("14px");
        button.setGraphic(iconView);
        button.getStyleClass().add("quick-access-button");
        button.setOnAction(e -> action.run());
        return button;
    }
    
    // وظائف الأزرار الرئيسية
    private void showManufacturingOrder() {
        ManufacturingOrderController controller = new ManufacturingOrderController();
        controller.showManufacturingOrderDialog();
    }
    
    private void showCostCalculation() {
        showPlaceholder("تكلفة تصنيع");
    }
    
    private void showDeliveryOrder() {
        showPlaceholder("أمر تسليم");
    }
    
    private void showReceiptOrder() {
        showPlaceholder("أمر استلام");
    }
    
    private void showManufacturingReports() {
        ManufacturingReportsController controller = new ManufacturingReportsController();
        controller.showManufacturingReportsDialog();
    }
    
    private void showManufacturingSettings() {
        showPlaceholder("إعدادات التصنيع");
    }
    
    // وظائف الوصول السريع
    private void createNewManufacturingOrder() {
        ManufacturingOrderController controller = new ManufacturingOrderController();
        controller.showNewManufacturingOrderDialog();
    }
    
    private void showTodayOrders() {
        showPlaceholder("أوامر اليوم");
    }
    
    private void showUrgentOrders() {
        showPlaceholder("أوامر عاجلة");
    }
    
    private void createBackup() {
        showBackupDialog();
    }
    
    /**
     * عرض نافذة النسخ الاحتياطي
     */
    private void showBackupDialog() {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle("النسخ الاحتياطي");
        alert.setHeaderText("إنشاء نسخة احتياطية");
        alert.setContentText("تم إنشاء نسخة احتياطية من بيانات التصنيع بنجاح!");
        alert.showAndWait();
    }
    
    /**
     * عرض شاشة مؤقتة للوظائف قيد التطوير
     */
    private void showPlaceholder(String featureName) {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle(featureName);
        alert.setHeaderText("قيد التطوير");
        alert.setContentText("هذه الوظيفة (" + featureName + ") قيد التطوير وستكون متاحة قريباً.");
        alert.showAndWait();
    }
}
