package com.accounting.controller;

import com.accounting.model.*;
import com.accounting.service.*;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.geometry.Insets;
import javafx.geometry.Pos;
import javafx.scene.Scene;
import javafx.scene.control.*;
import javafx.scene.control.cell.PropertyValueFactory;
import javafx.scene.layout.*;
import javafx.stage.FileChooser;
import javafx.stage.Modality;
import javafx.stage.Stage;
import de.jensd.fx.glyphs.fontawesome.FontAwesomeIcon;
import de.jensd.fx.glyphs.fontawesome.FontAwesomeIconView;

import java.io.File;
import java.text.DecimalFormat;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

/**
 * كونترولر قائمة الدخل
 * Income Statement Controller
 */
public class IncomeStatementController {
    
    private final IncomeStatementService incomeStatementService;
    private final DecimalFormat decimalFormat = new DecimalFormat("#,##0.00");
    
    // مكونات الواجهة
    private VBox mainContainer;
    private VBox contentArea;
    private HBox navigationBar;
    private Label currentPageLabel;
    
    // الصفحات المختلفة
    private enum Page {
        MAIN_DASHBOARD,
        INCOME_STATEMENTS_LIST,
        INCOME_STATEMENT_FORM,
        INCOME_STATEMENT_VIEW
    }
    
    private Page currentPage = Page.MAIN_DASHBOARD;
    private IncomeStatement currentStatement;
    
    public IncomeStatementController() {
        this.incomeStatementService = IncomeStatementService.getInstance();
    }
    
    /**
     * عرض نافذة قائمة الدخل
     */
    public void showIncomeStatementDialog() {
        Stage dialog = new Stage();
        dialog.setTitle("📊 قائمة الدخل");
        dialog.initModality(Modality.APPLICATION_MODAL);
        
        // تحديد حجم النافذة
        javafx.stage.Screen screen = javafx.stage.Screen.getPrimary();
        double screenHeight = screen.getVisualBounds().getHeight();
        double screenWidth = screen.getVisualBounds().getWidth();
        
        dialog.setWidth(Math.min(1600, screenWidth * 0.95));
        dialog.setHeight(Math.min(1000, screenHeight * 0.9));
        dialog.setResizable(true);
        
        // إنشاء الواجهة الرئيسية
        createMainInterface();
        
        Scene scene = new Scene(mainContainer);
        scene.getStylesheets().add(getClass().getResource("/css/styles.css").toExternalForm());
        dialog.setScene(scene);
        dialog.showAndWait();
    }
    
    /**
     * إنشاء الواجهة الرئيسية
     */
    private void createMainInterface() {
        mainContainer = new VBox(0);
        mainContainer.getStyleClass().add("main-container");
        
        // شريط التنقل
        navigationBar = createNavigationBar();
        
        // منطقة المحتوى
        contentArea = new VBox(20);
        contentArea.setPadding(new Insets(20));
        VBox.setVgrow(contentArea, Priority.ALWAYS);
        
        mainContainer.getChildren().addAll(navigationBar, contentArea);
        
        // عرض الصفحة الرئيسية
        showMainDashboard();
    }
    
    /**
     * إنشاء شريط التنقل
     */
    private HBox createNavigationBar() {
        HBox navBar = new HBox(15);
        navBar.setPadding(new Insets(15));
        navBar.setAlignment(Pos.CENTER_LEFT);
        navBar.getStyleClass().add("navigation-bar");
        
        // زر الرجوع
        Button backBtn = createNavButton("رجوع", FontAwesomeIcon.ARROW_LEFT, this::goBack);
        backBtn.setDisable(currentPage == Page.MAIN_DASHBOARD);
        
        // عنوان الصفحة الحالية
        currentPageLabel = new Label("📊 قائمة الدخل - الصفحة الرئيسية");
        currentPageLabel.getStyleClass().add("page-title");
        
        // فاصل
        Region spacer = new Region();
        HBox.setHgrow(spacer, Priority.ALWAYS);
        
        // زر إغلاق
        Button closeBtn = createNavButton("إغلاق", FontAwesomeIcon.TIMES, this::closeDialog);
        
        navBar.getChildren().addAll(backBtn, currentPageLabel, spacer, closeBtn);
        return navBar;
    }
    
    /**
     * إنشاء زر التنقل
     */
    private Button createNavButton(String text, FontAwesomeIcon icon, Runnable action) {
        Button button = new Button(text);
        
        FontAwesomeIconView iconView = new FontAwesomeIconView(icon);
        iconView.setSize("14px");
        button.setGraphic(iconView);
        
        button.getStyleClass().add("nav-button");
        button.setOnAction(e -> action.run());
        
        return button;
    }
    
    /**
     * عرض الصفحة الرئيسية
     */
    private void showMainDashboard() {
        currentPage = Page.MAIN_DASHBOARD;
        currentPageLabel.setText("📊 قائمة الدخل - الصفحة الرئيسية");
        updateNavigationButtons();
        
        contentArea.getChildren().clear();
        
        // العنوان الرئيسي
        Label titleLabel = new Label("📊 قائمة الدخل");
        titleLabel.getStyleClass().add("main-title");
        
        // الوصف
        Label descLabel = new Label("إدارة وعرض قوائم الدخل لتحليل الأداء المالي للشركة");
        descLabel.getStyleClass().add("description-label");
        descLabel.setWrapText(true);
        
        // شبكة الأزرار الرئيسية
        GridPane buttonsGrid = createMainButtonsGrid();
        
        // منطقة الإحصائيات السريعة
        VBox statsBox = createQuickStatsBox();
        
        contentArea.getChildren().addAll(titleLabel, descLabel, buttonsGrid, statsBox);
    }
    
    /**
     * إنشاء شبكة الأزرار الرئيسية
     */
    private GridPane createMainButtonsGrid() {
        GridPane grid = new GridPane();
        grid.setHgap(20);
        grid.setVgap(20);
        grid.setAlignment(Pos.CENTER);
        
        // الصف الأول
        Button newStatementBtn = createMainButton(
            "📊 إنشاء قائمة دخل جديدة",
            "إنشاء قائمة دخل لفترة محددة",
            FontAwesomeIcon.PLUS_CIRCLE,
            this::showNewIncomeStatementForm
        );
        
        Button viewStatementsBtn = createMainButton(
            "📋 عرض قوائم الدخل",
            "عرض وإدارة قوائم الدخل المحفوظة",
            FontAwesomeIcon.LIST,
            this::showIncomeStatementsList
        );
        
        Button monthlyReportBtn = createMainButton(
            "📅 تقرير شهري",
            "قائمة دخل للشهر الحالي",
            FontAwesomeIcon.CALENDAR,
            this::generateMonthlyReport
        );
        
        // الصف الثاني
        Button quarterlyReportBtn = createMainButton(
            "📈 تقرير ربع سنوي",
            "قائمة دخل للربع الحالي",
            FontAwesomeIcon.BAR_CHART,
            this::generateQuarterlyReport
        );
        
        Button yearlyReportBtn = createMainButton(
            "📊 تقرير سنوي",
            "قائمة دخل للسنة الحالية",
            FontAwesomeIcon.PIE_CHART,
            this::generateYearlyReport
        );
        
        Button compareBtn = createMainButton(
            "⚖️ مقارنة الفترات",
            "مقارنة قوائم الدخل لفترات مختلفة",
            FontAwesomeIcon.BALANCE_SCALE,
            this::showComparisonReport
        );
        
        // ترتيب الأزرار في الشبكة
        grid.add(newStatementBtn, 0, 0);
        grid.add(viewStatementsBtn, 1, 0);
        grid.add(monthlyReportBtn, 2, 0);
        grid.add(quarterlyReportBtn, 0, 1);
        grid.add(yearlyReportBtn, 1, 1);
        grid.add(compareBtn, 2, 1);
        
        return grid;
    }
    
    /**
     * إنشاء زر رئيسي
     */
    private Button createMainButton(String title, String description, FontAwesomeIcon icon, Runnable action) {
        VBox buttonContent = new VBox(10);
        buttonContent.setAlignment(Pos.CENTER);
        buttonContent.setPadding(new Insets(20));
        
        FontAwesomeIconView iconView = new FontAwesomeIconView(icon);
        iconView.setSize("32px");
        iconView.getStyleClass().add("main-button-icon");
        
        Label titleLabel = new Label(title);
        titleLabel.getStyleClass().add("main-button-title");
        titleLabel.setWrapText(true);
        
        Label descLabel = new Label(description);
        descLabel.getStyleClass().add("main-button-description");
        descLabel.setWrapText(true);
        
        buttonContent.getChildren().addAll(iconView, titleLabel, descLabel);
        
        Button button = new Button();
        button.setGraphic(buttonContent);
        button.getStyleClass().add("main-button");
        button.setPrefSize(250, 150);
        button.setOnAction(e -> action.run());
        
        return button;
    }
    
    /**
     * إنشاء منطقة الإحصائيات السريعة
     */
    private VBox createQuickStatsBox() {
        VBox statsBox = new VBox(15);
        statsBox.getStyleClass().add("stats-box");
        statsBox.setPadding(new Insets(20));
        
        Label statsTitle = new Label("📈 إحصائيات سريعة");
        statsTitle.getStyleClass().add("stats-title");
        
        // الحصول على آخر قائمة دخل
        List<IncomeStatement> statements = incomeStatementService.getAllIncomeStatements();
        if (!statements.isEmpty()) {
            IncomeStatement latestStatement = statements.get(statements.size() - 1);
            
            HBox statsRow1 = createStatsRow("إجمالي الإيرادات:", 
                decimalFormat.format(latestStatement.getTotalRevenues()) + " ج.م");
            HBox statsRow2 = createStatsRow("إجمالي الربح:", 
                decimalFormat.format(latestStatement.getGrossProfit()) + " ج.م");
            HBox statsRow3 = createStatsRow("صافي الدخل:", 
                decimalFormat.format(latestStatement.getNetIncome()) + " ج.م");
            HBox statsRow4 = createStatsRow("نسبة صافي الربح:", 
                String.format("%.2f%%", latestStatement.getNetProfitMargin()));
            
            statsBox.getChildren().addAll(statsTitle, statsRow1, statsRow2, statsRow3, statsRow4);
        } else {
            Label noDataLabel = new Label("لا توجد قوائم دخل محفوظة");
            noDataLabel.getStyleClass().add("no-data-label");
            statsBox.getChildren().addAll(statsTitle, noDataLabel);
        }
        
        return statsBox;
    }
    
    /**
     * إنشاء صف إحصائيات
     */
    private HBox createStatsRow(String label, String value) {
        HBox row = new HBox(10);
        row.setAlignment(Pos.CENTER_LEFT);
        
        Label labelControl = new Label(label);
        labelControl.getStyleClass().add("stats-label");
        
        Region spacer = new Region();
        HBox.setHgrow(spacer, Priority.ALWAYS);
        
        Label valueControl = new Label(value);
        valueControl.getStyleClass().add("stats-value");
        
        row.getChildren().addAll(labelControl, spacer, valueControl);
        return row;
    }
    
    // وظائف الأزرار
    private void showNewIncomeStatementForm() {
        currentPage = Page.INCOME_STATEMENT_FORM;
        currentPageLabel.setText("📊 قائمة الدخل - إنشاء قائمة جديدة");
        updateNavigationButtons();

        contentArea.getChildren().clear();
        contentArea.getChildren().add(createNewIncomeStatementForm());
    }

    private void showIncomeStatementsList() {
        currentPage = Page.INCOME_STATEMENTS_LIST;
        currentPageLabel.setText("📊 قائمة الدخل - عرض القوائم المحفوظة");
        updateNavigationButtons();

        contentArea.getChildren().clear();
        contentArea.getChildren().add(createIncomeStatementsListView());
    }
    
    private void generateMonthlyReport() {
        LocalDate now = LocalDate.now();
        LocalDate fromDate = now.withDayOfMonth(1);
        LocalDate toDate = now.withDayOfMonth(now.lengthOfMonth());
        
        IncomeStatement statement = incomeStatementService.generateIncomeStatement(
            fromDate, toDate, "شركة الزجاج والألومنيوم المتقدمة");
        
        showIncomeStatementView(statement);
    }
    
    private void generateQuarterlyReport() {
        LocalDate now = LocalDate.now();
        LocalDate quarterStart = now.withDayOfMonth(1).minusMonths(2);
        LocalDate quarterEnd = now.withDayOfMonth(now.lengthOfMonth());

        IncomeStatement statement = incomeStatementService.generateIncomeStatement(
            quarterStart, quarterEnd, "شركة الزجاج والألومنيوم المتقدمة");

        showIncomeStatementView(statement);
    }

    private void generateYearlyReport() {
        LocalDate now = LocalDate.now();
        LocalDate yearStart = now.withDayOfYear(1);
        LocalDate yearEnd = now.withDayOfYear(now.lengthOfYear());

        IncomeStatement statement = incomeStatementService.generateIncomeStatement(
            yearStart, yearEnd, "شركة الزجاج والألومنيوم المتقدمة");

        showIncomeStatementView(statement);
    }

    private void showComparisonReport() {
        currentPage = Page.INCOME_STATEMENT_VIEW;
        currentPageLabel.setText("📊 قائمة الدخل - مقارنة الفترات");
        updateNavigationButtons();

        contentArea.getChildren().clear();
        contentArea.getChildren().add(createComparisonView());
    }
    
    private void showIncomeStatementView(IncomeStatement statement) {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle("عرض قائمة الدخل");
        alert.setHeaderText("تم إنشاء قائمة الدخل بنجاح");

        String content = String.format("الفترة: %s\n\nإجمالي الإيرادات: %s ج.م\nإجمالي الربح: %s ج.م\nصافي الدخل: %s ج.م\nنسبة صافي الربح: %.2f%%",
            statement.getPeriod(),
            decimalFormat.format(statement.getTotalRevenues()),
            decimalFormat.format(statement.getGrossProfit()),
            decimalFormat.format(statement.getNetIncome()),
            statement.getNetProfitMargin());

        alert.setContentText(content);

        // إضافة أزرار التصدير
        ButtonType pdfButton = new ButtonType("تصدير PDF");
        ButtonType excelButton = new ButtonType("تصدير Excel");
        ButtonType closeButton = new ButtonType("إغلاق", ButtonBar.ButtonData.CANCEL_CLOSE);

        alert.getButtonTypes().setAll(pdfButton, excelButton, closeButton);

        Optional<ButtonType> result = alert.showAndWait();
        if (result.isPresent()) {
            if (result.get() == pdfButton) {
                exportToPDF(statement);
            } else if (result.get() == excelButton) {
                exportToExcel(statement);
            }
        }
    }

    private void exportToPDF(IncomeStatement statement) {
        showInfoAlert("تصدير PDF", "ميزة تصدير PDF قيد التطوير...");
    }

    private void exportToExcel(IncomeStatement statement) {
        showInfoAlert("تصدير Excel", "ميزة تصدير Excel قيد التطوير...");
    }
    
    // وظائف التنقل
    private void goBack() {
        if (currentPage != Page.MAIN_DASHBOARD) {
            showMainDashboard();
        }
    }
    
    private void closeDialog() {
        Stage stage = (Stage) mainContainer.getScene().getWindow();
        stage.close();
    }
    
    private void updateNavigationButtons() {
        // تحديث حالة أزرار التنقل
        Button backBtn = (Button) navigationBar.getChildren().get(0);
        backBtn.setDisable(currentPage == Page.MAIN_DASHBOARD);
    }
    
    private void showInfoAlert(String title, String message) {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle(title);
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }

    private void showWarningAlert(String title, String message) {
        Alert alert = new Alert(Alert.AlertType.WARNING);
        alert.setTitle(title);
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }

    /**
     * إنشاء نموذج قائمة دخل جديدة
     */
    private VBox createNewIncomeStatementForm() {
        VBox formContainer = new VBox(20);
        formContainer.setPadding(new Insets(20));

        Label titleLabel = new Label("📊 إنشاء قائمة دخل جديدة");
        titleLabel.getStyleClass().add("main-title");

        // نموذج البيانات الأساسية
        GridPane formGrid = new GridPane();
        formGrid.setHgap(15);
        formGrid.setVgap(15);
        formGrid.setPadding(new Insets(20));
        formGrid.getStyleClass().add("form-container");

        // اسم الشركة
        Label companyLabel = new Label("اسم الشركة:");
        TextField companyField = new TextField("شركة الزجاج والألومنيوم المتقدمة");

        // من تاريخ
        Label fromDateLabel = new Label("من تاريخ:");
        DatePicker fromDatePicker = new DatePicker(LocalDate.now().withDayOfMonth(1));

        // إلى تاريخ
        Label toDateLabel = new Label("إلى تاريخ:");
        DatePicker toDatePicker = new DatePicker(LocalDate.now());

        // ترتيب العناصر
        formGrid.add(companyLabel, 0, 0);
        formGrid.add(companyField, 1, 0);
        formGrid.add(fromDateLabel, 0, 1);
        formGrid.add(fromDatePicker, 1, 1);
        formGrid.add(toDateLabel, 0, 2);
        formGrid.add(toDatePicker, 1, 2);

        // أزرار الإجراءات
        HBox buttonsBox = new HBox(15);
        buttonsBox.setAlignment(Pos.CENTER);
        buttonsBox.setPadding(new Insets(20));

        Button generateBtn = new Button("إنشاء قائمة الدخل");
        generateBtn.getStyleClass().add("primary-button");
        generateBtn.setOnAction(e -> {
            String companyName = companyField.getText();
            LocalDate fromDate = fromDatePicker.getValue();
            LocalDate toDate = toDatePicker.getValue();

            if (companyName.trim().isEmpty() || fromDate == null || toDate == null) {
                showWarningAlert("بيانات ناقصة", "يرجى ملء جميع الحقول المطلوبة.");
                return;
            }

            if (fromDate.isAfter(toDate)) {
                showWarningAlert("خطأ في التاريخ", "تاريخ البداية يجب أن يكون قبل تاريخ النهاية.");
                return;
            }

            IncomeStatement statement = incomeStatementService.generateIncomeStatement(
                fromDate, toDate, companyName);
            showIncomeStatementView(statement);
        });

        Button cancelBtn = new Button("إلغاء");
        cancelBtn.getStyleClass().add("secondary-button");
        cancelBtn.setOnAction(e -> showMainDashboard());

        buttonsBox.getChildren().addAll(generateBtn, cancelBtn);

        formContainer.getChildren().addAll(titleLabel, formGrid, buttonsBox);
        return formContainer;
    }

    /**
     * إنشاء عرض قوائم الدخل المحفوظة
     */
    private VBox createIncomeStatementsListView() {
        VBox listContainer = new VBox(20);
        listContainer.setPadding(new Insets(20));

        Label titleLabel = new Label("📋 قوائم الدخل المحفوظة");
        titleLabel.getStyleClass().add("main-title");

        // شريط البحث والفلاتر
        HBox searchBox = new HBox(15);
        searchBox.setAlignment(Pos.CENTER_LEFT);
        searchBox.setPadding(new Insets(10));

        TextField searchField = new TextField();
        searchField.setPromptText("البحث في قوائم الدخل...");
        searchField.setPrefWidth(200);

        DatePicker fromDateFilter = new DatePicker();
        fromDateFilter.setPromptText("من تاريخ");

        DatePicker toDateFilter = new DatePicker();
        toDateFilter.setPromptText("إلى تاريخ");

        Button searchBtn = new Button("بحث");
        searchBtn.getStyleClass().add("primary-button");

        searchBox.getChildren().addAll(
            new Label("البحث:"), searchField,
            new Label("من:"), fromDateFilter,
            new Label("إلى:"), toDateFilter,
            searchBtn
        );

        // جدول قوائم الدخل
        TableView<IncomeStatement> statementsTable = new TableView<>();

        TableColumn<IncomeStatement, String> idCol = new TableColumn<>("رقم القائمة");
        idCol.setCellValueFactory(new PropertyValueFactory<>("statementId"));
        idCol.setPrefWidth(120);

        TableColumn<IncomeStatement, String> companyCol = new TableColumn<>("الشركة");
        companyCol.setCellValueFactory(new PropertyValueFactory<>("companyName"));
        companyCol.setPrefWidth(200);

        TableColumn<IncomeStatement, String> periodCol = new TableColumn<>("الفترة");
        periodCol.setCellValueFactory(new PropertyValueFactory<>("period"));
        periodCol.setPrefWidth(150);

        TableColumn<IncomeStatement, Double> revenueCol = new TableColumn<>("إجمالي الإيرادات");
        revenueCol.setCellValueFactory(new PropertyValueFactory<>("totalRevenues"));
        revenueCol.setPrefWidth(120);
        revenueCol.setCellFactory(col -> new TableCell<IncomeStatement, Double>() {
            @Override
            protected void updateItem(Double amount, boolean empty) {
                super.updateItem(amount, empty);
                if (empty || amount == null) {
                    setText("");
                } else {
                    setText(decimalFormat.format(amount));
                    setStyle("-fx-alignment: CENTER-RIGHT;");
                }
            }
        });

        TableColumn<IncomeStatement, Double> netIncomeCol = new TableColumn<>("صافي الدخل");
        netIncomeCol.setCellValueFactory(new PropertyValueFactory<>("netIncome"));
        netIncomeCol.setPrefWidth(120);
        netIncomeCol.setCellFactory(col -> new TableCell<IncomeStatement, Double>() {
            @Override
            protected void updateItem(Double amount, boolean empty) {
                super.updateItem(amount, empty);
                if (empty || amount == null) {
                    setText("");
                } else {
                    setText(decimalFormat.format(amount));
                    setStyle("-fx-alignment: CENTER-RIGHT;");
                    if (amount < 0) {
                        setStyle("-fx-alignment: CENTER-RIGHT; -fx-text-fill: red;");
                    } else {
                        setStyle("-fx-alignment: CENTER-RIGHT; -fx-text-fill: green;");
                    }
                }
            }
        });

        TableColumn<IncomeStatement, Void> actionsCol = new TableColumn<>("الإجراءات");
        actionsCol.setPrefWidth(150);
        actionsCol.setCellFactory(col -> new TableCell<IncomeStatement, Void>() {
            private final Button viewBtn = new Button("عرض");
            private final Button deleteBtn = new Button("حذف");

            {
                viewBtn.getStyleClass().add("view-button");
                deleteBtn.getStyleClass().add("delete-button");

                viewBtn.setOnAction(e -> {
                    IncomeStatement statement = getTableView().getItems().get(getIndex());
                    showIncomeStatementView(statement);
                });

                deleteBtn.setOnAction(e -> {
                    IncomeStatement statement = getTableView().getItems().get(getIndex());
                    Alert confirmAlert = new Alert(Alert.AlertType.CONFIRMATION);
                    confirmAlert.setTitle("تأكيد الحذف");
                    confirmAlert.setHeaderText(null);
                    confirmAlert.setContentText("هل أنت متأكد من حذف قائمة الدخل هذه؟");

                    Optional<ButtonType> result = confirmAlert.showAndWait();
                    if (result.isPresent() && result.get() == ButtonType.OK) {
                        incomeStatementService.deleteIncomeStatement(statement.getStatementId());
                        getTableView().getItems().remove(statement);
                    }
                });
            }

            @Override
            protected void updateItem(Void item, boolean empty) {
                super.updateItem(item, empty);
                if (empty) {
                    setGraphic(null);
                } else {
                    HBox buttons = new HBox(5);
                    buttons.getChildren().addAll(viewBtn, deleteBtn);
                    setGraphic(buttons);
                }
            }
        });

        statementsTable.getColumns().addAll(idCol, companyCol, periodCol, revenueCol, netIncomeCol, actionsCol);
        statementsTable.getItems().addAll(incomeStatementService.getAllIncomeStatements());

        // إعداد البحث
        searchBtn.setOnAction(e -> {
            String searchText = searchField.getText();
            LocalDate fromDate = fromDateFilter.getValue();
            LocalDate toDate = toDateFilter.getValue();

            List<IncomeStatement> filteredStatements = incomeStatementService.getAllIncomeStatements();

            if (searchText != null && !searchText.trim().isEmpty()) {
                filteredStatements = incomeStatementService.searchIncomeStatements(searchText);
            }

            if (fromDate != null && toDate != null) {
                filteredStatements = filteredStatements.stream()
                    .filter(s -> !s.getFromDate().isBefore(fromDate) && !s.getToDate().isAfter(toDate))
                    .collect(java.util.stream.Collectors.toList());
            }

            statementsTable.getItems().clear();
            statementsTable.getItems().addAll(filteredStatements);
        });

        VBox.setVgrow(statementsTable, Priority.ALWAYS);
        listContainer.getChildren().addAll(titleLabel, searchBox, statementsTable);
        return listContainer;
    }

    /**
     * إنشاء عرض مقارنة الفترات
     */
    private VBox createComparisonView() {
        VBox comparisonContainer = new VBox(20);
        comparisonContainer.setPadding(new Insets(20));

        Label titleLabel = new Label("⚖️ مقارنة قوائم الدخل");
        titleLabel.getStyleClass().add("main-title");

        // نموذج اختيار الفترات
        GridPane comparisonForm = new GridPane();
        comparisonForm.setHgap(15);
        comparisonForm.setVgap(15);
        comparisonForm.setPadding(new Insets(20));
        comparisonForm.getStyleClass().add("form-container");

        // الفترة الأولى
        Label period1Label = new Label("الفترة الأولى:");
        DatePicker fromDate1 = new DatePicker(LocalDate.now().minusMonths(2).withDayOfMonth(1));
        DatePicker toDate1 = new DatePicker(LocalDate.now().minusMonths(1).withDayOfMonth(LocalDate.now().minusMonths(1).lengthOfMonth()));

        // الفترة الثانية
        Label period2Label = new Label("الفترة الثانية:");
        DatePicker fromDate2 = new DatePicker(LocalDate.now().withDayOfMonth(1));
        DatePicker toDate2 = new DatePicker(LocalDate.now());

        comparisonForm.add(period1Label, 0, 0);
        comparisonForm.add(new Label("من:"), 1, 0);
        comparisonForm.add(fromDate1, 2, 0);
        comparisonForm.add(new Label("إلى:"), 3, 0);
        comparisonForm.add(toDate1, 4, 0);

        comparisonForm.add(period2Label, 0, 1);
        comparisonForm.add(new Label("من:"), 1, 1);
        comparisonForm.add(fromDate2, 2, 1);
        comparisonForm.add(new Label("إلى:"), 3, 1);
        comparisonForm.add(toDate2, 4, 1);

        // زر المقارنة
        Button compareBtn = new Button("مقارنة الفترات");
        compareBtn.getStyleClass().add("primary-button");
        compareBtn.setOnAction(e -> {
            if (fromDate1.getValue() == null || toDate1.getValue() == null ||
                fromDate2.getValue() == null || toDate2.getValue() == null) {
                showWarningAlert("بيانات ناقصة", "يرجى تحديد جميع التواريخ.");
                return;
            }

            IncomeStatement statement1 = incomeStatementService.generateIncomeStatement(
                fromDate1.getValue(), toDate1.getValue(), "شركة الزجاج والألومنيوم المتقدمة");
            IncomeStatement statement2 = incomeStatementService.generateIncomeStatement(
                fromDate2.getValue(), toDate2.getValue(), "شركة الزجاج والألومنيوم المتقدمة");

            showComparisonResults(statement1, statement2);
        });

        HBox buttonBox = new HBox();
        buttonBox.setAlignment(Pos.CENTER);
        buttonBox.getChildren().add(compareBtn);

        comparisonContainer.getChildren().addAll(titleLabel, comparisonForm, buttonBox);
        return comparisonContainer;
    }

    /**
     * عرض نتائج المقارنة
     */
    private void showComparisonResults(IncomeStatement statement1, IncomeStatement statement2) {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle("نتائج المقارنة");
        alert.setHeaderText("مقارنة قوائم الدخل");

        String comparison = String.format(
            "الفترة الأولى (%s):\n" +
            "إجمالي الإيرادات: %s ج.م\n" +
            "صافي الدخل: %s ج.م\n" +
            "نسبة صافي الربح: %.2f%%\n\n" +
            "الفترة الثانية (%s):\n" +
            "إجمالي الإيرادات: %s ج.م\n" +
            "صافي الدخل: %s ج.م\n" +
            "نسبة صافي الربح: %.2f%%\n\n" +
            "التغيير:\n" +
            "الإيرادات: %s ج.م (%.2f%%)\n" +
            "صافي الدخل: %s ج.م (%.2f%%)",

            statement1.getPeriod(),
            decimalFormat.format(statement1.getTotalRevenues()),
            decimalFormat.format(statement1.getNetIncome()),
            statement1.getNetProfitMargin(),

            statement2.getPeriod(),
            decimalFormat.format(statement2.getTotalRevenues()),
            decimalFormat.format(statement2.getNetIncome()),
            statement2.getNetProfitMargin(),

            decimalFormat.format(statement2.getTotalRevenues() - statement1.getTotalRevenues()),
            statement1.getTotalRevenues() > 0 ? ((statement2.getTotalRevenues() - statement1.getTotalRevenues()) / statement1.getTotalRevenues()) * 100 : 0,
            decimalFormat.format(statement2.getNetIncome() - statement1.getNetIncome()),
            statement1.getNetIncome() > 0 ? ((statement2.getNetIncome() - statement1.getNetIncome()) / statement1.getNetIncome()) * 100 : 0
        );

        alert.setContentText(comparison);
        alert.showAndWait();
    }
}
