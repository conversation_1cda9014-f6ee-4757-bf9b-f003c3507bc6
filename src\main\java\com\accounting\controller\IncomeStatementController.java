package com.accounting.controller;

import com.accounting.model.*;
import com.accounting.service.*;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.geometry.Insets;
import javafx.geometry.Pos;
import javafx.scene.Scene;
import javafx.scene.control.*;
import javafx.scene.control.cell.PropertyValueFactory;
import javafx.scene.layout.*;
import javafx.stage.FileChooser;
import javafx.stage.Modality;
import javafx.stage.Stage;
import de.jensd.fx.glyphs.fontawesome.FontAwesomeIcon;
import de.jensd.fx.glyphs.fontawesome.FontAwesomeIconView;

import java.io.File;
import java.text.DecimalFormat;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

/**
 * كونترولر قائمة الدخل
 * Income Statement Controller
 */
public class IncomeStatementController {
    
    private final IncomeStatementService incomeStatementService;
    private final DecimalFormat decimalFormat = new DecimalFormat("#,##0.00");
    
    // مكونات الواجهة
    private VBox mainContainer;
    private VBox contentArea;
    private HBox navigationBar;
    private Label currentPageLabel;
    
    // الصفحات المختلفة
    private enum Page {
        MAIN_DASHBOARD,
        INCOME_STATEMENTS_LIST,
        INCOME_STATEMENT_FORM,
        INCOME_STATEMENT_VIEW
    }
    
    private Page currentPage = Page.MAIN_DASHBOARD;
    private IncomeStatement currentStatement;
    
    public IncomeStatementController() {
        this.incomeStatementService = IncomeStatementService.getInstance();
    }
    
    /**
     * عرض نافذة قائمة الدخل
     */
    public void showIncomeStatementDialog() {
        Stage dialog = new Stage();
        dialog.setTitle("📊 قائمة الدخل");
        dialog.initModality(Modality.APPLICATION_MODAL);
        
        // تحديد حجم النافذة
        javafx.stage.Screen screen = javafx.stage.Screen.getPrimary();
        double screenHeight = screen.getVisualBounds().getHeight();
        double screenWidth = screen.getVisualBounds().getWidth();
        
        dialog.setWidth(Math.min(1600, screenWidth * 0.95));
        dialog.setHeight(Math.min(1000, screenHeight * 0.9));
        dialog.setResizable(true);
        
        // إنشاء الواجهة الرئيسية
        createMainInterface();
        
        Scene scene = new Scene(mainContainer);
        scene.getStylesheets().add(getClass().getResource("/css/styles.css").toExternalForm());
        dialog.setScene(scene);
        dialog.showAndWait();
    }
    
    /**
     * إنشاء الواجهة الرئيسية
     */
    private void createMainInterface() {
        mainContainer = new VBox(0);
        mainContainer.getStyleClass().add("main-container");
        
        // شريط التنقل
        navigationBar = createNavigationBar();
        
        // منطقة المحتوى
        contentArea = new VBox(20);
        contentArea.setPadding(new Insets(20));
        VBox.setVgrow(contentArea, Priority.ALWAYS);
        
        mainContainer.getChildren().addAll(navigationBar, contentArea);
        
        // عرض الصفحة الرئيسية
        showMainDashboard();
    }
    
    /**
     * إنشاء شريط التنقل
     */
    private HBox createNavigationBar() {
        HBox navBar = new HBox(15);
        navBar.setPadding(new Insets(15));
        navBar.setAlignment(Pos.CENTER_LEFT);
        navBar.getStyleClass().add("navigation-bar");
        
        // زر الرجوع
        Button backBtn = createNavButton("رجوع", FontAwesomeIcon.ARROW_LEFT, this::goBack);
        backBtn.setDisable(currentPage == Page.MAIN_DASHBOARD);
        
        // عنوان الصفحة الحالية
        currentPageLabel = new Label("📊 قائمة الدخل - الصفحة الرئيسية");
        currentPageLabel.getStyleClass().add("page-title");
        
        // فاصل
        Region spacer = new Region();
        HBox.setHgrow(spacer, Priority.ALWAYS);
        
        // زر إغلاق
        Button closeBtn = createNavButton("إغلاق", FontAwesomeIcon.TIMES, this::closeDialog);
        
        navBar.getChildren().addAll(backBtn, currentPageLabel, spacer, closeBtn);
        return navBar;
    }
    
    /**
     * إنشاء زر التنقل
     */
    private Button createNavButton(String text, FontAwesomeIcon icon, Runnable action) {
        Button button = new Button(text);
        
        FontAwesomeIconView iconView = new FontAwesomeIconView(icon);
        iconView.setSize("14px");
        button.setGraphic(iconView);
        
        button.getStyleClass().add("nav-button");
        button.setOnAction(e -> action.run());
        
        return button;
    }
    
    /**
     * عرض الصفحة الرئيسية
     */
    private void showMainDashboard() {
        currentPage = Page.MAIN_DASHBOARD;
        currentPageLabel.setText("📊 قائمة الدخل - الصفحة الرئيسية");
        updateNavigationButtons();
        
        contentArea.getChildren().clear();
        
        // العنوان الرئيسي
        Label titleLabel = new Label("📊 قائمة الدخل");
        titleLabel.getStyleClass().add("main-title");
        
        // الوصف
        Label descLabel = new Label("إدارة وعرض قوائم الدخل لتحليل الأداء المالي للشركة");
        descLabel.getStyleClass().add("description-label");
        descLabel.setWrapText(true);
        
        // شبكة الأزرار الرئيسية
        GridPane buttonsGrid = createMainButtonsGrid();
        
        // منطقة الإحصائيات السريعة
        VBox statsBox = createQuickStatsBox();
        
        contentArea.getChildren().addAll(titleLabel, descLabel, buttonsGrid, statsBox);
    }
    
    /**
     * إنشاء شبكة الأزرار الرئيسية
     */
    private GridPane createMainButtonsGrid() {
        GridPane grid = new GridPane();
        grid.setHgap(20);
        grid.setVgap(20);
        grid.setAlignment(Pos.CENTER);
        
        // الصف الأول
        Button newStatementBtn = createMainButton(
            "📊 إنشاء قائمة دخل جديدة",
            "إنشاء قائمة دخل لفترة محددة",
            FontAwesomeIcon.PLUS_CIRCLE,
            this::showNewIncomeStatementForm
        );
        
        Button viewStatementsBtn = createMainButton(
            "📋 عرض قوائم الدخل",
            "عرض وإدارة قوائم الدخل المحفوظة",
            FontAwesomeIcon.LIST,
            this::showIncomeStatementsList
        );
        
        Button monthlyReportBtn = createMainButton(
            "📅 تقرير شهري",
            "قائمة دخل للشهر الحالي",
            FontAwesomeIcon.CALENDAR,
            this::generateMonthlyReport
        );
        
        // الصف الثاني
        Button quarterlyReportBtn = createMainButton(
            "📈 تقرير ربع سنوي",
            "قائمة دخل للربع الحالي",
            FontAwesomeIcon.BAR_CHART,
            this::generateQuarterlyReport
        );
        
        Button yearlyReportBtn = createMainButton(
            "📊 تقرير سنوي",
            "قائمة دخل للسنة الحالية",
            FontAwesomeIcon.PIE_CHART,
            this::generateYearlyReport
        );
        
        Button compareBtn = createMainButton(
            "⚖️ مقارنة الفترات",
            "مقارنة قوائم الدخل لفترات مختلفة",
            FontAwesomeIcon.BALANCE_SCALE,
            this::showComparisonReport
        );
        
        // ترتيب الأزرار في الشبكة
        grid.add(newStatementBtn, 0, 0);
        grid.add(viewStatementsBtn, 1, 0);
        grid.add(monthlyReportBtn, 2, 0);
        grid.add(quarterlyReportBtn, 0, 1);
        grid.add(yearlyReportBtn, 1, 1);
        grid.add(compareBtn, 2, 1);
        
        return grid;
    }
    
    /**
     * إنشاء زر رئيسي
     */
    private Button createMainButton(String title, String description, FontAwesomeIcon icon, Runnable action) {
        VBox buttonContent = new VBox(10);
        buttonContent.setAlignment(Pos.CENTER);
        buttonContent.setPadding(new Insets(20));
        
        FontAwesomeIconView iconView = new FontAwesomeIconView(icon);
        iconView.setSize("32px");
        iconView.getStyleClass().add("main-button-icon");
        
        Label titleLabel = new Label(title);
        titleLabel.getStyleClass().add("main-button-title");
        titleLabel.setWrapText(true);
        
        Label descLabel = new Label(description);
        descLabel.getStyleClass().add("main-button-description");
        descLabel.setWrapText(true);
        
        buttonContent.getChildren().addAll(iconView, titleLabel, descLabel);
        
        Button button = new Button();
        button.setGraphic(buttonContent);
        button.getStyleClass().add("main-button");
        button.setPrefSize(250, 150);
        button.setOnAction(e -> action.run());
        
        return button;
    }
    
    /**
     * إنشاء منطقة الإحصائيات السريعة
     */
    private VBox createQuickStatsBox() {
        VBox statsBox = new VBox(15);
        statsBox.getStyleClass().add("stats-box");
        statsBox.setPadding(new Insets(20));
        
        Label statsTitle = new Label("📈 إحصائيات سريعة");
        statsTitle.getStyleClass().add("stats-title");
        
        // الحصول على آخر قائمة دخل
        List<IncomeStatement> statements = incomeStatementService.getAllIncomeStatements();
        if (!statements.isEmpty()) {
            IncomeStatement latestStatement = statements.get(statements.size() - 1);
            
            HBox statsRow1 = createStatsRow("إجمالي الإيرادات:", 
                decimalFormat.format(latestStatement.getTotalRevenues()) + " ج.م");
            HBox statsRow2 = createStatsRow("إجمالي الربح:", 
                decimalFormat.format(latestStatement.getGrossProfit()) + " ج.م");
            HBox statsRow3 = createStatsRow("صافي الدخل:", 
                decimalFormat.format(latestStatement.getNetIncome()) + " ج.م");
            HBox statsRow4 = createStatsRow("نسبة صافي الربح:", 
                String.format("%.2f%%", latestStatement.getNetProfitMargin()));
            
            statsBox.getChildren().addAll(statsTitle, statsRow1, statsRow2, statsRow3, statsRow4);
        } else {
            Label noDataLabel = new Label("لا توجد قوائم دخل محفوظة");
            noDataLabel.getStyleClass().add("no-data-label");
            statsBox.getChildren().addAll(statsTitle, noDataLabel);
        }
        
        return statsBox;
    }
    
    /**
     * إنشاء صف إحصائيات
     */
    private HBox createStatsRow(String label, String value) {
        HBox row = new HBox(10);
        row.setAlignment(Pos.CENTER_LEFT);
        
        Label labelControl = new Label(label);
        labelControl.getStyleClass().add("stats-label");
        
        Region spacer = new Region();
        HBox.setHgrow(spacer, Priority.ALWAYS);
        
        Label valueControl = new Label(value);
        valueControl.getStyleClass().add("stats-value");
        
        row.getChildren().addAll(labelControl, spacer, valueControl);
        return row;
    }
    
    // وظائف الأزرار
    private void showNewIncomeStatementForm() {
        showInfoAlert("نموذج إنشاء قائمة دخل جديدة", "سيتم تطوير هذه الميزة قريباً...");
    }
    
    private void showIncomeStatementsList() {
        showInfoAlert("قائمة قوائم الدخل", "سيتم تطوير هذه الميزة قريباً...");
    }
    
    private void generateMonthlyReport() {
        LocalDate now = LocalDate.now();
        LocalDate fromDate = now.withDayOfMonth(1);
        LocalDate toDate = now.withDayOfMonth(now.lengthOfMonth());
        
        IncomeStatement statement = incomeStatementService.generateIncomeStatement(
            fromDate, toDate, "شركة الزجاج والألومنيوم المتقدمة");
        
        showIncomeStatementView(statement);
    }
    
    private void generateQuarterlyReport() {
        showInfoAlert("التقرير الربع سنوي", "سيتم تطوير هذه الميزة قريباً...");
    }
    
    private void generateYearlyReport() {
        showInfoAlert("التقرير السنوي", "سيتم تطوير هذه الميزة قريباً...");
    }
    
    private void showComparisonReport() {
        showInfoAlert("مقارنة الفترات", "سيتم تطوير هذه الميزة قريباً...");
    }
    
    private void showIncomeStatementView(IncomeStatement statement) {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle("عرض قائمة الدخل");
        alert.setHeaderText("تم إنشاء قائمة الدخل بنجاح");

        String content = String.format("الفترة: %s\n\nإجمالي الإيرادات: %s ج.م\nإجمالي الربح: %s ج.م\nصافي الدخل: %s ج.م\nنسبة صافي الربح: %.2f%%",
            statement.getPeriod(),
            decimalFormat.format(statement.getTotalRevenues()),
            decimalFormat.format(statement.getGrossProfit()),
            decimalFormat.format(statement.getNetIncome()),
            statement.getNetProfitMargin());

        alert.setContentText(content);

        // إضافة أزرار التصدير
        ButtonType pdfButton = new ButtonType("تصدير PDF");
        ButtonType excelButton = new ButtonType("تصدير Excel");
        ButtonType closeButton = new ButtonType("إغلاق", ButtonBar.ButtonData.CANCEL_CLOSE);

        alert.getButtonTypes().setAll(pdfButton, excelButton, closeButton);

        Optional<ButtonType> result = alert.showAndWait();
        if (result.isPresent()) {
            if (result.get() == pdfButton) {
                exportToPDF(statement);
            } else if (result.get() == excelButton) {
                exportToExcel(statement);
            }
        }
    }

    private void exportToPDF(IncomeStatement statement) {
        showInfoAlert("تصدير PDF", "ميزة تصدير PDF قيد التطوير...");
    }

    private void exportToExcel(IncomeStatement statement) {
        showInfoAlert("تصدير Excel", "ميزة تصدير Excel قيد التطوير...");
    }
    
    // وظائف التنقل
    private void goBack() {
        if (currentPage != Page.MAIN_DASHBOARD) {
            showMainDashboard();
        }
    }
    
    private void closeDialog() {
        Stage stage = (Stage) mainContainer.getScene().getWindow();
        stage.close();
    }
    
    private void updateNavigationButtons() {
        // تحديث حالة أزرار التنقل
        Button backBtn = (Button) navigationBar.getChildren().get(0);
        backBtn.setDisable(currentPage == Page.MAIN_DASHBOARD);
    }
    
    private void showInfoAlert(String title, String message) {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle(title);
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }

    private void showWarningAlert(String title, String message) {
        Alert alert = new Alert(Alert.AlertType.WARNING);
        alert.setTitle(title);
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }
}
