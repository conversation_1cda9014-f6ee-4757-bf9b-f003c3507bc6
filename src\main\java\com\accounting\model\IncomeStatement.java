package com.accounting.model;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

/**
 * نموذج قائمة الدخل
 * Income Statement Model
 */
public class IncomeStatement {
    
    private String statementId;
    private LocalDate fromDate;
    private LocalDate toDate;
    private String companyName;
    private String period;
    
    // الإيرادات
    private List<IncomeStatementItem> revenues;
    private double totalRevenues;
    
    // تكلفة البضاعة المباعة
    private List<IncomeStatementItem> costOfGoodsSold;
    private double totalCostOfGoodsSold;
    
    // إجمالي الربح
    private double grossProfit;
    
    // المصروفات التشغيلية
    private List<IncomeStatementItem> operatingExpenses;
    private double totalOperatingExpenses;
    
    // الدخل التشغيلي
    private double operatingIncome;
    
    // الإيرادات والمصروفات الأخرى
    private List<IncomeStatementItem> otherIncomes;
    private List<IncomeStatementItem> otherExpenses;
    private double totalOtherIncomes;
    private double totalOtherExpenses;
    
    // صافي الدخل قبل الضرائب
    private double incomeBeforeTax;
    
    // الضرائب
    private double taxes;
    
    // صافي الدخل
    private double netIncome;
    
    // معلومات إضافية
    private LocalDate generatedDate;
    private String generatedBy;
    private String notes;
    
    /**
     * كونستركتور افتراضي
     */
    public IncomeStatement() {
        this.revenues = new ArrayList<>();
        this.costOfGoodsSold = new ArrayList<>();
        this.operatingExpenses = new ArrayList<>();
        this.otherIncomes = new ArrayList<>();
        this.otherExpenses = new ArrayList<>();
        this.generatedDate = LocalDate.now();
    }
    
    /**
     * كونستركتور مع المعاملات الأساسية
     */
    public IncomeStatement(String statementId, LocalDate fromDate, LocalDate toDate, String companyName) {
        this();
        this.statementId = statementId;
        this.fromDate = fromDate;
        this.toDate = toDate;
        this.companyName = companyName;
        this.period = formatPeriod(fromDate, toDate);
    }
    
    /**
     * تنسيق الفترة الزمنية
     */
    private String formatPeriod(LocalDate from, LocalDate to) {
        if (from == null || to == null) return "";
        
        if (from.getYear() == to.getYear()) {
            if (from.getMonth() == to.getMonth()) {
                return String.format("%s %d", getArabicMonth(from.getMonthValue()), from.getYear());
            } else {
                return String.format("من %s إلى %s %d", 
                    getArabicMonth(from.getMonthValue()), 
                    getArabicMonth(to.getMonthValue()), 
                    from.getYear());
            }
        } else {
            return String.format("من %s %d إلى %s %d", 
                getArabicMonth(from.getMonthValue()), from.getYear(),
                getArabicMonth(to.getMonthValue()), to.getYear());
        }
    }
    
    /**
     * الحصول على اسم الشهر بالعربية
     */
    private String getArabicMonth(int month) {
        String[] months = {
            "", "يناير", "فبراير", "مارس", "أبريل", "مايو", "يونيو",
            "يوليو", "أغسطس", "سبتمبر", "أكتوبر", "نوفمبر", "ديسمبر"
        };
        return months[month];
    }
    
    /**
     * حساب جميع المجاميع
     */
    public void calculateTotals() {
        // حساب إجمالي الإيرادات
        totalRevenues = revenues.stream().mapToDouble(IncomeStatementItem::getAmount).sum();
        
        // حساب إجمالي تكلفة البضاعة المباعة
        totalCostOfGoodsSold = costOfGoodsSold.stream().mapToDouble(IncomeStatementItem::getAmount).sum();
        
        // حساب إجمالي الربح
        grossProfit = totalRevenues - totalCostOfGoodsSold;
        
        // حساب إجمالي المصروفات التشغيلية
        totalOperatingExpenses = operatingExpenses.stream().mapToDouble(IncomeStatementItem::getAmount).sum();
        
        // حساب الدخل التشغيلي
        operatingIncome = grossProfit - totalOperatingExpenses;
        
        // حساب إجمالي الإيرادات الأخرى
        totalOtherIncomes = otherIncomes.stream().mapToDouble(IncomeStatementItem::getAmount).sum();
        
        // حساب إجمالي المصروفات الأخرى
        totalOtherExpenses = otherExpenses.stream().mapToDouble(IncomeStatementItem::getAmount).sum();
        
        // حساب صافي الدخل قبل الضرائب
        incomeBeforeTax = operatingIncome + totalOtherIncomes - totalOtherExpenses;
        
        // حساب صافي الدخل
        netIncome = incomeBeforeTax - taxes;
    }
    
    /**
     * إضافة بند إيراد
     */
    public void addRevenue(IncomeStatementItem item) {
        if (item != null) {
            revenues.add(item);
        }
    }
    
    /**
     * إضافة بند تكلفة البضاعة المباعة
     */
    public void addCostOfGoodsSold(IncomeStatementItem item) {
        if (item != null) {
            costOfGoodsSold.add(item);
        }
    }
    
    /**
     * إضافة بند مصروف تشغيلي
     */
    public void addOperatingExpense(IncomeStatementItem item) {
        if (item != null) {
            operatingExpenses.add(item);
        }
    }
    
    /**
     * إضافة بند إيراد آخر
     */
    public void addOtherIncome(IncomeStatementItem item) {
        if (item != null) {
            otherIncomes.add(item);
        }
    }
    
    /**
     * إضافة بند مصروف آخر
     */
    public void addOtherExpense(IncomeStatementItem item) {
        if (item != null) {
            otherExpenses.add(item);
        }
    }
    
    /**
     * التحقق من وجود أرباح
     */
    public boolean isProfitable() {
        return netIncome > 0;
    }
    
    /**
     * الحصول على نسبة إجمالي الربح
     */
    public double getGrossProfitMargin() {
        return totalRevenues > 0 ? (grossProfit / totalRevenues) * 100 : 0;
    }
    
    /**
     * الحصول على نسبة صافي الربح
     */
    public double getNetProfitMargin() {
        return totalRevenues > 0 ? (netIncome / totalRevenues) * 100 : 0;
    }
    
    /**
     * الحصول على نسبة المصروفات التشغيلية
     */
    public double getOperatingExpenseRatio() {
        return totalRevenues > 0 ? (totalOperatingExpenses / totalRevenues) * 100 : 0;
    }
    
    // Getters and Setters
    public String getStatementId() { return statementId; }
    public void setStatementId(String statementId) { this.statementId = statementId; }
    
    public LocalDate getFromDate() { return fromDate; }
    public void setFromDate(LocalDate fromDate) { this.fromDate = fromDate; }
    
    public LocalDate getToDate() { return toDate; }
    public void setToDate(LocalDate toDate) { this.toDate = toDate; }
    
    public String getCompanyName() { return companyName; }
    public void setCompanyName(String companyName) { this.companyName = companyName; }
    
    public String getPeriod() { return period; }
    public void setPeriod(String period) { this.period = period; }
    
    public List<IncomeStatementItem> getRevenues() { return revenues; }
    public void setRevenues(List<IncomeStatementItem> revenues) { this.revenues = revenues; }
    
    public double getTotalRevenues() { return totalRevenues; }
    public void setTotalRevenues(double totalRevenues) { this.totalRevenues = totalRevenues; }
    
    public List<IncomeStatementItem> getCostOfGoodsSold() { return costOfGoodsSold; }
    public void setCostOfGoodsSold(List<IncomeStatementItem> costOfGoodsSold) { this.costOfGoodsSold = costOfGoodsSold; }
    
    public double getTotalCostOfGoodsSold() { return totalCostOfGoodsSold; }
    public void setTotalCostOfGoodsSold(double totalCostOfGoodsSold) { this.totalCostOfGoodsSold = totalCostOfGoodsSold; }
    
    public double getGrossProfit() { return grossProfit; }
    public void setGrossProfit(double grossProfit) { this.grossProfit = grossProfit; }
    
    public List<IncomeStatementItem> getOperatingExpenses() { return operatingExpenses; }
    public void setOperatingExpenses(List<IncomeStatementItem> operatingExpenses) { this.operatingExpenses = operatingExpenses; }
    
    public double getTotalOperatingExpenses() { return totalOperatingExpenses; }
    public void setTotalOperatingExpenses(double totalOperatingExpenses) { this.totalOperatingExpenses = totalOperatingExpenses; }
    
    public double getOperatingIncome() { return operatingIncome; }
    public void setOperatingIncome(double operatingIncome) { this.operatingIncome = operatingIncome; }
    
    public List<IncomeStatementItem> getOtherIncomes() { return otherIncomes; }
    public void setOtherIncomes(List<IncomeStatementItem> otherIncomes) { this.otherIncomes = otherIncomes; }
    
    public List<IncomeStatementItem> getOtherExpenses() { return otherExpenses; }
    public void setOtherExpenses(List<IncomeStatementItem> otherExpenses) { this.otherExpenses = otherExpenses; }
    
    public double getTotalOtherIncomes() { return totalOtherIncomes; }
    public void setTotalOtherIncomes(double totalOtherIncomes) { this.totalOtherIncomes = totalOtherIncomes; }
    
    public double getTotalOtherExpenses() { return totalOtherExpenses; }
    public void setTotalOtherExpenses(double totalOtherExpenses) { this.totalOtherExpenses = totalOtherExpenses; }
    
    public double getIncomeBeforeTax() { return incomeBeforeTax; }
    public void setIncomeBeforeTax(double incomeBeforeTax) { this.incomeBeforeTax = incomeBeforeTax; }
    
    public double getTaxes() { return taxes; }
    public void setTaxes(double taxes) { this.taxes = taxes; }
    
    public double getNetIncome() { return netIncome; }
    public void setNetIncome(double netIncome) { this.netIncome = netIncome; }
    
    public LocalDate getGeneratedDate() { return generatedDate; }
    public void setGeneratedDate(LocalDate generatedDate) { this.generatedDate = generatedDate; }
    
    public String getGeneratedBy() { return generatedBy; }
    public void setGeneratedBy(String generatedBy) { this.generatedBy = generatedBy; }
    
    public String getNotes() { return notes; }
    public void setNotes(String notes) { this.notes = notes; }
}
