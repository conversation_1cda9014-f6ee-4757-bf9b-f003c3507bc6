@echo off 
setlocal 
cd /d "%~dp0" 
title Hossam Accounting System 
echo ======================================== 
echo       Hossam Accounting System 
echo ======================================== 
echo Starting application... 
echo. 
 
:: Try to run with current Java 
java -Dfile.encoding=UTF-8 -jar accounting-system-1.0.0.jar >nul 2>&1 
 
:: If it fails, show helpful message 
if %errorlevel% neq 0 ( 
    echo ❌ JavaFX is missing from your Java installation 
    echo. 
    echo 🚀 QUICK SOLUTION: 
    echo. 
    echo 1. We will open Oracle JDK download page 
    echo 2. Download JDK 17 or JDK 21 
    echo 3. Install it (replaces current Java) 
    echo 4. Run this file again 
    echo. 
    echo Oracle JDK includes JavaFX built-in! 
    echo. 
    set /p open="Open download page now? (y/n): " 
    if /i "%open%"=="y" start https://www.oracle.com/java/technologies/downloads/ 
) else ( 
    echo ✅ Application started successfully! 
) 
 
pause 
