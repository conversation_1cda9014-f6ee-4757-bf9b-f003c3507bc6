@echo off 
setlocal 
set JAVA_HOME=C:\Program Files\Java\jdk-24 
set PATH=%JAVA_HOME%\bin;%PATH% 
cd /d "%~dp0" 
title Hossam Accounting System 
echo ======================================== 
echo       Hossam Accounting System 
echo       Using JDK 24 with JavaFX 
echo ======================================== 
echo Starting application... 
echo. 
java -Dfile.encoding=UTF-8 -jar accounting-system-1.0.0.jar 
if %errorlevel% neq 0 ( 
    echo Application failed to start! 
    pause 
) 
