# البدء السريع - Quick Start

## ⚠️ مطلوب: تثبيت Java أولاً

### تحميل وتثبيت Java (مطلوب):

1. **اذهب إلى:** https://adoptium.net/
2. **اختر:** OpenJDK 17 (LTS) أو OpenJDK 21 (LTS)
3. **حمل:** النسخة المناسبة لنظام التشغيل (Windows x64)
4. **ثبت:** البرنامج واتبع التعليمات
5. **تأكد:** من تحديد خيار "Add to PATH" أثناء التثبيت

### التحقق من التثبيت:
افتح Command Prompt واكتب:
```
java -version
```

يجب أن ترى شيئاً مثل:
```
openjdk version "17.0.x" 2023-xx-xx
```

## 🚀 تشغيل البرنامج

بعد تثبيت Java:

### الطريقة الأولى (الأسهل):
1. انقر نقراً مزدوجاً على `run.bat`
2. انتظر (قد يستغرق دقائق في المرة الأولى)
3. سيفتح البرنامج

### الطريقة الثانية:
1. افتح Command Prompt في مجلد المشروع
2. اكتب: `mvnw.cmd clean javafx:run`
3. اضغط Enter وانتظر

## 📋 ما ستراه:

- **واجهة رئيسية** أنيقة وعصرية
- **قوائم علوية** لجميع الموديولات:
  - 🏢 بيانات الشركة
  - 🛒 المبيعات  
  - 📦 المخازن
  - 💰 الحسابات
  - 👥 الرواتب والأجور
  - 🏭 التصنيع
- **أزرار وصول سريع** في الشاشة الرئيسية
- **شريط حالة** في الأسفل

## 🔧 حل المشاكل:

### "java is not recognized"
- تأكد من تثبيت Java
- أعد تشغيل Command Prompt
- تأكد من إضافة Java إلى PATH

### "mvnw.cmd is not recognized"  
- تأكد من وجودك في مجلد المشروع الصحيح
- استخدم `run.bat` بدلاً من ذلك

### البرنامج لا يفتح
- تأكد من Java 17 أو أحدث
- تأكد من اتصالك بالإنترنت (للمرة الأولى)
- جرب `run-simple.bat`

## 📝 ملاحظات مهمة:

- ✅ **الواجهة جاهزة** - جميع القوائم والأزرار تعمل
- ⚠️ **الموديولات قيد التطوير** - ستظهر رسالة "قيد التطوير"
- 🔄 **التطوير التدريجي** - سيتم إضافة تفاصيل كل موديول لاحقاً
- 💾 **حفظ محلي** - جميع البيانات تُحفظ على جهازك

---

**🎯 الهدف الحالي:** عرض الواجهة الأساسية والتنقل بين الموديولات

**📞 للدعم:** تأكد من تثبيت Java أولاً، ثم استخدم `run.bat`
