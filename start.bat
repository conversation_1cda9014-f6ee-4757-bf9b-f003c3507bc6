@echo off
echo ========================================
echo    Integrated Accounting System
echo    نظام الحسابات المتكامل
echo ========================================
echo.

echo Welcome to the Integrated Accounting System!
echo.

echo Choose what you want to do:
echo.
echo    1. Run Application
echo    2. Check System Requirements  
echo    3. Test Java
echo    4. Developer Mode
echo    5. Exit
echo.

set /p choice="Enter your choice (1-5): "

if "%choice%"=="1" goto run_app
if "%choice%"=="2" goto check_system
if "%choice%"=="3" goto test_java
if "%choice%"=="4" goto dev_mode
if "%choice%"=="5" goto exit
goto invalid_choice

:run_app
echo.
echo Starting the application...
call run.bat
goto end

:check_system
echo.
echo Checking system requirements...
call check-system.bat
goto end

:test_java
echo.
echo Testing Java...
call test-java.bat
goto end

:dev_mode
echo.
echo Starting developer mode...
call run-dev.bat
goto end

:invalid_choice
echo.
echo Invalid choice, please try again
echo.
pause
cls
goto start

:exit
echo.
echo Thank you for using the Integrated Accounting System
goto end

:end
echo.
echo Press any key to exit...
pause >nul
