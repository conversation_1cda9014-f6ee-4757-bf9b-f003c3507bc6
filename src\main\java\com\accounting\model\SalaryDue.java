package com.accounting.model;

import java.time.LocalDate;
import java.time.YearMonth;

/**
 * نموذج المستحقات
 * Salary Due Model
 */
public class SalaryDue {
    private String dueId;
    private String employeeId;
    private String employeeName;
    private YearMonth month;
    private double baseSalary;
    private double overtime;
    private double bonus;
    private double deduction;
    private double finalDue;
    private LocalDate createdDate;
    private String notes;
    private boolean isActive;
    
    public SalaryDue() {
        this.isActive = true;
        this.createdDate = LocalDate.now();
        this.month = YearMonth.now();
    }
    
    public SalaryDue(String dueId, String employeeId, String employeeName, YearMonth month, 
                    double baseSalary, double overtime, double bonus, double deduction) {
        this.dueId = dueId;
        this.employeeId = employeeId;
        this.employeeName = employeeName;
        this.month = month;
        this.baseSalary = baseSalary;
        this.overtime = overtime;
        this.bonus = bonus;
        this.deduction = deduction;
        this.finalDue = baseSalary + overtime + bonus - deduction;
        this.createdDate = LocalDate.now();
        this.isActive = true;
    }
    
    /**
     * حساب المستحق النهائي
     */
    public void calculateFinalDue() {
        this.finalDue = baseSalary + overtime + bonus - deduction;
    }
    
    // Getters and Setters
    public String getDueId() { return dueId; }
    public void setDueId(String dueId) { this.dueId = dueId; }
    
    public String getEmployeeId() { return employeeId; }
    public void setEmployeeId(String employeeId) { this.employeeId = employeeId; }
    
    public String getEmployeeName() { return employeeName; }
    public void setEmployeeName(String employeeName) { this.employeeName = employeeName; }
    
    public YearMonth getMonth() { return month; }
    public void setMonth(YearMonth month) { this.month = month; }
    
    public double getBaseSalary() { return baseSalary; }
    public void setBaseSalary(double baseSalary) { 
        this.baseSalary = baseSalary;
        calculateFinalDue();
    }
    
    public double getOvertime() { return overtime; }
    public void setOvertime(double overtime) { 
        this.overtime = overtime;
        calculateFinalDue();
    }
    
    public double getBonus() { return bonus; }
    public void setBonus(double bonus) { 
        this.bonus = bonus;
        calculateFinalDue();
    }
    
    public double getDeduction() { return deduction; }
    public void setDeduction(double deduction) { 
        this.deduction = deduction;
        calculateFinalDue();
    }
    
    public double getFinalDue() { return finalDue; }
    public void setFinalDue(double finalDue) { this.finalDue = finalDue; }
    
    public LocalDate getCreatedDate() { return createdDate; }
    public void setCreatedDate(LocalDate createdDate) { this.createdDate = createdDate; }
    
    public String getNotes() { return notes; }
    public void setNotes(String notes) { this.notes = notes; }
    
    public boolean isActive() { return isActive; }
    public void setActive(boolean active) { isActive = active; }
    
    @Override
    public String toString() {
        return "مستحق " + month + " - " + finalDue + " ج.م";
    }
}
