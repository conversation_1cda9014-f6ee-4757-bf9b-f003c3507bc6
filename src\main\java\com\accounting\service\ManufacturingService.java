package com.accounting.service;

import com.accounting.model.ManufacturingOrder;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * خدمة إدارة أوامر التصنيع
 */
public class ManufacturingService {
    
    private static ManufacturingService instance;
    private final ObservableList<ManufacturingOrder> manufacturingOrders = FXCollections.observableArrayList();
    private final Map<String, ManufacturingOrder> ordersMap = new HashMap<>();
    private int nextOrderNumber = 1;
    
    private ManufacturingService() {
        // إنشاء بعض البيانات التجريبية
        initializeSampleData();
    }
    
    public static ManufacturingService getInstance() {
        if (instance == null) {
            instance = new ManufacturingService();
        }
        return instance;
    }
    
    /**
     * إضافة أمر تصنيع جديد
     */
    public boolean addManufacturingOrder(ManufacturingOrder order) {
        if (order == null || !order.isValid()) {
            return false;
        }
        
        // توليد رقم الأمر إذا لم يكن موجوداً
        if (order.getOrderNumber() == null || order.getOrderNumber().trim().isEmpty()) {
            order.setOrderNumber(generateOrderNumber());
        }
        
        // التحقق من عدم تكرار معرف الأمر
        if (ordersMap.containsKey(order.getOrderId())) {
            return false;
        }
        
        order.setCreatedDate(LocalDateTime.now());
        order.setStatus(ManufacturingOrder.OrderStatus.DRAFT);
        
        manufacturingOrders.add(order);
        ordersMap.put(order.getOrderId(), order);
        
        return true;
    }
    
    /**
     * تحديث أمر تصنيع موجود
     */
    public boolean updateManufacturingOrder(ManufacturingOrder order) {
        if (order == null || !ordersMap.containsKey(order.getOrderId())) {
            return false;
        }
        
        // التحقق من إمكانية التعديل
        if (order.getStatus() == ManufacturingOrder.OrderStatus.COMPLETED ||
            order.getStatus() == ManufacturingOrder.OrderStatus.CANCELLED) {
            return false;
        }
        
        order.setModifiedDate(LocalDateTime.now());
        
        // تحديث في الخريطة
        ordersMap.put(order.getOrderId(), order);
        
        // تحديث في القائمة
        int index = manufacturingOrders.indexOf(order);
        if (index >= 0) {
            manufacturingOrders.set(index, order);
        }
        
        return true;
    }
    
    /**
     * حذف أمر تصنيع
     */
    public boolean deleteManufacturingOrder(String orderId) {
        ManufacturingOrder order = getManufacturingOrderById(orderId);
        if (order == null) {
            return false;
        }
        
        // التحقق من إمكانية الحذف
        if (order.getStatus() == ManufacturingOrder.OrderStatus.IN_PROGRESS ||
            order.getStatus() == ManufacturingOrder.OrderStatus.COMPLETED) {
            return false;
        }
        
        manufacturingOrders.remove(order);
        ordersMap.remove(orderId);
        
        return true;
    }
    
    /**
     * تأكيد أمر التصنيع
     */
    public boolean confirmManufacturingOrder(String orderId) {
        ManufacturingOrder order = getManufacturingOrderById(orderId);
        if (order == null || order.getStatus() != ManufacturingOrder.OrderStatus.DRAFT) {
            return false;
        }
        
        order.setStatus(ManufacturingOrder.OrderStatus.CONFIRMED);
        order.setModifiedDate(LocalDateTime.now());
        
        return true;
    }
    
    /**
     * بدء تنفيذ أمر التصنيع
     */
    public boolean startManufacturingOrder(String orderId) {
        ManufacturingOrder order = getManufacturingOrderById(orderId);
        if (order == null || order.getStatus() != ManufacturingOrder.OrderStatus.CONFIRMED) {
            return false;
        }
        
        order.setStatus(ManufacturingOrder.OrderStatus.IN_PROGRESS);
        order.setModifiedDate(LocalDateTime.now());
        
        return true;
    }
    
    /**
     * إكمال أمر التصنيع
     */
    public boolean completeManufacturingOrder(String orderId) {
        ManufacturingOrder order = getManufacturingOrderById(orderId);
        if (order == null || order.getStatus() != ManufacturingOrder.OrderStatus.IN_PROGRESS) {
            return false;
        }
        
        order.setStatus(ManufacturingOrder.OrderStatus.COMPLETED);
        order.setModifiedDate(LocalDateTime.now());
        
        return true;
    }
    
    /**
     * إلغاء أمر التصنيع
     */
    public boolean cancelManufacturingOrder(String orderId) {
        ManufacturingOrder order = getManufacturingOrderById(orderId);
        if (order == null || order.getStatus() == ManufacturingOrder.OrderStatus.COMPLETED) {
            return false;
        }
        
        order.setStatus(ManufacturingOrder.OrderStatus.CANCELLED);
        order.setModifiedDate(LocalDateTime.now());
        
        return true;
    }
    
    /**
     * البحث عن أمر بالمعرف
     */
    public ManufacturingOrder getManufacturingOrderById(String orderId) {
        return ordersMap.get(orderId);
    }

    /**
     * البحث عن أمر بالمعرف (alias)
     */
    public ManufacturingOrder findManufacturingOrderById(String orderId) {
        return getManufacturingOrderById(orderId);
    }
    
    /**
     * البحث عن أمر برقم الأمر
     */
    public ManufacturingOrder getManufacturingOrderByNumber(String orderNumber) {
        return manufacturingOrders.stream()
                .filter(order -> order.getOrderNumber().equals(orderNumber))
                .findFirst()
                .orElse(null);
    }
    
    /**
     * الحصول على جميع أوامر التصنيع
     */
    public ObservableList<ManufacturingOrder> getAllManufacturingOrders() {
        return manufacturingOrders;
    }
    
    /**
     * الحصول على أوامر التصنيع حسب الحالة
     */
    public List<ManufacturingOrder> getManufacturingOrdersByStatus(ManufacturingOrder.OrderStatus status) {
        return manufacturingOrders.stream()
                .filter(order -> order.getStatus() == status)
                .collect(Collectors.toList());
    }
    
    /**
     * الحصول على أوامر التصنيع حسب العميل
     */
    public List<ManufacturingOrder> getManufacturingOrdersByCustomer(String customerName) {
        return manufacturingOrders.stream()
                .filter(order -> order.getCustomerName().toLowerCase().contains(customerName.toLowerCase()))
                .collect(Collectors.toList());
    }
    
    /**
     * الحصول على أوامر التصنيع حسب التاريخ
     */
    public List<ManufacturingOrder> getManufacturingOrdersByDateRange(LocalDate fromDate, LocalDate toDate) {
        return manufacturingOrders.stream()
                .filter(order -> {
                    LocalDate orderDate = order.getOrderDate();
                    return orderDate != null && 
                           !orderDate.isBefore(fromDate) && 
                           !orderDate.isAfter(toDate);
                })
                .collect(Collectors.toList());
    }
    
    /**
     * البحث في أوامر التصنيع
     */
    public List<ManufacturingOrder> searchManufacturingOrders(String searchText) {
        if (searchText == null || searchText.trim().isEmpty()) {
            return new ArrayList<>(manufacturingOrders);
        }
        
        String searchLower = searchText.toLowerCase();
        return manufacturingOrders.stream()
                .filter(order -> 
                    order.getOrderNumber().toLowerCase().contains(searchLower) ||
                    order.getCustomerName().toLowerCase().contains(searchLower) ||
                    (order.getInvoiceNumber() != null && order.getInvoiceNumber().toLowerCase().contains(searchLower)) ||
                    (order.getNotes() != null && order.getNotes().toLowerCase().contains(searchLower))
                )
                .collect(Collectors.toList());
    }
    
    /**
     * توليد رقم أمر جديد
     */
    public String generateOrderNumber() {
        String orderNumber;
        do {
            orderNumber = String.format("MO%06d", nextOrderNumber++);
        } while (getManufacturingOrderByNumber(orderNumber) != null);
        
        return orderNumber;
    }
    
    /**
     * الحصول على إحصائيات أوامر التصنيع
     */
    public Map<String, Object> getManufacturingStatistics() {
        Map<String, Object> stats = new HashMap<>();
        
        stats.put("إجمالي الأوامر", manufacturingOrders.size());
        
        // إحصائيات حسب الحالة
        for (ManufacturingOrder.OrderStatus status : ManufacturingOrder.OrderStatus.values()) {
            long count = manufacturingOrders.stream()
                    .filter(order -> order.getStatus() == status)
                    .count();
            stats.put("أوامر " + status.getArabicName(), count);
        }
        
        // إحصائيات الشهر الحالي
        LocalDate startOfMonth = LocalDate.now().withDayOfMonth(1);
        LocalDate endOfMonth = LocalDate.now().withDayOfMonth(LocalDate.now().lengthOfMonth());
        
        long thisMonthOrders = getManufacturingOrdersByDateRange(startOfMonth, endOfMonth).size();
        stats.put("أوامر الشهر الحالي", thisMonthOrders);
        
        // إحصائيات اليوم
        long todayOrders = manufacturingOrders.stream()
                .filter(order -> order.getOrderDate().equals(LocalDate.now()))
                .count();
        stats.put("أوامر اليوم", todayOrders);
        
        return stats;
    }
    
    /**
     * تصدير أوامر التصنيع إلى نص
     */
    public String exportManufacturingOrdersToText(LocalDate fromDate, LocalDate toDate) {
        StringBuilder export = new StringBuilder();
        export.append("تقرير أوامر التصنيع\n");
        export.append("من ").append(fromDate).append(" إلى ").append(toDate).append("\n");
        export.append("=".repeat(80)).append("\n\n");
        
        List<ManufacturingOrder> orders = getManufacturingOrdersByDateRange(fromDate, toDate);
        orders.sort(Comparator.comparing(ManufacturingOrder::getOrderDate)
                .thenComparing(ManufacturingOrder::getOrderNumber));
        
        for (ManufacturingOrder order : orders) {
            export.append("رقم الأمر: ").append(order.getOrderNumber()).append("\n");
            export.append("العميل: ").append(order.getCustomerName()).append("\n");
            export.append("رقم الفاتورة: ").append(order.getInvoiceNumber() != null ? order.getInvoiceNumber() : "").append("\n");
            export.append("التاريخ: ").append(order.getOrderDate()).append("\n");
            export.append("الحالة: ").append(order.getStatus().getArabicName()).append("\n");
            
            // الخدمات المختارة
            export.append("الخدمات: ");
            List<String> services = new ArrayList<>();
            if (order.isGlassCuttingSelected()) services.add("قص زجاج");
            if (order.isThermalGlassSelected()) services.add("حراري زجاج");
            if (order.isFilmSelected()) services.add("فيلم");
            if (order.isBulletProofFilmSelected()) services.add("فيلم ضد الرصاص");
            if (order.isDoubleGlassSelected()) services.add("دبل جلاس");
            if (order.isPolishSelected()) services.add("Polish");
            if (order.isHoleSelected()) services.add("Hole");
            if (order.isCncSelected()) services.add("CNC");
            if (order.isDrawSelected()) services.add("Draw");
            if (order.isOtherServicesSelected()) services.add("خدمات أخرى");
            export.append(String.join(", ", services)).append("\n");
            
            export.append("-".repeat(50)).append("\n\n");
        }
        
        return export.toString();
    }
    
    /**
     * إنشاء بيانات تجريبية
     */
    private void initializeSampleData() {
        // يمكن إضافة بعض البيانات التجريبية هنا إذا لزم الأمر
    }
    
    /**
     * نسخ احتياطية من أوامر التصنيع
     */
    public List<ManufacturingOrder> createBackup() {
        return manufacturingOrders.stream()
                .map(ManufacturingOrder::copy)
                .collect(Collectors.toList());
    }
    
    /**
     * استعادة من النسخة الاحتياطية
     */
    public void restoreFromBackup(List<ManufacturingOrder> backup) {
        manufacturingOrders.clear();
        ordersMap.clear();
        
        for (ManufacturingOrder order : backup) {
            ManufacturingOrder copy = order.copy();
            manufacturingOrders.add(copy);
            ordersMap.put(copy.getOrderId(), copy);
        }
        
        // إعادة حساب رقم الأمر التالي
        nextOrderNumber = manufacturingOrders.stream()
                .mapToInt(order -> {
                    String number = order.getOrderNumber().replaceAll("\\D", "");
                    return number.isEmpty() ? 0 : Integer.parseInt(number);
                })
                .max()
                .orElse(0) + 1;
    }
}
