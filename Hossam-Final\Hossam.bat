@echo off 
setlocal 
cd /d "%~dp0" 
title Hossam Accounting System 
echo ======================================== 
echo       Hossam Accounting System 
echo ======================================== 
echo. 
echo Checking Java and JavaFX... 
echo. 
 
:: Try different Java paths 
set JAVA_FOUND=0 
 
:: Try Liberica JDK paths 
if exist "C:\Program Files\BellSoft\LibericaJDK-21\bin\java.exe" ( 
    set JAVA_HOME=C:\Program Files\BellSoft\LibericaJDK-21 
    set JAVA_FOUND=1 
    echo Using Liberica JDK 21 
) else if exist "C:\Program Files\BellSoft\LibericaJDK-17\bin\java.exe" ( 
    set JAVA_HOME=C:\Program Files\BellSoft\LibericaJDK-17 
    set JAVA_FOUND=1 
    echo Using Liberica JDK 17 
) else if exist "C:\Program Files\Java\jdk-24\bin\java.exe" ( 
    set JAVA_HOME=C:\Program Files\Java\jdk-24 
    set JAVA_FOUND=1 
    echo Using Oracle JDK 24 
) else ( 
    echo Using system Java 
    set JAVA_FOUND=1 
) 
 
if %JAVA_FOUND%==1 ( 
    if defined JAVA_HOME set PATH=%JAVA_HOME%\bin;%PATH% 
    echo Starting application... 
    echo. 
    java -Dfile.encoding=UTF-8 -jar accounting-system-1.0.0.jar 
) 
 
if %errorlevel% neq 0 ( 
    echo. 
    echo ❌ JavaFX is still missing 
    echo. 
    echo 🚀 GUARANTEED SOLUTION: 
    echo. 
    echo 1. Download Liberica JDK Full (includes JavaFX) 
    echo 2. This is 100% guaranteed to work 
    echo. 
    echo Opening download page... 
    start https://bell-sw.com/pages/downloads/ 
    echo. 
    echo Instructions: 
    echo 1. Choose: Liberica JDK 21 Full 
    echo 2. Download and install 
    echo 3. Run this file again 
    echo. 
    pause 
) 
