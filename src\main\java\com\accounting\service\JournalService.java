package com.accounting.service;

import com.accounting.model.*;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * خدمة إدارة القيود المحاسبية ودفتر اليومية
 */
public class JournalService {
    
    private static JournalService instance;
    private final ObservableList<JournalEntry> journalEntries = FXCollections.observableArrayList();
    private final Map<String, JournalEntry> entriesMap = new HashMap<>();
    private int nextEntryNumber = 1;
    
    // المراجع للخدمات الأخرى
    private final AccountService accountService;
    private final LedgerService ledgerService;
    
    private JournalService() {
        this.accountService = AccountService.getInstance();
        this.ledgerService = LedgerService.getInstance();
    }
    
    public static JournalService getInstance() {
        if (instance == null) {
            instance = new JournalService();
        }
        return instance;
    }
    
    /**
     * إضافة قيد جديد
     */
    public boolean addJournalEntry(JournalEntry entry) {
        if (entry == null) return false;
        
        // توليد رقم القيد إذا لم يكن موجوداً
        if (entry.getEntryNumber() == null || entry.getEntryNumber().trim().isEmpty()) {
            entry.setEntryNumber(generateEntryNumber());
        }
        
        // التحقق من عدم تكرار رقم القيد
        if (entriesMap.containsKey(entry.getEntryId())) {
            return false;
        }
        
        entry.setCreatedDate(LocalDateTime.now());
        entry.setStatus(JournalEntry.EntryStatus.DRAFT);
        
        journalEntries.add(entry);
        entriesMap.put(entry.getEntryId(), entry);
        
        return true;
    }
    
    /**
     * تحديث قيد موجود
     */
    public boolean updateJournalEntry(JournalEntry entry) {
        if (entry == null || !entriesMap.containsKey(entry.getEntryId())) {
            return false;
        }
        
        // التحقق من إمكانية التعديل
        if (!entry.canBeEdited()) {
            return false;
        }
        
        // تحديث في الخريطة
        entriesMap.put(entry.getEntryId(), entry);
        
        // تحديث في القائمة
        int index = journalEntries.indexOf(entry);
        if (index >= 0) {
            journalEntries.set(index, entry);
        }
        
        return true;
    }
    
    /**
     * حذف قيد
     */
    public boolean deleteJournalEntry(String entryId) {
        JournalEntry entry = getJournalEntryById(entryId);
        if (entry == null || !entry.canBeDeleted()) {
            return false;
        }
        
        // إذا كان القيد مرحلاً، يجب إلغاء الترحيل أولاً
        if (entry.getStatus() == JournalEntry.EntryStatus.POSTED) {
            unpostJournalEntry(entryId, "النظام");
        }
        
        journalEntries.remove(entry);
        entriesMap.remove(entryId);
        
        return true;
    }
    
    /**
     * اعتماد قيد
     */
    public boolean approveJournalEntry(String entryId, String approvedBy) {
        JournalEntry entry = getJournalEntryById(entryId);
        if (entry == null) return false;
        
        return entry.approve(approvedBy);
    }
    
    /**
     * ترحيل قيد إلى دفتر الأستاذ
     */
    public boolean postJournalEntry(String entryId, String postedBy) {
        JournalEntry entry = getJournalEntryById(entryId);
        if (entry == null || !entry.isValid()) {
            return false;
        }
        
        // اعتماد القيد أولاً إذا لم يكن معتمداً
        if (entry.getStatus() == JournalEntry.EntryStatus.DRAFT) {
            if (!entry.approve(postedBy)) {
                return false;
            }
        }
        
        // ترحيل القيد
        if (!entry.post(postedBy)) {
            return false;
        }
        
        // ترحيل التفاصيل إلى دفتر الأستاذ
        for (JournalEntryDetail detail : entry.getEntryDetails()) {
            Account account = accountService.getAccountByCode(detail.getAccountCode());
            if (account != null) {
                // إنشاء قيد في دفتر الأستاذ
                LedgerEntry ledgerEntry = new LedgerEntry(account, detail);
                ledgerEntry.setCreatedBy(postedBy);
                ledgerService.addLedgerEntry(ledgerEntry);
                
                // تحديث رصيد الحساب
                accountService.updateAccountBalance(
                    detail.getAccountCode(),
                    detail.getDebitAmount(),
                    detail.getCreditAmount()
                );
            }
        }
        
        return true;
    }
    
    /**
     * إلغاء ترحيل قيد
     */
    public boolean unpostJournalEntry(String entryId, String unpostedBy) {
        JournalEntry entry = getJournalEntryById(entryId);
        if (entry == null || entry.getStatus() != JournalEntry.EntryStatus.POSTED) {
            return false;
        }
        
        // إلغاء ترحيل التفاصيل من دفتر الأستاذ
        for (JournalEntryDetail detail : entry.getEntryDetails()) {
            ledgerService.removeLedgerEntriesByJournalDetail(detail.getDetailId());
            
            // عكس تحديث رصيد الحساب
            accountService.updateAccountBalance(
                detail.getAccountCode(),
                -detail.getDebitAmount(),
                -detail.getCreditAmount()
            );
        }
        
        // تغيير حالة القيد إلى معتمد
        entry.setStatus(JournalEntry.EntryStatus.APPROVED);
        entry.setPostedBy(null);
        entry.setPostedDate(null);
        
        return true;
    }
    
    /**
     * إلغاء قيد
     */
    public boolean cancelJournalEntry(String entryId) {
        JournalEntry entry = getJournalEntryById(entryId);
        if (entry == null) return false;
        
        // إذا كان مرحلاً، يجب إلغاء الترحيل أولاً
        if (entry.getStatus() == JournalEntry.EntryStatus.POSTED) {
            unpostJournalEntry(entryId, "النظام");
        }
        
        return entry.cancel();
    }
    
    /**
     * البحث عن قيد بالمعرف
     */
    public JournalEntry getJournalEntryById(String entryId) {
        return entriesMap.get(entryId);
    }
    
    /**
     * البحث عن قيد برقم القيد
     */
    public JournalEntry getJournalEntryByNumber(String entryNumber) {
        return journalEntries.stream()
                .filter(entry -> entry.getEntryNumber().equals(entryNumber))
                .findFirst()
                .orElse(null);
    }
    
    /**
     * الحصول على جميع القيود
     */
    public ObservableList<JournalEntry> getAllJournalEntries() {
        return journalEntries;
    }
    
    /**
     * الحصول على القيود حسب الحالة
     */
    public List<JournalEntry> getJournalEntriesByStatus(JournalEntry.EntryStatus status) {
        return journalEntries.stream()
                .filter(entry -> entry.getStatus() == status)
                .collect(Collectors.toList());
    }
    
    /**
     * الحصول على القيود حسب النوع
     */
    public List<JournalEntry> getJournalEntriesByType(JournalEntry.EntryType entryType) {
        return journalEntries.stream()
                .filter(entry -> entry.getEntryType() == entryType)
                .collect(Collectors.toList());
    }
    
    /**
     * الحصول على القيود حسب التاريخ
     */
    public List<JournalEntry> getJournalEntriesByDateRange(LocalDate fromDate, LocalDate toDate) {
        return journalEntries.stream()
                .filter(entry -> {
                    LocalDate entryDate = entry.getEntryDate();
                    return entryDate != null && 
                           !entryDate.isBefore(fromDate) && 
                           !entryDate.isAfter(toDate);
                })
                .collect(Collectors.toList());
    }
    
    /**
     * الحصول على القيود حسب الحساب
     */
    public List<JournalEntry> getJournalEntriesByAccount(String accountCode) {
        return journalEntries.stream()
                .filter(entry -> entry.getEntryDetails().stream()
                        .anyMatch(detail -> detail.getAccountCode().equals(accountCode)))
                .collect(Collectors.toList());
    }
    
    /**
     * البحث في القيود
     */
    public List<JournalEntry> searchJournalEntries(String searchText) {
        if (searchText == null || searchText.trim().isEmpty()) {
            return new ArrayList<>(journalEntries);
        }
        
        String searchLower = searchText.toLowerCase();
        return journalEntries.stream()
                .filter(entry -> 
                    entry.getEntryNumber().toLowerCase().contains(searchLower) ||
                    entry.getDescription().toLowerCase().contains(searchLower) ||
                    (entry.getReference() != null && entry.getReference().toLowerCase().contains(searchLower))
                )
                .collect(Collectors.toList());
    }
    
    /**
     * توليد رقم قيد جديد
     */
    public String generateEntryNumber() {
        String entryNumber;
        do {
            entryNumber = String.format("JE%06d", nextEntryNumber++);
        } while (getJournalEntryByNumber(entryNumber) != null);
        
        return entryNumber;
    }
    
    /**
     * إنشاء قيد من فاتورة مبيعات
     */
    public JournalEntry createSalesJournalEntry(String customerName, double totalAmount, 
                                               double taxAmount, LocalDate saleDate, String invoiceNumber) {
        JournalEntry entry = new JournalEntry();
        entry.setEntryNumber(generateEntryNumber());
        entry.setEntryDate(saleDate);
        entry.setDescription("قيد مبيعات - فاتورة رقم " + invoiceNumber);
        entry.setReference(invoiceNumber);
        entry.setEntryType(JournalEntry.EntryType.SALES);
        entry.setSourceDocument("فاتورة مبيعات");
        
        // العميل (مدين)
        JournalEntryDetail customerDetail = new JournalEntryDetail();
        customerDetail.setAccountCode("112"); // العملاء
        customerDetail.setAccountName("العملاء - " + customerName);
        customerDetail.setDebitAmount(totalAmount + taxAmount);
        customerDetail.setCreditAmount(0.0);
        customerDetail.setDescription("مبيعات للعميل " + customerName);
        entry.addEntryDetail(customerDetail);
        
        // المبيعات (دائن)
        JournalEntryDetail salesDetail = new JournalEntryDetail();
        salesDetail.setAccountCode("411"); // مبيعات
        salesDetail.setAccountName("مبيعات الزجاج");
        salesDetail.setDebitAmount(0.0);
        salesDetail.setCreditAmount(totalAmount);
        salesDetail.setDescription("مبيعات زجاج وألومنيوم");
        entry.addEntryDetail(salesDetail);
        
        // الضريبة إذا وجدت (دائن)
        if (taxAmount > 0) {
            JournalEntryDetail taxDetail = new JournalEntryDetail();
            taxDetail.setAccountCode("214"); // ضريبة مستحقة
            taxDetail.setAccountName("ضريبة القيمة المضافة");
            taxDetail.setDebitAmount(0.0);
            taxDetail.setCreditAmount(taxAmount);
            taxDetail.setDescription("ضريبة قيمة مضافة");
            entry.addEntryDetail(taxDetail);
        }
        
        return entry;
    }
    
    /**
     * إنشاء قيد من فاتورة مشتريات
     */
    public JournalEntry createPurchaseJournalEntry(String supplierName, double totalAmount, 
                                                  double taxAmount, LocalDate purchaseDate, String invoiceNumber) {
        JournalEntry entry = new JournalEntry();
        entry.setEntryNumber(generateEntryNumber());
        entry.setEntryDate(purchaseDate);
        entry.setDescription("قيد مشتريات - فاتورة رقم " + invoiceNumber);
        entry.setReference(invoiceNumber);
        entry.setEntryType(JournalEntry.EntryType.PURCHASES);
        entry.setSourceDocument("فاتورة مشتريات");
        
        // المشتريات (مدين)
        JournalEntryDetail purchaseDetail = new JournalEntryDetail();
        purchaseDetail.setAccountCode("61"); // تكلفة المواد الخام
        purchaseDetail.setAccountName("تكلفة المواد الخام");
        purchaseDetail.setDebitAmount(totalAmount);
        purchaseDetail.setCreditAmount(0.0);
        purchaseDetail.setDescription("مشتريات مواد خام");
        entry.addEntryDetail(purchaseDetail);
        
        // الضريبة إذا وجدت (مدين)
        if (taxAmount > 0) {
            JournalEntryDetail taxDetail = new JournalEntryDetail();
            taxDetail.setAccountCode("115"); // ضريبة مدفوعة مقدماً
            taxDetail.setAccountName("ضريبة القيمة المضافة المدفوعة");
            taxDetail.setDebitAmount(taxAmount);
            taxDetail.setCreditAmount(0.0);
            taxDetail.setDescription("ضريبة قيمة مضافة مدفوعة");
            entry.addEntryDetail(taxDetail);
        }
        
        // المورد (دائن)
        JournalEntryDetail supplierDetail = new JournalEntryDetail();
        supplierDetail.setAccountCode("211"); // الموردون
        supplierDetail.setAccountName("الموردون - " + supplierName);
        supplierDetail.setDebitAmount(0.0);
        supplierDetail.setCreditAmount(totalAmount + taxAmount);
        supplierDetail.setDescription("مشتريات من المورد " + supplierName);
        entry.addEntryDetail(supplierDetail);
        
        return entry;
    }

    /**
     * إنشاء قيد دفع
     */
    public JournalEntry createPaymentJournalEntry(String paymentMethod, String payeeName,
                                                 double amount, LocalDate paymentDate, String reference) {
        JournalEntry entry = new JournalEntry();
        entry.setEntryNumber(generateEntryNumber());
        entry.setEntryDate(paymentDate);
        entry.setDescription("قيد دفع - " + payeeName);
        entry.setReference(reference);
        entry.setEntryType(JournalEntry.EntryType.PAYMENT);
        entry.setSourceDocument("سند دفع");

        // الحساب المدفوع له (مدين)
        JournalEntryDetail payeeDetail = new JournalEntryDetail();
        payeeDetail.setAccountCode("211"); // الموردون أو حساب آخر
        payeeDetail.setAccountName(payeeName);
        payeeDetail.setDebitAmount(amount);
        payeeDetail.setCreditAmount(0.0);
        payeeDetail.setDescription("دفع لـ " + payeeName);
        entry.addEntryDetail(payeeDetail);

        // وسيلة الدفع (دائن)
        JournalEntryDetail paymentDetail = new JournalEntryDetail();
        if ("نقدي".equals(paymentMethod)) {
            paymentDetail.setAccountCode("1111"); // الصندوق
            paymentDetail.setAccountName("الصندوق");
        } else {
            paymentDetail.setAccountCode("1112"); // البنك
            paymentDetail.setAccountName("البنك");
        }
        paymentDetail.setDebitAmount(0.0);
        paymentDetail.setCreditAmount(amount);
        paymentDetail.setDescription("دفع " + paymentMethod);
        entry.addEntryDetail(paymentDetail);

        return entry;
    }

    /**
     * إنشاء قيد قبض
     */
    public JournalEntry createReceiptJournalEntry(String receiptMethod, String payerName,
                                                 double amount, LocalDate receiptDate, String reference) {
        JournalEntry entry = new JournalEntry();
        entry.setEntryNumber(generateEntryNumber());
        entry.setEntryDate(receiptDate);
        entry.setDescription("قيد قبض - " + payerName);
        entry.setReference(reference);
        entry.setEntryType(JournalEntry.EntryType.RECEIPT);
        entry.setSourceDocument("سند قبض");

        // وسيلة القبض (مدين)
        JournalEntryDetail receiptDetail = new JournalEntryDetail();
        if ("نقدي".equals(receiptMethod)) {
            receiptDetail.setAccountCode("1111"); // الصندوق
            receiptDetail.setAccountName("الصندوق");
        } else {
            receiptDetail.setAccountCode("1112"); // البنك
            receiptDetail.setAccountName("البنك");
        }
        receiptDetail.setDebitAmount(amount);
        receiptDetail.setCreditAmount(0.0);
        receiptDetail.setDescription("قبض " + receiptMethod);
        entry.addEntryDetail(receiptDetail);

        // الحساب المقبوض منه (دائن)
        JournalEntryDetail payerDetail = new JournalEntryDetail();
        payerDetail.setAccountCode("112"); // العملاء أو حساب آخر
        payerDetail.setAccountName(payerName);
        payerDetail.setDebitAmount(0.0);
        payerDetail.setCreditAmount(amount);
        payerDetail.setDescription("قبض من " + payerName);
        entry.addEntryDetail(payerDetail);

        return entry;
    }

    /**
     * الحصول على إحصائيات القيود
     */
    public Map<String, Object> getJournalStatistics() {
        Map<String, Object> stats = new HashMap<>();

        stats.put("إجمالي القيود", journalEntries.size());
        stats.put("القيود المسودة", getJournalEntriesByStatus(JournalEntry.EntryStatus.DRAFT).size());
        stats.put("القيود المعتمدة", getJournalEntriesByStatus(JournalEntry.EntryStatus.APPROVED).size());
        stats.put("القيود المرحلة", getJournalEntriesByStatus(JournalEntry.EntryStatus.POSTED).size());
        stats.put("القيود الملغية", getJournalEntriesByStatus(JournalEntry.EntryStatus.CANCELLED).size());

        // إحصائيات حسب النوع
        for (JournalEntry.EntryType type : JournalEntry.EntryType.values()) {
            stats.put("قيود " + type.getArabicName(), getJournalEntriesByType(type).size());
        }

        // إجمالي المبالغ
        double totalDebits = journalEntries.stream()
                .filter(entry -> entry.getStatus() == JournalEntry.EntryStatus.POSTED)
                .mapToDouble(JournalEntry::getTotalDebit)
                .sum();
        double totalCredits = journalEntries.stream()
                .filter(entry -> entry.getStatus() == JournalEntry.EntryStatus.POSTED)
                .mapToDouble(JournalEntry::getTotalCredit)
                .sum();

        stats.put("إجمالي المدين", totalDebits);
        stats.put("إجمالي الدائن", totalCredits);
        stats.put("الفرق", Math.abs(totalDebits - totalCredits));

        return stats;
    }

    /**
     * التحقق من توازن جميع القيود
     */
    public boolean areAllEntriesBalanced() {
        return journalEntries.stream()
                .filter(entry -> entry.getStatus() == JournalEntry.EntryStatus.POSTED)
                .allMatch(JournalEntry::isBalanced);
    }

    /**
     * الحصول على القيود غير المتوازنة
     */
    public List<JournalEntry> getUnbalancedEntries() {
        return journalEntries.stream()
                .filter(entry -> !entry.isBalanced())
                .collect(Collectors.toList());
    }

    /**
     * ترحيل جميع القيود المعتمدة
     */
    public int postAllApprovedEntries(String postedBy) {
        List<JournalEntry> approvedEntries = getJournalEntriesByStatus(JournalEntry.EntryStatus.APPROVED);
        int postedCount = 0;

        for (JournalEntry entry : approvedEntries) {
            if (postJournalEntry(entry.getEntryId(), postedBy)) {
                postedCount++;
            }
        }

        return postedCount;
    }

    /**
     * إعادة ترقيم القيود
     */
    public void renumberEntries() {
        List<JournalEntry> sortedEntries = journalEntries.stream()
                .sorted(Comparator.comparing(JournalEntry::getEntryDate)
                        .thenComparing(JournalEntry::getCreatedDate))
                .collect(Collectors.toList());

        int number = 1;
        for (JournalEntry entry : sortedEntries) {
            entry.setEntryNumber(String.format("JE%06d", number++));
        }

        nextEntryNumber = number;
    }

    /**
     * تصدير القيود إلى نص
     */
    public String exportJournalToText(LocalDate fromDate, LocalDate toDate) {
        StringBuilder export = new StringBuilder();
        export.append("دفتر اليومية\n");
        export.append("من ").append(fromDate).append(" إلى ").append(toDate).append("\n");
        export.append("=".repeat(50)).append("\n\n");

        List<JournalEntry> entries = getJournalEntriesByDateRange(fromDate, toDate);
        entries.sort(Comparator.comparing(JournalEntry::getEntryDate)
                .thenComparing(JournalEntry::getEntryNumber));

        for (JournalEntry entry : entries) {
            export.append("رقم القيد: ").append(entry.getEntryNumber()).append("\n");
            export.append("التاريخ: ").append(entry.getEntryDate()).append("\n");
            export.append("الوصف: ").append(entry.getDescription()).append("\n");
            export.append("الحالة: ").append(entry.getStatus().getArabicName()).append("\n");
            export.append("-".repeat(30)).append("\n");

            for (JournalEntryDetail detail : entry.getEntryDetails()) {
                export.append(String.format("%-10s %-30s %10.2f %10.2f\n",
                        detail.getAccountCode(),
                        detail.getAccountName(),
                        detail.getDebitAmount(),
                        detail.getCreditAmount()));
            }

            export.append(String.format("المجموع: %38.2f %10.2f\n",
                    entry.getTotalDebit(), entry.getTotalCredit()));
            export.append("\n");
        }

        return export.toString();
    }

    /**
     * نسخ احتياطية من القيود
     */
    public List<JournalEntry> createBackup() {
        return journalEntries.stream()
                .map(JournalEntry::copy)
                .collect(Collectors.toList());
    }

    /**
     * استعادة من النسخة الاحتياطية
     */
    public void restoreFromBackup(List<JournalEntry> backup) {
        journalEntries.clear();
        entriesMap.clear();

        for (JournalEntry entry : backup) {
            JournalEntry copy = entry.copy();
            journalEntries.add(copy);
            entriesMap.put(copy.getEntryId(), copy);
        }

        // إعادة حساب رقم القيد التالي
        nextEntryNumber = journalEntries.stream()
                .mapToInt(entry -> {
                    String number = entry.getEntryNumber().replaceAll("\\D", "");
                    return number.isEmpty() ? 0 : Integer.parseInt(number);
                })
                .max()
                .orElse(0) + 1;
    }
}
