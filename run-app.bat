@echo off
chcp 65001 > nul
setlocal

echo.
echo ========================================
echo 🚀 تشغيل نظام المحاسبة
echo ========================================
echo.

:: تحديد متغيرات البيئة
set JAVA_HOME=C:\Program Files\Eclipse Adoptium\jdk-********-hotspot
set PATH=%JAVA_HOME%\bin;%PATH%

:: فحص وجود Java
java -version > nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Java غير مثبت أو غير موجود
    echo.
    echo 💡 يرجى تثبيت Java 17 أو أحدث من:
    echo    https://adoptium.net/
    echo.
    pause
    exit /b 1
)

echo ✅ Java متوفر
echo.

:: التحقق من وجود JAR المبني
if exist "target\accounting-system-1.0.0.jar" (
    echo 🎯 تشغيل البرنامج من JAR المبني...
    java -Dfile.encoding=UTF-8 -Djava.awt.headless=false -jar target\accounting-system-1.0.0.jar
) else (
    echo 📦 بناء وتشغيل البرنامج...
    call mvnw.cmd javafx:run
)

if %errorlevel% neq 0 (
    echo.
    echo ❌ فشل في تشغيل البرنامج
    echo.
    echo 🔍 للمساعدة في حل المشكلة:
    echo    1. تأكد من تثبيت Java 17 أو أحدث
    echo    2. تأكد من اتصال الإنترنت لتحميل المكتبات
    echo    3. قم بتشغيل build-installer.bat أولاً
    echo.
    pause
    exit /b 1
)

echo.
echo ✅ تم إغلاق البرنامج بنجاح
pause
