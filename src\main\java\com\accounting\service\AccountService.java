package com.accounting.service;

import com.accounting.model.Account;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * خدمة إدارة الحسابات ودليل الحسابات
 */
public class AccountService {
    
    private static AccountService instance;
    private final ObservableList<Account> accounts = FXCollections.observableArrayList();
    private final Map<String, Account> accountsMap = new HashMap<>();
    
    private AccountService() {
        initializeDefaultAccounts();
    }
    
    public static AccountService getInstance() {
        if (instance == null) {
            instance = new AccountService();
        }
        return instance;
    }
    
    /**
     * تهيئة الحسابات الافتراضية
     */
    private void initializeDefaultAccounts() {
        // الأصول
        addAccount(new Account("1", "الأصول", Account.AccountType.ASSETS, Account.AccountNature.DEBIT));
        addAccount(new Account("11", "الأصول المتداولة", Account.AccountType.ASSETS, Account.AccountNature.DEBIT));
        addAccount(new Account("111", "النقدية", Account.AccountType.ASSETS, Account.AccountNature.DEBIT));
        addAccount(new Account("1111", "الصندوق", Account.AccountType.ASSETS, Account.AccountNature.DEBIT));
        addAccount(new Account("1112", "البنك", Account.AccountType.ASSETS, Account.AccountNature.DEBIT));
        addAccount(new Account("112", "العملاء", Account.AccountType.ASSETS, Account.AccountNature.DEBIT));
        addAccount(new Account("113", "المخزون", Account.AccountType.ASSETS, Account.AccountNature.DEBIT));
        
        addAccount(new Account("12", "الأصول الثابتة", Account.AccountType.ASSETS, Account.AccountNature.DEBIT));
        addAccount(new Account("121", "الأراضي والمباني", Account.AccountType.ASSETS, Account.AccountNature.DEBIT));
        addAccount(new Account("122", "المعدات والآلات", Account.AccountType.ASSETS, Account.AccountNature.DEBIT));
        addAccount(new Account("123", "وسائل النقل", Account.AccountType.ASSETS, Account.AccountNature.DEBIT));
        
        // الخصوم
        addAccount(new Account("2", "الخصوم", Account.AccountType.LIABILITIES, Account.AccountNature.CREDIT));
        addAccount(new Account("21", "الخصوم المتداولة", Account.AccountType.LIABILITIES, Account.AccountNature.CREDIT));
        addAccount(new Account("211", "الموردون", Account.AccountType.LIABILITIES, Account.AccountNature.CREDIT));
        addAccount(new Account("212", "أوراق الدفع", Account.AccountType.LIABILITIES, Account.AccountNature.CREDIT));
        addAccount(new Account("213", "مصروفات مستحقة", Account.AccountType.LIABILITIES, Account.AccountNature.CREDIT));
        
        addAccount(new Account("22", "الخصوم طويلة الأجل", Account.AccountType.LIABILITIES, Account.AccountNature.CREDIT));
        addAccount(new Account("221", "قروض طويلة الأجل", Account.AccountType.LIABILITIES, Account.AccountNature.CREDIT));
        
        // حقوق الملكية
        addAccount(new Account("3", "حقوق الملكية", Account.AccountType.EQUITY, Account.AccountNature.CREDIT));
        addAccount(new Account("31", "رأس المال", Account.AccountType.EQUITY, Account.AccountNature.CREDIT));
        addAccount(new Account("32", "الأرباح المحتجزة", Account.AccountType.EQUITY, Account.AccountNature.CREDIT));
        addAccount(new Account("33", "أرباح العام الحالي", Account.AccountType.EQUITY, Account.AccountNature.CREDIT));
        
        // الإيرادات
        addAccount(new Account("4", "الإيرادات", Account.AccountType.REVENUE, Account.AccountNature.CREDIT));
        addAccount(new Account("41", "إيرادات المبيعات", Account.AccountType.REVENUE, Account.AccountNature.CREDIT));
        addAccount(new Account("411", "مبيعات الزجاج", Account.AccountType.REVENUE, Account.AccountNature.CREDIT));
        addAccount(new Account("412", "مبيعات الألومنيوم", Account.AccountType.REVENUE, Account.AccountNature.CREDIT));
        addAccount(new Account("42", "إيرادات أخرى", Account.AccountType.REVENUE, Account.AccountNature.CREDIT));
        
        // المصروفات
        addAccount(new Account("5", "المصروفات", Account.AccountType.EXPENSES, Account.AccountNature.DEBIT));
        addAccount(new Account("51", "مصروفات التشغيل", Account.AccountType.EXPENSES, Account.AccountNature.DEBIT));
        addAccount(new Account("511", "الرواتب والأجور", Account.AccountType.EXPENSES, Account.AccountNature.DEBIT));
        addAccount(new Account("512", "الإيجار", Account.AccountType.EXPENSES, Account.AccountNature.DEBIT));
        addAccount(new Account("513", "الكهرباء والمياه", Account.AccountType.EXPENSES, Account.AccountNature.DEBIT));
        addAccount(new Account("514", "مصروفات النقل", Account.AccountType.EXPENSES, Account.AccountNature.DEBIT));
        
        addAccount(new Account("52", "مصروفات إدارية", Account.AccountType.EXPENSES, Account.AccountNature.DEBIT));
        addAccount(new Account("521", "مصروفات مكتبية", Account.AccountType.EXPENSES, Account.AccountNature.DEBIT));
        addAccount(new Account("522", "مصروفات اتصالات", Account.AccountType.EXPENSES, Account.AccountNature.DEBIT));
        
        // تكلفة البضاعة المباعة
        addAccount(new Account("6", "تكلفة البضاعة المباعة", Account.AccountType.COST_OF_GOODS_SOLD, Account.AccountNature.DEBIT));
        addAccount(new Account("61", "تكلفة المواد الخام", Account.AccountType.COST_OF_GOODS_SOLD, Account.AccountNature.DEBIT));
        addAccount(new Account("62", "تكلفة العمالة المباشرة", Account.AccountType.COST_OF_GOODS_SOLD, Account.AccountNature.DEBIT));
        addAccount(new Account("63", "المصروفات الصناعية", Account.AccountType.COST_OF_GOODS_SOLD, Account.AccountNature.DEBIT));
        
        // إعداد التفرع الهرمي
        setupAccountHierarchy();
    }
    
    /**
     * إعداد التفرع الهرمي للحسابات
     */
    private void setupAccountHierarchy() {
        for (Account account : accounts) {
            String parentCode = getParentAccountCode(account.getAccountCode());
            if (parentCode != null) {
                Account parent = getAccountByCode(parentCode);
                if (parent != null) {
                    parent.addSubAccount(account);
                    account.setParentAccountCode(parentCode);
                }
            }
        }
    }
    
    /**
     * الحصول على رمز الحساب الأب
     */
    private String getParentAccountCode(String accountCode) {
        if (accountCode == null || accountCode.length() <= 1) return null;
        return accountCode.substring(0, accountCode.length() - 1);
    }
    
    /**
     * إضافة حساب جديد
     */
    public boolean addAccount(Account account) {
        if (account == null || account.getAccountCode() == null || account.getAccountCode().trim().isEmpty()) {
            return false;
        }
        
        // التحقق من عدم تكرار رمز الحساب
        if (accountsMap.containsKey(account.getAccountCode())) {
            return false;
        }
        
        account.setCreatedDate(LocalDateTime.now());
        account.setLastModified(LocalDateTime.now());
        
        accounts.add(account);
        accountsMap.put(account.getAccountCode(), account);
        
        // إعداد التفرع الهرمي
        String parentCode = getParentAccountCode(account.getAccountCode());
        if (parentCode != null) {
            Account parent = getAccountByCode(parentCode);
            if (parent != null) {
                parent.addSubAccount(account);
                account.setParentAccountCode(parentCode);
            }
        }
        
        return true;
    }
    
    /**
     * تحديث حساب موجود
     */
    public boolean updateAccount(Account account) {
        if (account == null || !accountsMap.containsKey(account.getAccountCode())) {
            return false;
        }
        
        account.setLastModified(LocalDateTime.now());
        
        // تحديث في الخريطة
        accountsMap.put(account.getAccountCode(), account);
        
        // تحديث في القائمة
        int index = accounts.indexOf(account);
        if (index >= 0) {
            accounts.set(index, account);
        }
        
        return true;
    }
    
    /**
     * حذف حساب
     */
    public boolean deleteAccount(String accountCode) {
        Account account = getAccountByCode(accountCode);
        if (account == null || !account.canBeDeleted()) {
            return false;
        }
        
        // إزالة من الحساب الأب
        if (account.getParentAccount() != null) {
            account.getParentAccount().removeSubAccount(account);
        }
        
        // إزالة من القوائم
        accounts.remove(account);
        accountsMap.remove(accountCode);
        
        return true;
    }
    
    /**
     * البحث عن حساب برمز الحساب
     */
    public Account getAccountByCode(String accountCode) {
        return accountsMap.get(accountCode);
    }
    
    /**
     * البحث عن حساب باسم الحساب
     */
    public Account getAccountByName(String accountName) {
        return accounts.stream()
                .filter(account -> account.getAccountName().equals(accountName))
                .findFirst()
                .orElse(null);
    }
    
    /**
     * الحصول على جميع الحسابات
     */
    public ObservableList<Account> getAllAccounts() {
        return accounts;
    }
    
    /**
     * الحصول على الحسابات حسب النوع
     */
    public List<Account> getAccountsByType(Account.AccountType accountType) {
        return accounts.stream()
                .filter(account -> account.getAccountType() == accountType)
                .collect(Collectors.toList());
    }
    
    /**
     * الحصول على الحسابات حسب الطبيعة
     */
    public List<Account> getAccountsByNature(Account.AccountNature accountNature) {
        return accounts.stream()
                .filter(account -> account.getAccountNature() == accountNature)
                .collect(Collectors.toList());
    }
    
    /**
     * الحصول على الحسابات الفرعية
     */
    public List<Account> getSubAccounts(String parentAccountCode) {
        Account parent = getAccountByCode(parentAccountCode);
        return parent != null ? parent.getSubAccounts() : new ArrayList<>();
    }
    
    /**
     * الحصول على الحسابات الأوراق (غير الأب)
     */
    public List<Account> getLeafAccounts() {
        return accounts.stream()
                .filter(account -> !account.isParent())
                .collect(Collectors.toList());
    }
    
    /**
     * الحصول على الحسابات النشطة
     */
    public List<Account> getActiveAccounts() {
        return accounts.stream()
                .filter(Account::isActive)
                .collect(Collectors.toList());
    }
    
    /**
     * البحث في الحسابات
     */
    public List<Account> searchAccounts(String searchText) {
        if (searchText == null || searchText.trim().isEmpty()) {
            return new ArrayList<>(accounts);
        }
        
        String searchLower = searchText.toLowerCase();
        return accounts.stream()
                .filter(account -> 
                    account.getAccountCode().toLowerCase().contains(searchLower) ||
                    account.getAccountName().toLowerCase().contains(searchLower) ||
                    (account.getAccountNameEn() != null && account.getAccountNameEn().toLowerCase().contains(searchLower))
                )
                .collect(Collectors.toList());
    }
    
    /**
     * تحديث رصيد حساب
     */
    public void updateAccountBalance(String accountCode, double debitAmount, double creditAmount) {
        Account account = getAccountByCode(accountCode);
        if (account != null) {
            account.updateBalance(debitAmount, creditAmount);
        }
    }
    
    /**
     * الحصول على شجرة الحسابات
     */
    public List<Account> getAccountsTree() {
        return accounts.stream()
                .filter(account -> account.getParentAccount() == null)
                .sorted(Comparator.comparing(Account::getAccountCode))
                .collect(Collectors.toList());
    }
    
    /**
     * الحصول على مسار الحساب الكامل
     */
    public String getAccountFullPath(String accountCode) {
        Account account = getAccountByCode(accountCode);
        return account != null ? account.getFullPath() : "";
    }
    
    /**
     * التحقق من صحة رمز الحساب
     */
    public boolean isValidAccountCode(String accountCode) {
        return accountCode != null && 
               !accountCode.trim().isEmpty() && 
               accountCode.matches("^[0-9]+$") && // أرقام فقط
               !accountsMap.containsKey(accountCode);
    }
    
    /**
     * توليد رمز حساب جديد
     */
    public String generateAccountCode(String parentAccountCode) {
        if (parentAccountCode == null || parentAccountCode.trim().isEmpty()) {
            // حساب رئيسي
            int maxCode = accounts.stream()
                    .filter(account -> account.getLevel() == 1)
                    .mapToInt(account -> Integer.parseInt(account.getAccountCode()))
                    .max()
                    .orElse(0);
            return String.valueOf(maxCode + 1);
        } else {
            // حساب فرعي
            int maxSubCode = accounts.stream()
                    .filter(account -> account.getAccountCode().startsWith(parentAccountCode) && 
                                     account.getAccountCode().length() == parentAccountCode.length() + 1)
                    .mapToInt(account -> Integer.parseInt(account.getAccountCode().substring(parentAccountCode.length())))
                    .max()
                    .orElse(0);
            return parentAccountCode + (maxSubCode + 1);
        }
    }
    
    /**
     * إعادة تعيين جميع الأرصدة
     */
    public void resetAllBalances() {
        for (Account account : accounts) {
            account.setCurrentBalance(account.getOpeningBalance());
            account.setDebitTotal(0.0);
            account.setCreditTotal(0.0);
        }
    }
    
    /**
     * الحصول على إحصائيات الحسابات
     */
    public Map<String, Integer> getAccountsStatistics() {
        Map<String, Integer> stats = new HashMap<>();
        stats.put("إجمالي الحسابات", accounts.size());
        stats.put("الحسابات النشطة", (int) accounts.stream().filter(Account::isActive).count());
        stats.put("الحسابات الرئيسية", (int) accounts.stream().filter(account -> account.getLevel() == 1).count());
        stats.put("الحسابات الفرعية", (int) accounts.stream().filter(Account::isParent).count());
        stats.put("حسابات الأصول", getAccountsByType(Account.AccountType.ASSETS).size());
        stats.put("حسابات الخصوم", getAccountsByType(Account.AccountType.LIABILITIES).size());
        stats.put("حسابات حقوق الملكية", getAccountsByType(Account.AccountType.EQUITY).size());
        stats.put("حسابات الإيرادات", getAccountsByType(Account.AccountType.REVENUE).size());
        stats.put("حسابات المصروفات", getAccountsByType(Account.AccountType.EXPENSES).size());
        return stats;
    }
    
    /**
     * تصدير دليل الحسابات
     */
    public String exportAccountsToText() {
        StringBuilder export = new StringBuilder();
        export.append("دليل الحسابات\n");
        export.append("=============\n\n");
        
        for (Account.AccountType type : Account.AccountType.values()) {
            export.append(type.getArabicName()).append(":\n");
            export.append("-".repeat(type.getArabicName().length() + 1)).append("\n");
            
            List<Account> typeAccounts = getAccountsByType(type);
            for (Account account : typeAccounts) {
                String indent = "  ".repeat(account.getLevel() - 1);
                export.append(indent).append(account.getAccountCode())
                      .append(" - ").append(account.getAccountName())
                      .append(" (").append(account.getAccountNature().getArabicName()).append(")")
                      .append("\n");
            }
            export.append("\n");
        }
        
        return export.toString();
    }

    /**
     * استيراد دليل الحسابات من ملف
     */
    public boolean importAccountsFromText(String importText) {
        // هذه الوظيفة يمكن تطويرها لاحقاً لاستيراد الحسابات من ملف نصي
        return false;
    }

    /**
     * نسخ احتياطية من دليل الحسابات
     */
    public List<Account> createBackup() {
        return accounts.stream()
                .map(Account::copy)
                .collect(Collectors.toList());
    }

    /**
     * استعادة من النسخة الاحتياطية
     */
    public void restoreFromBackup(List<Account> backup) {
        accounts.clear();
        accountsMap.clear();

        for (Account account : backup) {
            addAccount(account.copy());
        }

        setupAccountHierarchy();
    }
}
