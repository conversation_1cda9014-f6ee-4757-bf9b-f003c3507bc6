package com.accounting.service;

import com.accounting.model.ReceiptOrder;
import com.accounting.model.ReceiptItem;
import com.accounting.model.ManufacturingOrder;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * خدمة أوامر الاستلام
 */
public class ReceiptOrderService {
    
    private static ReceiptOrderService instance;
    private final ObservableList<ReceiptOrder> receiptOrders;
    private final Map<String, ReceiptOrder> ordersMap;
    private int orderCounter = 1;
    
    private ReceiptOrderService() {
        this.receiptOrders = FXCollections.observableArrayList();
        this.ordersMap = new HashMap<>();
        initializeSampleData();
    }
    
    /**
     * الحصول على مثيل الخدمة (Singleton)
     */
    public static ReceiptOrderService getInstance() {
        if (instance == null) {
            instance = new ReceiptOrderService();
        }
        return instance;
    }
    
    /**
     * تهيئة بيانات تجريبية
     */
    private void initializeSampleData() {
        // إنشاء أوامر استلام تجريبية
        ReceiptOrder order1 = new ReceiptOrder("RO-001", "شركة الزجاج المتقدم", LocalDate.now().minusDays(5));
        order1.setManufacturingOrderNumber("MO-001");
        order1.setInvoiceNumber("INV-001");
        order1.setStatus(ReceiptOrder.ReceiptStatus.CONFIRMED);
        order1.setNotes("استلام زجاج للمشروع الأول");
        
        // إضافة عناصر للأمر الأول
        ReceiptItem item1 = new ReceiptItem("زجاج شفاف 6مم", 50, "متر مربع");
        item1.setReceivedQuantity(30);
        item1.setNotes("استلام جزئي");
        order1.addReceiptItem(item1);
        
        ReceiptItem item2 = new ReceiptItem("زجاج ملون 8مم", 25, "متر مربع");
        item2.setReceivedQuantity(25);
        order1.addReceiptItem(item2);
        
        addReceiptOrder(order1);
        
        // أمر استلام ثاني
        ReceiptOrder order2 = new ReceiptOrder("RO-002", "مصنع الألومنيوم الحديث", LocalDate.now().minusDays(2));
        order2.setManufacturingOrderNumber("MO-002");
        order2.setInvoiceNumber("INV-002");
        order2.setStatus(ReceiptOrder.ReceiptStatus.IN_RECEIPT);
        order2.setNotes("استلام ألومنيوم للمشروع الثاني");
        
        // إضافة عناصر للأمر الثاني
        ReceiptItem item3 = new ReceiptItem("ألومنيوم أبيض", 100, "متر طولي");
        item3.setReceivedQuantity(0);
        order2.addReceiptItem(item3);
        
        ReceiptItem item4 = new ReceiptItem("ألومنيوم بني", 75, "متر طولي");
        item4.setReceivedQuantity(0);
        order2.addReceiptItem(item4);
        
        addReceiptOrder(order2);
        
        orderCounter = 3; // تحديث العداد
    }
    
    /**
     * إضافة أمر استلام جديد
     */
    public boolean addReceiptOrder(ReceiptOrder order) {
        if (order == null) return false;
        
        // تعيين رقم أمر إذا لم يكن موجود
        if (order.getOrderNumber() == null || order.getOrderNumber().isEmpty()) {
            order.setOrderNumber(generateOrderNumber());
        }
        
        receiptOrders.add(order);
        ordersMap.put(order.getReceiptOrderId(), order);
        return true;
    }
    
    /**
     * تحديث أمر استلام
     */
    public boolean updateReceiptOrder(ReceiptOrder order) {
        if (order == null || !ordersMap.containsKey(order.getReceiptOrderId())) {
            return false;
        }
        
        ordersMap.put(order.getReceiptOrderId(), order);
        
        // تحديث القائمة
        int index = receiptOrders.indexOf(order);
        if (index >= 0) {
            receiptOrders.set(index, order);
        }
        
        return true;
    }
    
    /**
     * حذف أمر استلام
     */
    public boolean deleteReceiptOrder(String orderId) {
        ReceiptOrder order = ordersMap.remove(orderId);
        if (order != null) {
            receiptOrders.remove(order);
            return true;
        }
        return false;
    }
    
    /**
     * البحث عن أمر بالمعرف
     */
    public ReceiptOrder findReceiptOrderById(String orderId) {
        return ordersMap.get(orderId);
    }
    
    /**
     * البحث عن أمر برقم الأمر
     */
    public ReceiptOrder findReceiptOrderByNumber(String orderNumber) {
        return receiptOrders.stream()
                .filter(order -> order.getOrderNumber().equals(orderNumber))
                .findFirst()
                .orElse(null);
    }
    
    /**
     * الحصول على جميع أوامر الاستلام
     */
    public ObservableList<ReceiptOrder> getAllReceiptOrders() {
        return receiptOrders;
    }
    
    /**
     * الحصول على أوامر الاستلام حسب الحالة
     */
    public List<ReceiptOrder> getReceiptOrdersByStatus(ReceiptOrder.ReceiptStatus status) {
        return receiptOrders.stream()
                .filter(order -> order.getStatus() == status)
                .collect(Collectors.toList());
    }
    
    /**
     * الحصول على أوامر الاستلام حسب المورد
     */
    public List<ReceiptOrder> getReceiptOrdersBySupplier(String supplierName) {
        return receiptOrders.stream()
                .filter(order -> order.getSupplierName().toLowerCase().contains(supplierName.toLowerCase()))
                .collect(Collectors.toList());
    }
    
    /**
     * الحصول على أوامر الاستلام في فترة زمنية
     */
    public List<ReceiptOrder> getReceiptOrdersByDateRange(LocalDate fromDate, LocalDate toDate) {
        return receiptOrders.stream()
                .filter(order -> {
                    LocalDate orderDate = order.getReceiptDate();
                    return orderDate != null && 
                           !orderDate.isBefore(fromDate) && 
                           !orderDate.isAfter(toDate);
                })
                .collect(Collectors.toList());
    }
    
    /**
     * تأكيد أمر الاستلام
     */
    public boolean confirmReceiptOrder(String orderId) {
        ReceiptOrder order = findReceiptOrderById(orderId);
        if (order != null && order.getStatus() == ReceiptOrder.ReceiptStatus.DRAFT) {
            order.setStatus(ReceiptOrder.ReceiptStatus.CONFIRMED);
            return updateReceiptOrder(order);
        }
        return false;
    }
    
    /**
     * إلغاء أمر الاستلام
     */
    public boolean cancelReceiptOrder(String orderId) {
        ReceiptOrder order = findReceiptOrderById(orderId);
        if (order != null && order.getStatus() != ReceiptOrder.ReceiptStatus.RECEIVED) {
            order.setStatus(ReceiptOrder.ReceiptStatus.CANCELLED);
            return updateReceiptOrder(order);
        }
        return false;
    }
    
    /**
     * إنشاء أمر استلام من أمر تصنيع
     */
    public ReceiptOrder createFromManufacturingOrder(ManufacturingOrder manufacturingOrder) {
        if (manufacturingOrder == null) return null;
        
        ReceiptOrder receiptOrder = new ReceiptOrder();
        receiptOrder.setOrderNumber(generateOrderNumber());
        receiptOrder.setManufacturingOrderNumber(manufacturingOrder.getOrderNumber());
        receiptOrder.setSupplierName("مورد افتراضي");
        receiptOrder.setReceiptDate(LocalDate.now());
        receiptOrder.setNotes("أمر استلام من أمر التصنيع: " + manufacturingOrder.getOrderNumber());
        
        // إضافة عناصر افتراضية بناءً على أمر التصنيع
        ReceiptItem item = new ReceiptItem("مواد لأمر التصنيع " + manufacturingOrder.getOrderNumber(), 1, "مجموعة");
        receiptOrder.addReceiptItem(item);
        
        addReceiptOrder(receiptOrder);
        return receiptOrder;
    }
    
    /**
     * توليد رقم أمر جديد
     */
    public String generateOrderNumber() {
        return String.format("RO-%03d", orderCounter++);
    }
    
    /**
     * الحصول على إحصائيات أوامر الاستلام
     */
    public Map<String, Integer> getReceiptOrderStatistics() {
        Map<String, Integer> stats = new HashMap<>();
        
        stats.put("إجمالي الأوامر", receiptOrders.size());
        stats.put("أوامر مسودة", (int) receiptOrders.stream().filter(o -> o.getStatus() == ReceiptOrder.ReceiptStatus.DRAFT).count());
        stats.put("أوامر مؤكدة", (int) receiptOrders.stream().filter(o -> o.getStatus() == ReceiptOrder.ReceiptStatus.CONFIRMED).count());
        stats.put("أوامر قيد الاستلام", (int) receiptOrders.stream().filter(o -> o.getStatus() == ReceiptOrder.ReceiptStatus.IN_RECEIPT).count());
        stats.put("أوامر مستلمة", (int) receiptOrders.stream().filter(o -> o.getStatus() == ReceiptOrder.ReceiptStatus.RECEIVED).count());
        stats.put("أوامر ملغية", (int) receiptOrders.stream().filter(o -> o.getStatus() == ReceiptOrder.ReceiptStatus.CANCELLED).count());
        
        return stats;
    }
    
    /**
     * البحث في أوامر الاستلام
     */
    public List<ReceiptOrder> searchReceiptOrders(String searchTerm) {
        if (searchTerm == null || searchTerm.trim().isEmpty()) {
            return new ArrayList<>(receiptOrders);
        }
        
        String term = searchTerm.toLowerCase().trim();
        return receiptOrders.stream()
                .filter(order -> 
                    order.getOrderNumber().toLowerCase().contains(term) ||
                    order.getSupplierName().toLowerCase().contains(term) ||
                    (order.getManufacturingOrderNumber() != null && order.getManufacturingOrderNumber().toLowerCase().contains(term)) ||
                    (order.getInvoiceNumber() != null && order.getInvoiceNumber().toLowerCase().contains(term))
                )
                .collect(Collectors.toList());
    }
}
