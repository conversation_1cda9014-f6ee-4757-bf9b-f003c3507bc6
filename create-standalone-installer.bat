@echo off
setlocal enabledelayedexpansion

echo.
echo ========================================
echo Creating Standalone Installer with Embedded Java
echo ========================================
echo.

:: Set Java environment
set JAVA_HOME=C:\Program Files\Eclipse Adoptium\jdk-********-hotspot
set PATH=%JAVA_HOME%\bin;%PATH%

echo Step 1: Checking requirements...
echo.

:: Check Java
java -version >nul 2>&1
if not %errorlevel%==0 (
    echo ERROR: Java not found
    echo Please install Java 17 or newer
    pause
    exit /b 1
)

echo ✅ Java is available
java -version

:: Check jlink
jlink --version >nul 2>&1
if not %errorlevel%==0 (
    echo ERROR: jlink not found
    echo Please use JDK (not JRE)
    pause
    exit /b 1
)

echo ✅ jlink is available

:: Check jpackage
jpackage --version >nul 2>&1
if not %errorlevel%==0 (
    echo ERROR: jpackage not found
    echo Please use Java 17 or newer
    pause
    exit /b 1
)

echo ✅ jpackage is available

echo.
echo Step 2: Building the application...
echo.

:: Clean and build
call mvnw.cmd clean package
if not %errorlevel%==0 (
    echo ERROR: Failed to build project
    pause
    exit /b 1
)

echo ✅ Application built successfully

echo.
echo Step 3: Creating custom Java runtime...
echo.

:: Remove old runtime if exists
if exist "target\java-runtime" rmdir /s /q "target\java-runtime"

:: Create custom runtime with jlink
echo Creating minimal Java runtime...
jlink --module-path "%JAVA_HOME%\jmods" --add-modules java.base,java.desktop,java.logging,java.management,java.naming,java.security.jgss,java.instrument,jdk.unsupported,java.xml,java.prefs,java.datatransfer,java.sql,javafx.controls,javafx.fxml,javafx.graphics,javafx.base,javafx.media,javafx.swing,javafx.web --output target\java-runtime --compress=2 --no-header-files --no-man-pages

if not %errorlevel%==0 (
    echo ERROR: Failed to create Java runtime
    echo Trying with basic modules...
    
    jlink --module-path "%JAVA_HOME%\jmods" --add-modules java.base,java.desktop,java.logging --output target\java-runtime --compress=2 --no-header-files --no-man-pages
    
    if not !errorlevel!==0 (
        echo ERROR: Failed to create minimal Java runtime
        pause
        exit /b 1
    )
)

echo ✅ Custom Java runtime created

echo.
echo Step 4: Attempting to create installer...
echo.

:: Create installer directory
if not exist "target\installer" mkdir "target\installer"

:: Try to create MSI installer
echo Attempting to create MSI installer...
jpackage --type msi --input target --dest target\installer --name "AccountingSystem" --main-jar accounting-system-1.0.0.jar --main-class com.accounting.AccountingApplication --runtime-image target\java-runtime --app-version 1.0.0 --vendor "Advanced Glass and Aluminum Company" --description "Integrated accounting system with embedded Java runtime" --copyright "© 2025 Advanced Glass and Aluminum Company" --win-dir-chooser --win-menu --win-shortcut --win-upgrade-uuid ************************************ --java-options "-Dfile.encoding=UTF-8" --java-options "-Djava.awt.headless=false" --java-options "--add-opens javafx.graphics/javafx.scene=ALL-UNNAMED" --java-options "--add-opens javafx.controls/javafx.scene.control=ALL-UNNAMED" --java-options "--add-opens javafx.base/javafx.beans.property=ALL-UNNAMED"

if %errorlevel%==0 (
    echo ✅ SUCCESS: MSI installer created!
    goto :success
)

echo ⚠️ MSI creation failed, trying EXE...

:: Try to create EXE installer
jpackage --type exe --input target --dest target\installer --name "AccountingSystem" --main-jar accounting-system-1.0.0.jar --main-class com.accounting.AccountingApplication --runtime-image target\java-runtime --app-version 1.0.0 --vendor "Advanced Glass and Aluminum Company" --description "Integrated accounting system with embedded Java runtime" --copyright "© 2025 Advanced Glass and Aluminum Company" --win-dir-chooser --win-menu --win-shortcut --java-options "-Dfile.encoding=UTF-8" --java-options "-Djava.awt.headless=false" --java-options "--add-opens javafx.graphics/javafx.scene=ALL-UNNAMED" --java-options "--add-opens javafx.controls/javafx.scene.control=ALL-UNNAMED" --java-options "--add-opens javafx.base/javafx.beans.property=ALL-UNNAMED"

if %errorlevel%==0 (
    echo ✅ SUCCESS: EXE installer created!
    goto :success
)

echo ❌ Both MSI and EXE creation failed
echo.
echo This is likely because WiX Toolset is not installed.
echo.
echo Alternative Solution: Creating app-image (folder-based distribution)
echo.

:: Create app-image (folder distribution)
jpackage --type app-image --input target --dest target\installer --name "AccountingSystem" --main-jar accounting-system-1.0.0.jar --main-class com.accounting.AccountingApplication --runtime-image target\java-runtime --app-version 1.0.0 --vendor "Advanced Glass and Aluminum Company" --java-options "-Dfile.encoding=UTF-8" --java-options "-Djava.awt.headless=false" --java-options "--add-opens javafx.graphics/javafx.scene=ALL-UNNAMED" --java-options "--add-opens javafx.controls/javafx.scene.control=ALL-UNNAMED" --java-options "--add-opens javafx.base/javafx.beans.property=ALL-UNNAMED"

if %errorlevel%==0 (
    echo ✅ SUCCESS: App-image created!
    echo.
    echo This creates a folder with embedded Java runtime.
    echo Users can run the .exe file directly without installing Java.
    goto :app_image_success
) else (
    echo ❌ All packaging methods failed
    goto :failure
)

:success
echo.
echo ========================================
echo SUCCESS: Standalone Installer Created!
echo ========================================
echo.
echo Location: target\installer\
echo.
echo Files created:
dir /b "target\installer\*.msi" "target\installer\*.exe" 2>nul
echo.

:: Show file sizes
for %%f in ("target\installer\*.msi" "target\installer\*.exe") do (
    if exist "%%f" (
        echo File: %%~nxf
        echo Size: %%~zf bytes (~%%~zf MB)
        echo.
    )
)

echo ✅ Features:
echo   - No Java installation required on target system
echo   - Embedded Java runtime included
echo   - Professional Windows installer
echo   - Start menu shortcuts
echo   - Add/Remove Programs integration
echo   - Automatic updates support
echo.
goto :end

:app_image_success
echo.
echo ========================================
echo App-Image Created Successfully!
echo ========================================
echo.
echo Location: target\installer\AccountingSystem\
echo.
echo This creates a complete application folder with:
echo   - AccountingSystem.exe (main executable)
echo   - Embedded Java runtime
echo   - All required libraries
echo.
echo To distribute:
echo   1. Copy the entire AccountingSystem folder
echo   2. Users run AccountingSystem.exe directly
echo   3. No Java installation required
echo.

:: Create distribution package
echo Creating distribution package...
if exist "AccountingSystem-Standalone" rmdir /s /q "AccountingSystem-Standalone"
xcopy "target\installer\AccountingSystem" "AccountingSystem-Standalone\" /E /I /Y

:: Create instructions
echo Creating user instructions...
echo # نظام المحاسبة المتكامل - النسخة المستقلة > "AccountingSystem-Standalone\تعليمات-التشغيل.txt"
echo ## Integrated Accounting System - Standalone Version >> "AccountingSystem-Standalone\تعليمات-التشغيل.txt"
echo. >> "AccountingSystem-Standalone\تعليمات-التشغيل.txt"
echo ### طريقة التشغيل: >> "AccountingSystem-Standalone\تعليمات-التشغيل.txt"
echo 1. انقر نقراً مزدوجاً على AccountingSystem.exe >> "AccountingSystem-Standalone\تعليمات-التشغيل.txt"
echo 2. لا يحتاج تثبيت Java منفصل >> "AccountingSystem-Standalone\تعليمات-التشغيل.txt"
echo 3. يعمل على Windows 10/11 مباشرة >> "AccountingSystem-Standalone\تعليمات-التشغيل.txt"
echo. >> "AccountingSystem-Standalone\تعليمات-التشغيل.txt"
echo ### المتطلبات: >> "AccountingSystem-Standalone\تعليمات-التشغيل.txt"
echo - Windows 10/11 (64-bit) >> "AccountingSystem-Standalone\تعليمات-التشغيل.txt"
echo - 4 GB RAM كحد أدنى >> "AccountingSystem-Standalone\تعليمات-التشغيل.txt"
echo - 200 MB مساحة قرص >> "AccountingSystem-Standalone\تعليمات-التشغيل.txt"
echo. >> "AccountingSystem-Standalone\تعليمات-التشغيل.txt"
echo ### الدعم التقني: >> "AccountingSystem-Standalone\تعليمات-التشغيل.txt"
echo <EMAIL> >> "AccountingSystem-Standalone\تعليمات-التشغيل.txt"
echo. >> "AccountingSystem-Standalone\تعليمات-التشغيل.txt"
echo © 2025 شركة الزجاج والألومنيوم المتقدمة >> "AccountingSystem-Standalone\تعليمات-التشغيل.txt"

echo ✅ Distribution package ready: AccountingSystem-Standalone\
goto :end

:failure
echo.
echo ========================================
echo Packaging Failed
echo ========================================
echo.
echo Possible solutions:
echo 1. Install WiX Toolset v3.11+ from https://wixtoolset.org/
echo 2. Add WiX to PATH environment variable
echo 3. Use the portable version instead
echo.
echo For WiX installation:
echo   - Download WiX v3.11.2 or later
echo   - Install to default location
echo   - Add C:\Program Files (x86)\WiX Toolset v3.11\bin to PATH
echo   - Restart command prompt and try again
echo.
goto :end

:end
echo.
echo Build process completed.
echo.
pause
