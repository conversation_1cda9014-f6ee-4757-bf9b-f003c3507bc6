package com.accounting.model;

import javafx.beans.property.*;
import java.time.LocalDateTime;
import java.util.UUID;

/**
 * نموذج تفاصيل القيد المحاسبي
 */
public class JournalEntryDetail {
    
    // الخصائص الأساسية
    private final StringProperty detailId = new SimpleStringProperty();
    private final IntegerProperty lineNumber = new SimpleIntegerProperty();
    private final StringProperty accountCode = new SimpleStringProperty();
    private final StringProperty accountName = new SimpleStringProperty();
    
    // المبالغ
    private final DoubleProperty debitAmount = new SimpleDoubleProperty(0.0);
    private final DoubleProperty creditAmount = new SimpleDoubleProperty(0.0);
    
    // الوصف والمرجع
    private final StringProperty description = new SimpleStringProperty();
    private final StringProperty reference = new SimpleStringProperty();
    private final StringProperty costCenter = new SimpleStringProperty();
    
    // معلومات إضافية
    private final StringProperty currency = new SimpleStringProperty("EGP");
    private final DoubleProperty exchangeRate = new SimpleDoubleProperty(1.0);
    private final DoubleProperty amountInBaseCurrency = new SimpleDoubleProperty(0.0);
    
    // الربط مع القيد الرئيسي
    private JournalEntry journalEntry;
    private Account account;
    
    // معلومات التدقيق
    private final ObjectProperty<LocalDateTime> createdDate = new SimpleObjectProperty<>(LocalDateTime.now());
    private final ObjectProperty<LocalDateTime> lastModified = new SimpleObjectProperty<>(LocalDateTime.now());
    
    // Constructors
    public JournalEntryDetail() {
        setDetailId(UUID.randomUUID().toString());
    }
    
    public JournalEntryDetail(String accountCode, String accountName, double debitAmount, double creditAmount) {
        this();
        setAccountCode(accountCode);
        setAccountName(accountName);
        setDebitAmount(debitAmount);
        setCreditAmount(creditAmount);
        calculateAmountInBaseCurrency();
    }
    
    public JournalEntryDetail(Account account, double debitAmount, double creditAmount, String description) {
        this();
        setAccount(account);
        setAccountCode(account.getAccountCode());
        setAccountName(account.getAccountName());
        setDebitAmount(debitAmount);
        setCreditAmount(creditAmount);
        setDescription(description);
        calculateAmountInBaseCurrency();
    }
    
    // Property getters
    public StringProperty detailIdProperty() { return detailId; }
    public IntegerProperty lineNumberProperty() { return lineNumber; }
    public StringProperty accountCodeProperty() { return accountCode; }
    public StringProperty accountNameProperty() { return accountName; }
    public DoubleProperty debitAmountProperty() { return debitAmount; }
    public DoubleProperty creditAmountProperty() { return creditAmount; }
    public StringProperty descriptionProperty() { return description; }
    public StringProperty referenceProperty() { return reference; }
    public StringProperty costCenterProperty() { return costCenter; }
    public StringProperty currencyProperty() { return currency; }
    public DoubleProperty exchangeRateProperty() { return exchangeRate; }
    public DoubleProperty amountInBaseCurrencyProperty() { return amountInBaseCurrency; }
    public ObjectProperty<LocalDateTime> createdDateProperty() { return createdDate; }
    public ObjectProperty<LocalDateTime> lastModifiedProperty() { return lastModified; }
    
    // Value getters and setters
    public String getDetailId() { return detailId.get(); }
    public void setDetailId(String detailId) { this.detailId.set(detailId); }
    
    public int getLineNumber() { return lineNumber.get(); }
    public void setLineNumber(int lineNumber) { this.lineNumber.set(lineNumber); }
    
    public String getAccountCode() { return accountCode.get(); }
    public void setAccountCode(String accountCode) { 
        this.accountCode.set(accountCode);
        setLastModified(LocalDateTime.now());
    }
    
    public String getAccountName() { return accountName.get(); }
    public void setAccountName(String accountName) { 
        this.accountName.set(accountName);
        setLastModified(LocalDateTime.now());
    }
    
    public double getDebitAmount() { return debitAmount.get(); }
    public void setDebitAmount(double debitAmount) { 
        this.debitAmount.set(debitAmount);
        calculateAmountInBaseCurrency();
        setLastModified(LocalDateTime.now());
        if (journalEntry != null) {
            journalEntry.recalculateTotals();
        }
    }
    
    public double getCreditAmount() { return creditAmount.get(); }
    public void setCreditAmount(double creditAmount) { 
        this.creditAmount.set(creditAmount);
        calculateAmountInBaseCurrency();
        setLastModified(LocalDateTime.now());
        if (journalEntry != null) {
            journalEntry.recalculateTotals();
        }
    }
    
    public String getDescription() { return description.get(); }
    public void setDescription(String description) { 
        this.description.set(description);
        setLastModified(LocalDateTime.now());
    }
    
    public String getReference() { return reference.get(); }
    public void setReference(String reference) { 
        this.reference.set(reference);
        setLastModified(LocalDateTime.now());
    }
    
    public String getCostCenter() { return costCenter.get(); }
    public void setCostCenter(String costCenter) { 
        this.costCenter.set(costCenter);
        setLastModified(LocalDateTime.now());
    }
    
    public String getCurrency() { return currency.get(); }
    public void setCurrency(String currency) { 
        this.currency.set(currency);
        calculateAmountInBaseCurrency();
        setLastModified(LocalDateTime.now());
    }
    
    public double getExchangeRate() { return exchangeRate.get(); }
    public void setExchangeRate(double exchangeRate) { 
        this.exchangeRate.set(exchangeRate);
        calculateAmountInBaseCurrency();
        setLastModified(LocalDateTime.now());
    }
    
    public double getAmountInBaseCurrency() { return amountInBaseCurrency.get(); }
    public void setAmountInBaseCurrency(double amountInBaseCurrency) { 
        this.amountInBaseCurrency.set(amountInBaseCurrency);
    }
    
    public LocalDateTime getCreatedDate() { return createdDate.get(); }
    public void setCreatedDate(LocalDateTime createdDate) { this.createdDate.set(createdDate); }
    
    public LocalDateTime getLastModified() { return lastModified.get(); }
    public void setLastModified(LocalDateTime lastModified) { this.lastModified.set(lastModified); }
    
    // Object references
    public JournalEntry getJournalEntry() { return journalEntry; }
    public void setJournalEntry(JournalEntry journalEntry) { this.journalEntry = journalEntry; }
    
    public Account getAccount() { return account; }
    public void setAccount(Account account) { 
        this.account = account;
        if (account != null) {
            setAccountCode(account.getAccountCode());
            setAccountName(account.getAccountName());
            setCurrency(account.getCurrency());
        }
        setLastModified(LocalDateTime.now());
    }
    
    /**
     * حساب المبلغ بالعملة الأساسية
     */
    private void calculateAmountInBaseCurrency() {
        double amount = Math.max(getDebitAmount(), getCreditAmount());
        setAmountInBaseCurrency(amount * getExchangeRate());
    }
    
    /**
     * الحصول على المبلغ الصافي (مدين - دائن)
     */
    public double getNetAmount() {
        return getDebitAmount() - getCreditAmount();
    }
    
    /**
     * التحقق من صحة التفصيل
     */
    public boolean isValid() {
        return getAccountCode() != null && !getAccountCode().trim().isEmpty() &&
               getAccountName() != null && !getAccountName().trim().isEmpty() &&
               (getDebitAmount() > 0 || getCreditAmount() > 0) &&
               !(getDebitAmount() > 0 && getCreditAmount() > 0) && // لا يمكن أن يكون مدين ودائن معاً
               getExchangeRate() > 0;
    }
    
    /**
     * التحقق من كون التفصيل مدين
     */
    public boolean isDebit() {
        return getDebitAmount() > 0;
    }
    
    /**
     * التحقق من كون التفصيل دائن
     */
    public boolean isCredit() {
        return getCreditAmount() > 0;
    }
    
    /**
     * الحصول على نوع الحركة
     */
    public String getMovementType() {
        if (isDebit()) return "مدين";
        if (isCredit()) return "دائن";
        return "غير محدد";
    }
    
    /**
     * الحصول على المبلغ المطلق
     */
    public double getAbsoluteAmount() {
        return Math.max(getDebitAmount(), getCreditAmount());
    }
    
    /**
     * تحديث المبلغ (مدين أو دائن)
     */
    public void updateAmount(double amount, boolean isDebit) {
        if (isDebit) {
            setDebitAmount(amount);
            setCreditAmount(0.0);
        } else {
            setCreditAmount(amount);
            setDebitAmount(0.0);
        }
    }
    
    /**
     * نسخ التفصيل
     */
    public JournalEntryDetail copy() {
        JournalEntryDetail copy = new JournalEntryDetail();
        copy.setLineNumber(getLineNumber());
        copy.setAccountCode(getAccountCode());
        copy.setAccountName(getAccountName());
        copy.setDebitAmount(getDebitAmount());
        copy.setCreditAmount(getCreditAmount());
        copy.setDescription(getDescription());
        copy.setReference(getReference());
        copy.setCostCenter(getCostCenter());
        copy.setCurrency(getCurrency());
        copy.setExchangeRate(getExchangeRate());
        copy.setAccount(getAccount());
        return copy;
    }
    
    /**
     * تحويل إلى نص منسق
     */
    public String getFormattedAmount() {
        if (isDebit()) {
            return String.format("%.2f مدين", getDebitAmount());
        } else if (isCredit()) {
            return String.format("%.2f دائن", getCreditAmount());
        }
        return "0.00";
    }
    
    @Override
    public String toString() {
        return getAccountCode() + " - " + getAccountName() + " (" + getFormattedAmount() + ")";
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        JournalEntryDetail detail = (JournalEntryDetail) obj;
        return getDetailId() != null && getDetailId().equals(detail.getDetailId());
    }
    
    @Override
    public int hashCode() {
        return getDetailId() != null ? getDetailId().hashCode() : 0;
    }
}
