package com.accounting.model;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * نموذج حركة المخزون
 * Stock Movement Model
 */
public class StockMovement {
    private String movementId;
    private String itemId;
    private String itemName;
    private String warehouseId;
    private String warehouseName;
    private MovementType movementType;
    private double quantity;
    private double unitPrice;
    private double totalValue;
    private double balanceAfter;

    // خصائص الأبعاد (للأصناف ذات الأبعاد)
    private boolean hasDimensions;
    private Double length;  // الطول (مم)
    private Double width;   // العرض (مم)
    private Integer pieces; // العدد
    private Double calculatedArea; // المساحة المحسوبة (م²)
    private LocalDateTime dateTime;
    private String user;
    private String reference;
    private String notes;
    
    public enum MovementType {
        IN("استلام", "إضافة للمخزن"),
        OUT("صرف", "سحب من المخزن"),
        ADJUSTMENT("تسوية", "تعديل الكمية"),
        TRANSFER("نقل", "نقل بين المخازن");
        
        private final String arabicName;
        private final String description;
        
        MovementType(String arabicName, String description) {
            this.arabicName = arabicName;
            this.description = description;
        }
        
        public String getArabicName() { return arabicName; }
        public String getDescription() { return description; }
        
        @Override
        public String toString() { return arabicName; }
    }
    
    public StockMovement() {
        this.dateTime = LocalDateTime.now();
    }
    
    public StockMovement(String movementId, String itemId, String itemName, String warehouseId,
                        MovementType movementType, double quantity, double unitPrice) {
        this();
        this.movementId = movementId;
        this.itemId = itemId;
        this.itemName = itemName;
        this.warehouseId = warehouseId;
        this.movementType = movementType;
        this.quantity = quantity;
        this.unitPrice = unitPrice;
        this.totalValue = quantity * unitPrice;
    }

    /**
     * حساب الكمية والقيمة للأصناف ذات الأبعاد
     */
    public void calculateDimensionValues() {
        if (hasDimensions && length != null && width != null && pieces != null) {
            // حساب المساحة: (الطول ÷ 1000) × (العرض ÷ 1000) × العدد
            calculatedArea = (length / 1000.0) * (width / 1000.0) * pieces;
            this.quantity = calculatedArea;
            this.totalValue = calculatedArea * unitPrice;
        }
    }
    
    // Getters and Setters
    public String getMovementId() { return movementId; }
    public void setMovementId(String movementId) { this.movementId = movementId; }
    
    public String getItemId() { return itemId; }
    public void setItemId(String itemId) { this.itemId = itemId; }
    
    public String getItemName() { return itemName; }
    public void setItemName(String itemName) { this.itemName = itemName; }
    
    public String getWarehouseId() { return warehouseId; }
    public void setWarehouseId(String warehouseId) { this.warehouseId = warehouseId; }
    
    public String getWarehouseName() { return warehouseName; }
    public void setWarehouseName(String warehouseName) { this.warehouseName = warehouseName; }
    
    public MovementType getMovementType() { return movementType; }
    public void setMovementType(MovementType movementType) { this.movementType = movementType; }
    
    public double getQuantity() { return quantity; }
    public void setQuantity(double quantity) { 
        this.quantity = quantity;
        this.totalValue = quantity * unitPrice;
    }
    
    public double getUnitPrice() { return unitPrice; }
    public void setUnitPrice(double unitPrice) { 
        this.unitPrice = unitPrice;
        this.totalValue = quantity * unitPrice;
    }
    
    public double getTotalValue() { return totalValue; }
    public void setTotalValue(double totalValue) { this.totalValue = totalValue; }
    
    public double getBalanceAfter() { return balanceAfter; }
    public void setBalanceAfter(double balanceAfter) { this.balanceAfter = balanceAfter; }
    
    public LocalDateTime getDateTime() { return dateTime; }
    public void setDateTime(LocalDateTime dateTime) { this.dateTime = dateTime; }
    
    public String getUser() { return user; }
    public void setUser(String user) { this.user = user; }
    
    public String getReference() { return reference; }
    public void setReference(String reference) { this.reference = reference; }
    
    public String getNotes() { return notes; }
    public void setNotes(String notes) { this.notes = notes; }

    // خصائص الأبعاد
    public boolean hasDimensions() { return hasDimensions; }
    public void setHasDimensions(boolean hasDimensions) { this.hasDimensions = hasDimensions; }

    public Double getLength() { return length; }
    public void setLength(Double length) {
        this.length = length;
        calculateDimensionValues();
    }

    public Double getWidth() { return width; }
    public void setWidth(Double width) {
        this.width = width;
        calculateDimensionValues();
    }

    public Integer getPieces() { return pieces; }
    public void setPieces(Integer pieces) {
        this.pieces = pieces;
        calculateDimensionValues();
    }

    public Double getCalculatedArea() { return calculatedArea; }
    public void setCalculatedArea(Double calculatedArea) { this.calculatedArea = calculatedArea; }

    @Override
    public String toString() {
        return movementType.getArabicName() + " - " + itemName + " - " + quantity;
    }
}
