package com.accounting.model;

/**
 * نموذج العميل
 * Customer Model
 */
public class Customer {
    private String id;
    private String name;
    private String phone;
    private String email;
    private String address;
    private String company;
    private String notes;
    
    public Customer() {}
    
    public Customer(String id, String name, String phone, String email, String address, String company, String notes) {
        this.id = id;
        this.name = name;
        this.phone = phone;
        this.email = email;
        this.address = address;
        this.company = company;
        this.notes = notes;
    }
    
    // Getters and Setters
    public String getId() { return id; }
    public void setId(String id) { this.id = id; }
    
    public String getName() { return name; }
    public void setName(String name) { this.name = name; }
    
    public String getPhone() { return phone; }
    public void setPhone(String phone) { this.phone = phone; }
    
    public String getEmail() { return email; }
    public void setEmail(String email) { this.email = email; }
    
    public String getAddress() { return address; }
    public void setAddress(String address) { this.address = address; }
    
    public String getCompany() { return company; }
    public void setCompany(String company) { this.company = company; }
    
    public String getNotes() { return notes; }
    public void setNotes(String notes) { this.notes = notes; }
    
    @Override
    public String toString() {
        return name + (company != null && !company.isEmpty() ? " - " + company : "");
    }
}
