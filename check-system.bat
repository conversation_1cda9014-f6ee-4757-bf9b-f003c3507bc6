@echo off
chcp 65001 >nul
echo ========================================
echo    فحص متطلبات النظام
echo    System Requirements Check
echo ========================================
echo.

echo 🔍 فحص Java...
java -version 2>nul
if %errorlevel% equ 0 (
    echo ✅ Java مثبت ومتاح
    echo.
    java -version
) else (
    echo ❌ Java غير مثبت أو غير متاح في PATH
    echo.
    echo 📥 يرجى تحميل وتثبيت Java من:
    echo    https://adoptium.net/
    echo.
    echo 💡 تأكد من اختيار:
    echo    - OpenJDK 17 أو 21 (LTS)
    echo    - Windows x64
    echo    - تحديد "Add to PATH" أثناء التثبيت
    echo.
    goto :end
)

echo.
echo 🔍 فحص إصدار Java...
for /f "tokens=3" %%g in ('java -version 2^>^&1 ^| findstr /i "version"') do (
    set JAVA_VERSION=%%g
)
set JAVA_VERSION=%JAVA_VERSION:"=%
echo إصدار Java: %JAVA_VERSION%

echo.
echo 🔍 فحص JAVA_HOME...
if defined JAVA_HOME (
    echo ✅ JAVA_HOME محدد: %JAVA_HOME%
) else (
    echo ⚠️  JAVA_HOME غير محدد (اختياري)
)

echo.
echo 🔍 فحص ملفات المشروع...
if exist "pom.xml" (
    echo ✅ pom.xml موجود
) else (
    echo ❌ pom.xml غير موجود
)

if exist "mvnw.cmd" (
    echo ✅ Maven Wrapper موجود
) else (
    echo ❌ Maven Wrapper غير موجود
)

if exist "src\main\java\com\accounting\AccountingApplication.java" (
    echo ✅ الكود المصدري موجود
) else (
    echo ❌ الكود المصدري غير موجود
)

echo.
echo 🔍 فحص الاتصال بالإنترنت...
ping -n 1 google.com >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ الاتصال بالإنترنت متاح
) else (
    echo ⚠️  لا يوجد اتصال بالإنترنت (مطلوب للتحميل الأولي)
)

echo.
echo ========================================
echo 📋 ملخص الفحص:
echo ========================================

java -version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ النظام جاهز لتشغيل البرنامج
    echo.
    echo 🚀 لتشغيل البرنامج:
    echo    1. انقر نقراً مزدوجاً على run.bat
    echo    2. أو اكتب: mvnw.cmd clean javafx:run
    echo.
) else (
    echo ❌ النظام غير جاهز
    echo 📥 يرجى تثبيت Java أولاً من: https://adoptium.net/
    echo.
)

:end
echo اضغط أي مفتاح للخروج...
pause >nul
