package com.accounting.service;

import com.accounting.model.*;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * خدمة إدارة دفتر الأستاذ العام والفرعي
 */
public class LedgerService {
    
    private static LedgerService instance;
    private final ObservableList<LedgerEntry> ledgerEntries = FXCollections.observableArrayList();
    private final Map<String, List<LedgerEntry>> accountLedgers = new HashMap<>();
    
    // المراجع للخدمات الأخرى
    private final AccountService accountService;
    
    private LedgerService() {
        this.accountService = AccountService.getInstance();
    }
    
    public static LedgerService getInstance() {
        if (instance == null) {
            instance = new LedgerService();
        }
        return instance;
    }
    
    /**
     * إضافة قيد إلى دفتر الأستاذ
     */
    public boolean addLedgerEntry(LedgerEntry entry) {
        if (entry == null || entry.getAccountCode() == null) {
            return false;
        }
        
        entry.setCreatedDate(LocalDateTime.now());
        
        // إضافة إلى القائمة العامة
        ledgerEntries.add(entry);
        
        // إضافة إلى دفتر الحساب المحدد
        String accountCode = entry.getAccountCode();
        accountLedgers.computeIfAbsent(accountCode, k -> new ArrayList<>()).add(entry);
        
        // إعادة حساب الأرصدة الجارية للحساب
        recalculateRunningBalances(accountCode);
        
        return true;
    }
    
    /**
     * حذف قيد من دفتر الأستاذ
     */
    public boolean removeLedgerEntry(String ledgerEntryId) {
        LedgerEntry entry = getLedgerEntryById(ledgerEntryId);
        if (entry == null) return false;
        
        String accountCode = entry.getAccountCode();
        
        // حذف من القائمة العامة
        ledgerEntries.remove(entry);
        
        // حذف من دفتر الحساب
        List<LedgerEntry> accountEntries = accountLedgers.get(accountCode);
        if (accountEntries != null) {
            accountEntries.remove(entry);
        }
        
        // إعادة حساب الأرصدة الجارية للحساب
        recalculateRunningBalances(accountCode);
        
        return true;
    }
    
    /**
     * حذف قيود بناءً على تفاصيل القيد المحاسبي
     */
    public void removeLedgerEntriesByJournalDetail(String journalEntryDetailId) {
        List<LedgerEntry> entriesToRemove = ledgerEntries.stream()
                .filter(entry -> journalEntryDetailId.equals(entry.getJournalEntryDetailId()))
                .collect(Collectors.toList());
        
        for (LedgerEntry entry : entriesToRemove) {
            removeLedgerEntry(entry.getLedgerEntryId());
        }
    }
    
    /**
     * البحث عن قيد بالمعرف
     */
    public LedgerEntry getLedgerEntryById(String ledgerEntryId) {
        return ledgerEntries.stream()
                .filter(entry -> entry.getLedgerEntryId().equals(ledgerEntryId))
                .findFirst()
                .orElse(null);
    }
    
    /**
     * الحصول على دفتر أستاذ حساب محدد
     */
    public List<LedgerEntry> getAccountLedger(String accountCode) {
        return accountLedgers.getOrDefault(accountCode, new ArrayList<>());
    }
    
    /**
     * الحصول على دفتر أستاذ حساب محدد خلال فترة
     */
    public List<LedgerEntry> getAccountLedger(String accountCode, LocalDate fromDate, LocalDate toDate) {
        return getAccountLedger(accountCode).stream()
                .filter(entry -> {
                    LocalDate entryDate = entry.getTransactionDate();
                    return entryDate != null && 
                           !entryDate.isBefore(fromDate) && 
                           !entryDate.isAfter(toDate);
                })
                .sorted(Comparator.comparing(LedgerEntry::getTransactionDate)
                        .thenComparing(LedgerEntry::getCreatedDate))
                .collect(Collectors.toList());
    }
    
    /**
     * الحصول على جميع قيود دفتر الأستاذ
     */
    public ObservableList<LedgerEntry> getAllLedgerEntries() {
        return ledgerEntries;
    }
    
    /**
     * الحصول على قيود دفتر الأستاذ خلال فترة
     */
    public List<LedgerEntry> getLedgerEntriesByDateRange(LocalDate fromDate, LocalDate toDate) {
        return ledgerEntries.stream()
                .filter(entry -> {
                    LocalDate entryDate = entry.getTransactionDate();
                    return entryDate != null && 
                           !entryDate.isBefore(fromDate) && 
                           !entryDate.isAfter(toDate);
                })
                .sorted(Comparator.comparing(LedgerEntry::getTransactionDate)
                        .thenComparing(LedgerEntry::getAccountCode)
                        .thenComparing(LedgerEntry::getCreatedDate))
                .collect(Collectors.toList());
    }
    
    /**
     * الحصول على قيود دفتر الأستاذ حسب نوع القيد
     */
    public List<LedgerEntry> getLedgerEntriesByEntryType(JournalEntry.EntryType entryType) {
        return ledgerEntries.stream()
                .filter(entry -> entry.getEntryType() == entryType)
                .collect(Collectors.toList());
    }
    
    /**
     * البحث في دفتر الأستاذ
     */
    public List<LedgerEntry> searchLedgerEntries(String searchText) {
        if (searchText == null || searchText.trim().isEmpty()) {
            return new ArrayList<>(ledgerEntries);
        }
        
        String searchLower = searchText.toLowerCase();
        return ledgerEntries.stream()
                .filter(entry -> 
                    entry.getAccountCode().toLowerCase().contains(searchLower) ||
                    entry.getAccountName().toLowerCase().contains(searchLower) ||
                    entry.getDescription().toLowerCase().contains(searchLower) ||
                    (entry.getReference() != null && entry.getReference().toLowerCase().contains(searchLower)) ||
                    (entry.getJournalEntryNumber() != null && entry.getJournalEntryNumber().toLowerCase().contains(searchLower))
                )
                .collect(Collectors.toList());
    }
    
    /**
     * إعادة حساب الأرصدة الجارية لحساب محدد
     */
    private void recalculateRunningBalances(String accountCode) {
        List<LedgerEntry> accountEntries = getAccountLedger(accountCode);
        if (accountEntries.isEmpty()) return;
        
        // ترتيب القيود حسب التاريخ
        accountEntries.sort(Comparator.comparing(LedgerEntry::getTransactionDate)
                .thenComparing(LedgerEntry::getCreatedDate));
        
        // الحصول على معلومات الحساب
        Account account = accountService.getAccountByCode(accountCode);
        if (account == null) return;
        
        double runningBalance = account.getOpeningBalance();
        
        for (LedgerEntry entry : accountEntries) {
            entry.calculateRunningBalance(runningBalance, account.getAccountNature());
            runningBalance = entry.getRunningBalance();
        }
    }
    
    /**
     * الحصول على رصيد حساب في تاريخ محدد
     */
    public double getAccountBalance(String accountCode, LocalDate asOfDate) {
        Account account = accountService.getAccountByCode(accountCode);
        if (account == null) return 0.0;
        
        double balance = account.getOpeningBalance();
        
        List<LedgerEntry> entries = getAccountLedger(accountCode).stream()
                .filter(entry -> !entry.getTransactionDate().isAfter(asOfDate))
                .sorted(Comparator.comparing(LedgerEntry::getTransactionDate)
                        .thenComparing(LedgerEntry::getCreatedDate))
                .collect(Collectors.toList());
        
        for (LedgerEntry entry : entries) {
            if (account.getAccountNature() == Account.AccountNature.DEBIT) {
                balance += entry.getDebitAmount() - entry.getCreditAmount();
            } else {
                balance += entry.getCreditAmount() - entry.getDebitAmount();
            }
        }
        
        return balance;
    }
    
    /**
     * الحصول على مجاميع حساب خلال فترة
     */
    public Map<String, Double> getAccountTotals(String accountCode, LocalDate fromDate, LocalDate toDate) {
        List<LedgerEntry> entries = getAccountLedger(accountCode, fromDate, toDate);
        
        double totalDebits = entries.stream().mapToDouble(LedgerEntry::getDebitAmount).sum();
        double totalCredits = entries.stream().mapToDouble(LedgerEntry::getCreditAmount).sum();
        
        Map<String, Double> totals = new HashMap<>();
        totals.put("totalDebits", totalDebits);
        totals.put("totalCredits", totalCredits);
        totals.put("netMovement", totalDebits - totalCredits);
        totals.put("openingBalance", getAccountBalance(accountCode, fromDate.minusDays(1)));
        totals.put("closingBalance", getAccountBalance(accountCode, toDate));
        
        return totals;
    }
    
    /**
     * إنشاء دفتر أستاذ عام
     */
    public Map<String, List<LedgerEntry>> createGeneralLedger(LocalDate fromDate, LocalDate toDate) {
        Map<String, List<LedgerEntry>> generalLedger = new HashMap<>();
        
        // الحصول على جميع الحسابات التي لها حركة
        Set<String> activeAccounts = ledgerEntries.stream()
                .filter(entry -> {
                    LocalDate entryDate = entry.getTransactionDate();
                    return entryDate != null && 
                           !entryDate.isBefore(fromDate) && 
                           !entryDate.isAfter(toDate);
                })
                .map(LedgerEntry::getAccountCode)
                .collect(Collectors.toSet());
        
        // إنشاء دفتر أستاذ لكل حساب
        for (String accountCode : activeAccounts) {
            List<LedgerEntry> accountEntries = getAccountLedger(accountCode, fromDate, toDate);
            if (!accountEntries.isEmpty()) {
                generalLedger.put(accountCode, accountEntries);
            }
        }
        
        return generalLedger;
    }
    
    /**
     * إنشاء دفتر أستاذ فرعي للعملاء
     */
    public Map<String, List<LedgerEntry>> createCustomersSubLedger(LocalDate fromDate, LocalDate toDate) {
        return createSubLedgerByAccountType("112", fromDate, toDate); // حساب العملاء
    }
    
    /**
     * إنشاء دفتر أستاذ فرعي للموردين
     */
    public Map<String, List<LedgerEntry>> createSuppliersSubLedger(LocalDate fromDate, LocalDate toDate) {
        return createSubLedgerByAccountType("211", fromDate, toDate); // حساب الموردين
    }
    
    /**
     * إنشاء دفتر أستاذ فرعي حسب نوع الحساب
     */
    private Map<String, List<LedgerEntry>> createSubLedgerByAccountType(String accountCode, 
                                                                       LocalDate fromDate, LocalDate toDate) {
        Map<String, List<LedgerEntry>> subLedger = new HashMap<>();
        
        List<LedgerEntry> entries = getLedgerEntriesByDateRange(fromDate, toDate).stream()
                .filter(entry -> entry.getAccountCode().startsWith(accountCode))
                .collect(Collectors.toList());
        
        // تجميع حسب اسم الحساب (العميل/المورد)
        Map<String, List<LedgerEntry>> groupedEntries = entries.stream()
                .collect(Collectors.groupingBy(LedgerEntry::getAccountName));
        
        return groupedEntries;
    }
    
    /**
     * الحصول على إحصائيات دفتر الأستاذ
     */
    public Map<String, Object> getLedgerStatistics() {
        Map<String, Object> stats = new HashMap<>();
        
        stats.put("إجمالي القيود", ledgerEntries.size());
        stats.put("عدد الحسابات النشطة", accountLedgers.size());
        
        // إحصائيات حسب نوع القيد
        for (JournalEntry.EntryType type : JournalEntry.EntryType.values()) {
            long count = ledgerEntries.stream()
                    .filter(entry -> entry.getEntryType() == type)
                    .count();
            stats.put("قيود " + type.getArabicName(), count);
        }
        
        // إجمالي المبالغ
        double totalDebits = ledgerEntries.stream().mapToDouble(LedgerEntry::getDebitAmount).sum();
        double totalCredits = ledgerEntries.stream().mapToDouble(LedgerEntry::getCreditAmount).sum();
        
        stats.put("إجمالي المدين", totalDebits);
        stats.put("إجمالي الدائن", totalCredits);
        stats.put("الفرق", Math.abs(totalDebits - totalCredits));
        
        return stats;
    }
    
    /**
     * تصدير دفتر الأستاذ إلى نص
     */
    public String exportLedgerToText(String accountCode, LocalDate fromDate, LocalDate toDate) {
        Account account = accountService.getAccountByCode(accountCode);
        if (account == null) return "";
        
        StringBuilder export = new StringBuilder();
        export.append("دفتر الأستاذ - ").append(account.getAccountName()).append("\n");
        export.append("رمز الحساب: ").append(accountCode).append("\n");
        export.append("من ").append(fromDate).append(" إلى ").append(toDate).append("\n");
        export.append("=".repeat(80)).append("\n\n");
        
        List<LedgerEntry> entries = getAccountLedger(accountCode, fromDate, toDate);
        
        export.append(String.format("%-12s %-12s %-30s %-10s %-10s %-12s\n",
                "التاريخ", "رقم القيد", "الوصف", "مدين", "دائن", "الرصيد"));
        export.append("-".repeat(80)).append("\n");
        
        double openingBalance = getAccountBalance(accountCode, fromDate.minusDays(1));
        export.append(String.format("%-12s %-12s %-30s %-10s %-10s %12.2f\n",
                "", "", "الرصيد الافتتاحي", "", "", openingBalance));
        
        for (LedgerEntry entry : entries) {
            export.append(String.format("%-12s %-12s %-30s %10.2f %10.2f %12.2f\n",
                    entry.getFormattedDate(),
                    entry.getJournalEntryNumber(),
                    entry.getDescription(),
                    entry.getDebitAmount(),
                    entry.getCreditAmount(),
                    entry.getRunningBalance()));
        }
        
        export.append("-".repeat(80)).append("\n");
        export.append(String.format("%-12s %-12s %-30s %10.2f %10.2f %12.2f\n",
                "", "", "المجموع",
                entries.stream().mapToDouble(LedgerEntry::getDebitAmount).sum(),
                entries.stream().mapToDouble(LedgerEntry::getCreditAmount).sum(),
                getAccountBalance(accountCode, toDate)));
        
        return export.toString();
    }
    
    /**
     * مسح جميع قيود دفتر الأستاذ
     */
    public void clearAllLedgerEntries() {
        ledgerEntries.clear();
        accountLedgers.clear();
    }
    
    /**
     * نسخ احتياطية من دفتر الأستاذ
     */
    public List<LedgerEntry> createBackup() {
        return new ArrayList<>(ledgerEntries);
    }
    
    /**
     * استعادة من النسخة الاحتياطية
     */
    public void restoreFromBackup(List<LedgerEntry> backup) {
        clearAllLedgerEntries();
        
        for (LedgerEntry entry : backup) {
            addLedgerEntry(entry);
        }
    }
}
