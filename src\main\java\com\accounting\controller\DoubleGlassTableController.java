package com.accounting.controller;

import com.accounting.model.DoubleGlassItem;
import javafx.geometry.Insets;
import javafx.geometry.Pos;
import javafx.scene.control.*;
import javafx.scene.control.cell.PropertyValueFactory;
import javafx.scene.control.cell.TextFieldTableCell;
import javafx.scene.control.cell.ComboBoxTableCell;
import javafx.scene.layout.*;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.util.converter.DoubleStringConverter;
import javafx.util.converter.IntegerStringConverter;
import de.jensd.fx.glyphs.fontawesome.FontAwesomeIcon;
import de.jensd.fx.glyphs.fontawesome.FontAwesomeIconView;
import java.text.DecimalFormat;

/**
 * كنترولر جدول دبل جلاس التفاعلي
 */
public class DoubleGlassTableController {
    
    private final ObservableList<DoubleGlassItem> doubleGlassItems;
    private final DecimalFormat decimalFormat = new DecimalFormat("#,##0.00");
    private TableView<DoubleGlassItem> table;
    private Label totalCountLabel;
    private Label totalQuantityLabel;
    
    // قائمة الوحدات المتاحة
    private final ObservableList<String> availableUnits = FXCollections.observableArrayList(
        "قطعة", "متر", "متر مربع", "كيلو", "لتر", "طن", "صندوق", "علبة"
    );
    
    public DoubleGlassTableController(ObservableList<DoubleGlassItem> items) {
        this.doubleGlassItems = items;
    }
    
    /**
     * إنشاء جدول دبل جلاس التفاعلي
     */
    public VBox createDoubleGlassTable() {
        VBox container = new VBox(15);
        container.setPadding(new Insets(15));
        container.setFillWidth(true);
        VBox.setVgrow(container, Priority.ALWAYS);
        
        // العنوان
        Label title = new Label("🔗 جدول دبل جلاس (خامات)");
        title.getStyleClass().add("section-title");
        
        // شريط الأدوات
        HBox toolbar = createToolbar();
        
        // الجدول
        table = createTable();
        table.setMinHeight(350);
        table.setPrefHeight(500);
        table.setColumnResizePolicy(TableView.CONSTRAINED_RESIZE_POLICY);
        VBox.setVgrow(table, Priority.ALWAYS);
        
        // وضع الجدول في ScrollPane
        ScrollPane scrollPane = new ScrollPane(table);
        scrollPane.setFitToWidth(true);
        scrollPane.setVbarPolicy(ScrollPane.ScrollBarPolicy.AS_NEEDED);
        scrollPane.setHbarPolicy(ScrollPane.ScrollBarPolicy.NEVER);
        scrollPane.setPrefHeight(500);
        VBox.setVgrow(scrollPane, Priority.ALWAYS);
        
        // الإجماليات
        HBox totalsBox = createTotalsBox();
        
        container.getChildren().addAll(title, toolbar, scrollPane, totalsBox);
        return container;
    }
    
    /**
     * إنشاء شريط الأدوات
     */
    private HBox createToolbar() {
        HBox toolbar = new HBox(12);
        toolbar.setAlignment(Pos.CENTER_LEFT);
        toolbar.setPadding(new Insets(10));
        toolbar.getStyleClass().add("toolbar");
        
        Button addRowBtn = new Button("إضافة صف");
        FontAwesomeIconView addIcon = new FontAwesomeIconView(FontAwesomeIcon.PLUS_CIRCLE);
        addIcon.setSize("16px");
        addIcon.setStyle("-fx-fill: #27ae60;");
        addRowBtn.setGraphic(addIcon);
        addRowBtn.getStyleClass().add("add-button");
        addRowBtn.setStyle("-fx-background-radius: 20; -fx-font-weight: bold; -fx-background-color: #eafaf1;");
        addRowBtn.setTooltip(new Tooltip("إضافة صف جديد"));
        addRowBtn.setOnAction(e -> addNewRow());
        
        Button deleteRowBtn = new Button("حذف صف");
        FontAwesomeIconView deleteIcon = new FontAwesomeIconView(FontAwesomeIcon.TRASH);
        deleteIcon.setSize("16px");
        deleteIcon.setStyle("-fx-fill: #e74c3c;");
        deleteRowBtn.setGraphic(deleteIcon);
        deleteRowBtn.getStyleClass().add("delete-button");
        deleteRowBtn.setStyle("-fx-background-radius: 20; -fx-background-color: #fdecea;");
        deleteRowBtn.setTooltip(new Tooltip("حذف الصف المحدد"));
        deleteRowBtn.setOnAction(e -> deleteSelectedRow());
        
        Button clearAllBtn = new Button("مسح الكل");
        FontAwesomeIconView clearIcon = new FontAwesomeIconView(FontAwesomeIcon.ERASER);
        clearIcon.setSize("16px");
        clearIcon.setStyle("-fx-fill: #f39c12;");
        clearAllBtn.setGraphic(clearIcon);
        clearAllBtn.getStyleClass().add("unpost-button");
        clearAllBtn.setStyle("-fx-background-radius: 20; -fx-background-color: #fff6e3;");
        clearAllBtn.setTooltip(new Tooltip("مسح كل الصفوف"));
        clearAllBtn.setOnAction(e -> clearAllRows());
        
        toolbar.getChildren().addAll(addRowBtn, deleteRowBtn, clearAllBtn);
        return toolbar;
    }
    
    /**
     * إنشاء الجدول
     */
    private TableView<DoubleGlassItem> createTable() {
        TableView<DoubleGlassItem> tableView = new TableView<>();
        tableView.setEditable(true);
        tableView.getStyleClass().add("double-glass-table");
        tableView.setMinHeight(350);
        tableView.setPrefHeight(500);
        tableView.setColumnResizePolicy(TableView.CONSTRAINED_RESIZE_POLICY);
        VBox.setVgrow(tableView, Priority.ALWAYS);
        
        // عمود الترقيم
        TableColumn<DoubleGlassItem, Integer> indexCol = new TableColumn<>("ت");
        indexCol.setCellValueFactory(cellData -> {
            int index = tableView.getItems().indexOf(cellData.getValue()) + 1;
            return new javafx.beans.property.SimpleIntegerProperty(index).asObject();
        });
        indexCol.setPrefWidth(40);
        indexCol.setSortable(false);
        
        // عمود الوصف
        TableColumn<DoubleGlassItem, String> descCol = new TableColumn<>("الوصف");
        descCol.setCellValueFactory(new PropertyValueFactory<>("description"));
        descCol.setCellFactory(TextFieldTableCell.forTableColumn());
        descCol.setOnEditCommit(event -> {
            event.getRowValue().setDescription(event.getNewValue());
            updateTotals();
        });
        descCol.setPrefWidth(200);
        
        // عمود إجمالي العدد
        TableColumn<DoubleGlassItem, Integer> totalCountCol = new TableColumn<>("إجمالي العدد");
        totalCountCol.setCellValueFactory(new PropertyValueFactory<>("totalCount"));
        totalCountCol.setCellFactory(TextFieldTableCell.forTableColumn(new IntegerStringConverter()));
        totalCountCol.setOnEditCommit(event -> {
            event.getRowValue().setTotalCount(event.getNewValue());
            updateTotals();
        });
        totalCountCol.setPrefWidth(120);
        
        // عمود إجمالي الكمية
        TableColumn<DoubleGlassItem, Double> totalQuantityCol = new TableColumn<>("إجمالي الكمية");
        totalQuantityCol.setCellValueFactory(new PropertyValueFactory<>("totalQuantity"));
        totalQuantityCol.setCellFactory(TextFieldTableCell.forTableColumn(new DoubleStringConverter()));
        totalQuantityCol.setOnEditCommit(event -> {
            event.getRowValue().setTotalQuantity(event.getNewValue());
            updateTotals();
        });
        totalQuantityCol.setPrefWidth(120);
        
        // عمود الوحدة
        TableColumn<DoubleGlassItem, String> unitCol = new TableColumn<>("الوحدة");
        unitCol.setCellValueFactory(new PropertyValueFactory<>("unit"));
        unitCol.setCellFactory(ComboBoxTableCell.forTableColumn(availableUnits));
        unitCol.setOnEditCommit(event -> {
            event.getRowValue().setUnit(event.getNewValue());
        });
        unitCol.setPrefWidth(100);
        
        // عمود الملاحظات
        TableColumn<DoubleGlassItem, String> notesCol = new TableColumn<>("ملاحظات");
        notesCol.setCellValueFactory(new PropertyValueFactory<>("notes"));
        notesCol.setCellFactory(TextFieldTableCell.forTableColumn());
        notesCol.setOnEditCommit(event -> {
            event.getRowValue().setNotes(event.getNewValue());
        });
        notesCol.setPrefWidth(200);
        
        // إضافة الأعمدة
        tableView.getColumns().addAll(
            indexCol, descCol, totalCountCol, totalQuantityCol, unitCol, notesCol
        );
        
        // ربط البيانات
        tableView.setItems(doubleGlassItems);
        
        // إضافة مستمع للتحديث التلقائي للترقيم
        doubleGlassItems.addListener((javafx.collections.ListChangeListener<DoubleGlassItem>) change -> {
            tableView.refresh();
            updateTotals();
        });
        
        return tableView;
    }
    
    /**
     * إنشاء صندوق الإجماليات
     */
    private HBox createTotalsBox() {
        HBox totalsBox = new HBox(30);
        totalsBox.setAlignment(Pos.CENTER);
        totalsBox.setPadding(new Insets(15));
        totalsBox.getStyleClass().add("totals-container");
        
        // إجمالي العدد
        VBox totalCountBox = new VBox(5);
        totalCountBox.setAlignment(Pos.CENTER);
        Label totalCountTitleLabel = new Label("إجمالي العدد");
        totalCountTitleLabel.getStyleClass().add("totals-title");
        totalCountLabel = new Label("0");
        totalCountLabel.getStyleClass().add("totals-value");
        totalCountBox.getChildren().addAll(totalCountTitleLabel, totalCountLabel);
        
        // إجمالي الكمية
        VBox totalQuantityBox = new VBox(5);
        totalQuantityBox.setAlignment(Pos.CENTER);
        Label totalQuantityTitleLabel = new Label("إجمالي الكمية");
        totalQuantityTitleLabel.getStyleClass().add("totals-title");
        totalQuantityLabel = new Label("0.00");
        totalQuantityLabel.getStyleClass().add("totals-value");
        totalQuantityBox.getChildren().addAll(totalQuantityTitleLabel, totalQuantityLabel);
        
        totalsBox.getChildren().addAll(totalCountBox, totalQuantityBox);
        return totalsBox;
    }
    
    /**
     * إضافة صف جديد
     */
    private void addNewRow() {
        DoubleGlassItem newItem = new DoubleGlassItem();
        newItem.setDescription("خامة جديدة");
        newItem.setTotalCount(1);
        newItem.setTotalQuantity(1.0);
        newItem.setUnit("قطعة");
        doubleGlassItems.add(newItem);
        
        // تحديد الصف الجديد
        table.getSelectionModel().selectLast();
        table.scrollTo(doubleGlassItems.size() - 1);
    }
    
    /**
     * حذف الصف المحدد
     */
    private void deleteSelectedRow() {
        DoubleGlassItem selectedItem = table.getSelectionModel().getSelectedItem();
        if (selectedItem != null) {
            Alert confirmAlert = new Alert(Alert.AlertType.CONFIRMATION);
            confirmAlert.setTitle("تأكيد الحذف");
            confirmAlert.setHeaderText("حذف صف");
            confirmAlert.setContentText("هل أنت متأكد من حذف هذا الصف؟");
            
            confirmAlert.showAndWait().ifPresent(response -> {
                if (response == ButtonType.OK) {
                    doubleGlassItems.remove(selectedItem);
                }
            });
        } else {
            showWarningAlert("يرجى اختيار صف للحذف.");
        }
    }
    
    /**
     * مسح جميع الصفوف
     */
    private void clearAllRows() {
        if (!doubleGlassItems.isEmpty()) {
            Alert confirmAlert = new Alert(Alert.AlertType.CONFIRMATION);
            confirmAlert.setTitle("تأكيد المسح");
            confirmAlert.setHeaderText("مسح جميع الصفوف");
            confirmAlert.setContentText("هل أنت متأكد من مسح جميع الصفوف؟");
            
            confirmAlert.showAndWait().ifPresent(response -> {
                if (response == ButtonType.OK) {
                    doubleGlassItems.clear();
                }
            });
        }
    }
    
    /**
     * تحديث الإجماليات
     */
    private void updateTotals() {
        int totalCount = doubleGlassItems.stream()
                .mapToInt(DoubleGlassItem::getTotalCount)
                .sum();
        
        double totalQuantity = doubleGlassItems.stream()
                .mapToDouble(DoubleGlassItem::getTotalQuantity)
                .sum();
        
        totalCountLabel.setText(String.valueOf(totalCount));
        totalQuantityLabel.setText(decimalFormat.format(totalQuantity));
    }
    
    /**
     * الحصول على الجدول
     */
    public TableView<DoubleGlassItem> getTable() {
        return table;
    }
    
    /**
     * تحديث العرض
     */
    public void refresh() {
        if (table != null) {
            table.refresh();
            updateTotals();
        }
    }
    
    /**
     * عرض رسالة تحذير
     */
    private void showWarningAlert(String message) {
        Alert alert = new Alert(Alert.AlertType.WARNING);
        alert.setTitle("تحذير");
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }
}
