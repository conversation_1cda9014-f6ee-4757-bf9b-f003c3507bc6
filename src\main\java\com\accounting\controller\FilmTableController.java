package com.accounting.controller;

import com.accounting.model.FilmItem;
import javafx.geometry.Insets;
import javafx.geometry.Pos;
import javafx.scene.control.*;
import javafx.scene.control.cell.PropertyValueFactory;
import javafx.scene.control.cell.TextFieldTableCell;
import javafx.scene.layout.*;
import javafx.collections.ObservableList;
import javafx.util.converter.DoubleStringConverter;
import javafx.util.converter.IntegerStringConverter;
import de.jensd.fx.glyphs.fontawesome.FontAwesomeIcon;
import de.jensd.fx.glyphs.fontawesome.FontAwesomeIconView;
import java.text.DecimalFormat;

/**
 * كنترولر جدول الفيلم التفاعلي
 */
public class FilmTableController {
    
    private final ObservableList<FilmItem> filmItems;
    private final DecimalFormat decimalFormat = new DecimalFormat("#,##0.00");
    private TableView<FilmItem> table;
    private Label totalCountLabel;
    private Label totalSquareMetersLabel;
    private Label totalLengthMetersLabel;
    
    public FilmTableController(ObservableList<FilmItem> items) {
        this.filmItems = items;
    }
    
    /**
     * إنشاء جدول الفيلم التفاعلي
     */
    public VBox createFilmTable() {
        VBox container = new VBox(15);
        container.setPadding(new Insets(15));
        container.setFillWidth(true);
        VBox.setVgrow(container, Priority.ALWAYS);
        
        // العنوان
        Label title = new Label("🎬 جدول الفيلم");
        title.getStyleClass().add("section-title");
        
        // شريط الأدوات
        HBox toolbar = createToolbar();
        
        // الجدول
        table = createTable();
        table.setMinHeight(350);
        table.setPrefHeight(500);
        table.setColumnResizePolicy(TableView.CONSTRAINED_RESIZE_POLICY);
        VBox.setVgrow(table, Priority.ALWAYS);
        
        // وضع الجدول في ScrollPane
        ScrollPane scrollPane = new ScrollPane(table);
        scrollPane.setFitToWidth(true);
        scrollPane.setVbarPolicy(ScrollPane.ScrollBarPolicy.AS_NEEDED);
        scrollPane.setHbarPolicy(ScrollPane.ScrollBarPolicy.NEVER);
        scrollPane.setPrefHeight(500);
        VBox.setVgrow(scrollPane, Priority.ALWAYS);
        
        // الإجماليات
        HBox totalsBox = createTotalsBox();
        
        container.getChildren().addAll(title, toolbar, scrollPane, totalsBox);
        return container;
    }
    
    /**
     * إنشاء شريط الأدوات
     */
    private HBox createToolbar() {
        HBox toolbar = new HBox(12);
        toolbar.setAlignment(Pos.CENTER_LEFT);
        toolbar.setPadding(new Insets(10));
        toolbar.getStyleClass().add("toolbar");
        
        Button addRowBtn = new Button("إضافة صف");
        FontAwesomeIconView addIcon = new FontAwesomeIconView(FontAwesomeIcon.PLUS_CIRCLE);
        addIcon.setSize("16px");
        addIcon.setStyle("-fx-fill: #27ae60;");
        addRowBtn.setGraphic(addIcon);
        addRowBtn.getStyleClass().add("add-button");
        addRowBtn.setStyle("-fx-background-radius: 20; -fx-font-weight: bold; -fx-background-color: #eafaf1;");
        addRowBtn.setTooltip(new Tooltip("إضافة صف جديد"));
        addRowBtn.setOnAction(e -> addNewRow());
        
        Button deleteRowBtn = new Button("حذف صف");
        FontAwesomeIconView deleteIcon = new FontAwesomeIconView(FontAwesomeIcon.TRASH);
        deleteIcon.setSize("16px");
        deleteIcon.setStyle("-fx-fill: #e74c3c;");
        deleteRowBtn.setGraphic(deleteIcon);
        deleteRowBtn.getStyleClass().add("delete-button");
        deleteRowBtn.setStyle("-fx-background-radius: 20; -fx-background-color: #fdecea;");
        deleteRowBtn.setTooltip(new Tooltip("حذف الصف المحدد"));
        deleteRowBtn.setOnAction(e -> deleteSelectedRow());
        
        Button clearAllBtn = new Button("مسح الكل");
        FontAwesomeIconView clearIcon = new FontAwesomeIconView(FontAwesomeIcon.ERASER);
        clearIcon.setSize("16px");
        clearIcon.setStyle("-fx-fill: #f39c12;");
        clearAllBtn.setGraphic(clearIcon);
        clearAllBtn.getStyleClass().add("unpost-button");
        clearAllBtn.setStyle("-fx-background-radius: 20; -fx-background-color: #fff6e3;");
        clearAllBtn.setTooltip(new Tooltip("مسح كل الصفوف"));
        clearAllBtn.setOnAction(e -> clearAllRows());
        
        toolbar.getChildren().addAll(addRowBtn, deleteRowBtn, clearAllBtn);
        return toolbar;
    }
    
    /**
     * إنشاء الجدول
     */
    private TableView<FilmItem> createTable() {
        TableView<FilmItem> tableView = new TableView<>();
        tableView.setEditable(true);
        tableView.getStyleClass().add("film-table");
        tableView.setMinHeight(350);
        tableView.setPrefHeight(500);
        tableView.setColumnResizePolicy(TableView.CONSTRAINED_RESIZE_POLICY);
        VBox.setVgrow(tableView, Priority.ALWAYS);
        
        // عمود الترقيم
        TableColumn<FilmItem, Integer> indexCol = new TableColumn<>("ت");
        indexCol.setCellValueFactory(cellData -> {
            int index = tableView.getItems().indexOf(cellData.getValue()) + 1;
            return new javafx.beans.property.SimpleIntegerProperty(index).asObject();
        });
        indexCol.setPrefWidth(40);
        indexCol.setSortable(false);
        
        // عمود الوصف
        TableColumn<FilmItem, String> descCol = new TableColumn<>("الوصف");
        descCol.setCellValueFactory(new PropertyValueFactory<>("description"));
        descCol.setCellFactory(TextFieldTableCell.forTableColumn());
        descCol.setOnEditCommit(event -> {
            event.getRowValue().setDescription(event.getNewValue());
            updateTotals();
        });
        descCol.setPrefWidth(150);
        
        // عمود الطول
        TableColumn<FilmItem, Double> lengthCol = new TableColumn<>("الطول (مم)");
        lengthCol.setCellValueFactory(new PropertyValueFactory<>("length"));
        lengthCol.setCellFactory(TextFieldTableCell.forTableColumn(new DoubleStringConverter()));
        lengthCol.setOnEditCommit(event -> {
            event.getRowValue().setLength(event.getNewValue());
            updateTotals();
        });
        lengthCol.setPrefWidth(100);
        
        // عمود العرض
        TableColumn<FilmItem, Double> widthCol = new TableColumn<>("العرض (مم)");
        widthCol.setCellValueFactory(new PropertyValueFactory<>("width"));
        widthCol.setCellFactory(TextFieldTableCell.forTableColumn(new DoubleStringConverter()));
        widthCol.setOnEditCommit(event -> {
            event.getRowValue().setWidth(event.getNewValue());
            updateTotals();
        });
        widthCol.setPrefWidth(100);
        
        // عمود المتر المربع
        TableColumn<FilmItem, Double> squareMetersCol = new TableColumn<>("المتر المربع");
        squareMetersCol.setCellValueFactory(new PropertyValueFactory<>("squareMeters"));
        squareMetersCol.setCellFactory(column -> new TableCell<FilmItem, Double>() {
            @Override
            protected void updateItem(Double item, boolean empty) {
                super.updateItem(item, empty);
                if (empty || item == null) {
                    setText(null);
                } else {
                    setText(decimalFormat.format(item));
                }
            }
        });
        squareMetersCol.setPrefWidth(100);
        squareMetersCol.setEditable(false);
        
        // عمود العدد
        TableColumn<FilmItem, Integer> countCol = new TableColumn<>("العدد");
        countCol.setCellValueFactory(new PropertyValueFactory<>("count"));
        countCol.setCellFactory(TextFieldTableCell.forTableColumn(new IntegerStringConverter()));
        countCol.setOnEditCommit(event -> {
            event.getRowValue().setCount(event.getNewValue());
            updateTotals();
        });
        countCol.setPrefWidth(80);
        
        // عمود إجمالي المتر المربع
        TableColumn<FilmItem, Double> totalSquareMetersCol = new TableColumn<>("إجمالي المتر المربع");
        totalSquareMetersCol.setCellValueFactory(new PropertyValueFactory<>("totalSquareMeters"));
        totalSquareMetersCol.setCellFactory(column -> new TableCell<FilmItem, Double>() {
            @Override
            protected void updateItem(Double item, boolean empty) {
                super.updateItem(item, empty);
                if (empty || item == null) {
                    setText(null);
                } else {
                    setText(decimalFormat.format(item));
                }
            }
        });
        totalSquareMetersCol.setPrefWidth(120);
        totalSquareMetersCol.setEditable(false);
        
        // عمود الملاحظات
        TableColumn<FilmItem, String> notesCol = new TableColumn<>("ملاحظات");
        notesCol.setCellValueFactory(new PropertyValueFactory<>("notes"));
        notesCol.setCellFactory(TextFieldTableCell.forTableColumn());
        notesCol.setOnEditCommit(event -> {
            event.getRowValue().setNotes(event.getNewValue());
        });
        notesCol.setPrefWidth(150);
        
        // إضافة الأعمدة
        tableView.getColumns().addAll(
            indexCol, descCol, lengthCol, widthCol, 
            squareMetersCol, countCol, totalSquareMetersCol, notesCol
        );
        
        // ربط البيانات
        tableView.setItems(filmItems);
        
        // إضافة مستمع للتحديث التلقائي للترقيم
        filmItems.addListener((javafx.collections.ListChangeListener<FilmItem>) change -> {
            tableView.refresh();
            updateTotals();
        });
        
        return tableView;
    }
    
    /**
     * إنشاء صندوق الإجماليات
     */
    private HBox createTotalsBox() {
        HBox totalsBox = new HBox(30);
        totalsBox.setAlignment(Pos.CENTER);
        totalsBox.setPadding(new Insets(15));
        totalsBox.getStyleClass().add("totals-container");
        
        // إجمالي العدد
        VBox totalCountBox = new VBox(5);
        totalCountBox.setAlignment(Pos.CENTER);
        Label totalCountTitleLabel = new Label("إجمالي العدد");
        totalCountTitleLabel.getStyleClass().add("totals-title");
        totalCountLabel = new Label("0");
        totalCountLabel.getStyleClass().add("totals-value");
        totalCountBox.getChildren().addAll(totalCountTitleLabel, totalCountLabel);
        
        // إجمالي المتر المربع
        VBox totalSquareMetersBox = new VBox(5);
        totalSquareMetersBox.setAlignment(Pos.CENTER);
        Label totalSquareMetersTitleLabel = new Label("إجمالي المتر المربع");
        totalSquareMetersTitleLabel.getStyleClass().add("totals-title");
        totalSquareMetersLabel = new Label("0.00");
        totalSquareMetersLabel.getStyleClass().add("totals-value");
        totalSquareMetersBox.getChildren().addAll(totalSquareMetersTitleLabel, totalSquareMetersLabel);
        
        // إجمالي الأطوال بالمتر
        VBox totalLengthMetersBox = new VBox(5);
        totalLengthMetersBox.setAlignment(Pos.CENTER);
        Label totalLengthMetersTitleLabel = new Label("إجمالي الأطوال بالمتر");
        totalLengthMetersTitleLabel.getStyleClass().add("totals-title");
        totalLengthMetersLabel = new Label("0.00");
        totalLengthMetersLabel.getStyleClass().add("totals-value");
        totalLengthMetersBox.getChildren().addAll(totalLengthMetersTitleLabel, totalLengthMetersLabel);
        
        totalsBox.getChildren().addAll(totalCountBox, totalSquareMetersBox, totalLengthMetersBox);
        return totalsBox;
    }
    
    /**
     * إضافة صف جديد
     */
    private void addNewRow() {
        FilmItem newItem = new FilmItem();
        newItem.setDescription("فيلم جديد");
        newItem.setLength(1000.0);
        newItem.setWidth(1000.0);
        newItem.setCount(1);
        filmItems.add(newItem);
        
        // تحديد الصف الجديد
        table.getSelectionModel().selectLast();
        table.scrollTo(filmItems.size() - 1);
    }
    
    /**
     * حذف الصف المحدد
     */
    private void deleteSelectedRow() {
        FilmItem selectedItem = table.getSelectionModel().getSelectedItem();
        if (selectedItem != null) {
            Alert confirmAlert = new Alert(Alert.AlertType.CONFIRMATION);
            confirmAlert.setTitle("تأكيد الحذف");
            confirmAlert.setHeaderText("حذف صف");
            confirmAlert.setContentText("هل أنت متأكد من حذف هذا الصف؟");
            
            confirmAlert.showAndWait().ifPresent(response -> {
                if (response == ButtonType.OK) {
                    filmItems.remove(selectedItem);
                }
            });
        } else {
            showWarningAlert("يرجى اختيار صف للحذف.");
        }
    }
    
    /**
     * مسح جميع الصفوف
     */
    private void clearAllRows() {
        if (!filmItems.isEmpty()) {
            Alert confirmAlert = new Alert(Alert.AlertType.CONFIRMATION);
            confirmAlert.setTitle("تأكيد المسح");
            confirmAlert.setHeaderText("مسح جميع الصفوف");
            confirmAlert.setContentText("هل أنت متأكد من مسح جميع الصفوف؟");
            
            confirmAlert.showAndWait().ifPresent(response -> {
                if (response == ButtonType.OK) {
                    filmItems.clear();
                }
            });
        }
    }
    
    /**
     * تحديث الإجماليات
     */
    private void updateTotals() {
        int totalCount = filmItems.stream()
                .mapToInt(FilmItem::getCount)
                .sum();
        
        double totalSquareMeters = filmItems.stream()
                .mapToDouble(FilmItem::getTotalSquareMeters)
                .sum();
        
        double totalLengthMeters = filmItems.stream()
                .mapToDouble(FilmItem::getTotalLengthMeters)
                .sum();
        
        totalCountLabel.setText(String.valueOf(totalCount));
        totalSquareMetersLabel.setText(decimalFormat.format(totalSquareMeters) + " م²");
        totalLengthMetersLabel.setText(decimalFormat.format(totalLengthMeters) + " م");
    }
    
    /**
     * الحصول على الجدول
     */
    public TableView<FilmItem> getTable() {
        return table;
    }
    
    /**
     * تحديث العرض
     */
    public void refresh() {
        if (table != null) {
            table.refresh();
            updateTotals();
        }
    }
    
    /**
     * عرض رسالة تحذير
     */
    private void showWarningAlert(String message) {
        Alert alert = new Alert(Alert.AlertType.WARNING);
        alert.setTitle("تحذير");
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }
}
