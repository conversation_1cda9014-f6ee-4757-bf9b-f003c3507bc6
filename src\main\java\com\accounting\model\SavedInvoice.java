package com.accounting.model;

import javafx.beans.property.*;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * نموذج الفاتورة المحفوظة
 * Saved Invoice Model
 */
public class SavedInvoice {
    private final StringProperty invoiceNumber = new SimpleStringProperty();
    private final ObjectProperty<Customer> customer = new SimpleObjectProperty<>();
    private final ObjectProperty<LocalDateTime> dateTime = new SimpleObjectProperty<>();
    private final DoubleProperty subtotal = new SimpleDoubleProperty();
    private final DoubleProperty discount = new SimpleDoubleProperty();
    private final DoubleProperty total = new SimpleDoubleProperty();
    private final StringProperty notes = new SimpleStringProperty();
    private final StringProperty status = new SimpleStringProperty("مؤكدة");
    private final ObservableList<InvoiceItem> items = FXCollections.observableArrayList();
    
    // DateTimeFormatter للتنسيق
    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    
    public SavedInvoice() {
        this.dateTime.set(LocalDateTime.now());
    }
    
    public SavedInvoice(String invoiceNumber, Customer customer, ObservableList<InvoiceItem> items, 
                       double subtotal, double discount, double total, String notes) {
        this();
        this.invoiceNumber.set(invoiceNumber);
        this.customer.set(customer);
        this.items.setAll(items);
        this.subtotal.set(subtotal);
        this.discount.set(discount);
        this.total.set(total);
        this.notes.set(notes);
    }
    
    // Property getters
    public StringProperty invoiceNumberProperty() { return invoiceNumber; }
    public ObjectProperty<Customer> customerProperty() { return customer; }
    public ObjectProperty<LocalDateTime> dateTimeProperty() { return dateTime; }
    public DoubleProperty subtotalProperty() { return subtotal; }
    public DoubleProperty discountProperty() { return discount; }
    public DoubleProperty totalProperty() { return total; }
    public StringProperty notesProperty() { return notes; }
    public StringProperty statusProperty() { return status; }
    
    // Value getters and setters
    public String getInvoiceNumber() { return invoiceNumber.get(); }
    public void setInvoiceNumber(String invoiceNumber) { this.invoiceNumber.set(invoiceNumber); }
    
    public Customer getCustomer() { return customer.get(); }
    public void setCustomer(Customer customer) { this.customer.set(customer); }
    
    public LocalDateTime getDateTime() { return dateTime.get(); }
    public void setDateTime(LocalDateTime dateTime) { this.dateTime.set(dateTime); }
    
    public double getSubtotal() { return subtotal.get(); }
    public void setSubtotal(double subtotal) { this.subtotal.set(subtotal); }
    
    public double getDiscount() { return discount.get(); }
    public void setDiscount(double discount) { this.discount.set(discount); }
    
    public double getTotal() { return total.get(); }
    public void setTotal(double total) { this.total.set(total); }
    
    public String getNotes() { return notes.get(); }
    public void setNotes(String notes) { this.notes.set(notes); }
    
    public String getStatus() { return status.get(); }
    public void setStatus(String status) { this.status.set(status); }
    
    public ObservableList<InvoiceItem> getItems() { return items; }
    
    // Helper methods
    public String getFormattedDateTime() {
        return dateTime.get() != null ? dateTime.get().format(DATE_TIME_FORMATTER) : "";
    }
    
    public String getFormattedDate() {
        return dateTime.get() != null ? dateTime.get().toLocalDate().toString() : "";
    }
    
    public String getFormattedTime() {
        return dateTime.get() != null ? dateTime.get().toLocalTime().toString().substring(0, 8) : "";
    }
    
    public String getCustomerName() {
        return customer.get() != null ? customer.get().getName() : "عميل نقدي";
    }
    
    public String getCustomerPhone() {
        return customer.get() != null ? customer.get().getPhone() : "";
    }
    
    public int getItemsCount() {
        return items.size();
    }
    
    @Override
    public String toString() {
        return "فاتورة رقم: " + getInvoiceNumber() + " - " + getCustomerName() + " - " + getTotal() + " ج.م";
    }
}
