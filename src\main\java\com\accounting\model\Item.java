package com.accounting.model;

import java.time.LocalDate;

/**
 * نموذج الصنف
 * Item Model
 */
public class Item {
    private String itemId;
    private String name;
    private String category;
    private Unit unit;
    private double currentQuantity;
    private double unitPrice;
    private double totalValue;
    private String warehouseId;
    private String warehouseName;
    private String supplier;
    private LocalDate lastUpdated;
    private String notes;
    private boolean isActive;
    
    // خصائص الأبعاد
    private boolean hasDimensions; // هل الصنف له أبعاد؟
    private Double length;  // الطول (مم)
    private Double width;   // العرض (مم)
    private Integer pieces; // العدد (للأصناف ذات الأبعاد)
    private Double calculatedArea; // المساحة المحسوبة (م²)

    // خصائص إضافية
    private Double thickness; // السمك
    private String color;   // اللون
    private String type;    // النوع (شفاف، ملون، مقسى، إلخ)
    
    public Item() {
        this.isActive = true;
        this.lastUpdated = LocalDate.now();
        this.currentQuantity = 0.0;
        this.totalValue = 0.0;
    }
    
    public Item(String itemId, String name, String category, Unit unit, String warehouseId) {
        this();
        this.itemId = itemId;
        this.name = name;
        this.category = category;
        this.unit = unit;
        this.warehouseId = warehouseId;
    }
    
    /**
     * حساب المساحة للأصناف ذات الأبعاد (بالمتر المربع)
     */
    public double calculateArea() {
        if (hasDimensions && length != null && width != null) {
            double areaPerPiece = (length / 1000.0) * (width / 1000.0); // تحويل من مم إلى م
            int piecesCount = pieces != null ? pieces : 1;
            calculatedArea = areaPerPiece * piecesCount;
            return calculatedArea;
        }
        return 0.0;
    }

    /**
     * حساب الكمية بناءً على نوع الصنف
     */
    public double calculateQuantity() {
        if (hasDimensions) {
            return calculateArea(); // للأصناف ذات الأبعاد: الكمية = المساحة
        } else {
            return currentQuantity; // للأصناف العادية: الكمية المدخلة يدوياً
        }
    }
    
    /**
     * تحديث القيمة الإجمالية
     */
    public void updateTotalValue() {
        this.totalValue = currentQuantity * unitPrice;
    }
    
    // Getters and Setters
    public String getItemId() { return itemId; }
    public void setItemId(String itemId) { this.itemId = itemId; }
    
    public String getName() { return name; }
    public void setName(String name) { this.name = name; }
    
    public String getCategory() { return category; }
    public void setCategory(String category) { this.category = category; }
    
    public Unit getUnit() { return unit; }
    public void setUnit(Unit unit) { this.unit = unit; }
    
    public double getCurrentQuantity() { return currentQuantity; }
    public void setCurrentQuantity(double currentQuantity) { 
        this.currentQuantity = currentQuantity;
        updateTotalValue();
    }
    
    public double getUnitPrice() { return unitPrice; }
    public void setUnitPrice(double unitPrice) { 
        this.unitPrice = unitPrice;
        updateTotalValue();
    }
    
    public double getTotalValue() { return totalValue; }
    public void setTotalValue(double totalValue) { this.totalValue = totalValue; }
    
    public String getWarehouseId() { return warehouseId; }
    public void setWarehouseId(String warehouseId) { this.warehouseId = warehouseId; }
    
    public String getWarehouseName() { return warehouseName; }
    public void setWarehouseName(String warehouseName) { this.warehouseName = warehouseName; }
    
    public String getSupplier() { return supplier; }
    public void setSupplier(String supplier) { this.supplier = supplier; }
    
    public LocalDate getLastUpdated() { return lastUpdated; }
    public void setLastUpdated(LocalDate lastUpdated) { this.lastUpdated = lastUpdated; }
    
    public String getNotes() { return notes; }
    public void setNotes(String notes) { this.notes = notes; }
    
    public boolean isActive() { return isActive; }
    public void setActive(boolean active) { isActive = active; }
    
    // خصائص الأبعاد
    public boolean hasDimensions() { return hasDimensions; }
    public void setHasDimensions(boolean hasDimensions) {
        this.hasDimensions = hasDimensions;
        if (!hasDimensions) {
            // إذا تم إلغاء الأبعاد، مسح القيم المرتبطة
            this.length = null;
            this.width = null;
            this.pieces = null;
            this.calculatedArea = null;
        }
    }

    public Double getLength() { return length; }
    public void setLength(Double length) {
        this.length = length;
        if (hasDimensions) {
            calculateArea();
        }
    }

    public Double getWidth() { return width; }
    public void setWidth(Double width) {
        this.width = width;
        if (hasDimensions) {
            calculateArea();
        }
    }

    public Integer getPieces() { return pieces; }
    public void setPieces(Integer pieces) {
        this.pieces = pieces;
        if (hasDimensions) {
            calculateArea();
        }
    }

    public Double getCalculatedArea() { return calculatedArea; }
    public void setCalculatedArea(Double calculatedArea) { this.calculatedArea = calculatedArea; }

    // خصائص إضافية
    public Double getThickness() { return thickness; }
    public void setThickness(Double thickness) { this.thickness = thickness; }

    public String getColor() { return color; }
    public void setColor(String color) { this.color = color; }

    public String getType() { return type; }
    public void setType(String type) { this.type = type; }
    
    @Override
    public String toString() {
        return name + " (" + itemId + ")";
    }
}
