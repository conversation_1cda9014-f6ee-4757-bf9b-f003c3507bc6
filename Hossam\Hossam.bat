@echo off 
setlocal 
 
echo ======================================== 
echo       نظام المحاسبة - Hossam 
echo       Accounting System 
echo ======================================== 
echo. 
echo Starting application... 
echo. 
 
:: Check for Java 
java -version >nul 2>&1 
if not %errorlevel%==0 ( 
    echo Java not found! 
    echo. 
    echo Please install Java 17 from: 
    echo https://adoptium.net/ 
    echo. 
    echo Opening download page... 
    start https://adoptium.net/temurin/releases/ 
    pause 
    exit /b 1 
) 
 
:: Start the application 
java -Dfile.encoding=UTF-8 -jar accounting-system-1.0.0.jar 
 
if not %errorlevel%==0 ( 
    echo. 
    echo Application failed to start! 
    pause 
) 
