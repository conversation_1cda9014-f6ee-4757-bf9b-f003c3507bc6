package com.accounting.model;

import javafx.beans.property.*;
import java.util.UUID;

/**
 * نموذج المقاسات المطلوبة
 */
public class RequiredMeasurement {
    
    private final StringProperty itemId = new SimpleStringProperty();
    private final StringProperty description = new SimpleStringProperty();
    private final DoubleProperty length = new SimpleDoubleProperty(); // الطول بالمليمتر
    private final DoubleProperty width = new SimpleDoubleProperty(); // العرض بالمليمتر
    private final DoubleProperty squareMeters = new SimpleDoubleProperty(); // المتر المربع
    private final DoubleProperty linearMeters = new SimpleDoubleProperty(); // المتر الطولي
    private final IntegerProperty count = new SimpleIntegerProperty(); // العدد
    private final DoubleProperty totalSquareMeters = new SimpleDoubleProperty(); // إجمالي المتر المربع
    private final DoubleProperty totalLinearMeters = new SimpleDoubleProperty(); // إجمالي المتر الطولي
    
    /**
     * المنشئ الافتراضي
     */
    public RequiredMeasurement() {
        this.itemId.set(UUID.randomUUID().toString());
        
        // ربط الحسابات التلقائية
        setupCalculations();
    }
    
    /**
     * منشئ مع المعاملات
     */
    public RequiredMeasurement(String description, double length, double width, int count) {
        this();
        this.description.set(description);
        this.length.set(length);
        this.width.set(width);
        this.count.set(count);
    }
    
    /**
     * إعداد الحسابات التلقائية
     */
    private void setupCalculations() {
        // حساب المتر المربع والطولي عند تغيير الطول أو العرض
        length.addListener((obs, oldVal, newVal) -> calculateMeasurements());
        width.addListener((obs, oldVal, newVal) -> calculateMeasurements());
        
        // حساب الإجماليات عند تغيير العدد أو المقاسات
        squareMeters.addListener((obs, oldVal, newVal) -> calculateTotals());
        linearMeters.addListener((obs, oldVal, newVal) -> calculateTotals());
        count.addListener((obs, oldVal, newVal) -> calculateTotals());
    }
    
    /**
     * حساب المتر المربع والطولي
     */
    private void calculateMeasurements() {
        double lengthM = length.get() / 1000.0; // تحويل من مم إلى متر
        double widthM = width.get() / 1000.0; // تحويل من مم إلى متر
        
        // حساب المتر المربع
        squareMeters.set(lengthM * widthM);
        
        // حساب المتر الطولي: (الطول × 2 + العرض × 2) / 1000
        double perimeter = (length.get() * 2 + width.get() * 2) / 1000.0;
        linearMeters.set(perimeter);
    }
    
    /**
     * حساب الإجماليات
     */
    private void calculateTotals() {
        totalSquareMeters.set(squareMeters.get() * count.get());
        totalLinearMeters.set(linearMeters.get() * count.get());
    }
    
    // Getters and Setters
    public String getItemId() { return itemId.get(); }
    public void setItemId(String itemId) { this.itemId.set(itemId); }
    public StringProperty itemIdProperty() { return itemId; }
    
    public String getDescription() { return description.get(); }
    public void setDescription(String description) { this.description.set(description); }
    public StringProperty descriptionProperty() { return description; }
    
    public double getLength() { return length.get(); }
    public void setLength(double length) { this.length.set(length); }
    public DoubleProperty lengthProperty() { return length; }
    
    public double getWidth() { return width.get(); }
    public void setWidth(double width) { this.width.set(width); }
    public DoubleProperty widthProperty() { return width; }
    
    public double getSquareMeters() { return squareMeters.get(); }
    public void setSquareMeters(double squareMeters) { this.squareMeters.set(squareMeters); }
    public DoubleProperty squareMetersProperty() { return squareMeters; }
    
    public double getLinearMeters() { return linearMeters.get(); }
    public void setLinearMeters(double linearMeters) { this.linearMeters.set(linearMeters); }
    public DoubleProperty linearMetersProperty() { return linearMeters; }
    
    public int getCount() { return count.get(); }
    public void setCount(int count) { this.count.set(count); }
    public IntegerProperty countProperty() { return count; }
    
    public double getTotalSquareMeters() { return totalSquareMeters.get(); }
    public void setTotalSquareMeters(double totalSquareMeters) { this.totalSquareMeters.set(totalSquareMeters); }
    public DoubleProperty totalSquareMetersProperty() { return totalSquareMeters; }
    
    public double getTotalLinearMeters() { return totalLinearMeters.get(); }
    public void setTotalLinearMeters(double totalLinearMeters) { this.totalLinearMeters.set(totalLinearMeters); }
    public DoubleProperty totalLinearMetersProperty() { return totalLinearMeters; }
    
    /**
     * التحقق من صحة البيانات
     */
    public boolean isValid() {
        return description.get() != null && !description.get().trim().isEmpty() &&
               length.get() > 0 && width.get() > 0 && count.get() > 0;
    }
    
    /**
     * نسخ العنصر
     */
    public RequiredMeasurement copy() {
        RequiredMeasurement copy = new RequiredMeasurement();
        copy.setDescription(this.getDescription());
        copy.setLength(this.getLength());
        copy.setWidth(this.getWidth());
        copy.setCount(this.getCount());
        return copy;
    }
    
    /**
     * تمثيل نصي للعنصر
     */
    @Override
    public String toString() {
        return String.format("%s - %.0f×%.0f مم - %d قطعة - %.2f م² - %.2f م طولي", 
                description.get() != null ? description.get() : "",
                length.get(), width.get(), count.get(), 
                totalSquareMeters.get(), totalLinearMeters.get());
    }
}
