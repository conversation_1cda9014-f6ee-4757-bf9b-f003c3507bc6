@echo off 
setlocal enabledelayedexpansion 
 
echo ======================================== 
echo نظام المحاسبة المتكامل 
echo Integrated Accounting System 
echo ======================================== 
echo. 
 
:: Check for Java 
java -version >nul 2>&1 
if %errorlevel%==0 ( 
    echo ✅ Java found on system 
    goto :run_app 
) 
 
:: Check for portable Java 
if exist "java-portable\bin\java.exe" ( 
    echo ✅ Using portable Java 
    set JAVA_HOME=%~dp0java-portable 
    set PATH=!JAVA_HOME!\bin;!PATH! 
    goto :run_app 
) 
 
:: Java not found 
echo ❌ Java not found 
echo. 
echo This application requires Java 17 or newer. 
echo. 
echo Options: 
echo 1. Install Java from: https://adoptium.net/ 
echo 2. Download portable Java using: download-java.bat 
echo. 
set /p choice="Would you like to open Java download page? (y/n): " 
if /i "!choice!"=="y" start https://adoptium.net/temurin/releases/ 
pause 
exit /b 1 
 
:run_app 
echo Starting Accounting System... 
echo. 
java -Dfile.encoding=UTF-8 -Djava.awt.headless=false --add-opens javafx.graphics/javafx.scene=ALL-UNNAMED --add-opens javafx.controls/javafx.scene.control=ALL-UNNAMED --add-opens javafx.base/javafx.beans.property=ALL-UNNAMED -jar accounting-system-1.0.0.jar 
 
if not %errorlevel%==0 ( 
    echo. 
    echo ERROR: Application failed to start 
    echo. 
    pause 
) 
