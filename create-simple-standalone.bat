@echo off
setlocal

echo.
echo ========================================
echo Creating Simple Standalone Application
echo ========================================
echo.

:: Set Java environment
set JAVA_HOME=C:\Program Files\Eclipse Adoptium\jdk-********-hotspot
set PATH=%JAVA_HOME%\bin;%PATH%

echo Checking Java...
java -version >nul 2>&1
if not %errorlevel%==0 (
    echo ERROR: Java not found
    pause
    exit /b 1
)

echo ✅ Java available

echo.
echo Building application...
call mvnw.cmd clean package -q
if not %errorlevel%==0 (
    echo ERROR: Build failed
    pause
    exit /b 1
)

echo ✅ Application built

echo.
echo Creating standalone app with embedded runtime...

:: Create output directory
if exist "AccountingSystem-Standalone" rmdir /s /q "AccountingSystem-Standalone"
mkdir "AccountingSystem-Standalone"

:: Try app-image first (fastest method)
echo Creating app-image...
jpackage --type app-image --input target --dest . --name "AccountingSystem-Standalone" --main-jar accounting-system-1.0.0.jar --main-class com.accounting.AccountingApplication --java-options "-Dfile.encoding=UTF-8" --java-options "--add-opens javafx.graphics/javafx.scene=ALL-UNNAMED" --java-options "--add-opens javafx.controls/javafx.scene.control=ALL-UNNAMED" --java-options "--add-opens javafx.base/javafx.beans.property=ALL-UNNAMED"

if %errorlevel%==0 (
    echo ✅ SUCCESS: Standalone app created!
    
    :: Create user guide
    echo Creating user guide...
    echo # نظام المحاسبة المتكامل > "AccountingSystem-Standalone\README.txt"
    echo ## Integrated Accounting System >> "AccountingSystem-Standalone\README.txt"
    echo. >> "AccountingSystem-Standalone\README.txt"
    echo ### How to run: >> "AccountingSystem-Standalone\README.txt"
    echo 1. Double-click AccountingSystem-Standalone.exe >> "AccountingSystem-Standalone\README.txt"
    echo 2. No Java installation required >> "AccountingSystem-Standalone\README.txt"
    echo 3. Works on Windows 10/11 directly >> "AccountingSystem-Standalone\README.txt"
    echo. >> "AccountingSystem-Standalone\README.txt"
    echo ### طريقة التشغيل: >> "AccountingSystem-Standalone\README.txt"
    echo 1. انقر نقراً مزدوجاً على AccountingSystem-Standalone.exe >> "AccountingSystem-Standalone\README.txt"
    echo 2. لا يحتاج تثبيت Java >> "AccountingSystem-Standalone\README.txt"
    echo 3. يعمل على Windows 10/11 مباشرة >> "AccountingSystem-Standalone\README.txt"
    echo. >> "AccountingSystem-Standalone\README.txt"
    echo Support: <EMAIL> >> "AccountingSystem-Standalone\README.txt"
    
    echo.
    echo ========================================
    echo SUCCESS!
    echo ========================================
    echo.
    echo Standalone application created in:
    echo   AccountingSystem-Standalone\
    echo.
    echo Main executable:
    echo   AccountingSystem-Standalone.exe
    echo.
    echo Features:
    echo   ✅ No Java installation required
    echo   ✅ Embedded Java runtime
    echo   ✅ Ready to distribute
    echo   ✅ Works on any Windows 10/11 system
    echo.
    
    :: Show folder contents
    echo Contents:
    dir /b "AccountingSystem-Standalone"
    echo.
    
    :: Calculate total size
    echo Calculating size...
    for /f "tokens=3" %%a in ('dir "AccountingSystem-Standalone" /s /-c ^| find "File(s)"') do set size=%%a
    echo Total size: %size% bytes
    
    echo.
    echo To distribute:
    echo   1. Copy the entire AccountingSystem-Standalone folder
    echo   2. Or create a ZIP file
    echo   3. Users run AccountingSystem-Standalone.exe directly
    echo.
    
) else (
    echo ❌ App-image creation failed
    echo.
    echo This might be due to:
    echo   1. Missing JavaFX modules
    echo   2. Module path issues
    echo   3. JPackage configuration
    echo.
    echo Alternative: Use the portable version with Java bundled
    echo.
)

echo.
pause
