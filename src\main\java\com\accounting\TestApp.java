package com.accounting;

/**
 * تطبيق اختبار بسيط للتأكد من عمل Java
 * Simple test application to verify Java is working
 */
public class TestApp {
    
    public static void main(String[] args) {
        System.out.println("========================================");
        System.out.println("    نظام الحسابات المتكامل - اختبار");
        System.out.println("    Accounting System - Test");
        System.out.println("========================================");
        System.out.println();
        
        System.out.println("✅ Java يعمل بشكل صحيح!");
        System.out.println("✅ Java is working correctly!");
        System.out.println();
        
        System.out.println("📋 معلومات النظام:");
        System.out.println("Java Version: " + System.getProperty("java.version"));
        System.out.println("Java Vendor: " + System.getProperty("java.vendor"));
        System.out.println("OS Name: " + System.getProperty("os.name"));
        System.out.println("OS Version: " + System.getProperty("os.version"));
        System.out.println("User Name: " + System.getProperty("user.name"));
        System.out.println();
        
        System.out.println("🎯 الخطوة التالية: تشغيل البرنامج الرئيسي");
        System.out.println("🎯 Next step: Run the main application");
        System.out.println();
        
        System.out.println("استخدم: mvnw.cmd clean javafx:run");
        System.out.println("Use: mvnw.cmd clean javafx:run");
        System.out.println();
        
        System.out.println("========================================");
    }
}
