package com.accounting.controller;

import com.accounting.model.GlassCuttingItem;
import javafx.geometry.Insets;
import javafx.geometry.Pos;
import javafx.scene.control.*;
import javafx.scene.control.cell.PropertyValueFactory;
import javafx.scene.control.cell.TextFieldTableCell;
import javafx.scene.layout.*;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.util.converter.DoubleStringConverter;
import javafx.util.converter.IntegerStringConverter;
import de.jensd.fx.glyphs.fontawesome.FontAwesomeIcon;
import de.jensd.fx.glyphs.fontawesome.FontAwesomeIconView;
import java.text.DecimalFormat;

/**
 * كنترولر جدول قص الزجاج التفاعلي
 */
public class GlassCuttingTableController {
    
    private final ObservableList<GlassCuttingItem> glassCuttingItems;
    private final DecimalFormat decimalFormat = new DecimalFormat("#,##0.00");
    private TableView<GlassCuttingItem> table;
    private Label totalCountLabel;
    private Label totalSquareMetersLabel;
    
    public GlassCuttingTableController(ObservableList<GlassCuttingItem> items) {
        this.glassCuttingItems = items;
    }
    
    /**
     * إنشاء جدول قص الزجاج التفاعلي
     */
    public VBox createGlassCuttingTable() {
        VBox container = new VBox(15);
        container.setPadding(new Insets(15));
        
        // العنوان
        Label title = new Label("🔪 جدول قص الزجاج");
        title.getStyleClass().add("section-title");
        
        // شريط الأدوات
        HBox toolbar = createToolbar();
        
        // الجدول
        table = createTable();
        
        // وضع الجدول في ScrollPane
        ScrollPane scrollPane = new ScrollPane(table);
        scrollPane.setFitToWidth(true);
        scrollPane.setVbarPolicy(ScrollPane.ScrollBarPolicy.AS_NEEDED);
        scrollPane.setHbarPolicy(ScrollPane.ScrollBarPolicy.NEVER);
        scrollPane.setPrefHeight(300);
        VBox.setVgrow(scrollPane, Priority.ALWAYS);
        
        // الإجماليات
        HBox totalsBox = createTotalsBox();
        
        container.getChildren().addAll(title, toolbar, scrollPane, totalsBox);
        return container;
    }
    
    /**
     * إنشاء شريط الأدوات
     */
    private HBox createToolbar() {
        HBox toolbar = new HBox(10);
        toolbar.setAlignment(Pos.CENTER_LEFT);
        toolbar.setPadding(new Insets(10));
        toolbar.getStyleClass().add("toolbar");
        
        Button addRowBtn = new Button("إضافة صف");
        FontAwesomeIconView addIcon = new FontAwesomeIconView(FontAwesomeIcon.PLUS);
        addIcon.setSize("12px");
        addRowBtn.setGraphic(addIcon);
        addRowBtn.getStyleClass().add("add-button");
        addRowBtn.setOnAction(e -> addNewRow());
        
        Button deleteRowBtn = new Button("حذف صف");
        FontAwesomeIconView deleteIcon = new FontAwesomeIconView(FontAwesomeIcon.MINUS);
        deleteIcon.setSize("12px");
        deleteRowBtn.setGraphic(deleteIcon);
        deleteRowBtn.getStyleClass().add("delete-button");
        deleteRowBtn.setOnAction(e -> deleteSelectedRow());
        
        Button clearAllBtn = new Button("مسح الكل");
        FontAwesomeIconView clearIcon = new FontAwesomeIconView(FontAwesomeIcon.TRASH);
        clearIcon.setSize("12px");
        clearAllBtn.setGraphic(clearIcon);
        clearAllBtn.getStyleClass().add("unpost-button");
        clearAllBtn.setOnAction(e -> clearAllRows());
        
        toolbar.getChildren().addAll(addRowBtn, deleteRowBtn, clearAllBtn);
        return toolbar;
    }
    
    /**
     * إنشاء الجدول
     */
    private TableView<GlassCuttingItem> createTable() {
        TableView<GlassCuttingItem> tableView = new TableView<>();
        tableView.setEditable(true);
        tableView.getStyleClass().add("glass-cutting-table");
        
        // عمود الترقيم
        TableColumn<GlassCuttingItem, Integer> indexCol = new TableColumn<>("ت");
        indexCol.setCellValueFactory(cellData -> {
            int index = tableView.getItems().indexOf(cellData.getValue()) + 1;
            return new javafx.beans.property.SimpleIntegerProperty(index).asObject();
        });
        indexCol.setPrefWidth(40);
        indexCol.setSortable(false);
        
        // عمود الوصف
        TableColumn<GlassCuttingItem, String> descCol = new TableColumn<>("الوصف");
        descCol.setCellValueFactory(new PropertyValueFactory<>("description"));
        descCol.setCellFactory(TextFieldTableCell.forTableColumn());
        descCol.setOnEditCommit(event -> {
            event.getRowValue().setDescription(event.getNewValue());
            updateTotals();
        });
        descCol.setPrefWidth(150);
        
        // عمود الطول
        TableColumn<GlassCuttingItem, Double> lengthCol = new TableColumn<>("الطول (مم)");
        lengthCol.setCellValueFactory(new PropertyValueFactory<>("length"));
        lengthCol.setCellFactory(TextFieldTableCell.forTableColumn(new DoubleStringConverter()));
        lengthCol.setOnEditCommit(event -> {
            event.getRowValue().setLength(event.getNewValue());
            updateTotals();
        });
        lengthCol.setPrefWidth(100);
        
        // عمود العرض
        TableColumn<GlassCuttingItem, Double> widthCol = new TableColumn<>("العرض (مم)");
        widthCol.setCellValueFactory(new PropertyValueFactory<>("width"));
        widthCol.setCellFactory(TextFieldTableCell.forTableColumn(new DoubleStringConverter()));
        widthCol.setOnEditCommit(event -> {
            event.getRowValue().setWidth(event.getNewValue());
            updateTotals();
        });
        widthCol.setPrefWidth(100);
        
        // عمود المتر المربع
        TableColumn<GlassCuttingItem, Double> squareMetersCol = new TableColumn<>("المتر المربع");
        squareMetersCol.setCellValueFactory(new PropertyValueFactory<>("squareMeters"));
        squareMetersCol.setCellFactory(column -> new TableCell<GlassCuttingItem, Double>() {
            @Override
            protected void updateItem(Double item, boolean empty) {
                super.updateItem(item, empty);
                if (empty || item == null) {
                    setText(null);
                } else {
                    setText(decimalFormat.format(item));
                }
            }
        });
        squareMetersCol.setPrefWidth(100);
        squareMetersCol.setEditable(false);
        
        // عمود العدد
        TableColumn<GlassCuttingItem, Integer> countCol = new TableColumn<>("العدد");
        countCol.setCellValueFactory(new PropertyValueFactory<>("count"));
        countCol.setCellFactory(TextFieldTableCell.forTableColumn(new IntegerStringConverter()));
        countCol.setOnEditCommit(event -> {
            event.getRowValue().setCount(event.getNewValue());
            updateTotals();
        });
        countCol.setPrefWidth(80);
        
        // عمود إجمالي المتر المربع
        TableColumn<GlassCuttingItem, Double> totalSquareMetersCol = new TableColumn<>("إجمالي المتر المربع");
        totalSquareMetersCol.setCellValueFactory(new PropertyValueFactory<>("totalSquareMeters"));
        totalSquareMetersCol.setCellFactory(column -> new TableCell<GlassCuttingItem, Double>() {
            @Override
            protected void updateItem(Double item, boolean empty) {
                super.updateItem(item, empty);
                if (empty || item == null) {
                    setText(null);
                } else {
                    setText(decimalFormat.format(item));
                }
            }
        });
        totalSquareMetersCol.setPrefWidth(120);
        totalSquareMetersCol.setEditable(false);
        
        // عمود الملاحظات
        TableColumn<GlassCuttingItem, String> notesCol = new TableColumn<>("ملاحظات");
        notesCol.setCellValueFactory(new PropertyValueFactory<>("notes"));
        notesCol.setCellFactory(TextFieldTableCell.forTableColumn());
        notesCol.setOnEditCommit(event -> {
            event.getRowValue().setNotes(event.getNewValue());
        });
        notesCol.setPrefWidth(150);
        
        // إضافة الأعمدة
        tableView.getColumns().addAll(
            indexCol, descCol, lengthCol, widthCol, 
            squareMetersCol, countCol, totalSquareMetersCol, notesCol
        );
        
        // ربط البيانات
        tableView.setItems(glassCuttingItems);
        
        // إضافة مستمع للتحديث التلقائي للترقيم
        glassCuttingItems.addListener((javafx.collections.ListChangeListener<GlassCuttingItem>) change -> {
            tableView.refresh();
            updateTotals();
        });
        
        return tableView;
    }
    
    /**
     * إنشاء صندوق الإجماليات
     */
    private HBox createTotalsBox() {
        HBox totalsBox = new HBox(30);
        totalsBox.setAlignment(Pos.CENTER);
        totalsBox.setPadding(new Insets(15));
        totalsBox.getStyleClass().add("totals-container");
        
        // إجمالي العدد
        VBox totalCountBox = new VBox(5);
        totalCountBox.setAlignment(Pos.CENTER);
        Label totalCountTitleLabel = new Label("إجمالي العدد");
        totalCountTitleLabel.getStyleClass().add("totals-title");
        totalCountLabel = new Label("0");
        totalCountLabel.getStyleClass().add("totals-value");
        totalCountBox.getChildren().addAll(totalCountTitleLabel, totalCountLabel);
        
        // إجمالي المتر المربع
        VBox totalSquareMetersBox = new VBox(5);
        totalSquareMetersBox.setAlignment(Pos.CENTER);
        Label totalSquareMetersTitleLabel = new Label("إجمالي المتر المربع");
        totalSquareMetersTitleLabel.getStyleClass().add("totals-title");
        totalSquareMetersLabel = new Label("0.00");
        totalSquareMetersLabel.getStyleClass().add("totals-value");
        totalSquareMetersBox.getChildren().addAll(totalSquareMetersTitleLabel, totalSquareMetersLabel);
        
        totalsBox.getChildren().addAll(totalCountBox, totalSquareMetersBox);
        return totalsBox;
    }
    
    /**
     * إضافة صف جديد
     */
    private void addNewRow() {
        GlassCuttingItem newItem = new GlassCuttingItem();
        newItem.setDescription("زجاج جديد");
        newItem.setLength(1000.0);
        newItem.setWidth(1000.0);
        newItem.setCount(1);
        glassCuttingItems.add(newItem);
        
        // تحديد الصف الجديد
        table.getSelectionModel().selectLast();
        table.scrollTo(glassCuttingItems.size() - 1);
    }
    
    /**
     * حذف الصف المحدد
     */
    private void deleteSelectedRow() {
        GlassCuttingItem selectedItem = table.getSelectionModel().getSelectedItem();
        if (selectedItem != null) {
            Alert confirmAlert = new Alert(Alert.AlertType.CONFIRMATION);
            confirmAlert.setTitle("تأكيد الحذف");
            confirmAlert.setHeaderText("حذف صف");
            confirmAlert.setContentText("هل أنت متأكد من حذف هذا الصف؟");
            
            confirmAlert.showAndWait().ifPresent(response -> {
                if (response == ButtonType.OK) {
                    glassCuttingItems.remove(selectedItem);
                }
            });
        } else {
            showWarningAlert("يرجى اختيار صف للحذف.");
        }
    }
    
    /**
     * مسح جميع الصفوف
     */
    private void clearAllRows() {
        if (!glassCuttingItems.isEmpty()) {
            Alert confirmAlert = new Alert(Alert.AlertType.CONFIRMATION);
            confirmAlert.setTitle("تأكيد المسح");
            confirmAlert.setHeaderText("مسح جميع الصفوف");
            confirmAlert.setContentText("هل أنت متأكد من مسح جميع الصفوف؟");
            
            confirmAlert.showAndWait().ifPresent(response -> {
                if (response == ButtonType.OK) {
                    glassCuttingItems.clear();
                }
            });
        }
    }
    
    /**
     * تحديث الإجماليات
     */
    private void updateTotals() {
        int totalCount = glassCuttingItems.stream()
                .mapToInt(GlassCuttingItem::getCount)
                .sum();
        
        double totalSquareMeters = glassCuttingItems.stream()
                .mapToDouble(GlassCuttingItem::getTotalSquareMeters)
                .sum();
        
        totalCountLabel.setText(String.valueOf(totalCount));
        totalSquareMetersLabel.setText(decimalFormat.format(totalSquareMeters) + " م²");
    }
    
    /**
     * الحصول على الجدول
     */
    public TableView<GlassCuttingItem> getTable() {
        return table;
    }
    
    /**
     * تحديث العرض
     */
    public void refresh() {
        if (table != null) {
            table.refresh();
            updateTotals();
        }
    }
    
    /**
     * عرض رسالة تحذير
     */
    private void showWarningAlert(String message) {
        Alert alert = new Alert(Alert.AlertType.WARNING);
        alert.setTitle("تحذير");
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }
}
