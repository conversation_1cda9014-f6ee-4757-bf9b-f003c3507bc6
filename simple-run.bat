@echo off
echo ========================================
echo    Integrated Accounting System
echo ========================================
echo.

echo Checking Java...
java -version
if %errorlevel% neq 0 (
    echo ERROR: Java is not installed
    echo Please install Java 17+ from: https://adoptium.net/
    pause
    exit /b 1
)

echo.
echo Java is available!
echo.

echo Starting the application...
echo Please wait, this may take a few minutes on first run...
echo.

call mvnw.cmd clean javafx:run

if %errorlevel% neq 0 (
    echo.
    echo ERROR: Failed to start the application
    echo Make sure you have internet connection for first run
    pause
) else (
    echo.
    echo Application closed successfully
)

echo.
pause
