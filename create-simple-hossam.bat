@echo off
setlocal

echo.
echo ========================================
echo Creating Simple Hossam App
echo ========================================
echo.

:: Create simple Hossam folder
if exist "Hossam-Simple" rmdir /s /q "Hossam-Simple"
mkdir "Hossam-Simple"

echo Copying application...
copy "target\accounting-system-1.0.0.jar" "Hossam-Simple\"

:: Create super simple launcher
echo @echo off > "Hossam-Simple\Hossam.bat"
echo echo Starting Hossam Accounting System... >> "Hossam-Simple\Hossam.bat"
echo java -jar accounting-system-1.0.0.jar >> "Hossam-Simple\Hossam.bat"
echo pause >> "Hossam-Simple\Hossam.bat"

:: Create silent launcher
echo Set objShell = CreateObject("WScript.Shell") > "Hossam-Simple\Hossam.vbs"
echo objShell.Run "Hossam.bat", 0, False >> "Hossam-Simple\Hossam.vbs"

:: Create simple instructions
echo Hossam Accounting System > "Hossam-Simple\README.txt"
echo ======================= >> "Hossam-Simple\README.txt"
echo. >> "Hossam-Simple\README.txt"
echo How to run: >> "Hossam-Simple\README.txt"
echo 1. Double-click Hossam.bat >> "Hossam-Simple\README.txt"
echo 2. Or double-click Hossam.vbs (silent) >> "Hossam-Simple\README.txt"
echo. >> "Hossam-Simple\README.txt"
echo Requirements: >> "Hossam-Simple\README.txt"
echo - Java 17+ installed >> "Hossam-Simple\README.txt"
echo - Download from: https://adoptium.net/ >> "Hossam-Simple\README.txt"

:: Create ZIP
powershell -command "Compress-Archive -Path 'Hossam-Simple\*' -DestinationPath 'Hossam-Simple.zip' -Force" 2>nul

echo.
echo ========================================
echo SUCCESS: Simple Hossam Created!
echo ========================================
echo.
echo Files:
dir /b "Hossam-Simple"
echo.
echo Ready to distribute:
echo - Folder: Hossam-Simple\
echo - ZIP: Hossam-Simple.zip
echo.
echo To run: Double-click Hossam.bat
echo.

pause
