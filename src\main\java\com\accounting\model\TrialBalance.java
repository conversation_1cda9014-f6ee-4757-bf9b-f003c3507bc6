package com.accounting.model;

import javafx.beans.property.*;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * نموذج ميزان المراجعة
 */
public class TrialBalance {
    
    // الخصائص الأساسية
    private final StringProperty accountCode = new SimpleStringProperty();
    private final StringProperty accountName = new SimpleStringProperty();
    private final ObjectProperty<Account.AccountType> accountType = new SimpleObjectProperty<>();
    private final ObjectProperty<Account.AccountNature> accountNature = new SimpleObjectProperty<>();
    
    // الأرصدة الافتتاحية
    private final DoubleProperty openingDebit = new SimpleDoubleProperty(0.0);
    private final DoubleProperty openingCredit = new SimpleDoubleProperty(0.0);
    
    // مجاميع الحركة خلال الفترة
    private final DoubleProperty periodDebitTotal = new SimpleDoubleProperty(0.0);
    private final DoubleProperty periodCreditTotal = new SimpleDoubleProperty(0.0);
    
    // الأرصدة الختامية
    private final DoubleProperty closingDebit = new SimpleDoubleProperty(0.0);
    private final DoubleProperty closingCredit = new SimpleDoubleProperty(0.0);
    
    // معلومات الفترة
    private final ObjectProperty<LocalDate> fromDate = new SimpleObjectProperty<>();
    private final ObjectProperty<LocalDate> toDate = new SimpleObjectProperty<>();
    private final ObjectProperty<LocalDateTime> generatedDate = new SimpleObjectProperty<>(LocalDateTime.now());
    
    // معلومات إضافية
    private final StringProperty currency = new SimpleStringProperty("EGP");
    private final IntegerProperty level = new SimpleIntegerProperty();
    private final BooleanProperty isParent = new SimpleBooleanProperty(false);
    private final BooleanProperty includeSubAccounts = new SimpleBooleanProperty(true);
    
    // المرجع للحساب
    private Account account;
    
    // Constructors
    public TrialBalance() {}
    
    public TrialBalance(Account account, LocalDate fromDate, LocalDate toDate) {
        setAccount(account);
        setAccountCode(account.getAccountCode());
        setAccountName(account.getAccountName());
        setAccountType(account.getAccountType());
        setAccountNature(account.getAccountNature());
        setLevel(account.getLevel());
        setIsParent(account.isParent());
        setCurrency(account.getCurrency());
        setFromDate(fromDate);
        setToDate(toDate);
        setOpeningDebit(account.getOpeningBalance() > 0 && account.getAccountNature() == Account.AccountNature.DEBIT ? 
                       account.getOpeningBalance() : 0.0);
        setOpeningCredit(account.getOpeningBalance() > 0 && account.getAccountNature() == Account.AccountNature.CREDIT ? 
                        account.getOpeningBalance() : 0.0);
    }
    
    // Property getters
    public StringProperty accountCodeProperty() { return accountCode; }
    public StringProperty accountNameProperty() { return accountName; }
    public ObjectProperty<Account.AccountType> accountTypeProperty() { return accountType; }
    public ObjectProperty<Account.AccountNature> accountNatureProperty() { return accountNature; }
    public DoubleProperty openingDebitProperty() { return openingDebit; }
    public DoubleProperty openingCreditProperty() { return openingCredit; }
    public DoubleProperty periodDebitTotalProperty() { return periodDebitTotal; }
    public DoubleProperty periodCreditTotalProperty() { return periodCreditTotal; }
    public DoubleProperty closingDebitProperty() { return closingDebit; }
    public DoubleProperty closingCreditProperty() { return closingCredit; }
    public ObjectProperty<LocalDate> fromDateProperty() { return fromDate; }
    public ObjectProperty<LocalDate> toDateProperty() { return toDate; }
    public ObjectProperty<LocalDateTime> generatedDateProperty() { return generatedDate; }
    public StringProperty currencyProperty() { return currency; }
    public IntegerProperty levelProperty() { return level; }
    public BooleanProperty isParentProperty() { return isParent; }
    public BooleanProperty includeSubAccountsProperty() { return includeSubAccounts; }
    
    // Value getters and setters
    public String getAccountCode() { return accountCode.get(); }
    public void setAccountCode(String accountCode) { this.accountCode.set(accountCode); }
    
    public String getAccountName() { return accountName.get(); }
    public void setAccountName(String accountName) { this.accountName.set(accountName); }
    
    public Account.AccountType getAccountType() { return accountType.get(); }
    public void setAccountType(Account.AccountType accountType) { this.accountType.set(accountType); }
    
    public Account.AccountNature getAccountNature() { return accountNature.get(); }
    public void setAccountNature(Account.AccountNature accountNature) { this.accountNature.set(accountNature); }
    
    public double getOpeningDebit() { return openingDebit.get(); }
    public void setOpeningDebit(double openingDebit) { this.openingDebit.set(openingDebit); }
    
    public double getOpeningCredit() { return openingCredit.get(); }
    public void setOpeningCredit(double openingCredit) { this.openingCredit.set(openingCredit); }
    
    public double getPeriodDebitTotal() { return periodDebitTotal.get(); }
    public void setPeriodDebitTotal(double periodDebitTotal) { 
        this.periodDebitTotal.set(periodDebitTotal);
        calculateClosingBalances();
    }
    
    public double getPeriodCreditTotal() { return periodCreditTotal.get(); }
    public void setPeriodCreditTotal(double periodCreditTotal) { 
        this.periodCreditTotal.set(periodCreditTotal);
        calculateClosingBalances();
    }
    
    public double getClosingDebit() { return closingDebit.get(); }
    public void setClosingDebit(double closingDebit) { this.closingDebit.set(closingDebit); }
    
    public double getClosingCredit() { return closingCredit.get(); }
    public void setClosingCredit(double closingCredit) { this.closingCredit.set(closingCredit); }
    
    public LocalDate getFromDate() { return fromDate.get(); }
    public void setFromDate(LocalDate fromDate) { this.fromDate.set(fromDate); }
    
    public LocalDate getToDate() { return toDate.get(); }
    public void setToDate(LocalDate toDate) { this.toDate.set(toDate); }
    
    public LocalDateTime getGeneratedDate() { return generatedDate.get(); }
    public void setGeneratedDate(LocalDateTime generatedDate) { this.generatedDate.set(generatedDate); }
    
    public String getCurrency() { return currency.get(); }
    public void setCurrency(String currency) { this.currency.set(currency); }
    
    public int getLevel() { return level.get(); }
    public void setLevel(int level) { this.level.set(level); }
    
    public boolean isParent() { return isParent.get(); }
    public void setIsParent(boolean isParent) { this.isParent.set(isParent); }
    
    public boolean isIncludeSubAccounts() { return includeSubAccounts.get(); }
    public void setIncludeSubAccounts(boolean includeSubAccounts) { this.includeSubAccounts.set(includeSubAccounts); }
    
    // Object reference
    public Account getAccount() { return account; }
    public void setAccount(Account account) { this.account = account; }
    
    /**
     * حساب الأرصدة الختامية
     */
    private void calculateClosingBalances() {
        double openingBalance = getOpeningDebit() - getOpeningCredit();
        double periodMovement = getPeriodDebitTotal() - getPeriodCreditTotal();
        double closingBalance = openingBalance + periodMovement;
        
        if (closingBalance > 0) {
            setClosingDebit(closingBalance);
            setClosingCredit(0.0);
        } else if (closingBalance < 0) {
            setClosingDebit(0.0);
            setClosingCredit(Math.abs(closingBalance));
        } else {
            setClosingDebit(0.0);
            setClosingCredit(0.0);
        }
    }
    
    /**
     * إضافة حركة للفترة
     */
    public void addMovement(double debitAmount, double creditAmount) {
        setPeriodDebitTotal(getPeriodDebitTotal() + debitAmount);
        setPeriodCreditTotal(getPeriodCreditTotal() + creditAmount);
    }
    
    /**
     * الحصول على الرصيد الافتتاحي الصافي
     */
    public double getOpeningBalance() {
        return getOpeningDebit() - getOpeningCredit();
    }
    
    /**
     * الحصول على إجمالي الحركة خلال الفترة
     */
    public double getPeriodMovement() {
        return getPeriodDebitTotal() - getPeriodCreditTotal();
    }
    
    /**
     * الحصول على الرصيد الختامي الصافي
     */
    public double getClosingBalance() {
        return getClosingDebit() - getClosingCredit();
    }
    
    /**
     * الحصول على إجمالي المدين (افتتاحي + فترة)
     */
    public double getTotalDebit() {
        return getOpeningDebit() + getPeriodDebitTotal();
    }
    
    /**
     * الحصول على إجمالي الدائن (افتتاحي + فترة)
     */
    public double getTotalCredit() {
        return getOpeningCredit() + getPeriodCreditTotal();
    }
    
    /**
     * التحقق من وجود حركة في الحساب
     */
    public boolean hasMovement() {
        return getPeriodDebitTotal() > 0 || getPeriodCreditTotal() > 0 || 
               getOpeningDebit() > 0 || getOpeningCredit() > 0;
    }
    
    /**
     * التحقق من توازن الحساب
     */
    public boolean isBalanced() {
        return Math.abs(getTotalDebit() - getTotalCredit()) < 0.01;
    }
    
    /**
     * الحصول على نوع الرصيد الختامي
     */
    public String getClosingBalanceType() {
        if (getClosingDebit() > 0) return "مدين";
        if (getClosingCredit() > 0) return "دائن";
        return "متوازن";
    }
    
    /**
     * الحصول على الرصيد الختامي المطلق
     */
    public double getAbsoluteClosingBalance() {
        return Math.max(getClosingDebit(), getClosingCredit());
    }
    
    /**
     * تحويل إلى نص منسق للطباعة
     */
    public String getFormattedSummary() {
        StringBuilder summary = new StringBuilder();
        summary.append("الحساب: ").append(getAccountCode()).append(" - ").append(getAccountName()).append("\n");
        summary.append("الفترة: من ").append(getFromDate()).append(" إلى ").append(getToDate()).append("\n");
        summary.append("الرصيد الافتتاحي: ").append(String.format("%.2f", getOpeningBalance())).append("\n");
        summary.append("حركة الفترة: ").append(String.format("%.2f", getPeriodMovement())).append("\n");
        summary.append("الرصيد الختامي: ").append(String.format("%.2f", getClosingBalance())).append(" (").append(getClosingBalanceType()).append(")");
        return summary.toString();
    }
    
    /**
     * إعادة تعيين البيانات
     */
    public void reset() {
        setOpeningDebit(0.0);
        setOpeningCredit(0.0);
        setPeriodDebitTotal(0.0);
        setPeriodCreditTotal(0.0);
        setClosingDebit(0.0);
        setClosingCredit(0.0);
        setGeneratedDate(LocalDateTime.now());
    }
    
    /**
     * نسخ ميزان المراجعة
     */
    public TrialBalance copy() {
        TrialBalance copy = new TrialBalance();
        copy.setAccountCode(getAccountCode());
        copy.setAccountName(getAccountName());
        copy.setAccountType(getAccountType());
        copy.setAccountNature(getAccountNature());
        copy.setOpeningDebit(getOpeningDebit());
        copy.setOpeningCredit(getOpeningCredit());
        copy.setPeriodDebitTotal(getPeriodDebitTotal());
        copy.setPeriodCreditTotal(getPeriodCreditTotal());
        copy.setClosingDebit(getClosingDebit());
        copy.setClosingCredit(getClosingCredit());
        copy.setFromDate(getFromDate());
        copy.setToDate(getToDate());
        copy.setCurrency(getCurrency());
        copy.setLevel(getLevel());
        copy.setIsParent(isParent());
        copy.setIncludeSubAccounts(isIncludeSubAccounts());
        copy.setAccount(getAccount());
        return copy;
    }
    
    @Override
    public String toString() {
        return getAccountCode() + " - " + getAccountName() + " (رصيد: " + 
               String.format("%.2f", getClosingBalance()) + " " + getClosingBalanceType() + ")";
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        TrialBalance that = (TrialBalance) obj;
        return getAccountCode() != null && getAccountCode().equals(that.getAccountCode()) &&
               getFromDate() != null && getFromDate().equals(that.getFromDate()) &&
               getToDate() != null && getToDate().equals(that.getToDate());
    }
    
    @Override
    public int hashCode() {
        int result = getAccountCode() != null ? getAccountCode().hashCode() : 0;
        result = 31 * result + (getFromDate() != null ? getFromDate().hashCode() : 0);
        result = 31 * result + (getToDate() != null ? getToDate().hashCode() : 0);
        return result;
    }
}
