@echo off 
setlocal 
 
echo ======================================== 
echo Starting Accounting System 
echo ======================================== 
echo. 
 
:: Check Java 
java -version >nul 2>&1 
if not %errorlevel%==0 ( 
    echo ERROR: Java not found 
    echo Please install Java 17 or newer from: 
    echo https://adoptium.net/ 
    echo. 
    pause 
    exit /b 1 
) 
 
echo Starting application... 
echo. 
 
:: Start the application 
java -Dfile.encoding=UTF-8 -Djava.awt.headless=false --add-opens javafx.graphics/javafx.scene=ALL-UNNAMED --add-opens javafx.controls/javafx.scene.control=ALL-UNNAMED --add-opens javafx.base/javafx.beans.property=ALL-UNNAMED -jar accounting-system-1.0.0.jar 
 
if not %errorlevel%==0 ( 
    echo. 
    echo ERROR: Failed to start application 
    echo. 
    echo Troubleshooting: 
    echo 1. Make sure Java 17+ is installed 
    echo 2. Check if antivirus is blocking the application 
    echo 3. Try running as administrator 
    echo. 
    pause 
    exit /b 1 
) 
 
echo. 
echo Application closed successfully 
pause 
