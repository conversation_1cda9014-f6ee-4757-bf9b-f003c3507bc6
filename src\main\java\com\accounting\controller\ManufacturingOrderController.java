package com.accounting.controller;

import com.accounting.model.*;
import com.accounting.service.ManufacturingService;
import javafx.geometry.Insets;
import javafx.geometry.Pos;
import javafx.scene.Scene;
import javafx.scene.control.*;
import javafx.scene.control.cell.PropertyValueFactory;
import javafx.scene.layout.*;
import javafx.stage.Modality;
import javafx.stage.Stage;
import de.jensd.fx.glyphs.fontawesome.FontAwesomeIcon;
import de.jensd.fx.glyphs.fontawesome.FontAwesomeIconView;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import java.time.LocalDate;
import java.text.DecimalFormat;
import java.util.Optional;
import java.util.function.Consumer;

/**
 * كنترولر أوامر التصنيع
 */
public class ManufacturingOrderController {
    
    private final ManufacturingService manufacturingService;
    private final DecimalFormat decimalFormat = new DecimalFormat("#,##0.00");
    private TableView<ManufacturingOrder> ordersTable;
    private TextField searchField;
    private DatePicker fromDatePicker;
    private DatePicker toDatePicker;
    private ComboBox<ManufacturingOrder.OrderStatus> statusFilter;
    
    public ManufacturingOrderController() {
        this.manufacturingService = ManufacturingService.getInstance();
    }

    /**
     * إنشاء صفحة أوامر التصنيع للنافذة الواحدة
     */
    public VBox createManufacturingOrdersPage(Consumer<ManufacturingOrder> onEditOrder,
                                             Runnable onShowReports,
                                             Consumer<ManufacturingOrder> onPrintOrder) {
        VBox container = new VBox(15);
        container.setPadding(new Insets(20));

        // العنوان والوصف
        Label titleLabel = new Label("📋 إدارة أوامر التصنيع");
        titleLabel.getStyleClass().add("section-title");

        Label descLabel = new Label("إدارة أوامر التصنيع للزجاج والألومنيوم");
        descLabel.getStyleClass().add("section-description");

        // شريط الأدوات
        HBox toolbar = createPageToolbar(onEditOrder, onShowReports);

        // الفلاتر
        VBox filtersBox = createFiltersBox();

        // الجدول
        ordersTable = createOrdersTableForPage(onEditOrder, onPrintOrder);

        // تحميل البيانات
        refreshOrdersTable();

        // وضع الجدول في ScrollPane
        ScrollPane scrollPane = new ScrollPane(ordersTable);
        scrollPane.setFitToWidth(true);
        scrollPane.setVbarPolicy(ScrollPane.ScrollBarPolicy.AS_NEEDED);
        scrollPane.setHbarPolicy(ScrollPane.ScrollBarPolicy.NEVER);
        scrollPane.setPrefHeight(400);
        VBox.setVgrow(scrollPane, Priority.ALWAYS);

        container.getChildren().addAll(titleLabel, descLabel, toolbar, filtersBox, scrollPane);
        return container;
    }

    /**
     * إنشاء شريط أدوات الصفحة
     */
    private HBox createPageToolbar(Consumer<ManufacturingOrder> onEditOrder, Runnable onShowReports) {
        HBox toolbar = new HBox(15);
        toolbar.setAlignment(Pos.CENTER_LEFT);
        toolbar.setPadding(new Insets(10));
        toolbar.getStyleClass().add("toolbar");

        Button newOrderBtn = new Button("أمر تصنيع جديد");
        FontAwesomeIconView newIcon = new FontAwesomeIconView(FontAwesomeIcon.PLUS);
        newIcon.setSize("12px");
        newOrderBtn.setGraphic(newIcon);
        newOrderBtn.getStyleClass().add("add-button");
        newOrderBtn.setOnAction(e -> onEditOrder.accept(null));

        Button reportsBtn = new Button("التقارير");
        FontAwesomeIconView reportsIcon = new FontAwesomeIconView(FontAwesomeIcon.CHART_BAR);
        reportsIcon.setSize("12px");
        reportsBtn.setGraphic(reportsIcon);
        reportsBtn.getStyleClass().add("reports-button");
        reportsBtn.setOnAction(e -> onShowReports.run());

        Button refreshBtn = new Button("تحديث");
        FontAwesomeIconView refreshIcon = new FontAwesomeIconView(FontAwesomeIcon.REFRESH);
        refreshIcon.setSize("12px");
        refreshBtn.setGraphic(refreshIcon);
        refreshBtn.getStyleClass().add("refresh-button");
        refreshBtn.setOnAction(e -> refreshOrdersTable());

        toolbar.getChildren().addAll(newOrderBtn, reportsBtn, refreshBtn);
        return toolbar;
    }

    /**
     * إنشاء جدول الأوامر للصفحة
     */
    private TableView<ManufacturingOrder> createOrdersTableForPage(Consumer<ManufacturingOrder> onEditOrder, Consumer<ManufacturingOrder> onPrintOrder) {
        TableView<ManufacturingOrder> table = new TableView<>();
        table.getStyleClass().add("orders-table");

        // الأعمدة الأساسية
        TableColumn<ManufacturingOrder, String> numberCol = new TableColumn<>("رقم الأمر");
        numberCol.setCellValueFactory(new PropertyValueFactory<>("orderNumber"));
        numberCol.setPrefWidth(100);

        TableColumn<ManufacturingOrder, String> customerCol = new TableColumn<>("العميل");
        customerCol.setCellValueFactory(new PropertyValueFactory<>("customerName"));
        customerCol.setPrefWidth(150);

        TableColumn<ManufacturingOrder, String> invoiceCol = new TableColumn<>("رقم الفاتورة");
        invoiceCol.setCellValueFactory(new PropertyValueFactory<>("invoiceNumber"));
        invoiceCol.setPrefWidth(120);

        TableColumn<ManufacturingOrder, LocalDate> dateCol = new TableColumn<>("التاريخ");
        dateCol.setCellValueFactory(new PropertyValueFactory<>("orderDate"));
        dateCol.setPrefWidth(100);

        TableColumn<ManufacturingOrder, String> statusCol = new TableColumn<>("الحالة");
        statusCol.setCellValueFactory(cellData ->
            cellData.getValue().statusProperty().asString());
        statusCol.setPrefWidth(100);

        // عمود الإجراءات
        TableColumn<ManufacturingOrder, Void> actionsCol = new TableColumn<>("الإجراءات");
        actionsCol.setCellFactory(col -> new TableCell<ManufacturingOrder, Void>() {
            private final Button editBtn = new Button("تعديل");
            private final Button printBtn = new Button("طباعة");
            private final Button deliveryBtn = new Button("أمر تسليم");
            private final HBox actionsBox = new HBox(5);

            {
                FontAwesomeIconView editIcon = new FontAwesomeIconView(FontAwesomeIcon.EDIT);
                editIcon.setSize("10px");
                editBtn.setGraphic(editIcon);
                editBtn.getStyleClass().add("edit-button");

                FontAwesomeIconView printIcon = new FontAwesomeIconView(FontAwesomeIcon.PRINT);
                printIcon.setSize("10px");
                printBtn.setGraphic(printIcon);
                printBtn.getStyleClass().add("print-button");

                FontAwesomeIconView deliveryIcon = new FontAwesomeIconView(FontAwesomeIcon.TRUCK);
                deliveryIcon.setSize("10px");
                deliveryBtn.setGraphic(deliveryIcon);
                deliveryBtn.getStyleClass().add("delivery-button");

                actionsBox.getChildren().addAll(editBtn, printBtn, deliveryBtn);
                actionsBox.setAlignment(Pos.CENTER);
            }

            @Override
            protected void updateItem(Void item, boolean empty) {
                super.updateItem(item, empty);
                if (empty) {
                    setGraphic(null);
                } else {
                    ManufacturingOrder order = getTableView().getItems().get(getIndex());
                    editBtn.setOnAction(e -> onEditOrder.accept(order));
                    printBtn.setOnAction(e -> onPrintOrder.accept(order));
                    deliveryBtn.setOnAction(e -> createDeliveryOrderFromManufacturing(order));
                    setGraphic(actionsBox);
                }
            }
        });
        actionsCol.setPrefWidth(200);

        // إضافة الأعمدة
        table.getColumns().addAll(numberCol, customerCol, invoiceCol, dateCol, statusCol, actionsCol);

        // تخصيص عرض الصفوف حسب الحالة
        table.setRowFactory(tv -> {
            TableRow<ManufacturingOrder> row = new TableRow<>();
            row.itemProperty().addListener((obs, oldOrder, newOrder) -> {
                if (newOrder != null) {
                    String style = "";
                    switch (newOrder.getStatus()) {
                        case DRAFT:
                            style = "-fx-background-color: #fff3cd;";
                            break;
                        case CONFIRMED:
                            style = "-fx-background-color: #d1ecf1;";
                            break;
                        case IN_PROGRESS:
                            style = "-fx-background-color: #f8d7da;";
                            break;
                        case COMPLETED:
                            style = "-fx-background-color: #d4edda;";
                            break;
                        case CANCELLED:
                            style = "-fx-background-color: #f5c6cb;";
                            break;
                    }
                    row.setStyle(style);
                }
            });
            return row;
        });

        return table;
    }

    /**
     * إنشاء أمر تسليم من أمر التصنيع
     */
    private void createDeliveryOrderFromManufacturing(ManufacturingOrder manufacturingOrder) {
        // سيتم تنفيذ هذا لاحقاً
        showInfoAlert("ميزة إنشاء أمر التسليم من أمر التصنيع قيد التطوير.");
    }
    
    /**
     * عرض نافذة أوامر التصنيع
     */
    public void showManufacturingOrderDialog() {
        Stage dialog = new Stage();
        dialog.setTitle("📋 Manufacturing Orders - أوامر التصنيع");
        dialog.initModality(Modality.APPLICATION_MODAL);
        
        // تحديد حجم النافذة
        javafx.stage.Screen screen = javafx.stage.Screen.getPrimary();
        double screenHeight = screen.getVisualBounds().getHeight();
        double screenWidth = screen.getVisualBounds().getWidth();
        
        dialog.setWidth(Math.min(1400, screenWidth * 0.95));
        dialog.setHeight(Math.min(900, screenHeight * 0.9));
        dialog.setResizable(true);
        
        VBox mainLayout = new VBox(15);
        mainLayout.setPadding(new Insets(20));
        
        // العنوان والوصف
        Label titleLabel = new Label("📋 Manufacturing Orders - أوامر التصنيع");
        titleLabel.getStyleClass().add("dialog-title");
        
        Label descLabel = new Label("إدارة أوامر التصنيع للزجاج والألومنيوم");
        descLabel.getStyleClass().add("dialog-description");
        
        // شريط الأدوات والفلاتر
        VBox filtersBox = createFiltersBox();
        
        // جدول الأوامر
        ordersTable = createOrdersTable();
        
        // تحميل البيانات
        refreshOrdersTable();
        
        // وضع الجدول في ScrollPane
        ScrollPane scrollPane = new ScrollPane(ordersTable);
        scrollPane.setFitToWidth(true);
        scrollPane.setVbarPolicy(ScrollPane.ScrollBarPolicy.AS_NEEDED);
        scrollPane.setHbarPolicy(ScrollPane.ScrollBarPolicy.NEVER);
        scrollPane.getStyleClass().add("orders-scroll-pane");
        VBox.setVgrow(scrollPane, Priority.ALWAYS);
        
        // أزرار الإجراءات
        HBox buttonBox = createButtonBox(dialog);
        
        mainLayout.getChildren().addAll(
            titleLabel, descLabel, filtersBox, scrollPane, buttonBox
        );
        
        Scene scene = new Scene(mainLayout);
        scene.getStylesheets().add(getClass().getResource("/css/styles.css").toExternalForm());
        dialog.setScene(scene);
        dialog.showAndWait();
    }
    
    /**
     * عرض نافذة أمر تصنيع جديد
     */
    public void showNewManufacturingOrderDialog() {
        ManufacturingOrderFormController formController = new ManufacturingOrderFormController(manufacturingService);
        Optional<ManufacturingOrder> result = formController.showManufacturingOrderForm(null);
        
        if (result.isPresent()) {
            showSuccessAlert("تم إنشاء أمر التصنيع بنجاح!");
        }
    }
    
    /**
     * إنشاء صندوق الفلاتر
     */
    private VBox createFiltersBox() {
        VBox filtersBox = new VBox(10);
        filtersBox.setPadding(new Insets(15));
        filtersBox.getStyleClass().add("filters-container");
        
        Label filtersTitle = new Label("🔍 الفلاتر والبحث");
        filtersTitle.getStyleClass().add("filters-title");
        
        // الصف الأول من الفلاتر
        HBox firstRow = new HBox(15);
        firstRow.setAlignment(Pos.CENTER_LEFT);
        
        // البحث
        Label searchLabel = new Label("البحث:");
        searchField = new TextField();
        searchField.setPromptText("ابحث في الأوامر...");
        searchField.setPrefWidth(200);
        searchField.textProperty().addListener((obs, oldVal, newVal) -> applyFilters());
        
        // فلتر التاريخ من
        Label fromLabel = new Label("من تاريخ:");
        fromDatePicker = new DatePicker();
        fromDatePicker.setValue(LocalDate.now().withDayOfMonth(1)); // أول الشهر
        fromDatePicker.valueProperty().addListener((obs, oldVal, newVal) -> applyFilters());
        
        // فلتر التاريخ إلى
        Label toLabel = new Label("إلى تاريخ:");
        toDatePicker = new DatePicker();
        toDatePicker.setValue(LocalDate.now()); // اليوم
        toDatePicker.valueProperty().addListener((obs, oldVal, newVal) -> applyFilters());
        
        firstRow.getChildren().addAll(
            searchLabel, searchField,
            new Separator(),
            fromLabel, fromDatePicker,
            toLabel, toDatePicker
        );
        
        // الصف الثاني من الفلاتر
        HBox secondRow = new HBox(15);
        secondRow.setAlignment(Pos.CENTER_LEFT);
        
        // فلتر الحالة
        Label statusLabel = new Label("الحالة:");
        statusFilter = new ComboBox<>();
        statusFilter.getItems().add(null); // جميع الحالات
        statusFilter.getItems().addAll(ManufacturingOrder.OrderStatus.values());
        statusFilter.setPromptText("جميع الحالات");
        statusFilter.valueProperty().addListener((obs, oldVal, newVal) -> applyFilters());
        
        // أزرار الفلاتر
        Button applyBtn = new Button("تطبيق");
        FontAwesomeIconView applyIcon = new FontAwesomeIconView(FontAwesomeIcon.SEARCH);
        applyIcon.setSize("12px");
        applyBtn.setGraphic(applyIcon);
        applyBtn.getStyleClass().add("filter-button");
        applyBtn.setOnAction(e -> applyFilters());
        
        Button resetBtn = new Button("إعادة تعيين");
        FontAwesomeIconView resetIcon = new FontAwesomeIconView(FontAwesomeIcon.REFRESH);
        resetIcon.setSize("12px");
        resetBtn.setGraphic(resetIcon);
        resetBtn.getStyleClass().add("reset-button");
        resetBtn.setOnAction(e -> resetFilters());
        
        secondRow.getChildren().addAll(
            statusLabel, statusFilter,
            new Separator(),
            applyBtn, resetBtn
        );
        
        filtersBox.getChildren().addAll(filtersTitle, firstRow, secondRow);
        return filtersBox;
    }
    
    /**
     * إنشاء جدول الأوامر
     */
    private TableView<ManufacturingOrder> createOrdersTable() {
        TableView<ManufacturingOrder> table = new TableView<>();
        table.getStyleClass().add("orders-table");
        
        // الأعمدة
        TableColumn<ManufacturingOrder, String> numberCol = new TableColumn<>("رقم الأمر");
        numberCol.setCellValueFactory(new PropertyValueFactory<>("orderNumber"));
        numberCol.setPrefWidth(100);
        
        TableColumn<ManufacturingOrder, String> customerCol = new TableColumn<>("العميل");
        customerCol.setCellValueFactory(new PropertyValueFactory<>("customerName"));
        customerCol.setPrefWidth(150);
        
        TableColumn<ManufacturingOrder, String> invoiceCol = new TableColumn<>("رقم الفاتورة");
        invoiceCol.setCellValueFactory(new PropertyValueFactory<>("invoiceNumber"));
        invoiceCol.setPrefWidth(120);
        
        TableColumn<ManufacturingOrder, LocalDate> dateCol = new TableColumn<>("التاريخ");
        dateCol.setCellValueFactory(new PropertyValueFactory<>("orderDate"));
        dateCol.setPrefWidth(100);
        
        TableColumn<ManufacturingOrder, String> statusCol = new TableColumn<>("الحالة");
        statusCol.setCellValueFactory(cellData -> 
            cellData.getValue().statusProperty().asString());
        statusCol.setPrefWidth(100);
        
        TableColumn<ManufacturingOrder, String> servicesCol = new TableColumn<>("الخدمات");
        servicesCol.setCellValueFactory(cellData -> {
            ManufacturingOrder order = cellData.getValue();
            StringBuilder services = new StringBuilder();
            if (order.isGlassCuttingSelected()) services.append("قص زجاج، ");
            if (order.isThermalGlassSelected()) services.append("حراري، ");
            if (order.isFilmSelected()) services.append("فيلم، ");
            if (order.isBulletProofFilmSelected()) services.append("فيلم ضد الرصاص، ");
            if (order.isDoubleGlassSelected()) services.append("دبل جلاس، ");
            if (order.isPolishSelected()) services.append("Polish، ");
            if (order.isHoleSelected()) services.append("Hole، ");
            if (order.isCncSelected()) services.append("CNC، ");
            if (order.isDrawSelected()) services.append("Draw، ");
            if (order.isOtherServicesSelected()) services.append("أخرى، ");
            
            String result = services.toString();
            if (result.endsWith("، ")) {
                result = result.substring(0, result.length() - 2);
            }
            return new javafx.beans.property.SimpleStringProperty(result);
        });
        servicesCol.setPrefWidth(200);
        
        TableColumn<ManufacturingOrder, String> notesCol = new TableColumn<>("ملاحظات");
        notesCol.setCellValueFactory(new PropertyValueFactory<>("notes"));
        notesCol.setPrefWidth(150);
        
        // إضافة الأعمدة
        table.getColumns().addAll(numberCol, customerCol, invoiceCol, dateCol, statusCol, servicesCol, notesCol);
        
        // تخصيص عرض الصفوف حسب الحالة
        table.setRowFactory(tv -> {
            TableRow<ManufacturingOrder> row = new TableRow<>();
            row.itemProperty().addListener((obs, oldOrder, newOrder) -> {
                if (newOrder != null) {
                    String style = "";
                    switch (newOrder.getStatus()) {
                        case DRAFT:
                            style = "-fx-background-color: #fff3cd;";
                            break;
                        case CONFIRMED:
                            style = "-fx-background-color: #d1ecf1;";
                            break;
                        case IN_PROGRESS:
                            style = "-fx-background-color: #f8d7da;";
                            break;
                        case COMPLETED:
                            style = "-fx-background-color: #d4edda;";
                            break;
                        case CANCELLED:
                            style = "-fx-background-color: #f5c6cb;";
                            break;
                    }
                    row.setStyle(style);
                }
            });
            return row;
        });
        
        return table;
    }
    
    /**
     * إنشاء صندوق الأزرار
     */
    private HBox createButtonBox(Stage dialog) {
        HBox buttonBox = new HBox(15);
        buttonBox.setAlignment(Pos.CENTER_LEFT);
        buttonBox.setPadding(new Insets(10, 0, 0, 0));
        
        // أزرار الإجراءات الأساسية
        Button addBtn = new Button("أمر جديد");
        FontAwesomeIconView addIcon = new FontAwesomeIconView(FontAwesomeIcon.PLUS);
        addIcon.setSize("12px");
        addBtn.setGraphic(addIcon);
        addBtn.getStyleClass().add("add-button");
        addBtn.setOnAction(e -> showNewManufacturingOrderDialog());
        
        Button editBtn = new Button("تعديل");
        FontAwesomeIconView editIcon = new FontAwesomeIconView(FontAwesomeIcon.EDIT);
        editIcon.setSize("12px");
        editBtn.setGraphic(editIcon);
        editBtn.getStyleClass().add("edit-button");
        editBtn.setOnAction(e -> editSelectedOrder());
        
        Button viewBtn = new Button("عرض");
        FontAwesomeIconView viewIcon = new FontAwesomeIconView(FontAwesomeIcon.EYE);
        viewIcon.setSize("12px");
        viewBtn.setGraphic(viewIcon);
        viewBtn.getStyleClass().add("view-button");
        viewBtn.setOnAction(e -> viewSelectedOrder());
        
        Button deleteBtn = new Button("حذف");
        FontAwesomeIconView deleteIcon = new FontAwesomeIconView(FontAwesomeIcon.TRASH);
        deleteIcon.setSize("12px");
        deleteBtn.setGraphic(deleteIcon);
        deleteBtn.getStyleClass().add("delete-button");
        deleteBtn.setOnAction(e -> deleteSelectedOrder());
        
        // أزرار الحالة
        Button confirmBtn = new Button("تأكيد");
        FontAwesomeIconView confirmIcon = new FontAwesomeIconView(FontAwesomeIcon.CHECK);
        confirmIcon.setSize("12px");
        confirmBtn.setGraphic(confirmIcon);
        confirmBtn.getStyleClass().add("approve-button");
        confirmBtn.setOnAction(e -> confirmSelectedOrder());
        
        Button startBtn = new Button("بدء التنفيذ");
        FontAwesomeIconView startIcon = new FontAwesomeIconView(FontAwesomeIcon.PLAY);
        startIcon.setSize("12px");
        startBtn.setGraphic(startIcon);
        startBtn.getStyleClass().add("post-button");
        startBtn.setOnAction(e -> startSelectedOrder());
        
        Button completeBtn = new Button("إكمال");
        FontAwesomeIconView completeIcon = new FontAwesomeIconView(FontAwesomeIcon.CHECK_CIRCLE);
        completeIcon.setSize("12px");
        completeBtn.setGraphic(completeIcon);
        completeBtn.getStyleClass().add("approve-button");
        completeBtn.setOnAction(e -> completeSelectedOrder());
        
        Button cancelBtn = new Button("إلغاء");
        FontAwesomeIconView cancelIcon = new FontAwesomeIconView(FontAwesomeIcon.TIMES);
        cancelIcon.setSize("12px");
        cancelBtn.setGraphic(cancelIcon);
        cancelBtn.getStyleClass().add("unpost-button");
        cancelBtn.setOnAction(e -> cancelSelectedOrder());
        
        // أزرار أخرى
        Button printBtn = new Button("طباعة");
        FontAwesomeIconView printIcon = new FontAwesomeIconView(FontAwesomeIcon.PRINT);
        printIcon.setSize("12px");
        printBtn.setGraphic(printIcon);
        printBtn.getStyleClass().add("print-button");
        printBtn.setOnAction(e -> printSelectedOrder());
        
        Button refreshBtn = new Button("تحديث");
        FontAwesomeIconView refreshIcon = new FontAwesomeIconView(FontAwesomeIcon.REFRESH);
        refreshIcon.setSize("12px");
        refreshBtn.setGraphic(refreshIcon);
        refreshBtn.getStyleClass().add("refresh-button");
        refreshBtn.setOnAction(e -> refreshOrdersTable());
        
        Button closeBtn = new Button("إغلاق");
        closeBtn.setOnAction(e -> dialog.close());
        
        buttonBox.getChildren().addAll(
            addBtn, editBtn, viewBtn, deleteBtn,
            new Separator(),
            confirmBtn, startBtn, completeBtn, cancelBtn,
            new Separator(),
            printBtn, refreshBtn, closeBtn
        );
        
        return buttonBox;
    }
    
    /**
     * تحديث جدول الأوامر
     */
    private void refreshOrdersTable() {
        ObservableList<ManufacturingOrder> orders = FXCollections.observableArrayList(
            manufacturingService.getAllManufacturingOrders()
        );
        ordersTable.setItems(orders);
    }
    
    /**
     * تطبيق الفلاتر
     */
    private void applyFilters() {
        ObservableList<ManufacturingOrder> filteredOrders = FXCollections.observableArrayList();
        
        for (ManufacturingOrder order : manufacturingService.getAllManufacturingOrders()) {
            boolean matches = true;
            
            // فلتر البحث النصي
            String searchText = searchField.getText();
            if (searchText != null && !searchText.trim().isEmpty()) {
                String searchLower = searchText.toLowerCase();
                boolean textMatch = order.getOrderNumber().toLowerCase().contains(searchLower) ||
                                  order.getCustomerName().toLowerCase().contains(searchLower) ||
                                  (order.getInvoiceNumber() != null && order.getInvoiceNumber().toLowerCase().contains(searchLower)) ||
                                  (order.getNotes() != null && order.getNotes().toLowerCase().contains(searchLower));
                if (!textMatch) {
                    matches = false;
                }
            }
            
            // فلتر التاريخ
            LocalDate fromDate = fromDatePicker.getValue();
            LocalDate toDate = toDatePicker.getValue();
            if (fromDate != null && order.getOrderDate().isBefore(fromDate)) {
                matches = false;
            }
            if (toDate != null && order.getOrderDate().isAfter(toDate)) {
                matches = false;
            }
            
            // فلتر الحالة
            ManufacturingOrder.OrderStatus selectedStatus = statusFilter.getValue();
            if (selectedStatus != null && order.getStatus() != selectedStatus) {
                matches = false;
            }
            
            if (matches) {
                filteredOrders.add(order);
            }
        }
        
        ordersTable.setItems(filteredOrders);
    }
    
    /**
     * إعادة تعيين الفلاتر
     */
    private void resetFilters() {
        searchField.clear();
        fromDatePicker.setValue(LocalDate.now().withDayOfMonth(1));
        toDatePicker.setValue(LocalDate.now());
        statusFilter.setValue(null);
        refreshOrdersTable();
    }
    
    // وظائف الإجراءات
    private void editSelectedOrder() {
        ManufacturingOrder selectedOrder = ordersTable.getSelectionModel().getSelectedItem();
        if (selectedOrder == null) {
            showWarningAlert("يرجى اختيار أمر للتعديل.");
            return;
        }
        
        if (selectedOrder.getStatus() == ManufacturingOrder.OrderStatus.COMPLETED ||
            selectedOrder.getStatus() == ManufacturingOrder.OrderStatus.CANCELLED) {
            showWarningAlert("لا يمكن تعديل أمر مكتمل أو ملغي.");
            return;
        }
        
        ManufacturingOrderFormController formController = new ManufacturingOrderFormController(manufacturingService);
        Optional<ManufacturingOrder> result = formController.showManufacturingOrderForm(selectedOrder);
        
        if (result.isPresent()) {
            refreshOrdersTable();
            showSuccessAlert("تم تحديث أمر التصنيع بنجاح!");
        }
    }
    
    private void viewSelectedOrder() {
        ManufacturingOrder selectedOrder = ordersTable.getSelectionModel().getSelectedItem();
        if (selectedOrder == null) {
            showWarningAlert("يرجى اختيار أمر للعرض.");
            return;
        }
        
        showInfoAlert("عرض تفاصيل أمر التصنيع قيد التطوير.");
    }
    
    private void deleteSelectedOrder() {
        ManufacturingOrder selectedOrder = ordersTable.getSelectionModel().getSelectedItem();
        if (selectedOrder == null) {
            showWarningAlert("يرجى اختيار أمر للحذف.");
            return;
        }
        
        if (selectedOrder.getStatus() == ManufacturingOrder.OrderStatus.IN_PROGRESS ||
            selectedOrder.getStatus() == ManufacturingOrder.OrderStatus.COMPLETED) {
            showErrorAlert("لا يمكن حذف أمر قيد التنفيذ أو مكتمل.");
            return;
        }
        
        Alert confirmAlert = new Alert(Alert.AlertType.CONFIRMATION);
        confirmAlert.setTitle("تأكيد الحذف");
        confirmAlert.setHeaderText("حذف أمر التصنيع");
        confirmAlert.setContentText("هل أنت متأكد من حذف أمر التصنيع: " + selectedOrder.getOrderNumber() + "؟");
        
        Optional<ButtonType> result = confirmAlert.showAndWait();
        if (result.isPresent() && result.get() == ButtonType.OK) {
            if (manufacturingService.deleteManufacturingOrder(selectedOrder.getOrderId())) {
                refreshOrdersTable();
                showSuccessAlert("تم حذف أمر التصنيع بنجاح!");
            } else {
                showErrorAlert("فشل في حذف أمر التصنيع.");
            }
        }
    }
    
    private void confirmSelectedOrder() {
        ManufacturingOrder selectedOrder = ordersTable.getSelectionModel().getSelectedItem();
        if (selectedOrder == null) {
            showWarningAlert("يرجى اختيار أمر للتأكيد.");
            return;
        }
        
        if (manufacturingService.confirmManufacturingOrder(selectedOrder.getOrderId())) {
            refreshOrdersTable();
            showSuccessAlert("تم تأكيد أمر التصنيع بنجاح!");
        } else {
            showErrorAlert("فشل في تأكيد أمر التصنيع.");
        }
    }
    
    private void startSelectedOrder() {
        ManufacturingOrder selectedOrder = ordersTable.getSelectionModel().getSelectedItem();
        if (selectedOrder == null) {
            showWarningAlert("يرجى اختيار أمر لبدء التنفيذ.");
            return;
        }
        
        if (manufacturingService.startManufacturingOrder(selectedOrder.getOrderId())) {
            refreshOrdersTable();
            showSuccessAlert("تم بدء تنفيذ أمر التصنيع بنجاح!");
        } else {
            showErrorAlert("فشل في بدء تنفيذ أمر التصنيع.");
        }
    }
    
    private void completeSelectedOrder() {
        ManufacturingOrder selectedOrder = ordersTable.getSelectionModel().getSelectedItem();
        if (selectedOrder == null) {
            showWarningAlert("يرجى اختيار أمر للإكمال.");
            return;
        }
        
        if (manufacturingService.completeManufacturingOrder(selectedOrder.getOrderId())) {
            refreshOrdersTable();
            showSuccessAlert("تم إكمال أمر التصنيع بنجاح!");
        } else {
            showErrorAlert("فشل في إكمال أمر التصنيع.");
        }
    }
    
    private void cancelSelectedOrder() {
        ManufacturingOrder selectedOrder = ordersTable.getSelectionModel().getSelectedItem();
        if (selectedOrder == null) {
            showWarningAlert("يرجى اختيار أمر للإلغاء.");
            return;
        }
        
        Alert confirmAlert = new Alert(Alert.AlertType.CONFIRMATION);
        confirmAlert.setTitle("تأكيد الإلغاء");
        confirmAlert.setHeaderText("إلغاء أمر التصنيع");
        confirmAlert.setContentText("هل أنت متأكد من إلغاء أمر التصنيع: " + selectedOrder.getOrderNumber() + "؟");
        
        Optional<ButtonType> result = confirmAlert.showAndWait();
        if (result.isPresent() && result.get() == ButtonType.OK) {
            if (manufacturingService.cancelManufacturingOrder(selectedOrder.getOrderId())) {
                refreshOrdersTable();
                showSuccessAlert("تم إلغاء أمر التصنيع بنجاح!");
            } else {
                showErrorAlert("فشل في إلغاء أمر التصنيع.");
            }
        }
    }
    
    private void printSelectedOrder() {
        ManufacturingOrder selectedOrder = ordersTable.getSelectionModel().getSelectedItem();
        if (selectedOrder == null) {
            showWarningAlert("يرجى اختيار أمر للطباعة.");
            return;
        }
        
        showInfoAlert("ميزة الطباعة ستكون متاحة قريباً.");
    }
    
    // رسائل التنبيه
    private void showSuccessAlert(String message) {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle("نجح");
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }
    
    private void showErrorAlert(String message) {
        Alert alert = new Alert(Alert.AlertType.ERROR);
        alert.setTitle("خطأ");
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }
    
    private void showWarningAlert(String message) {
        Alert alert = new Alert(Alert.AlertType.WARNING);
        alert.setTitle("تحذير");
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }
    
    private void showInfoAlert(String message) {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle("معلومات");
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }
}
