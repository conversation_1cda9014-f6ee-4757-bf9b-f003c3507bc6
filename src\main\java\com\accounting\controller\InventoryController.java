package com.accounting.controller;

import com.accounting.model.*;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.collections.transformation.FilteredList;
import javafx.geometry.Insets;
import javafx.geometry.Pos;
import javafx.scene.Scene;
import javafx.scene.control.*;
import javafx.scene.control.cell.PropertyValueFactory;
import javafx.scene.layout.*;
import javafx.stage.Modality;
import javafx.stage.Stage;
import de.jensd.fx.glyphs.fontawesome.FontAwesomeIcon;
import de.jensd.fx.glyphs.fontawesome.FontAwesomeIconView;

import java.text.DecimalFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * متحكم موديول المخازن
 * Inventory Module Controller
 */
public class InventoryController {
    
    private ObservableList<Warehouse> warehouses = FXCollections.observableArrayList();
    private ObservableList<Item> items = FXCollections.observableArrayList();
    private ObservableList<StockMovement> stockMovements = FXCollections.observableArrayList();
    
    private FilteredList<Item> filteredItems;
    private FilteredList<StockMovement> filteredMovements;
    private DecimalFormat decimalFormat = new DecimalFormat("#,##0.00");
    private DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("dd/MM/yyyy");
    private DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm");
    
    // مراجع للواجهة
    private ComboBox<Warehouse> warehouseFilter;
    private ComboBox<String> categoryFilter;
    private TextField searchField;
    private TableView<Item> itemsTable;
    private TableView<StockMovement> movementsTable;
    private TableView<Warehouse> warehousesTable;
    
    public InventoryController() {
        initializeData();
        setupFilters();
    }
    
    /**
     * تهيئة البيانات الافتراضية
     */
    private void initializeData() {
        // إضافة مخازن افتراضية
        warehouses.addAll(
            new Warehouse("WH001", "مخزن الزجاج", "الطابق الأول - قسم أ", "مخزن خاص بجميع أنواع الزجاج"),
            new Warehouse("WH002", "مخزن الألومنيوم", "الطابق الأول - قسم ب", "مخزن خاص بالألومنيوم والمعادن"),
            new Warehouse("WH003", "مخزن الأكسسوارات", "الطابق الثاني", "مخزن المقابض والأكسسوارات"),
            new Warehouse("WH004", "مخزن المواد الخام", "المستودع الخارجي", "مخزن المواد الخام والكيماويات"),
            new Warehouse("WH005", "مخزن المنتجات النهائية", "الطابق الثالث", "مخزن المنتجات الجاهزة للتسليم")
        );
        
        // إضافة أصناف افتراضية
        addSampleItems();
        
        // إضافة حركات افتراضية
        addSampleMovements();
    }
    
    /**
     * إضافة أصناف تجريبية
     */
    private void addSampleItems() {
        // زجاج شفاف (له أبعاد)
        Item glass1 = new Item("GL001", "زجاج شفاف 6مم", "زجاج", Unit.SQUARE_METER, "WH001");
        glass1.setWarehouseName("مخزن الزجاج");
        glass1.setHasDimensions(true);
        glass1.setLength(2000.0);
        glass1.setWidth(1000.0);
        glass1.setPieces(75);
        glass1.setThickness(6.0);
        glass1.setColor("شفاف");
        glass1.setType("عادي");
        glass1.setCurrentQuantity(glass1.calculateArea());
        glass1.setUnitPrice(45.0);
        glass1.setSupplier("شركة الزجاج المصري");
        glass1.setNotes("زجاج عالي الجودة - له أبعاد");
        
        // ألومنيوم (بدون أبعاد)
        Item aluminum1 = new Item("AL001", "ألومنيوم أبيض 50×50", "ألومنيوم", Unit.LINEAR_METER, "WH002");
        aluminum1.setWarehouseName("مخزن الألومنيوم");
        aluminum1.setHasDimensions(false);
        aluminum1.setCurrentQuantity(500.0);
        aluminum1.setUnitPrice(25.0);
        aluminum1.setSupplier("مصنع الألومنيوم الحديث");
        aluminum1.setColor("أبيض");
        aluminum1.setType("مربع");
        aluminum1.setNotes("ألومنيوم عادي - بدون أبعاد");
        
        // مقابض (بدون أبعاد)
        Item handle1 = new Item("HD001", "مقبض ألومنيوم فضي", "أكسسوارات", Unit.PIECE, "WH003");
        handle1.setWarehouseName("مخزن الأكسسوارات");
        handle1.setHasDimensions(false);
        handle1.setCurrentQuantity(200.0);
        handle1.setUnitPrice(15.0);
        handle1.setSupplier("شركة الأكسسوارات المتقدمة");
        handle1.setColor("فضي");
        handle1.setNotes("مقابض عادية - بدون أبعاد");
        
        // مادة لاصقة (بدون أبعاد)
        Item adhesive1 = new Item("CH001", "سيليكون شفاف", "مواد كيميائية", Unit.LITER, "WH004");
        adhesive1.setWarehouseName("مخزن المواد الخام");
        adhesive1.setHasDimensions(false);
        adhesive1.setCurrentQuantity(50.0);
        adhesive1.setUnitPrice(35.0);
        adhesive1.setSupplier("شركة الكيماويات الصناعية");
        adhesive1.setColor("شفاف");
        adhesive1.setNotes("مواد كيميائية - بدون أبعاد");

        // إضافة صنف زجاج آخر له أبعاد
        Item glass2 = new Item("GL002", "زجاج ملون أزرق 8مم", "زجاج", Unit.SQUARE_METER, "WH001");
        glass2.setWarehouseName("مخزن الزجاج");
        glass2.setHasDimensions(true);
        glass2.setLength(1500.0);
        glass2.setWidth(800.0);
        glass2.setPieces(20);
        glass2.setThickness(8.0);
        glass2.setColor("أزرق");
        glass2.setType("ملون");
        glass2.setCurrentQuantity(glass2.calculateArea());
        glass2.setUnitPrice(65.0);
        glass2.setSupplier("شركة الزجاج الملون");
        glass2.setNotes("زجاج ملون عالي الجودة - له أبعاد");

        items.addAll(glass1, aluminum1, handle1, adhesive1, glass2);
    }
    
    /**
     * إضافة حركات تجريبية
     */
    private void addSampleMovements() {
        // حركة استلام زجاج
        StockMovement movement1 = new StockMovement("MOV001", "GL001", "زجاج شفاف 6مم", "WH001",
                StockMovement.MovementType.IN, 100.0, 45.0);
        movement1.setWarehouseName("مخزن الزجاج");
        movement1.setBalanceAfter(100.0);
        movement1.setUser("أحمد محمد");
        movement1.setReference("فاتورة شراء رقم 2024001");
        movement1.setNotes("استلام دفعة جديدة من الزجاج");
        
        // حركة صرف ألومنيوم
        StockMovement movement2 = new StockMovement("MOV002", "AL001", "ألومنيوم أبيض 50×50", "WH002",
                StockMovement.MovementType.OUT, 50.0, 25.0);
        movement2.setWarehouseName("مخزن الألومنيوم");
        movement2.setBalanceAfter(450.0);
        movement2.setUser("فاطمة علي");
        movement2.setReference("أمر إنتاج رقم ********");
        movement2.setNotes("صرف للإنتاج");
        
        stockMovements.addAll(movement1, movement2);
    }
    
    /**
     * إعداد المرشحات
     */
    private void setupFilters() {
        filteredItems = new FilteredList<>(items, p -> true);
        filteredMovements = new FilteredList<>(stockMovements, p -> true);
    }
    
    /**
     * إنشاء واجهة موديول المخازن البسيطة
     */
    public VBox createInventoryModule() {
        VBox mainContainer = new VBox(15);
        mainContainer.setPadding(new Insets(15));
        
        // العنوان الرئيسي
        Label titleLabel = new Label("📦 موديول إدارة المخازن");
        titleLabel.setStyle("-fx-font-size: 18px; -fx-font-weight: bold; -fx-text-fill: #2c3e50;");
        
        // شريط الأدوات
        HBox toolbar = createToolbar();
        
        // منطقة الفلترة والبحث
        VBox filterSection = createFilterSection();
        
        // منطقة المحتوى الرئيسية
        TabPane tabPane = createMainTabs();
        VBox.setVgrow(tabPane, Priority.ALWAYS);
        
        mainContainer.getChildren().addAll(titleLabel, toolbar, filterSection, tabPane);

        // تهيئة مستمعات الفلاتر بعد إنشاء العناصر
        setupFilterListeners();

        return mainContainer;
    }
    
    /**
     * إنشاء شريط الأدوات
     */
    private HBox createToolbar() {
        HBox toolbar = new HBox(15);
        toolbar.setAlignment(Pos.CENTER_LEFT);
        toolbar.setPadding(new Insets(10));
        toolbar.setStyle("-fx-background-color: white; -fx-border-color: #e9ecef; -fx-border-width: 0 0 1 0;");
        
        // زر إضافة صنف
        Button addItemBtn = createToolbarButton("إضافة صنف", FontAwesomeIcon.PLUS_CIRCLE, this::showAddItemDialog);
        
        // زر استلام
        Button receiveBtn = createToolbarButton("استلام", FontAwesomeIcon.ARROW_DOWN, this::showReceiveDialog);
        
        // زر صرف
        Button issueBtn = createToolbarButton("صرف", FontAwesomeIcon.ARROW_UP, this::showIssueDialog);
        
        // فاصل
        Separator separator = new Separator();
        separator.setOrientation(javafx.geometry.Orientation.VERTICAL);
        
        // زر التقارير
        Button reportsBtn = createToolbarButton("التقارير", FontAwesomeIcon.BAR_CHART, this::showReports);
        
        toolbar.getChildren().addAll(addItemBtn, receiveBtn, issueBtn, separator, reportsBtn);
        return toolbar;
    }
    
    /**
     * إنشاء زر شريط الأدوات
     */
    private Button createToolbarButton(String text, FontAwesomeIcon icon, Runnable action) {
        Button button = new Button(text);
        
        FontAwesomeIconView iconView = new FontAwesomeIconView(icon);
        iconView.setSize("12px");
        iconView.setStyle("-fx-fill: white;");
        button.setGraphic(iconView);
        
        button.setStyle("-fx-background-color: #007bff; -fx-text-fill: white; -fx-font-size: 12px; " +
                       "-fx-padding: 8 15 8 15; -fx-background-radius: 4; -fx-cursor: hand;");
        
        button.setOnAction(e -> action.run());
        return button;
    }
    
    /**
     * إنشاء منطقة الفلترة
     */
    private VBox createFilterSection() {
        VBox filterSection = new VBox(10);
        filterSection.setPadding(new Insets(10));
        filterSection.setStyle("-fx-background-color: #f8f9fa; -fx-border-color: #e9ecef; " +
                              "-fx-border-width: 1; -fx-border-radius: 5; -fx-background-radius: 5;");
        
        Label filterTitle = new Label("🔍 البحث والفلترة");
        filterTitle.setStyle("-fx-font-size: 14px; -fx-font-weight: bold; -fx-text-fill: #495057;");
        
        HBox filterBox = new HBox(15);
        filterBox.setAlignment(Pos.CENTER_LEFT);
        
        // البحث النصي
        Label searchLabel = new Label("البحث:");
        searchField = new TextField();
        searchField.setPromptText("ابحث في الأصناف...");
        searchField.setPrefWidth(200);
        
        // فلتر المخزن
        Label warehouseLabel = new Label("المخزن:");
        warehouseFilter = new ComboBox<>();
        warehouseFilter.getItems().add(null); // جميع المخازن
        warehouseFilter.getItems().addAll(warehouses);
        warehouseFilter.setPrefWidth(150);
        
        // فلتر الفئة
        Label categoryLabel = new Label("الفئة:");
        categoryFilter = new ComboBox<>();
        categoryFilter.getItems().add(null); // جميع الفئات
        categoryFilter.getItems().addAll("زجاج", "ألومنيوم", "أكسسوارات", "مواد كيميائية");
        categoryFilter.setPrefWidth(120);
        
        filterBox.getChildren().addAll(searchLabel, searchField, warehouseLabel, warehouseFilter, 
                                      categoryLabel, categoryFilter);
        
        filterSection.getChildren().addAll(filterTitle, filterBox);
        return filterSection;
    }
    
    /**
     * إنشاء التبويبات الرئيسية
     */
    private TabPane createMainTabs() {
        TabPane tabPane = new TabPane();
        
        // تبويب الأصناف
        Tab itemsTab = new Tab("📦 الأصناف");
        itemsTab.setContent(createItemsTab());
        itemsTab.setClosable(false);
        
        // تبويب حركة المخزون
        Tab movementsTab = new Tab("📊 حركة المخزون");
        movementsTab.setContent(createMovementsTab());
        movementsTab.setClosable(false);
        
        // تبويب المخازن
        Tab warehousesTab = new Tab("🏢 المخازن");
        warehousesTab.setContent(createWarehousesTab());
        warehousesTab.setClosable(false);
        
        tabPane.getTabs().addAll(itemsTab, movementsTab, warehousesTab);
        return tabPane;
    }
    
    /**
     * إنشاء تبويب الأصناف
     */
    private VBox createItemsTab() {
        VBox itemsContent = new VBox(10);
        itemsContent.setPadding(new Insets(10));
        
        itemsTable = createItemsTable();
        VBox.setVgrow(itemsTable, Priority.ALWAYS);
        
        itemsContent.getChildren().add(itemsTable);
        return itemsContent;
    }
    
    /**
     * إنشاء جدول الأصناف
     */
    private TableView<Item> createItemsTable() {
        TableView<Item> table = new TableView<>();
        table.setItems(filteredItems);
        
        // الأعمدة
        TableColumn<Item, String> idCol = new TableColumn<>("الكود");
        idCol.setCellValueFactory(new PropertyValueFactory<>("itemId"));
        idCol.setPrefWidth(80);
        
        TableColumn<Item, String> nameCol = new TableColumn<>("اسم الصنف");
        nameCol.setCellValueFactory(new PropertyValueFactory<>("name"));
        nameCol.setPrefWidth(200);
        
        TableColumn<Item, String> categoryCol = new TableColumn<>("الفئة");
        categoryCol.setCellValueFactory(new PropertyValueFactory<>("category"));
        categoryCol.setPrefWidth(100);
        
        TableColumn<Item, String> warehouseCol = new TableColumn<>("المخزن");
        warehouseCol.setCellValueFactory(new PropertyValueFactory<>("warehouseName"));
        warehouseCol.setPrefWidth(150);
        
        TableColumn<Item, Double> quantityCol = new TableColumn<>("الكمية");
        quantityCol.setCellValueFactory(new PropertyValueFactory<>("currentQuantity"));
        quantityCol.setPrefWidth(100);
        
        TableColumn<Item, Double> priceCol = new TableColumn<>("سعر الوحدة");
        priceCol.setCellValueFactory(new PropertyValueFactory<>("unitPrice"));
        priceCol.setPrefWidth(100);
        
        table.getColumns().addAll(idCol, nameCol, categoryCol, warehouseCol, quantityCol, priceCol);
        return table;
    }
    
    /**
     * إنشاء تبويب حركة المخزون
     */
    private VBox createMovementsTab() {
        VBox movementsContent = new VBox(10);
        movementsContent.setPadding(new Insets(10));
        
        movementsTable = createMovementsTable();
        VBox.setVgrow(movementsTable, Priority.ALWAYS);
        
        movementsContent.getChildren().add(movementsTable);
        return movementsContent;
    }
    
    /**
     * إنشاء جدول حركة المخزون
     */
    private TableView<StockMovement> createMovementsTable() {
        TableView<StockMovement> table = new TableView<>();
        table.setItems(filteredMovements);
        
        // الأعمدة
        TableColumn<StockMovement, String> idCol = new TableColumn<>("رقم الحركة");
        idCol.setCellValueFactory(new PropertyValueFactory<>("movementId"));
        idCol.setPrefWidth(100);
        
        TableColumn<StockMovement, String> itemCol = new TableColumn<>("الصنف");
        itemCol.setCellValueFactory(new PropertyValueFactory<>("itemName"));
        itemCol.setPrefWidth(200);
        
        TableColumn<StockMovement, String> typeCol = new TableColumn<>("نوع الحركة");
        typeCol.setCellValueFactory(cellData -> 
            new javafx.beans.property.SimpleStringProperty(cellData.getValue().getMovementType().getArabicName()));
        typeCol.setPrefWidth(100);
        
        TableColumn<StockMovement, Double> quantityCol = new TableColumn<>("الكمية");
        quantityCol.setCellValueFactory(new PropertyValueFactory<>("quantity"));
        quantityCol.setPrefWidth(100);
        
        TableColumn<StockMovement, String> userCol = new TableColumn<>("المستخدم");
        userCol.setCellValueFactory(new PropertyValueFactory<>("user"));
        userCol.setPrefWidth(120);
        
        table.getColumns().addAll(idCol, itemCol, typeCol, quantityCol, userCol);
        return table;
    }
    
    /**
     * إنشاء تبويب المخازن
     */
    private VBox createWarehousesTab() {
        VBox warehousesContent = new VBox(10);
        warehousesContent.setPadding(new Insets(10));
        
        warehousesTable = createWarehousesTable();
        VBox.setVgrow(warehousesTable, Priority.ALWAYS);
        
        warehousesContent.getChildren().add(warehousesTable);
        return warehousesContent;
    }
    
    /**
     * إنشاء جدول المخازن
     */
    private TableView<Warehouse> createWarehousesTable() {
        TableView<Warehouse> table = new TableView<>();
        table.setItems(warehouses);
        
        // الأعمدة
        TableColumn<Warehouse, String> idCol = new TableColumn<>("كود المخزن");
        idCol.setCellValueFactory(new PropertyValueFactory<>("warehouseId"));
        idCol.setPrefWidth(100);
        
        TableColumn<Warehouse, String> nameCol = new TableColumn<>("اسم المخزن");
        nameCol.setCellValueFactory(new PropertyValueFactory<>("name"));
        nameCol.setPrefWidth(200);
        
        TableColumn<Warehouse, String> locationCol = new TableColumn<>("الموقع");
        locationCol.setCellValueFactory(new PropertyValueFactory<>("location"));
        locationCol.setPrefWidth(200);
        
        TableColumn<Warehouse, String> descCol = new TableColumn<>("الوصف");
        descCol.setCellValueFactory(new PropertyValueFactory<>("description"));
        descCol.setPrefWidth(300);
        
        table.getColumns().addAll(idCol, nameCol, locationCol, descCol);
        return table;
    }
    
    // وظائف الأزرار
    private void showAddItemDialog() {
        InventoryDialogs dialogs = new InventoryDialogs();
        dialogs.showItemDialog(null, warehouses, items);
        
        if (itemsTable != null) {
            itemsTable.refresh();
        }
    }
    
    private void showReceiveDialog() {
        showPlaceholderAlert("استلام أصناف", "نافذة استلام الأصناف");
    }
    
    private void showIssueDialog() {
        showPlaceholderAlert("صرف أصناف", "نافذة صرف الأصناف");
    }
    
    private void showReports() {
        showPlaceholderAlert("التقارير", "نافذة التقارير");
    }
    
    private void showPlaceholderAlert(String title, String message) {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle(title);
        alert.setHeaderText(null);
        alert.setContentText(message + "\n\nسيتم تطوير هذه الميزة قريباً...");
        alert.showAndWait();
    }

    // وظائف إضافية للتوافق مع النظام

    /**
     * إضافة صنف جديد
     */
    public void addItem(Item item) {
        items.add(item);
        if (itemsTable != null) {
            itemsTable.refresh();
        }
    }

    /**
     * تحديث صنف موجود
     */
    public void updateItem(Item item) {
        for (int i = 0; i < items.size(); i++) {
            if (items.get(i).getItemId().equals(item.getItemId())) {
                items.set(i, item);
                break;
            }
        }
        if (itemsTable != null) {
            itemsTable.refresh();
        }
    }

    /**
     * حذف صنف
     */
    public void deleteItem(String itemId) {
        items.removeIf(item -> item.getItemId().equals(itemId));
        if (itemsTable != null) {
            itemsTable.refresh();
        }
    }

    /**
     * إضافة حركة مخزون
     */
    public void addStockMovement(StockMovement movement) {
        stockMovements.add(movement);
        if (movementsTable != null) {
            movementsTable.refresh();
        }
    }

    /**
     * الحصول على قائمة الأصناف
     */
    public ObservableList<Item> getItems() {
        return items;
    }

    /**
     * الحصول على قائمة المخازن
     */
    public ObservableList<Warehouse> getWarehouses() {
        return warehouses;
    }

    /**
     * الحصول على قائمة حركات المخزون
     */
    public ObservableList<StockMovement> getStockMovements() {
        return stockMovements;
    }

    /**
     * البحث عن صنف بالكود
     */
    public Item findItemById(String itemId) {
        return items.stream()
                .filter(item -> item.getItemId().equals(itemId))
                .findFirst()
                .orElse(null);
    }

    /**
     * البحث عن مخزن بالكود
     */
    public Warehouse findWarehouseById(String warehouseId) {
        return warehouses.stream()
                .filter(warehouse -> warehouse.getWarehouseId().equals(warehouseId))
                .findFirst()
                .orElse(null);
    }

    /**
     * تحديث الكميات بعد حركة المخزون
     */
    public void updateItemQuantity(String itemId, double quantity, StockMovement.MovementType type) {
        Item item = findItemById(itemId);
        if (item != null) {
            if (type == StockMovement.MovementType.IN) {
                item.setCurrentQuantity(item.getCurrentQuantity() + quantity);
            } else if (type == StockMovement.MovementType.OUT) {
                item.setCurrentQuantity(item.getCurrentQuantity() - quantity);
            }

            if (itemsTable != null) {
                itemsTable.refresh();
            }
        }
    }

    /**
     * تطبيق الفلاتر على الأصناف
     */
    private void applyFilters() {
        filteredItems.setPredicate(item -> {
            // فلتر البحث النصي
            if (searchField != null && !searchField.getText().isEmpty()) {
                String searchText = searchField.getText().toLowerCase();
                if (!item.getName().toLowerCase().contains(searchText) &&
                    !item.getItemId().toLowerCase().contains(searchText) &&
                    !item.getCategory().toLowerCase().contains(searchText)) {
                    return false;
                }
            }

            // فلتر المخزن
            if (warehouseFilter != null && warehouseFilter.getValue() != null) {
                if (!item.getWarehouseId().equals(warehouseFilter.getValue().getWarehouseId())) {
                    return false;
                }
            }

            // فلتر الفئة
            if (categoryFilter != null && categoryFilter.getValue() != null) {
                if (!item.getCategory().equals(categoryFilter.getValue())) {
                    return false;
                }
            }

            return true;
        });
    }

    /**
     * تهيئة مستمعات الفلاتر
     */
    private void setupFilterListeners() {
        if (searchField != null) {
            searchField.textProperty().addListener((observable, oldValue, newValue) -> applyFilters());
        }

        if (warehouseFilter != null) {
            warehouseFilter.valueProperty().addListener((observable, oldValue, newValue) -> applyFilters());
        }

        if (categoryFilter != null) {
            categoryFilter.valueProperty().addListener((observable, oldValue, newValue) -> applyFilters());
        }
    }
}
