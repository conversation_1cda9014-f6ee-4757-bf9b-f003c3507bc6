package com.accounting.controller;

import com.accounting.model.*;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.collections.transformation.FilteredList;
import javafx.geometry.Insets;
import javafx.geometry.Pos;
import javafx.scene.Scene;
import javafx.scene.control.*;
import javafx.scene.control.cell.PropertyValueFactory;
import javafx.scene.layout.*;
import javafx.stage.Modality;
import javafx.stage.Stage;
import de.jensd.fx.glyphs.fontawesome.FontAwesomeIcon;
import de.jensd.fx.glyphs.fontawesome.FontAwesomeIconView;

import java.text.DecimalFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;

/**
 * متحكم موديول المخازن
 * Inventory Module Controller
 */
public class InventoryController {
    
    private ObservableList<Warehouse> warehouses = FXCollections.observableArrayList();
    private ObservableList<Item> items = FXCollections.observableArrayList();
    private ObservableList<StockMovement> stockMovements = FXCollections.observableArrayList();
    
    private FilteredList<Item> filteredItems;
    private FilteredList<StockMovement> filteredMovements;
    private DecimalFormat decimalFormat = new DecimalFormat("#,##0.00");
    private DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("dd/MM/yyyy");
    private DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm");
    
    // مراجع للواجهة
    private ComboBox<Warehouse> warehouseFilter;
    private ComboBox<String> categoryFilter;
    private TextField searchField;
    private TableView<Item> itemsTable;
    
    public InventoryController() {
        initializeData();
        setupFilters();
    }

    /**
     * تهيئة نظام الإشعارات
     */
    public void initializeNotificationSystem(Stage primaryStage) {
        this.notificationSystem = new NotificationSystem(primaryStage);

        // فحص أولي للأصناف المنخفضة
        notificationSystem.checkLowStockItems(items);

        // إشعار ترحيبي
        notificationSystem.showInfoNotification("مرحباً بك!", "تم تحميل موديول المخازن بنجاح");
    }
    
    /**
     * تهيئة البيانات الافتراضية
     */
    private void initializeData() {
        // إضافة مخازن افتراضية
        warehouses.addAll(
            new Warehouse("WH001", "مخزن الزجاج", "الطابق الأول - قسم أ", "مخزن خاص بجميع أنواع الزجاج"),
            new Warehouse("WH002", "مخزن الألومنيوم", "الطابق الأول - قسم ب", "مخزن خاص بالألومنيوم والمعادن"),
            new Warehouse("WH003", "مخزن الأكسسوارات", "الطابق الثاني", "مخزن المقابض والأكسسوارات"),
            new Warehouse("WH004", "مخزن المواد الخام", "المستودع الخارجي", "مخزن المواد الخام والكيماويات"),
            new Warehouse("WH005", "مخزن المنتجات النهائية", "الطابق الثالث", "مخزن المنتجات الجاهزة للتسليم"),
            new Warehouse("WH006", "مخزن قطع الغيار", "الطابق الأول - قسم ج", "مخزن قطع الغيار والصيانة"),
            new Warehouse("WH007", "مخزن الأدوات", "ورشة العمل", "مخزن الأدوات والمعدات"),
            new Warehouse("WH008", "مخزن التالف", "المستودع الخلفي", "مخزن الأصناف التالفة والمرتجعة"),
            new Warehouse("WH009", "مخزن الحجر الصحي", "منطقة منفصلة", "مخزن مؤقت للفحص والحجر الصحي"),
            new Warehouse("WH010", "مخزن الطوارئ", "الطابق السفلي", "مخزن احتياطي للحالات الطارئة")
        );
        
        // إضافة أصناف افتراضية
        addSampleItems();
        
        // إضافة حركات افتراضية
        addSampleMovements();
    }
    
    /**
     * إضافة أصناف تجريبية
     */
    private void addSampleItems() {
        // زجاج شفاف (له أبعاد)
        Item glass1 = new Item("GL001", "زجاج شفاف 6مم", "زجاج", Unit.SQUARE_METER, "WH001");
        glass1.setWarehouseName("مخزن الزجاج");
        glass1.setHasDimensions(true);
        glass1.setLength(2000.0); // 2 متر
        glass1.setWidth(1000.0); // 1 متر
        glass1.setPieces(75); // 75 قطعة
        glass1.setThickness(6.0);
        glass1.setColor("شفاف");
        glass1.setType("عادي");
        glass1.setCurrentQuantity(glass1.calculateArea()); // 150 م² محسوبة
        glass1.setUnitPrice(45.0);
        glass1.setSupplier("شركة الزجاج المصري");
        glass1.setNotes("زجاج عالي الجودة - له أبعاد");
        
        // ألومنيوم (بدون أبعاد)
        Item aluminum1 = new Item("AL001", "ألومنيوم أبيض 50×50", "ألومنيوم", Unit.LINEAR_METER, "WH002");
        aluminum1.setWarehouseName("مخزن الألومنيوم");
        aluminum1.setHasDimensions(false); // بدون أبعاد
        aluminum1.setCurrentQuantity(500.0);
        aluminum1.setUnitPrice(25.0);
        aluminum1.setSupplier("مصنع الألومنيوم الحديث");
        aluminum1.setColor("أبيض");
        aluminum1.setType("مربع");
        aluminum1.setNotes("ألومنيوم عادي - بدون أبعاد");
        
        // مقابض (بدون أبعاد)
        Item handle1 = new Item("HD001", "مقبض ألومنيوم فضي", "أكسسوارات", Unit.PIECE, "WH003");
        handle1.setWarehouseName("مخزن الأكسسوارات");
        handle1.setHasDimensions(false); // بدون أبعاد
        handle1.setCurrentQuantity(200.0);
        handle1.setUnitPrice(15.0);
        handle1.setSupplier("شركة الأكسسوارات المتقدمة");
        handle1.setColor("فضي");
        handle1.setNotes("مقابض عادية - بدون أبعاد");
        
        // مادة لاصقة (بدون أبعاد)
        Item adhesive1 = new Item("CH001", "سيليكون شفاف", "مواد كيميائية", Unit.LITER, "WH004");
        adhesive1.setWarehouseName("مخزن المواد الخام");
        adhesive1.setHasDimensions(false); // بدون أبعاد
        adhesive1.setCurrentQuantity(50.0);
        adhesive1.setUnitPrice(35.0);
        adhesive1.setSupplier("شركة الكيماويات الصناعية");
        adhesive1.setColor("شفاف");
        adhesive1.setNotes("مواد كيميائية - بدون أبعاد");

        // إضافة صنف زجاج آخر له أبعاد
        Item glass2 = new Item("GL002", "زجاج ملون أزرق 8مم", "زجاج", Unit.SQUARE_METER, "WH001");
        glass2.setWarehouseName("مخزن الزجاج");
        glass2.setHasDimensions(true);
        glass2.setLength(1500.0); // 1.5 متر
        glass2.setWidth(800.0);   // 0.8 متر
        glass2.setPieces(20);     // 20 قطعة
        glass2.setThickness(8.0);
        glass2.setColor("أزرق");
        glass2.setType("ملون");
        glass2.setCurrentQuantity(glass2.calculateArea()); // 24 م² محسوبة
        glass2.setUnitPrice(65.0);
        glass2.setSupplier("شركة الزجاج الملون");
        glass2.setNotes("زجاج ملون عالي الجودة - له أبعاد");

        items.addAll(glass1, aluminum1, handle1, adhesive1, glass2);
    }
    
    /**
     * إضافة حركات تجريبية
     */
    private void addSampleMovements() {
        // حركة استلام زجاج
        StockMovement movement1 = new StockMovement("MOV001", "GL001", "زجاج شفاف 6مم", "WH001",
                StockMovement.MovementType.IN, 100.0, 45.0);
        movement1.setWarehouseName("مخزن الزجاج");
        movement1.setBalanceAfter(100.0);
        movement1.setUser("أحمد محمد");
        movement1.setReference("فاتورة شراء رقم 2024001");
        movement1.setNotes("استلام دفعة جديدة من الزجاج");
        
        // حركة صرف ألومنيوم
        StockMovement movement2 = new StockMovement("MOV002", "AL001", "ألومنيوم أبيض 50×50", "WH002",
                StockMovement.MovementType.OUT, 50.0, 25.0);
        movement2.setWarehouseName("مخزن الألومنيوم");
        movement2.setBalanceAfter(450.0);
        movement2.setUser("فاطمة علي");
        movement2.setReference("أمر إنتاج رقم P2024001");
        movement2.setNotes("صرف للإنتاج");
        
        // حركة استلام مقابض
        StockMovement movement3 = new StockMovement("MOV003", "HD001", "مقبض ألومنيوم فضي", "WH003",
                StockMovement.MovementType.IN, 100.0, 15.0);
        movement3.setWarehouseName("مخزن الأكسسوارات");
        movement3.setBalanceAfter(300.0);
        movement3.setUser("محمد أحمد");
        movement3.setReference("فاتورة شراء رقم 2024002");
        movement3.setNotes("استلام مقابض جديدة");
        
        // حركة صرف سيليكون
        StockMovement movement4 = new StockMovement("MOV004", "CH001", "سيليكون شفاف", "WH004",
                StockMovement.MovementType.OUT, 10.0, 35.0);
        movement4.setWarehouseName("مخزن المواد الخام");
        movement4.setBalanceAfter(40.0);
        movement4.setUser("سارة محمود");
        movement4.setReference("أمر إنتاج رقم P2024002");
        movement4.setNotes("صرف للإنتاج");
        
        // حركة استلام زجاج ملون
        StockMovement movement5 = new StockMovement("MOV005", "GL002", "زجاج ملون أزرق 8مم", "WH001",
                StockMovement.MovementType.IN, 50.0, 65.0);
        movement5.setWarehouseName("مخزن الزجاج");
        movement5.setBalanceAfter(74.0);
        movement5.setUser("علي حسن");
        movement5.setReference("فاتورة شراء رقم 2024003");
        movement5.setNotes("استلام زجاج ملون عالي الجودة");
        
        // حركة صرف ألومنيوم
        StockMovement movement6 = new StockMovement("MOV006", "AL001", "ألومنيوم أبيض 50×50", "WH002",
                StockMovement.MovementType.OUT, 25.0, 25.0);
        movement6.setWarehouseName("مخزن الألومنيوم");
        movement6.setBalanceAfter(425.0);
        movement6.setUser("نور الدين");
        movement6.setReference("أمر إنتاج رقم P2024003");
        movement6.setNotes("صرف للعميل الجديد");
        
        // حركة استلام مقابض إضافية
        StockMovement movement7 = new StockMovement("MOV007", "HD001", "مقبض ألومنيوم فضي", "WH003",
                StockMovement.MovementType.IN, 50.0, 15.0);
        movement7.setWarehouseName("مخزن الأكسسوارات");
        movement7.setBalanceAfter(350.0);
        movement7.setUser("ليلى أحمد");
        movement7.setReference("فاتورة شراء رقم 2024004");
        movement7.setNotes("تعزيز المخزون");
        
        // حركة صرف زجاج شفاف
        StockMovement movement8 = new StockMovement("MOV008", "GL001", "زجاج شفاف 6مم", "WH001",
                StockMovement.MovementType.OUT, 30.0, 45.0);
        movement8.setWarehouseName("مخزن الزجاج");
        movement8.setBalanceAfter(120.0);
        movement8.setUser("خالد محمد");
        movement8.setReference("أمر إنتاج رقم P2024004");
        movement8.setNotes("صرف لمشروع خاص");
        
        // حركة تعديل مخزون
        StockMovement movement9 = new StockMovement("MOV009", "CH001", "سيليكون شفاف", "WH004",
                StockMovement.MovementType.ADJUSTMENT, 5.0, 35.0);
        movement9.setWarehouseName("مخزن المواد الخام");
        movement9.setBalanceAfter(45.0);
        movement9.setUser("مدير المخزن");
        movement9.setReference("تعديل جرد");
        movement9.setNotes("تعديل بعد الجرد الدوري");
        
        // حركة نقل بين المخازن
        StockMovement movement10 = new StockMovement("MOV010", "HD001", "مقبض ألومنيوم فضي", "WH003",
                StockMovement.MovementType.TRANSFER, 20.0, 15.0);
        movement10.setWarehouseName("مخزن الأكسسوارات");
        movement10.setBalanceAfter(330.0);
        movement10.setUser("مشرف النقل");
        movement10.setReference("أمر نقل رقم ********");
        movement10.setNotes("نقل إلى مخزن فرعي");
        
        stockMovements.addAll(movement1, movement2, movement3, movement4, movement5, 
                             movement6, movement7, movement8, movement9, movement10);
    }
    
    /**
     * إعداد المرشحات
     */
    private void setupFilters() {
        filteredItems = new FilteredList<>(items, p -> true);
        filteredMovements = new FilteredList<>(stockMovements, p -> true);
    }

    /**
     * إنشاء واجهة موديول المخازن المحسنة والعملية
     */
    public VBox createInventoryModule() {
        VBox mainContainer = new VBox();
        mainContainer.getStyleClass().add("enhanced-inventory-module");

        // الرأس المحسن
        VBox header = createEnhancedHeader();

        // شريط التنقل الجانبي
        HBox contentArea = new HBox();
        VBox.setVgrow(contentArea, Priority.ALWAYS);

        // القائمة الجانبية
        VBox sidebar = createSidebar();
        sidebar.setPrefWidth(250);
        sidebar.setMinWidth(250);
        sidebar.setMaxWidth(250);

        // المحتوى الرئيسي
        VBox mainContent = createMainContentArea();
        HBox.setHgrow(mainContent, Priority.ALWAYS);

        contentArea.getChildren().addAll(sidebar, mainContent);

        mainContainer.getChildren().addAll(header, contentArea);
        return mainContainer;
    }

    /**
     * إنشاء الرأس المحسن مع نظام الإشعارات
     */
    private VBox createEnhancedHeader() {
        VBox header = new VBox(10);
        header.setPadding(new Insets(20));
        header.setStyle("-fx-background-color: linear-gradient(to right, #667eea, #764ba2); " +
                       "-fx-background-radius: 0 0 15 15;");

        // الصف الأول - العنوان والإشعارات
        HBox topRow = new HBox();
        topRow.setAlignment(Pos.CENTER_LEFT);

        // العنوان الرئيسي
        HBox titleBox = new HBox(15);
        titleBox.setAlignment(Pos.CENTER_LEFT);

        FontAwesomeIconView mainIcon = new FontAwesomeIconView(FontAwesomeIcon.CUBES);
        mainIcon.setSize("32px");
        mainIcon.setStyle("-fx-fill: white;");

        Label titleLabel = new Label("📦 موديول إدارة المخازن المتقدم");
        titleLabel.setStyle("-fx-font-size: 24px; -fx-font-weight: bold; -fx-text-fill: white;");

        titleBox.getChildren().addAll(mainIcon, titleLabel);

        // منطقة الإشعارات
        HBox notificationArea = createNotificationArea();

        topRow.getChildren().addAll(titleBox, new Region(), notificationArea);
        HBox.setHgrow(topRow.getChildren().get(1), Priority.ALWAYS);

        // الوصف
        Label descLabel = new Label("إدارة شاملة للمخزون مع تقارير تفاعلية وتحليلات متقدمة وإشعارات ذكية");
        descLabel.setStyle("-fx-font-size: 14px; -fx-text-fill: #f8f9fa; -fx-opacity: 0.9;");

        // شريط الإحصائيات السريعة
        HBox quickStats = createQuickStatsBar();

        header.getChildren().addAll(topRow, descLabel, quickStats);
        return header;
    }

    /**
     * إنشاء منطقة الإشعارات
     */
    private HBox createNotificationArea() {
        HBox notificationArea = new HBox(10);
        notificationArea.setAlignment(Pos.CENTER_RIGHT);

        // زر الإشعارات
        Button notificationBtn = new Button();
        FontAwesomeIconView bellIcon = new FontAwesomeIconView(FontAwesomeIcon.BELL);
        bellIcon.setSize("18px");
        bellIcon.setStyle("-fx-fill: white;");
        notificationBtn.setGraphic(bellIcon);
        notificationBtn.setStyle("-fx-background-color: rgba(255,255,255,0.2); " +
                                "-fx-border-color: rgba(255,255,255,0.3); " +
                                "-fx-border-width: 1; " +
                                "-fx-border-radius: 20; " +
                                "-fx-background-radius: 20; " +
                                "-fx-padding: 8 12 8 12; " +
                                "-fx-cursor: hand;");

        // عداد الإشعارات
        Label notificationCount = new Label("0");
        notificationCount.setStyle("-fx-background-color: #e74c3c; " +
                                 "-fx-text-fill: white; " +
                                 "-fx-font-size: 10px; " +
                                 "-fx-font-weight: bold; " +
                                 "-fx-padding: 2 6 2 6; " +
                                 "-fx-background-radius: 10; " +
                                 "-fx-min-width: 18; " +
                                 "-fx-alignment: center;");
        notificationCount.setVisible(false);

        // تأثيرات التفاعل
        notificationBtn.setOnMouseEntered(e -> {
            notificationBtn.setStyle("-fx-background-color: rgba(255,255,255,0.3); " +
                                   "-fx-border-color: rgba(255,255,255,0.5); " +
                                   "-fx-border-width: 1; " +
                                   "-fx-border-radius: 20; " +
                                   "-fx-background-radius: 20; " +
                                   "-fx-padding: 8 12 8 12; " +
                                   "-fx-cursor: hand;");
        });

        notificationBtn.setOnMouseExited(e -> {
            notificationBtn.setStyle("-fx-background-color: rgba(255,255,255,0.2); " +
                                   "-fx-border-color: rgba(255,255,255,0.3); " +
                                   "-fx-border-width: 1; " +
                                   "-fx-border-radius: 20; " +
                                   "-fx-background-radius: 20; " +
                                   "-fx-padding: 8 12 8 12; " +
                                   "-fx-cursor: hand;");
        });

        notificationBtn.setOnAction(e -> showNotificationPanel());

        // StackPane لوضع العداد فوق الزر
        StackPane notificationStack = new StackPane();
        notificationStack.getChildren().addAll(notificationBtn, notificationCount);
        StackPane.setAlignment(notificationCount, Pos.TOP_RIGHT);
        StackPane.setMargin(notificationCount, new Insets(-5, -5, 0, 0));

        // زر التحديث التلقائي
        Button autoRefreshBtn = new Button();
        FontAwesomeIconView refreshIcon = new FontAwesomeIconView(FontAwesomeIcon.REFRESH);
        refreshIcon.setSize("16px");
        refreshIcon.setStyle("-fx-fill: white;");
        autoRefreshBtn.setGraphic(refreshIcon);
        autoRefreshBtn.setStyle("-fx-background-color: rgba(255,255,255,0.2); " +
                               "-fx-border-color: rgba(255,255,255,0.3); " +
                               "-fx-border-width: 1; " +
                               "-fx-border-radius: 20; " +
                               "-fx-background-radius: 20; " +
                               "-fx-padding: 8 10 8 10; " +
                               "-fx-cursor: hand;");
        autoRefreshBtn.setOnAction(e -> performAutoRefresh());

        notificationArea.getChildren().addAll(autoRefreshBtn, notificationStack);
        return notificationArea;
    }

    /**
     * إظهار لوحة الإشعارات
     */
    private void showNotificationPanel() {
        if (notificationSystem != null) {
            showPlaceholderAlert("الإشعارات", "لوحة الإشعارات\n\nعدد الإشعارات: " + notificationSystem.getNotificationCount());
        } else {
            showPlaceholderAlert("الإشعارات", "نظام الإشعارات غير مفعل");
        }
    }

    /**
     * تنفيذ التحديث التلقائي
     */
    private void performAutoRefresh() {
        // تحديث البيانات
        refreshTables();

        // فحص الأصناف المنخفضة
        if (notificationSystem != null) {
            notificationSystem.checkLowStockItems(items);
        }

        showSuccessAlert("تم التحديث التلقائي بنجاح!");
    }

    /**
     * إنشاء شريط الإحصائيات السريعة
     */
    private HBox createQuickStatsBar() {
        HBox statsBar = new HBox(20);
        statsBar.setAlignment(Pos.CENTER_LEFT);
        statsBar.setPadding(new Insets(15, 0, 0, 0));

        // إجمالي الأصناف
        VBox totalItemsCard = createQuickStatCard("إجمالي الأصناف",
                                                 String.valueOf(items.size()),
                                                 FontAwesomeIcon.CUBE, "#ffffff");

        // إجمالي القيمة
        double totalValue = items.stream().mapToDouble(Item::getTotalValue).sum();
        VBox totalValueCard = createQuickStatCard("إجمالي القيمة",
                                                 decimalFormat.format(totalValue) + " ج.م",
                                                 FontAwesomeIcon.MONEY, "#ffffff");

        // الأصناف المنخفضة
        long lowStockItems = items.stream().filter(item -> item.getCurrentQuantity() < 10).count();
        VBox lowStockCard = createQuickStatCard("أصناف منخفضة",
                                               String.valueOf(lowStockItems),
                                               FontAwesomeIcon.EXCLAMATION_TRIANGLE, "#ffeb3b");

        // المخازن النشطة
        VBox warehousesCard = createQuickStatCard("المخازن النشطة",
                                                 String.valueOf(warehouses.size()),
                                                 FontAwesomeIcon.BUILDING, "#ffffff");

        statsBar.getChildren().addAll(totalItemsCard, totalValueCard, lowStockCard, warehousesCard);
        return statsBar;
    }

    /**
     * إنشاء بطاقة إحصائية سريعة
     */
    private VBox createQuickStatCard(String title, String value, FontAwesomeIcon icon, String color) {
        VBox card = new VBox(5);
        card.setAlignment(Pos.CENTER);
        card.setPadding(new Insets(10));
        card.setStyle("-fx-background-color: rgba(255,255,255,0.1); " +
                     "-fx-background-radius: 8; " +
                     "-fx-border-color: rgba(255,255,255,0.2); " +
                     "-fx-border-width: 1; " +
                     "-fx-border-radius: 8;");

        FontAwesomeIconView iconView = new FontAwesomeIconView(icon);
        iconView.setSize("16px");
        iconView.setStyle("-fx-fill: " + color + ";");

        Label valueLabel = new Label(value);
        valueLabel.setStyle("-fx-font-size: 16px; -fx-font-weight: bold; -fx-text-fill: " + color + ";");

        Label titleLabel = new Label(title);
        titleLabel.setStyle("-fx-font-size: 11px; -fx-text-fill: " + color + "; -fx-opacity: 0.8;");

        card.getChildren().addAll(iconView, valueLabel, titleLabel);
        return card;
    }

    /**
     * إنشاء القائمة الجانبية
     */
    private VBox createSidebar() {
        VBox sidebar = new VBox();
        sidebar.setPadding(new Insets(20, 15, 20, 15));
        sidebar.setStyle("-fx-background-color: #f8f9fa; " +
                        "-fx-border-color: #e9ecef; " +
                        "-fx-border-width: 0 1 0 0;");

        Label menuTitle = new Label("📋 القوائم الرئيسية");
        menuTitle.setStyle("-fx-font-size: 14px; -fx-font-weight: bold; -fx-text-fill: #495057; " +
                          "-fx-padding: 0 0 15 0;");

        // أزرار التنقل
        VBox menuButtons = new VBox(5);

        Button dashboardBtn = createSidebarButton("لوحة التحكم", FontAwesomeIcon.DASHBOARD, () -> showDashboard());
        Button itemsBtn = createSidebarButton("إدارة الأصناف", FontAwesomeIcon.CUBE, () -> showItemsManagement());
        Button movementsBtn = createSidebarButton("حركة المخزون", FontAwesomeIcon.EXCHANGE, () -> showMovementsManagement());
        Button warehousesBtn = createSidebarButton("إدارة المخازن", FontAwesomeIcon.BUILDING, () -> showWarehousesManagement());
        Button reportsBtn = createSidebarButton("التقارير والتحليلات", FontAwesomeIcon.BAR_CHART, () -> showReportsAndAnalytics());

        menuButtons.getChildren().addAll(dashboardBtn, itemsBtn, movementsBtn, warehousesBtn, reportsBtn);

        // قسم العمليات السريعة
        Label operationsTitle = new Label("⚡ عمليات سريعة");
        operationsTitle.setStyle("-fx-font-size: 14px; -fx-font-weight: bold; -fx-text-fill: #495057; " +
                               "-fx-padding: 20 0 15 0;");

        VBox operationsButtons = new VBox(5);

        Button addItemBtn = createSidebarButton("إضافة صنف", FontAwesomeIcon.PLUS, () -> showAddItemDialog());
        Button receiveBtn = createSidebarButton("استلام", FontAwesomeIcon.ARROW_DOWN, () -> showReceiveDialog());
        Button issueBtn = createSidebarButton("صرف", FontAwesomeIcon.ARROW_UP, () -> showIssueDialog());

        operationsButtons.getChildren().addAll(addItemBtn, receiveBtn, issueBtn);

        sidebar.getChildren().addAll(menuTitle, menuButtons, operationsTitle, operationsButtons);
        return sidebar;
    }

    /**
     * إنشاء زر القائمة الجانبية
     */
    private Button createSidebarButton(String text, FontAwesomeIcon icon, Runnable action) {
        Button button = new Button(text);
        button.setPrefWidth(220);
        button.setAlignment(Pos.CENTER_LEFT);
        button.setPadding(new Insets(12, 15, 12, 15));

        FontAwesomeIconView iconView = new FontAwesomeIconView(icon);
        iconView.setSize("14px");
        iconView.setStyle("-fx-fill: #6c757d;");
        button.setGraphic(iconView);

        button.setStyle("-fx-background-color: transparent; " +
                       "-fx-text-fill: #495057; " +
                       "-fx-font-size: 13px; " +
                       "-fx-border-color: transparent; " +
                       "-fx-border-width: 0; " +
                       "-fx-background-radius: 8; " +
                       "-fx-cursor: hand;");

        // تأثيرات التفاعل
        button.setOnMouseEntered(e -> {
            button.setStyle("-fx-background-color: #e9ecef; " +
                           "-fx-text-fill: #495057; " +
                           "-fx-font-size: 13px; " +
                           "-fx-border-color: transparent; " +
                           "-fx-border-width: 0; " +
                           "-fx-background-radius: 8; " +
                           "-fx-cursor: hand;");
            iconView.setStyle("-fx-fill: #495057;");
        });

        button.setOnMouseExited(e -> {
            button.setStyle("-fx-background-color: transparent; " +
                           "-fx-text-fill: #495057; " +
                           "-fx-font-size: 13px; " +
                           "-fx-border-color: transparent; " +
                           "-fx-border-width: 0; " +
                           "-fx-background-radius: 8; " +
                           "-fx-cursor: hand;");
            iconView.setStyle("-fx-fill: #6c757d;");
        });

        button.setOnAction(e -> action.run());
        return button;
    }

    /**
     * إنشاء منطقة المحتوى الرئيسية
     */
    private VBox createMainContentArea() {
        VBox mainContent = new VBox();
        mainContent.setPadding(new Insets(20));
        mainContent.setStyle("-fx-background-color: white;");

        // حفظ مرجع للمحتوى الرئيسي
        currentMainContent = mainContent;

        // عرض لوحة التحكم افتراضياً
        VBox dashboardContent = createDashboardContent();
        mainContent.getChildren().add(dashboardContent);

        return mainContent;
    }

    // متغيرات للمحتوى الحالي
    private VBox currentMainContent;

    // نظام الإشعارات
    private NotificationSystem notificationSystem;

    // وظائف التنقل
    private void showDashboard() {
        switchToContent(createDashboardContent());
    }

    private void showItemsManagement() {
        switchToContent(createItemsManagementContent());
    }

    private void showMovementsManagement() {
        switchToContent(createMovementsManagementContent());
    }

    private void showWarehousesManagement() {
        switchToContent(createWarehousesManagementContent());
    }

    private void showReportsAndAnalytics() {
        switchToContent(createReportsAndAnalyticsContent());
    }

    /**
     * تبديل المحتوى
     */
    private void switchToContent(VBox newContent) {
        if (currentMainContent != null) {
            currentMainContent.getChildren().clear();
            currentMainContent.getChildren().add(newContent);
        }
    }

    /**
     * إنشاء محتوى إدارة الأصناف المحسن
     */
    private VBox createItemsManagementContent() {
        VBox content = new VBox(20);

        // العنوان مع إحصائيات سريعة
        HBox headerBox = new HBox(20);
        headerBox.setAlignment(Pos.CENTER_LEFT);

        Label titleLabel = new Label("📦 إدارة الأصناف المتقدمة");
        titleLabel.setStyle("-fx-font-size: 20px; -fx-font-weight: bold; -fx-text-fill: #2c3e50;");

        // إحصائيات سريعة للأصناف
        HBox quickStats = new HBox(15);
        quickStats.setAlignment(Pos.CENTER_LEFT);

        Label totalItemsLabel = new Label("إجمالي: " + items.size());
        totalItemsLabel.setStyle("-fx-background-color: #3498db; -fx-text-fill: white; " +
                               "-fx-padding: 5 10 5 10; -fx-background-radius: 15;");

        long lowStockCount = items.stream().filter(item -> item.getCurrentQuantity() < 10).count();
        Label lowStockLabel = new Label("منخفضة: " + lowStockCount);
        lowStockLabel.setStyle("-fx-background-color: #e74c3c; -fx-text-fill: white; " +
                             "-fx-padding: 5 10 5 10; -fx-background-radius: 15;");

        double totalValue = items.stream().mapToDouble(Item::getTotalValue).sum();
        Label totalValueLabel = new Label("القيمة: " + decimalFormat.format(totalValue) + " ج.م");
        totalValueLabel.setStyle("-fx-background-color: #2ecc71; -fx-text-fill: white; " +
                               "-fx-padding: 5 10 5 10; -fx-background-radius: 15;");

        quickStats.getChildren().addAll(totalItemsLabel, lowStockLabel, totalValueLabel);
        headerBox.getChildren().addAll(titleLabel, quickStats);

        // شريط الأدوات المحسن
        HBox enhancedToolbar = createEnhancedItemsToolbar();

        // منطقة الفلترة المتقدمة
        VBox advancedFilterSection = createAdvancedFilterSection();

        // جدول الأصناف المحسن
        itemsTable = createEnhancedItemsTable();

        // تحديد ارتفاع مناسب لعرض 12 صفوف مع scroll
        double tableHeight = (12 * 35) + 40; // 460 بكسل
        itemsTable.setPrefHeight(tableHeight);
        itemsTable.setMinHeight(tableHeight);
        VBox.setVgrow(itemsTable, Priority.ALWAYS);

        content.getChildren().addAll(headerBox, enhancedToolbar, advancedFilterSection, itemsTable);
        return content;
    }

    /**
     * إنشاء شريط أدوات محسن للأصناف
     */
    private HBox createEnhancedItemsToolbar() {
        HBox toolbar = new HBox(15);
        toolbar.setAlignment(Pos.CENTER_LEFT);
        toolbar.setPadding(new Insets(10));
        toolbar.setStyle("-fx-background-color: white; " +
                        "-fx-border-color: #e9ecef; " +
                        "-fx-border-width: 0 0 1 0;");

        // أزرار العمليات الأساسية
        Button addItemBtn = createToolbarButton("إضافة صنف", FontAwesomeIcon.PLUS_CIRCLE, "#28a745", this::showAddItemDialog);
        Button editItemBtn = createToolbarButton("تعديل", FontAwesomeIcon.EDIT, "#007bff", this::editSelectedItem);
        Button deleteItemBtn = createToolbarButton("حذف", FontAwesomeIcon.TRASH, "#dc3545", this::deleteSelectedItem);

        Separator separator1 = new Separator();
        separator1.setOrientation(javafx.geometry.Orientation.VERTICAL);

        // أزرار العمليات السريعة
        Button receiveBtn = createToolbarButton("استلام سريع", FontAwesomeIcon.ARROW_DOWN, "#17a2b8", this::showReceiveDialog);
        Button issueBtn = createToolbarButton("صرف سريع", FontAwesomeIcon.ARROW_UP, "#fd7e14", this::showIssueDialog);

        Separator separator2 = new Separator();
        separator2.setOrientation(javafx.geometry.Orientation.VERTICAL);

        // أزرار التقارير والتصدير
        Button exportBtn = createToolbarButton("تصدير", FontAwesomeIcon.DOWNLOAD, "#6c757d", this::exportItems);
        Button printBtn = createToolbarButton("طباعة", FontAwesomeIcon.PRINT, "#6c757d", this::printItems);
        Button refreshBtn = createToolbarButton("تحديث", FontAwesomeIcon.REFRESH, "#17a2b8", this::refreshItemsTable);

        Separator separator3 = new Separator();
        separator3.setOrientation(javafx.geometry.Orientation.VERTICAL);

        // أزرار العرض
        Button viewModeBtn = createToolbarButton("عرض مفصل", FontAwesomeIcon.LIST, "#6f42c1", this::toggleViewMode);
        Button chartViewBtn = createToolbarButton("عرض بياني", FontAwesomeIcon.BAR_CHART, "#e83e8c", this::showItemsChart);

        toolbar.getChildren().addAll(
            addItemBtn, editItemBtn, deleteItemBtn, separator1,
            receiveBtn, issueBtn, separator2,
            exportBtn, printBtn, refreshBtn, separator3,
            viewModeBtn, chartViewBtn
        );

        return toolbar;
    }

    /**
     * إنشاء زر شريط الأدوات المحسن
     */
    private Button createToolbarButton(String text, FontAwesomeIcon icon, String color, Runnable action) {
        Button button = new Button(text);

        FontAwesomeIconView iconView = new FontAwesomeIconView(icon);
        iconView.setSize("12px");
        iconView.setStyle("-fx-fill: white;");
        button.setGraphic(iconView);

        button.setStyle("-fx-background-color: " + color + "; " +
                       "-fx-text-fill: white; " +
                       "-fx-font-size: 11px; " +
                       "-fx-padding: 6 12 6 12; " +
                       "-fx-background-radius: 4; " +
                       "-fx-cursor: hand;");

        // تأثيرات التفاعل
        button.setOnMouseEntered(e -> {
            button.setStyle("-fx-background-color: derive(" + color + ", -10%); " +
                           "-fx-text-fill: white; " +
                           "-fx-font-size: 11px; " +
                           "-fx-padding: 6 12 6 12; " +
                           "-fx-background-radius: 4; " +
                           "-fx-cursor: hand; " +
                           "-fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.2), 3, 0, 0, 1);");
        });

        button.setOnMouseExited(e -> {
            button.setStyle("-fx-background-color: " + color + "; " +
                           "-fx-text-fill: white; " +
                           "-fx-font-size: 11px; " +
                           "-fx-padding: 6 12 6 12; " +
                           "-fx-background-radius: 4; " +
                           "-fx-cursor: hand;");
        });

        button.setOnAction(e -> action.run());
        return button;
    }

    /**
     * إنشاء قسم الفلترة المتقدمة
     */
    private VBox createAdvancedFilterSection() {
        VBox filterSection = new VBox(15);
        filterSection.setPadding(new Insets(15));
        filterSection.setStyle("-fx-background-color: white; " +
                              "-fx-border-color: #e9ecef; " +
                              "-fx-border-width: 1; " +
                              "-fx-border-radius: 8; " +
                              "-fx-background-radius: 8;");

        // عنوان الفلاتر
        HBox filterHeader = new HBox(10);
        filterHeader.setAlignment(Pos.CENTER_LEFT);

        FontAwesomeIconView filterIcon = new FontAwesomeIconView(FontAwesomeIcon.FILTER);
        filterIcon.setSize("14px");
        filterIcon.setStyle("-fx-fill: #007bff;");

        Label filterTitle = new Label("🔍 فلاتر البحث المتقدمة");
        filterTitle.setStyle("-fx-font-size: 14px; -fx-font-weight: bold; -fx-text-fill: #495057;");

        Button toggleFiltersBtn = new Button("إخفاء/إظهار");
        toggleFiltersBtn.setStyle("-fx-background-color: transparent; -fx-text-fill: #007bff; -fx-font-size: 11px;");

        filterHeader.getChildren().addAll(filterIcon, filterTitle, new Region(), toggleFiltersBtn);
        HBox.setHgrow(filterHeader.getChildren().get(2), Priority.ALWAYS);

        // الصف الأول من الفلاتر
        HBox firstFilterRow = new HBox(15);
        firstFilterRow.setAlignment(Pos.CENTER_LEFT);

        // البحث النصي المحسن
        Label searchLabel = new Label("البحث:");
        TextField enhancedSearchField = new TextField();
        enhancedSearchField.setPromptText("ابحث في الاسم، الكود، المورد...");
        enhancedSearchField.setPrefWidth(250);
        enhancedSearchField.textProperty().addListener((obs, oldVal, newVal) -> applyAdvancedFilters());

        // فلتر المخزن
        Label warehouseLabel = new Label("المخزن:");
        ComboBox<Warehouse> warehouseFilterCombo = new ComboBox<>();
        warehouseFilterCombo.getItems().add(null); // جميع المخازن
        warehouseFilterCombo.getItems().addAll(warehouses);
        warehouseFilterCombo.setPrefWidth(180);
        warehouseFilterCombo.valueProperty().addListener((obs, oldVal, newVal) -> applyAdvancedFilters());

        // فلتر الفئة
        Label categoryLabel = new Label("الفئة:");
        ComboBox<String> categoryFilterCombo = new ComboBox<>();
        categoryFilterCombo.getItems().add(null); // جميع الفئات
        categoryFilterCombo.getItems().addAll(items.stream()
            .map(Item::getCategory)
            .distinct()
            .sorted()
            .collect(java.util.stream.Collectors.toList()));
        categoryFilterCombo.setPrefWidth(150);
        categoryFilterCombo.valueProperty().addListener((obs, oldVal, newVal) -> applyAdvancedFilters());

        firstFilterRow.getChildren().addAll(
            searchLabel, enhancedSearchField,
            new Separator(), warehouseLabel, warehouseFilterCombo,
            new Separator(), categoryLabel, categoryFilterCombo
        );

        // الصف الثاني من الفلاتر
        HBox secondFilterRow = new HBox(15);
        secondFilterRow.setAlignment(Pos.CENTER_LEFT);

        // فلتر الوحدة
        Label unitLabel = new Label("الوحدة:");
        ComboBox<Unit> unitFilterCombo = new ComboBox<>();
        unitFilterCombo.getItems().add(null); // جميع الوحدات
        unitFilterCombo.getItems().addAll(Unit.values());
        unitFilterCombo.setPrefWidth(120);
        unitFilterCombo.valueProperty().addListener((obs, oldVal, newVal) -> applyAdvancedFilters());

        // فلتر الكمية
        Label quantityLabel = new Label("الكمية:");
        ComboBox<String> quantityFilterCombo = new ComboBox<>();
        quantityFilterCombo.getItems().addAll("الكل", "أكبر من 0", "أقل من 10", "أقل من 5", "نفدت");
        quantityFilterCombo.setValue("الكل");
        quantityFilterCombo.setPrefWidth(120);
        quantityFilterCombo.valueProperty().addListener((obs, oldVal, newVal) -> applyAdvancedFilters());

        // فلتر القيمة
        Label valueLabel = new Label("نطاق القيمة:");
        TextField minValueField = new TextField();
        minValueField.setPromptText("من");
        minValueField.setPrefWidth(80);
        TextField maxValueField = new TextField();
        maxValueField.setPromptText("إلى");
        maxValueField.setPrefWidth(80);

        minValueField.textProperty().addListener((obs, oldVal, newVal) -> applyAdvancedFilters());
        maxValueField.textProperty().addListener((obs, oldVal, newVal) -> applyAdvancedFilters());

        secondFilterRow.getChildren().addAll(
            unitLabel, unitFilterCombo,
            new Separator(), quantityLabel, quantityFilterCombo,
            new Separator(), valueLabel, minValueField, new Label("-"), maxValueField
        );

        // أزرار الفلاتر
        HBox filterButtons = new HBox(10);
        filterButtons.setAlignment(Pos.CENTER_RIGHT);

        Button applyFiltersBtn = new Button("تطبيق الفلاتر");
        FontAwesomeIconView applyIcon = new FontAwesomeIconView(FontAwesomeIcon.SEARCH);
        applyIcon.setSize("11px");
        applyFiltersBtn.setGraphic(applyIcon);
        applyFiltersBtn.setStyle("-fx-background-color: #007bff; -fx-text-fill: white; -fx-font-size: 11px;");
        applyFiltersBtn.setOnAction(e -> applyAdvancedFilters());

        Button resetFiltersBtn = new Button("إعادة تعيين");
        FontAwesomeIconView resetIcon = new FontAwesomeIconView(FontAwesomeIcon.REFRESH);
        resetIcon.setSize("11px");
        resetFiltersBtn.setGraphic(resetIcon);
        resetFiltersBtn.setStyle("-fx-background-color: #6c757d; -fx-text-fill: white; -fx-font-size: 11px;");
        resetFiltersBtn.setOnAction(e -> resetAdvancedFilters());

        Button saveFiltersBtn = new Button("حفظ الفلاتر");
        FontAwesomeIconView saveIcon = new FontAwesomeIconView(FontAwesomeIcon.SAVE);
        saveIcon.setSize("11px");
        saveFiltersBtn.setGraphic(saveIcon);
        saveFiltersBtn.setStyle("-fx-background-color: #28a745; -fx-text-fill: white; -fx-font-size: 11px;");

        filterButtons.getChildren().addAll(applyFiltersBtn, resetFiltersBtn, saveFiltersBtn);

        // حفظ مراجع الفلاتر للاستخدام في التطبيق
        this.searchField = enhancedSearchField;
        this.warehouseFilter = warehouseFilterCombo;
        this.categoryFilter = categoryFilterCombo;

        filterSection.getChildren().addAll(filterHeader, firstFilterRow, secondFilterRow, filterButtons);
        return filterSection;
    }

    /**
     * إنشاء محتوى حركة المخزون
     */
    private VBox createMovementsManagementContent() {
        VBox content = new VBox(20);

        // العنوان
        Label titleLabel = new Label("📊 حركة المخزون");
        titleLabel.setStyle("-fx-font-size: 20px; -fx-font-weight: bold; -fx-text-fill: #2c3e50;");

        // جدول حركة المخزون
        TableView<StockMovement> movementsTable = createMovementsTable();

        // تحديد ارتفاع مناسب لعرض 10 صفوف
        double tableHeight = (10 * 40) + 35; // 435 بكسل
        movementsTable.setPrefHeight(tableHeight);
        movementsTable.setMinHeight(tableHeight);
        movementsTable.setMaxHeight(Double.MAX_VALUE);
        VBox.setVgrow(movementsTable, Priority.ALWAYS);

        content.getChildren().addAll(titleLabel, movementsTable);
        return content;
    }

    /**
     * إنشاء محتوى إدارة المخازن
     */
    private VBox createWarehousesManagementContent() {
        VBox content = new VBox(20);

        // العنوان
        Label titleLabel = new Label("🏢 إدارة المخازن");
        titleLabel.setStyle("-fx-font-size: 20px; -fx-font-weight: bold; -fx-text-fill: #2c3e50;");

        // جدول المخازن
        TableView<Warehouse> warehousesTable = createWarehousesTable();

        // تحديد ارتفاع مناسب لعرض 8 صفوف
        double tableHeight = (8 * 40) + 35; // 355 بكسل
        warehousesTable.setPrefHeight(tableHeight);
        warehousesTable.setMinHeight(tableHeight);
        warehousesTable.setMaxHeight(Double.MAX_VALUE);
        VBox.setVgrow(warehousesTable, Priority.ALWAYS);

        content.getChildren().addAll(titleLabel, warehousesTable);
        return content;
    }

    /**
     * إنشاء محتوى التقارير والتحليلات المتقدمة
     */
    private VBox createReportsAndAnalyticsContent() {
        VBox content = new VBox(20);

        // العنوان
        Label titleLabel = new Label("📈 التقارير والتحليلات المتقدمة");
        titleLabel.setStyle("-fx-font-size: 20px; -fx-font-weight: bold; -fx-text-fill: #2c3e50;");

        // التبويبات للتقارير المختلفة
        TabPane reportsTabPane = new TabPane();
        reportsTabPane.setPrefHeight(600);

        // تبويب الرسوم البيانية
        Tab chartsTab = new Tab("📊 الرسوم البيانية");
        chartsTab.setContent(createChartsContent());
        chartsTab.setClosable(false);

        // تبويب التقارير التفصيلية
        Tab detailedReportsTab = new Tab("📋 التقارير التفصيلية");
        detailedReportsTab.setContent(createDetailedReportsContent());
        detailedReportsTab.setClosable(false);

        // تبويب التحليلات المتقدمة
        Tab analyticsTab = new Tab("🔍 التحليلات المتقدمة");
        analyticsTab.setContent(createAdvancedAnalyticsContent());
        analyticsTab.setClosable(false);

        // تبويب التقارير المخصصة
        Tab customReportsTab = new Tab("⚙️ تقارير مخصصة");
        customReportsTab.setContent(createCustomReportsContent());
        customReportsTab.setClosable(false);

        reportsTabPane.getTabs().addAll(chartsTab, detailedReportsTab, analyticsTab, customReportsTab);

        content.getChildren().addAll(titleLabel, reportsTabPane);
        return content;
    }

    /**
     * إنشاء محتوى الرسوم البيانية التفاعلية
     */
    private VBox createChartsContent() {
        VBox chartsContent = new VBox(20);
        chartsContent.setPadding(new Insets(20));

        // شريط أدوات الرسوم البيانية
        HBox chartsToolbar = new HBox(15);
        chartsToolbar.setAlignment(Pos.CENTER_LEFT);

        Button refreshChartsBtn = new Button("تحديث الرسوم");
        FontAwesomeIconView refreshIcon = new FontAwesomeIconView(FontAwesomeIcon.REFRESH);
        refreshIcon.setSize("12px");
        refreshChartsBtn.setGraphic(refreshIcon);
        refreshChartsBtn.setStyle("-fx-background-color: #17a2b8; -fx-text-fill: white;");
        refreshChartsBtn.setOnAction(e -> refreshCharts());

        Button exportChartsBtn = new Button("تصدير الرسوم");
        FontAwesomeIconView exportIcon = new FontAwesomeIconView(FontAwesomeIcon.DOWNLOAD);
        exportIcon.setSize("12px");
        exportChartsBtn.setGraphic(exportIcon);
        exportChartsBtn.setStyle("-fx-background-color: #28a745; -fx-text-fill: white;");
        exportChartsBtn.setOnAction(e -> exportCharts());

        chartsToolbar.getChildren().addAll(refreshChartsBtn, exportChartsBtn);

        // الصف الأول من الرسوم البيانية
        HBox firstRowCharts = new HBox(20);
        firstRowCharts.setAlignment(Pos.CENTER);

        // رسم بياني دائري للفئات
        PieChart categoryChart = createEnhancedCategoryPieChart();
        categoryChart.setPrefSize(400, 300);

        // رسم بياني عمودي للمخازن
        BarChart<String, Number> warehouseChart = createEnhancedWarehouseBarChart();
        warehouseChart.setPrefSize(500, 300);

        firstRowCharts.getChildren().addAll(categoryChart, warehouseChart);

        // الصف الثاني من الرسوم البيانية
        HBox secondRowCharts = new HBox(20);
        secondRowCharts.setAlignment(Pos.CENTER);

        // رسم بياني خطي لحركة المخزون
        LineChart<String, Number> movementChart = createMovementLineChart();
        movementChart.setPrefSize(500, 300);

        // رسم بياني للقيم
        BarChart<String, Number> valueChart = createValueBarChart();
        valueChart.setPrefSize(400, 300);

        secondRowCharts.getChildren().addAll(movementChart, valueChart);

        // الصف الثالث - رسم بياني للاتجاهات
        HBox thirdRowCharts = new HBox(20);
        thirdRowCharts.setAlignment(Pos.CENTER);

        // رسم بياني للأصناف المنخفضة
        BarChart<String, Number> lowStockChart = createLowStockChart();
        lowStockChart.setPrefSize(600, 300);

        // رسم بياني دائري للوحدات
        PieChart unitsChart = createUnitsDistributionChart();
        unitsChart.setPrefSize(350, 300);

        thirdRowCharts.getChildren().addAll(lowStockChart, unitsChart);

        chartsContent.getChildren().addAll(chartsToolbar, firstRowCharts, secondRowCharts, thirdRowCharts);
        return chartsContent;
    }

    /**
     * إنشاء رسم بياني دائري محسن للفئات
     */
    private PieChart createEnhancedCategoryPieChart() {
        PieChart chart = new PieChart();
        chart.setTitle("📊 توزيع الأصناف حسب الفئة");

        Map<String, Long> categoryCount = items.stream()
            .collect(java.util.stream.Collectors.groupingBy(Item::getCategory, java.util.stream.Collectors.counting()));

        // ألوان مخصصة للفئات
        String[] colors = {"#FF6B6B", "#4ECDC4", "#45B7D1", "#96CEB4", "#FFEAA7", "#DDA0DD", "#98D8C8"};
        int colorIndex = 0;

        for (Map.Entry<String, Long> entry : categoryCount.entrySet()) {
            PieChart.Data data = new PieChart.Data(entry.getKey() + " (" + entry.getValue() + ")", entry.getValue());
            chart.getData().add(data);

            // تطبيق الألوان المخصصة
            final String color = colors[colorIndex % colors.length];
            colorIndex++;

            data.nodeProperty().addListener((obs, oldNode, newNode) -> {
                if (newNode != null) {
                    newNode.setStyle("-fx-pie-color: " + color + ";");
                }
            });
        }

        chart.setLegendVisible(true);
        chart.setLabelsVisible(true);
        chart.setStartAngle(90);

        return chart;
    }

    /**
     * إنشاء رسم بياني عمودي محسن للمخازن
     */
    private BarChart<String, Number> createEnhancedWarehouseBarChart() {
        CategoryAxis xAxis = new CategoryAxis();
        xAxis.setLabel("المخازن");

        NumberAxis yAxis = new NumberAxis();
        yAxis.setLabel("عدد الأصناف");

        BarChart<String, Number> chart = new BarChart<>(xAxis, yAxis);
        chart.setTitle("🏢 عدد الأصناف في كل مخزن");

        XYChart.Series<String, Number> itemsSeries = new XYChart.Series<>();
        itemsSeries.setName("عدد الأصناف");

        XYChart.Series<String, Number> valueSeries = new XYChart.Series<>();
        valueSeries.setName("إجمالي القيمة (بالآلاف)");

        warehouses.forEach(warehouse -> {
            long itemCount = items.stream()
                .filter(item -> item.getWarehouseId().equals(warehouse.getWarehouseId()))
                .count();

            double totalValue = items.stream()
                .filter(item -> item.getWarehouseId().equals(warehouse.getWarehouseId()))
                .mapToDouble(Item::getTotalValue)
                .sum() / 1000; // تحويل إلى آلاف

            itemsSeries.getData().add(new XYChart.Data<>(warehouse.getName(), itemCount));
            valueSeries.getData().add(new XYChart.Data<>(warehouse.getName(), totalValue));
        });

        chart.getData().addAll(itemsSeries, valueSeries);
        chart.setLegendVisible(true);

        return chart;
    }

    /**
     * إنشاء رسم بياني خطي لحركة المخزون
     */
    private LineChart<String, Number> createMovementLineChart() {
        CategoryAxis xAxis = new CategoryAxis();
        xAxis.setLabel("التاريخ");

        NumberAxis yAxis = new NumberAxis();
        yAxis.setLabel("عدد الحركات");

        LineChart<String, Number> chart = new LineChart<>(xAxis, yAxis);
        chart.setTitle("📈 اتجاه حركة المخزون");

        XYChart.Series<String, Number> inSeries = new XYChart.Series<>();
        inSeries.setName("استلام");

        XYChart.Series<String, Number> outSeries = new XYChart.Series<>();
        outSeries.setName("صرف");

        // تجميع الحركات حسب التاريخ
        Map<String, Long> inMovements = stockMovements.stream()
            .filter(m -> m.getMovementType() == StockMovement.MovementType.IN)
            .collect(java.util.stream.Collectors.groupingBy(
                m -> m.getDateTime().toLocalDate().format(dateFormatter),
                java.util.stream.Collectors.counting()
            ));

        Map<String, Long> outMovements = stockMovements.stream()
            .filter(m -> m.getMovementType() == StockMovement.MovementType.OUT)
            .collect(java.util.stream.Collectors.groupingBy(
                m -> m.getDateTime().toLocalDate().format(dateFormatter),
                java.util.stream.Collectors.counting()
            ));

        // إضافة البيانات للرسم البياني
        inMovements.forEach((date, count) ->
            inSeries.getData().add(new XYChart.Data<>(date, count)));

        outMovements.forEach((date, count) ->
            outSeries.getData().add(new XYChart.Data<>(date, count)));

        chart.getData().addAll(inSeries, outSeries);
        chart.setCreateSymbols(true);
        chart.setLegendVisible(true);

        return chart;
    }

    /**
     * إنشاء رسم بياني للقيم
     */
    private BarChart<String, Number> createValueBarChart() {
        CategoryAxis xAxis = new CategoryAxis();
        xAxis.setLabel("الفئات");

        NumberAxis yAxis = new NumberAxis();
        yAxis.setLabel("القيمة (ج.م)");

        BarChart<String, Number> chart = new BarChart<>(xAxis, yAxis);
        chart.setTitle("💰 توزيع القيم حسب الفئة");

        XYChart.Series<String, Number> series = new XYChart.Series<>();
        series.setName("إجمالي القيمة");

        Map<String, Double> categoryValues = items.stream()
            .collect(java.util.stream.Collectors.groupingBy(
                Item::getCategory,
                java.util.stream.Collectors.summingDouble(Item::getTotalValue)
            ));

        categoryValues.forEach((category, value) ->
            series.getData().add(new XYChart.Data<>(category, value)));

        chart.getData().add(series);
        chart.setLegendVisible(false);

        return chart;
    }

    /**
     * إنشاء رسم بياني للأصناف المنخفضة
     */
    private BarChart<String, Number> createLowStockChart() {
        CategoryAxis xAxis = new CategoryAxis();
        xAxis.setLabel("الأصناف");

        NumberAxis yAxis = new NumberAxis();
        yAxis.setLabel("الكمية المتبقية");

        BarChart<String, Number> chart = new BarChart<>(xAxis, yAxis);
        chart.setTitle("⚠️ الأصناف المنخفضة (أقل من 10 وحدات)");

        XYChart.Series<String, Number> series = new XYChart.Series<>();
        series.setName("الكمية المتبقية");

        items.stream()
            .filter(item -> item.getCurrentQuantity() < 10)
            .forEach(item ->
                series.getData().add(new XYChart.Data<>(item.getName(), item.getCurrentQuantity())));

        chart.getData().add(series);
        chart.setLegendVisible(false);

        // تلوين الأعمدة بالأحمر للتنبيه
        chart.getData().get(0).getData().forEach(data -> {
            data.nodeProperty().addListener((obs, oldNode, newNode) -> {
                if (newNode != null) {
                    newNode.setStyle("-fx-bar-fill: #e74c3c;");
                }
            });
        });

        return chart;
    }

    /**
     * إنشاء رسم بياني دائري لتوزيع الوحدات
     */
    private PieChart createUnitsDistributionChart() {
        PieChart chart = new PieChart();
        chart.setTitle("📏 توزيع الأصناف حسب الوحدة");

        Map<String, Long> unitsCount = items.stream()
            .collect(java.util.stream.Collectors.groupingBy(
                item -> item.getUnit().getArabicName(),
                java.util.stream.Collectors.counting()
            ));

        unitsCount.forEach((unit, count) -> {
            PieChart.Data data = new PieChart.Data(unit + " (" + count + ")", count);
            chart.getData().add(data);
        });

        chart.setLegendVisible(true);
        chart.setLabelsVisible(true);

        return chart;
    }

    /**
     * إنشاء محتوى التقارير التفصيلية
     */
    private VBox createDetailedReportsContent() {
        VBox reportsContent = new VBox(20);
        reportsContent.setPadding(new Insets(20));

        // شريط أدوات التقارير
        HBox reportsToolbar = new HBox(15);
        reportsToolbar.setAlignment(Pos.CENTER_LEFT);

        Button generateReportBtn = new Button("إنشاء تقرير");
        FontAwesomeIconView generateIcon = new FontAwesomeIconView(FontAwesomeIcon.FILE_TEXT);
        generateIcon.setSize("12px");
        generateReportBtn.setGraphic(generateIcon);
        generateReportBtn.setStyle("-fx-background-color: #007bff; -fx-text-fill: white;");

        Button printReportBtn = new Button("طباعة");
        FontAwesomeIconView printIcon = new FontAwesomeIconView(FontAwesomeIcon.PRINT);
        printIcon.setSize("12px");
        printReportBtn.setGraphic(printIcon);
        printReportBtn.setStyle("-fx-background-color: #6c757d; -fx-text-fill: white;");

        Button exportPdfBtn = new Button("تصدير PDF");
        FontAwesomeIconView pdfIcon = new FontAwesomeIconView(FontAwesomeIcon.FILE_PDF_O);
        pdfIcon.setSize("12px");
        exportPdfBtn.setGraphic(pdfIcon);
        exportPdfBtn.setStyle("-fx-background-color: #dc3545; -fx-text-fill: white;");

        Button exportExcelBtn = new Button("تصدير Excel");
        FontAwesomeIconView excelIcon = new FontAwesomeIconView(FontAwesomeIcon.FILE_EXCEL_O);
        excelIcon.setSize("12px");
        exportExcelBtn.setGraphic(excelIcon);
        exportExcelBtn.setStyle("-fx-background-color: #28a745; -fx-text-fill: white;");

        reportsToolbar.getChildren().addAll(generateReportBtn, printReportBtn, exportPdfBtn, exportExcelBtn);

        // قائمة التقارير التفصيلية
        VBox reportsBox = new VBox(15);

        // تقرير الأصناف التفصيلي
        Button itemsReportBtn = createDetailedReportButton(
            "📦 تقرير الأصناف التفصيلي",
            "عرض جميع الأصناف مع الكميات والقيم والمواصفات الكاملة",
            FontAwesomeIcon.CUBE,
            () -> showDetailedItemsReport()
        );

        // تقرير حركة المخزون التفصيلي
        Button movementsReportBtn = createDetailedReportButton(
            "📊 تقرير حركة المخزون التفصيلي",
            "عرض جميع حركات الاستلام والصرف مع التفاصيل الكاملة",
            FontAwesomeIcon.EXCHANGE,
            () -> showDetailedMovementsReport()
        );

        // تقرير المخازن التفصيلي
        Button warehousesReportBtn = createDetailedReportButton(
            "🏢 تقرير المخازن التفصيلي",
            "عرض تفاصيل جميع المخازن مع الأصناف والقيم",
            FontAwesomeIcon.BUILDING,
            () -> showDetailedWarehousesReport()
        );

        // تقرير الأصناف المنخفضة التفصيلي
        Button lowStockReportBtn = createDetailedReportButton(
            "⚠️ تقرير الأصناف المنخفضة التفصيلي",
            "عرض الأصناف التي تحتاج إعادة تموين مع التوصيات",
            FontAwesomeIcon.EXCLAMATION_TRIANGLE,
            () -> showDetailedLowStockReport()
        );

        // تقرير تقييم المخزون التفصيلي
        Button valuationReportBtn = createDetailedReportButton(
            "💰 تقرير تقييم المخزون التفصيلي",
            "عرض القيمة الإجمالية للمخزون مع التحليل المالي",
            FontAwesomeIcon.CALCULATOR,
            () -> showDetailedValuationReport()
        );

        reportsBox.getChildren().addAll(itemsReportBtn, movementsReportBtn, warehousesReportBtn,
                                       lowStockReportBtn, valuationReportBtn);

        reportsContent.getChildren().addAll(reportsToolbar, reportsBox);
        return reportsContent;
    }

    /**
     * إنشاء محتوى التحليلات المتقدمة
     */
    private VBox createAdvancedAnalyticsContent() {
        VBox analyticsContent = new VBox(20);
        analyticsContent.setPadding(new Insets(20));

        // العنوان
        Label titleLabel = new Label("🔍 التحليلات المتقدمة والذكية");
        titleLabel.setStyle("-fx-font-size: 18px; -fx-font-weight: bold; -fx-text-fill: #2c3e50;");

        // بطاقات التحليلات الذكية
        HBox analyticsCards = new HBox(20);
        analyticsCards.setAlignment(Pos.CENTER);

        // تحليل الاتجاهات
        VBox trendsCard = createAnalyticsCard(
            "📈 تحليل الاتجاهات",
            "تحليل اتجاهات الاستهلاك والطلب",
            "#3498db",
            () -> showTrendsAnalysis()
        );

        // تحليل الأداء
        VBox performanceCard = createAnalyticsCard(
            "⚡ تحليل الأداء",
            "تحليل أداء المخازن والأصناف",
            "#2ecc71",
            () -> showPerformanceAnalysis()
        );

        // التنبؤات الذكية
        VBox forecastCard = createAnalyticsCard(
            "🔮 التنبؤات الذكية",
            "توقعات الطلب والاحتياجات المستقبلية",
            "#9b59b6",
            () -> showForecastAnalysis()
        );

        // تحليل التكاليف
        VBox costCard = createAnalyticsCard(
            "💸 تحليل التكاليف",
            "تحليل تكاليف التخزين والإدارة",
            "#e74c3c",
            () -> showCostAnalysis()
        );

        analyticsCards.getChildren().addAll(trendsCard, performanceCard, forecastCard, costCard);

        // مؤشرات الأداء الرئيسية (KPIs)
        VBox kpisSection = createKPIsSection();

        // تحليل ABC للأصناف
        VBox abcAnalysis = createABCAnalysisSection();

        analyticsContent.getChildren().addAll(titleLabel, analyticsCards, kpisSection, abcAnalysis);
        return analyticsContent;
    }

    /**
     * إنشاء بطاقة تحليلات
     */
    private VBox createAnalyticsCard(String title, String description, String color, Runnable action) {
        VBox card = new VBox(15);
        card.setAlignment(Pos.CENTER);
        card.setPadding(new Insets(20));
        card.setPrefSize(200, 150);
        card.setStyle("-fx-background-color: white; " +
                     "-fx-border-color: " + color + "; " +
                     "-fx-border-width: 2; " +
                     "-fx-border-radius: 12; " +
                     "-fx-background-radius: 12; " +
                     "-fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.1), 5, 0, 0, 2); " +
                     "-fx-cursor: hand;");

        Label titleLabel = new Label(title);
        titleLabel.setStyle("-fx-font-size: 14px; -fx-font-weight: bold; -fx-text-fill: " + color + ";");
        titleLabel.setWrapText(true);

        Label descLabel = new Label(description);
        descLabel.setStyle("-fx-font-size: 11px; -fx-text-fill: #6c757d; -fx-text-alignment: center;");
        descLabel.setWrapText(true);

        Button actionBtn = new Button("عرض التحليل");
        actionBtn.setStyle("-fx-background-color: " + color + "; -fx-text-fill: white; -fx-font-size: 11px;");
        actionBtn.setOnAction(e -> action.run());

        // تأثيرات التفاعل
        card.setOnMouseEntered(e -> {
            card.setStyle("-fx-background-color: #f8f9fa; " +
                         "-fx-border-color: " + color + "; " +
                         "-fx-border-width: 3; " +
                         "-fx-border-radius: 12; " +
                         "-fx-background-radius: 12; " +
                         "-fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.2), 8, 0, 0, 3); " +
                         "-fx-cursor: hand;");
        });

        card.setOnMouseExited(e -> {
            card.setStyle("-fx-background-color: white; " +
                         "-fx-border-color: " + color + "; " +
                         "-fx-border-width: 2; " +
                         "-fx-border-radius: 12; " +
                         "-fx-background-radius: 12; " +
                         "-fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.1), 5, 0, 0, 2); " +
                         "-fx-cursor: hand;");
        });

        card.getChildren().addAll(titleLabel, descLabel, actionBtn);
        return card;
    }

    /**
     * إنشاء قسم مؤشرات الأداء الرئيسية
     */
    private VBox createKPIsSection() {
        VBox kpisSection = new VBox(15);

        Label kpisTitle = new Label("📊 مؤشرات الأداء الرئيسية (KPIs)");
        kpisTitle.setStyle("-fx-font-size: 16px; -fx-font-weight: bold; -fx-text-fill: #2c3e50;");

        HBox kpisBox = new HBox(20);
        kpisBox.setAlignment(Pos.CENTER);

        // معدل دوران المخزون
        double totalValue = items.stream().mapToDouble(Item::getTotalValue).sum();
        double totalMovementValue = stockMovements.stream()
            .filter(m -> m.getMovementType() == StockMovement.MovementType.OUT)
            .mapToDouble(StockMovement::getTotalValue)
            .sum();
        double turnoverRate = totalValue > 0 ? (totalMovementValue / totalValue) * 100 : 0;

        VBox turnoverKPI = createKPICard("معدل دوران المخزون",
                                        decimalFormat.format(turnoverRate) + "%",
                                        "#3498db");

        // نسبة الأصناف المنخفضة
        long lowStockCount = items.stream().filter(item -> item.getCurrentQuantity() < 10).count();
        double lowStockPercentage = items.size() > 0 ? ((double) lowStockCount / items.size()) * 100 : 0;

        VBox lowStockKPI = createKPICard("نسبة الأصناف المنخفضة",
                                        decimalFormat.format(lowStockPercentage) + "%",
                                        "#e74c3c");

        // متوسط قيمة الصنف
        double avgItemValue = items.stream().mapToDouble(Item::getTotalValue).average().orElse(0);

        VBox avgValueKPI = createKPICard("متوسط قيمة الصنف",
                                        decimalFormat.format(avgItemValue) + " ج.م",
                                        "#2ecc71");

        // عدد الحركات اليومية
        long todayMovements = stockMovements.stream()
            .filter(m -> m.getDateTime().toLocalDate().equals(LocalDate.now()))
            .count();

        VBox dailyMovementsKPI = createKPICard("حركات اليوم",
                                              String.valueOf(todayMovements),
                                              "#f39c12");

        kpisBox.getChildren().addAll(turnoverKPI, lowStockKPI, avgValueKPI, dailyMovementsKPI);

        kpisSection.getChildren().addAll(kpisTitle, kpisBox);
        return kpisSection;
    }

    /**
     * إنشاء بطاقة مؤشر أداء
     */
    private VBox createKPICard(String title, String value, String color) {
        VBox card = new VBox(10);
        card.setAlignment(Pos.CENTER);
        card.setPadding(new Insets(15));
        card.setPrefSize(150, 100);
        card.setStyle("-fx-background-color: white; " +
                     "-fx-border-color: " + color + "; " +
                     "-fx-border-width: 1; " +
                     "-fx-border-radius: 8; " +
                     "-fx-background-radius: 8; " +
                     "-fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.1), 3, 0, 0, 1);");

        Label valueLabel = new Label(value);
        valueLabel.setStyle("-fx-font-size: 18px; -fx-font-weight: bold; -fx-text-fill: " + color + ";");

        Label titleLabel = new Label(title);
        titleLabel.setStyle("-fx-font-size: 11px; -fx-text-fill: #6c757d; -fx-text-alignment: center;");
        titleLabel.setWrapText(true);

        card.getChildren().addAll(valueLabel, titleLabel);
        return card;
    }

    /**
     * إنشاء قسم تحليل ABC
     */
    private VBox createABCAnalysisSection() {
        VBox abcSection = new VBox(15);

        Label abcTitle = new Label("🎯 تحليل ABC للأصناف");
        abcTitle.setStyle("-fx-font-size: 16px; -fx-font-weight: bold; -fx-text-fill: #2c3e50;");

        // تصنيف الأصناف حسب القيمة
        List<Item> sortedItems = items.stream()
            .sorted((a, b) -> Double.compare(b.getTotalValue(), a.getTotalValue()))
            .collect(java.util.stream.Collectors.toList());

        int totalItems = sortedItems.size();
        int aCount = (int) Math.ceil(totalItems * 0.2); // أعلى 20%
        int bCount = (int) Math.ceil(totalItems * 0.3); // التالي 30%
        int cCount = totalItems - aCount - bCount; // الباقي 50%

        HBox abcCards = new HBox(20);
        abcCards.setAlignment(Pos.CENTER);

        // فئة A - عالية القيمة
        VBox categoryA = createABCCategoryCard("فئة A", "عالية القيمة", aCount, "#e74c3c");

        // فئة B - متوسطة القيمة
        VBox categoryB = createABCCategoryCard("فئة B", "متوسطة القيمة", bCount, "#f39c12");

        // فئة C - منخفضة القيمة
        VBox categoryC = createABCCategoryCard("فئة C", "منخفضة القيمة", cCount, "#2ecc71");

        abcCards.getChildren().addAll(categoryA, categoryB, categoryC);

        abcSection.getChildren().addAll(abcTitle, abcCards);
        return abcSection;
    }

    /**
     * إنشاء بطاقة فئة ABC
     */
    private VBox createABCCategoryCard(String category, String description, int count, String color) {
        VBox card = new VBox(10);
        card.setAlignment(Pos.CENTER);
        card.setPadding(new Insets(20));
        card.setPrefSize(180, 120);
        card.setStyle("-fx-background-color: white; " +
                     "-fx-border-color: " + color + "; " +
                     "-fx-border-width: 2; " +
                     "-fx-border-radius: 10; " +
                     "-fx-background-radius: 10; " +
                     "-fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.1), 4, 0, 0, 2);");

        Label categoryLabel = new Label(category);
        categoryLabel.setStyle("-fx-font-size: 16px; -fx-font-weight: bold; -fx-text-fill: " + color + ";");

        Label descLabel = new Label(description);
        descLabel.setStyle("-fx-font-size: 12px; -fx-text-fill: #6c757d;");

        Label countLabel = new Label(count + " صنف");
        countLabel.setStyle("-fx-font-size: 14px; -fx-font-weight: bold; -fx-text-fill: " + color + ";");

        card.getChildren().addAll(categoryLabel, descLabel, countLabel);
        return card;
    }

    /**
     * إنشاء محتوى التقارير المخصصة
     */
    private VBox createCustomReportsContent() {
        VBox customContent = new VBox(20);
        customContent.setPadding(new Insets(20));

        // العنوان
        Label titleLabel = new Label("⚙️ منشئ التقارير المخصصة");
        titleLabel.setStyle("-fx-font-size: 18px; -fx-font-weight: bold; -fx-text-fill: #2c3e50;");

        // نموذج إنشاء التقرير المخصص
        VBox reportBuilder = createReportBuilderForm();

        // قوالب التقارير الجاهزة
        VBox reportTemplates = createReportTemplatesSection();

        customContent.getChildren().addAll(titleLabel, reportBuilder, reportTemplates);
        return customContent;
    }

    /**
     * إنشاء نموذج منشئ التقارير
     */
    private VBox createReportBuilderForm() {
        VBox builderForm = new VBox(15);
        builderForm.setPadding(new Insets(20));
        builderForm.setStyle("-fx-background-color: white; " +
                           "-fx-border-color: #e9ecef; " +
                           "-fx-border-width: 1; " +
                           "-fx-border-radius: 8; " +
                           "-fx-background-radius: 8;");

        Label formTitle = new Label("🔧 إنشاء تقرير مخصص");
        formTitle.setStyle("-fx-font-size: 16px; -fx-font-weight: bold; -fx-text-fill: #495057;");

        // الصف الأول - اختيار نوع التقرير
        HBox firstRow = new HBox(15);
        firstRow.setAlignment(Pos.CENTER_LEFT);

        Label reportTypeLabel = new Label("نوع التقرير:");
        ComboBox<String> reportTypeCombo = new ComboBox<>();
        reportTypeCombo.getItems().addAll("تقرير الأصناف", "تقرير الحركات", "تقرير المخازن", "تقرير مالي");
        reportTypeCombo.setPrefWidth(200);

        Label dateRangeLabel = new Label("الفترة الزمنية:");
        DatePicker fromDate = new DatePicker(LocalDate.now().minusMonths(1));
        DatePicker toDate = new DatePicker(LocalDate.now());

        firstRow.getChildren().addAll(reportTypeLabel, reportTypeCombo,
                                     new Separator(), dateRangeLabel, fromDate, new Label("إلى"), toDate);

        // الصف الثاني - الفلاتر
        HBox secondRow = new HBox(15);
        secondRow.setAlignment(Pos.CENTER_LEFT);

        Label warehouseLabel = new Label("المخزن:");
        ComboBox<Warehouse> warehouseCombo = new ComboBox<>();
        warehouseCombo.getItems().add(null); // جميع المخازن
        warehouseCombo.getItems().addAll(warehouses);
        warehouseCombo.setPrefWidth(200);

        Label categoryLabel = new Label("الفئة:");
        ComboBox<String> categoryCombo = new ComboBox<>();
        categoryCombo.getItems().add(null); // جميع الفئات
        categoryCombo.getItems().addAll(items.stream()
            .map(Item::getCategory)
            .distinct()
            .sorted()
            .collect(java.util.stream.Collectors.toList()));
        categoryCombo.setPrefWidth(150);

        secondRow.getChildren().addAll(warehouseLabel, warehouseCombo,
                                      new Separator(), categoryLabel, categoryCombo);

        // الصف الثالث - خيارات التقرير
        HBox thirdRow = new HBox(15);
        thirdRow.setAlignment(Pos.CENTER_LEFT);

        CheckBox includeChartsCheck = new CheckBox("تضمين الرسوم البيانية");
        includeChartsCheck.setSelected(true);

        CheckBox includeDetailsCheck = new CheckBox("تضمين التفاصيل");
        includeDetailsCheck.setSelected(true);

        CheckBox includeSummaryCheck = new CheckBox("تضمين الملخص");
        includeSummaryCheck.setSelected(true);

        thirdRow.getChildren().addAll(includeChartsCheck, includeDetailsCheck, includeSummaryCheck);

        // أزرار العمل
        HBox actionButtons = new HBox(10);
        actionButtons.setAlignment(Pos.CENTER_RIGHT);

        Button generateBtn = new Button("إنشاء التقرير");
        FontAwesomeIconView generateIcon = new FontAwesomeIconView(FontAwesomeIcon.PLAY);
        generateIcon.setSize("12px");
        generateBtn.setGraphic(generateIcon);
        generateBtn.setStyle("-fx-background-color: #007bff; -fx-text-fill: white;");
        generateBtn.setOnAction(e -> generateCustomReport(reportTypeCombo, fromDate, toDate,
                                                         warehouseCombo, categoryCombo,
                                                         includeChartsCheck, includeDetailsCheck, includeSummaryCheck));

        Button previewBtn = new Button("معاينة");
        FontAwesomeIconView previewIcon = new FontAwesomeIconView(FontAwesomeIcon.EYE);
        previewIcon.setSize("12px");
        previewBtn.setGraphic(previewIcon);
        previewBtn.setStyle("-fx-background-color: #28a745; -fx-text-fill: white;");

        Button saveTemplateBtn = new Button("حفظ كقالب");
        FontAwesomeIconView saveIcon = new FontAwesomeIconView(FontAwesomeIcon.SAVE);
        saveIcon.setSize("12px");
        saveTemplateBtn.setGraphic(saveIcon);
        saveTemplateBtn.setStyle("-fx-background-color: #17a2b8; -fx-text-fill: white;");

        actionButtons.getChildren().addAll(generateBtn, previewBtn, saveTemplateBtn);

        builderForm.getChildren().addAll(formTitle, firstRow, secondRow, thirdRow, actionButtons);
        return builderForm;
    }

    /**
     * إنشاء قسم قوالب التقارير
     */
    private VBox createReportTemplatesSection() {
        VBox templatesSection = new VBox(15);

        Label templatesTitle = new Label("📋 قوالب التقارير الجاهزة");
        templatesTitle.setStyle("-fx-font-size: 16px; -fx-font-weight: bold; -fx-text-fill: #2c3e50;");

        HBox templatesBox = new HBox(20);
        templatesBox.setAlignment(Pos.CENTER);

        // قالب التقرير الشهري
        VBox monthlyTemplate = createReportTemplate(
            "📅 التقرير الشهري",
            "تقرير شامل لحركة المخزون خلال الشهر",
            "#3498db",
            () -> generateMonthlyReport()
        );

        // قالب تقرير الجرد
        VBox inventoryTemplate = createReportTemplate(
            "📊 تقرير الجرد",
            "تقرير جرد شامل لجميع الأصناف والمخازن",
            "#2ecc71",
            () -> generateInventoryReport()
        );

        // قالب تقرير الأداء
        VBox performanceTemplate = createReportTemplate(
            "⚡ تقرير الأداء",
            "تقرير أداء المخازن ومؤشرات الكفاءة",
            "#e74c3c",
            () -> generatePerformanceReport()
        );

        // قالب التقرير المالي
        VBox financialTemplate = createReportTemplate(
            "💰 التقرير المالي",
            "تقرير مالي شامل لقيم المخزون والتكاليف",
            "#f39c12",
            () -> generateFinancialReport()
        );

        templatesBox.getChildren().addAll(monthlyTemplate, inventoryTemplate, performanceTemplate, financialTemplate);

        templatesSection.getChildren().addAll(templatesTitle, templatesBox);
        return templatesSection;
    }

    /**
     * إنشاء قالب تقرير
     */
    private VBox createReportTemplate(String title, String description, String color, Runnable action) {
        VBox template = new VBox(15);
        template.setAlignment(Pos.CENTER);
        template.setPadding(new Insets(20));
        template.setPrefSize(200, 150);
        template.setStyle("-fx-background-color: white; " +
                         "-fx-border-color: " + color + "; " +
                         "-fx-border-width: 2; " +
                         "-fx-border-radius: 12; " +
                         "-fx-background-radius: 12; " +
                         "-fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.1), 5, 0, 0, 2); " +
                         "-fx-cursor: hand;");

        Label titleLabel = new Label(title);
        titleLabel.setStyle("-fx-font-size: 14px; -fx-font-weight: bold; -fx-text-fill: " + color + ";");
        titleLabel.setWrapText(true);

        Label descLabel = new Label(description);
        descLabel.setStyle("-fx-font-size: 11px; -fx-text-fill: #6c757d; -fx-text-alignment: center;");
        descLabel.setWrapText(true);

        Button useTemplateBtn = new Button("استخدام القالب");
        useTemplateBtn.setStyle("-fx-background-color: " + color + "; -fx-text-fill: white; -fx-font-size: 11px;");
        useTemplateBtn.setOnAction(e -> action.run());

        // تأثيرات التفاعل
        template.setOnMouseEntered(e -> {
            template.setStyle("-fx-background-color: #f8f9fa; " +
                             "-fx-border-color: " + color + "; " +
                             "-fx-border-width: 3; " +
                             "-fx-border-radius: 12; " +
                             "-fx-background-radius: 12; " +
                             "-fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.2), 8, 0, 0, 3); " +
                             "-fx-cursor: hand;");
        });

        template.setOnMouseExited(e -> {
            template.setStyle("-fx-background-color: white; " +
                             "-fx-border-color: " + color + "; " +
                             "-fx-border-width: 2; " +
                             "-fx-border-radius: 12; " +
                             "-fx-background-radius: 12; " +
                             "-fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.1), 5, 0, 0, 2); " +
                             "-fx-cursor: hand;");
        });

        template.getChildren().addAll(titleLabel, descLabel, useTemplateBtn);
        return template;
    }

    // ===============================
    // وظائف مساعدة للتقارير والتحليلات
    // ===============================

    /**
     * تحديث الرسوم البيانية
     */
    private void refreshCharts() {
        showSuccessAlert("تم تحديث الرسوم البيانية بنجاح!");
    }

    /**
     * تصدير الرسوم البيانية
     */
    private void exportCharts() {
        showPlaceholderAlert("تصدير الرسوم", "سيتم تصدير الرسوم البيانية كملف PDF");
    }

    /**
     * إنشاء تقرير مخصص
     */
    private void generateCustomReport(ComboBox<String> reportType, DatePicker fromDate, DatePicker toDate,
                                    ComboBox<Warehouse> warehouse, ComboBox<String> category,
                                    CheckBox includeCharts, CheckBox includeDetails, CheckBox includeSummary) {
        String reportInfo = "نوع التقرير: " + reportType.getValue() + "\n" +
                           "الفترة: من " + fromDate.getValue() + " إلى " + toDate.getValue() + "\n" +
                           "المخزن: " + (warehouse.getValue() != null ? warehouse.getValue().getName() : "جميع المخازن") + "\n" +
                           "الفئة: " + (category.getValue() != null ? category.getValue() : "جميع الفئات") + "\n" +
                           "تضمين الرسوم: " + (includeCharts.isSelected() ? "نعم" : "لا") + "\n" +
                           "تضمين التفاصيل: " + (includeDetails.isSelected() ? "نعم" : "لا") + "\n" +
                           "تضمين الملخص: " + (includeSummary.isSelected() ? "نعم" : "لا");

        showPlaceholderAlert("إنشاء تقرير مخصص", "تم إنشاء التقرير بالمواصفات التالية:\n\n" + reportInfo);
    }

    // وظائف قوالب التقارير
    private void generateMonthlyReport() {
        showPlaceholderAlert("التقرير الشهري", "سيتم إنشاء التقرير الشهري الشامل");
    }

    private void generateInventoryReport() {
        showPlaceholderAlert("تقرير الجرد", "سيتم إنشاء تقرير الجرد الشامل");
    }

    private void generatePerformanceReport() {
        showPlaceholderAlert("تقرير الأداء", "سيتم إنشاء تقرير أداء المخازن");
    }

    private void generateFinancialReport() {
        showPlaceholderAlert("التقرير المالي", "سيتم إنشاء التقرير المالي الشامل");
    }

    // وظائف التحليلات المتقدمة
    private void showTrendsAnalysis() {
        showPlaceholderAlert("تحليل الاتجاهات", "سيتم عرض تحليل اتجاهات الاستهلاك والطلب");
    }

    private void showPerformanceAnalysis() {
        showPlaceholderAlert("تحليل الأداء", "سيتم عرض تحليل أداء المخازن والأصناف");
    }

    private void showForecastAnalysis() {
        showPlaceholderAlert("التنبؤات الذكية", "سيتم عرض توقعات الطلب والاحتياجات المستقبلية");
    }

    private void showCostAnalysis() {
        showPlaceholderAlert("تحليل التكاليف", "سيتم عرض تحليل تكاليف التخزين والإدارة");
    }

    // ===============================
    // وظائف إدارة الأصناف المحسنة
    // ===============================

    /**
     * تعديل الصنف المحدد
     */
    private void editSelectedItem() {
        Item selectedItem = itemsTable.getSelectionModel().getSelectedItem();
        if (selectedItem != null) {
            InventoryDialogs dialogs = new InventoryDialogs();
            dialogs.showItemDialog(selectedItem, warehouses, items);
            refreshItemsTable();
        } else {
            showErrorAlert("يرجى اختيار صنف للتعديل");
        }
    }

    /**
     * حذف الصنف المحدد
     */
    private void deleteSelectedItem() {
        Item selectedItem = itemsTable.getSelectionModel().getSelectedItem();
        if (selectedItem != null) {
            Alert confirmAlert = new Alert(Alert.AlertType.CONFIRMATION);
            confirmAlert.setTitle("تأكيد الحذف");
            confirmAlert.setHeaderText("حذف الصنف");
            confirmAlert.setContentText("هل أنت متأكد من حذف الصنف: " + selectedItem.getName() + "؟");

            confirmAlert.showAndWait().ifPresent(response -> {
                if (response == ButtonType.OK) {
                    items.remove(selectedItem);
                    refreshItemsTable();
                    showSuccessAlert("تم حذف الصنف بنجاح");
                }
            });
        } else {
            showErrorAlert("يرجى اختيار صنف للحذف");
        }
    }

    /**
     * تصدير الأصناف
     */
    private void exportItems() {
        showPlaceholderAlert("تصدير الأصناف", "سيتم تصدير قائمة الأصناف إلى ملف Excel");
    }

    /**
     * طباعة الأصناف
     */
    private void printItems() {
        showPlaceholderAlert("طباعة الأصناف", "سيتم طباعة قائمة الأصناف");
    }

    /**
     * تحديث جدول الأصناف
     */
    private void refreshItemsTable() {
        if (itemsTable != null) {
            itemsTable.refresh();
            applyAdvancedFilters();
        }
    }

    /**
     * تبديل وضع العرض
     */
    private void toggleViewMode() {
        showPlaceholderAlert("وضع العرض", "سيتم تبديل وضع العرض بين المفصل والمبسط");
    }

    /**
     * عرض الرسم البياني للأصناف
     */
    private void showItemsChart() {
        showPlaceholderAlert("الرسم البياني", "سيتم عرض الرسم البياني للأصناف");
    }

    /**
     * تطبيق الفلاتر المتقدمة
     */
    private void applyAdvancedFilters() {
        if (filteredItems == null) return;

        filteredItems.setPredicate(item -> {
            // فلتر البحث النصي
            if (searchField != null && !searchField.getText().trim().isEmpty()) {
                String searchText = searchField.getText().toLowerCase();
                boolean textMatch = item.getName().toLowerCase().contains(searchText) ||
                                  item.getItemId().toLowerCase().contains(searchText) ||
                                  (item.getSupplier() != null && item.getSupplier().toLowerCase().contains(searchText));
                if (!textMatch) return false;
            }

            // فلتر المخزن
            if (warehouseFilter != null && warehouseFilter.getValue() != null) {
                if (!item.getWarehouseId().equals(warehouseFilter.getValue().getWarehouseId())) {
                    return false;
                }
            }

            // فلتر الفئة
            if (categoryFilter != null && categoryFilter.getValue() != null) {
                if (!item.getCategory().equals(categoryFilter.getValue())) {
                    return false;
                }
            }

            return true;
        });

        if (itemsTable != null) {
            itemsTable.setItems(filteredItems);
        }
    }

    /**
     * إعادة تعيين الفلاتر المتقدمة
     */
    private void resetAdvancedFilters() {
        if (searchField != null) searchField.clear();
        if (warehouseFilter != null) warehouseFilter.setValue(null);
        if (categoryFilter != null) categoryFilter.setValue(null);

        applyAdvancedFilters();
        showSuccessAlert("تم إعادة تعيين الفلاتر بنجاح");
    }

    /**
     * إنشاء جدول الأصناف المحسن
     */
    private TableView<Item> createEnhancedItemsTable() {
        TableView<Item> table = new TableView<>();
        table.setItems(filteredItems);

        // تخصيص الجدول
        table.setRowFactory(tv -> {
            TableRow<Item> row = new TableRow<>();
            row.itemProperty().addListener((obs, oldItem, newItem) -> {
                if (newItem != null) {
                    if (newItem.getCurrentQuantity() < 5) {
                        row.setStyle("-fx-background-color: #ffebee;"); // أحمر فاتح للأصناف النادرة
                    } else if (newItem.getCurrentQuantity() < 10) {
                        row.setStyle("-fx-background-color: #fff3e0;"); // برتقالي فاتح للأصناف المنخفضة
                    } else {
                        row.setStyle(""); // عادي
                    }
                }
            });
            return row;
        });

        // الأعمدة المحسنة
        createEnhancedItemsTableColumns(table);

        return table;
    }

    /**
     * إنشاء أعمدة الجدول المحسنة
     */
    private void createEnhancedItemsTableColumns(TableView<Item> table) {
        // العمود: الحالة (مؤشر بصري)
        TableColumn<Item, String> statusCol = new TableColumn<>("الحالة");
        statusCol.setPrefWidth(60);
        statusCol.setCellFactory(col -> new TableCell<Item, String>() {
            @Override
            protected void updateItem(String item, boolean empty) {
                super.updateItem(item, empty);
                if (empty) {
                    setText(null);
                    setGraphic(null);
                } else {
                    Item rowItem = getTableView().getItems().get(getIndex());
                    Label statusLabel = new Label();
                    if (rowItem.getCurrentQuantity() < 5) {
                        statusLabel.setText("🔴");
                        statusLabel.setTooltip(new Tooltip("كمية نادرة"));
                    } else if (rowItem.getCurrentQuantity() < 10) {
                        statusLabel.setText("🟡");
                        statusLabel.setTooltip(new Tooltip("كمية منخفضة"));
                    } else {
                        statusLabel.setText("🟢");
                        statusLabel.setTooltip(new Tooltip("كمية جيدة"));
                    }
                    setGraphic(statusLabel);
                    setText(null);
                }
            }
        });

        // باقي الأعمدة (نفس الأعمدة الموجودة مع تحسينات)
        TableColumn<Item, String> idCol = new TableColumn<>("الكود");
        idCol.setCellValueFactory(new PropertyValueFactory<>("itemId"));
        idCol.setPrefWidth(80);

        TableColumn<Item, String> nameCol = new TableColumn<>("اسم الصنف");
        nameCol.setCellValueFactory(new PropertyValueFactory<>("name"));
        nameCol.setPrefWidth(200);

        TableColumn<Item, String> categoryCol = new TableColumn<>("الفئة");
        categoryCol.setCellValueFactory(new PropertyValueFactory<>("category"));
        categoryCol.setPrefWidth(100);

        TableColumn<Item, String> warehouseCol = new TableColumn<>("المخزن");
        warehouseCol.setCellValueFactory(new PropertyValueFactory<>("warehouseName"));
        warehouseCol.setPrefWidth(120);

        TableColumn<Item, Double> quantityCol = new TableColumn<>("الكمية");
        quantityCol.setCellValueFactory(new PropertyValueFactory<>("currentQuantity"));
        quantityCol.setPrefWidth(80);
        quantityCol.setCellFactory(col -> new TableCell<Item, Double>() {
            @Override
            protected void updateItem(Double item, boolean empty) {
                super.updateItem(item, empty);
                if (empty || item == null) {
                    setText(null);
                    setStyle("");
                } else {
                    Item rowItem = getTableView().getItems().get(getIndex());
                    setText(decimalFormat.format(item) + " " + rowItem.getUnit().getSymbol());

                    if (item < 5) {
                        setStyle("-fx-text-fill: #dc3545; -fx-font-weight: bold;");
                    } else if (item < 10) {
                        setStyle("-fx-text-fill: #fd7e14; -fx-font-weight: bold;");
                    } else {
                        setStyle("-fx-text-fill: #28a745; -fx-font-weight: bold;");
                    }
                }
            }
        });

        TableColumn<Item, Double> priceCol = new TableColumn<>("سعر الوحدة");
        priceCol.setCellValueFactory(new PropertyValueFactory<>("unitPrice"));
        priceCol.setPrefWidth(100);
        priceCol.setCellFactory(col -> new TableCell<Item, Double>() {
            @Override
            protected void updateItem(Double item, boolean empty) {
                super.updateItem(item, empty);
                if (empty || item == null) {
                    setText(null);
                } else {
                    setText(decimalFormat.format(item) + " ج.م");
                }
            }
        });

        TableColumn<Item, Double> totalValueCol = new TableColumn<>("إجمالي القيمة");
        totalValueCol.setCellValueFactory(cellData ->
            new javafx.beans.property.SimpleDoubleProperty(cellData.getValue().getTotalValue()).asObject());
        totalValueCol.setPrefWidth(120);
        totalValueCol.setCellFactory(col -> new TableCell<Item, Double>() {
            @Override
            protected void updateItem(Double item, boolean empty) {
                super.updateItem(item, empty);
                if (empty || item == null) {
                    setText(null);
                } else {
                    setText(decimalFormat.format(item) + " ج.م");
                    setStyle("-fx-text-fill: #007bff; -fx-font-weight: bold;");
                }
            }
        });

        table.getColumns().addAll(statusCol, idCol, nameCol, categoryCol, warehouseCol, quantityCol, priceCol, totalValueCol);
    }

    /**
     * إنشاء محتوى لوحة التحكم
     */
    private VBox createDashboardContent() {
        VBox dashboard = new VBox(20);

        // العنوان
        Label titleLabel = new Label("📊 لوحة التحكم - نظرة عامة على المخزون");
        titleLabel.setStyle("-fx-font-size: 20px; -fx-font-weight: bold; -fx-text-fill: #2c3e50;");

        // بطاقات الإحصائيات الرئيسية
        HBox mainStatsBox = createMainStatsCards();

        // جدول الأصناف المنخفضة
        VBox lowStockSection = createLowStockSection();

        dashboard.getChildren().addAll(titleLabel, mainStatsBox, lowStockSection);
        return dashboard;
    }

    /**
     * إنشاء بطاقات الإحصائيات الرئيسية
     */
    private HBox createMainStatsCards() {
        HBox statsBox = new HBox(20);
        statsBox.setAlignment(Pos.CENTER);

        // إجمالي الأصناف
        VBox totalItemsCard = createMainStatCard("إجمالي الأصناف",
                                                String.valueOf(items.size()),
                                                FontAwesomeIcon.CUBE, "#3498db");

        // إجمالي القيمة
        double totalValue = items.stream().mapToDouble(Item::getTotalValue).sum();
        VBox totalValueCard = createMainStatCard("إجمالي القيمة",
                                                decimalFormat.format(totalValue) + " ج.م",
                                                FontAwesomeIcon.MONEY, "#2ecc71");

        // الأصناف المنخفضة
        long lowStockItems = items.stream().filter(item -> item.getCurrentQuantity() < 10).count();
        VBox lowStockCard = createMainStatCard("أصناف منخفضة",
                                              String.valueOf(lowStockItems),
                                              FontAwesomeIcon.EXCLAMATION_TRIANGLE, "#e74c3c");

        // المخازن النشطة
        VBox warehousesCard = createMainStatCard("المخازن النشطة",
                                                String.valueOf(warehouses.size()),
                                                FontAwesomeIcon.BUILDING, "#f39c12");

        // حركات اليوم
        long todayMovements = stockMovements.stream()
            .filter(m -> m.getDateTime().toLocalDate().equals(LocalDate.now()))
            .count();
        VBox todayMovementsCard = createMainStatCard("حركات اليوم",
                                                    String.valueOf(todayMovements),
                                                    FontAwesomeIcon.EXCHANGE, "#9b59b6");

        statsBox.getChildren().addAll(totalItemsCard, totalValueCard, lowStockCard, warehousesCard, todayMovementsCard);
        return statsBox;
    }

    /**
     * إنشاء بطاقة إحصائية رئيسية
     */
    private VBox createMainStatCard(String title, String value, FontAwesomeIcon icon, String color) {
        VBox card = new VBox(10);
        card.setAlignment(Pos.CENTER);
        card.setPadding(new Insets(20));
        card.setPrefSize(180, 120);
        card.setStyle("-fx-background-color: white; " +
                     "-fx-border-color: " + color + "; " +
                     "-fx-border-width: 2; " +
                     "-fx-border-radius: 12; " +
                     "-fx-background-radius: 12; " +
                     "-fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.1), 5, 0, 0, 2);");

        FontAwesomeIconView iconView = new FontAwesomeIconView(icon);
        iconView.setSize("24px");
        iconView.setStyle("-fx-fill: " + color + ";");

        Label valueLabel = new Label(value);
        valueLabel.setStyle("-fx-font-size: 18px; -fx-font-weight: bold; -fx-text-fill: " + color + ";");

        Label titleLabel = new Label(title);
        titleLabel.setStyle("-fx-font-size: 12px; -fx-text-fill: #6c757d; -fx-text-alignment: center;");
        titleLabel.setWrapText(true);

        card.getChildren().addAll(iconView, valueLabel, titleLabel);
        return card;
    }

    /**
     * إنشاء قسم الأصناف المنخفضة
     */
    private VBox createLowStockSection() {
        VBox section = new VBox(15);

        Label titleLabel = new Label("⚠️ الأصناف المنخفضة (أقل من 10 وحدات)");
        titleLabel.setStyle("-fx-font-size: 16px; -fx-font-weight: bold; -fx-text-fill: #e74c3c;");

        TableView<Item> lowStockTable = new TableView<>();
        lowStockTable.setPrefHeight(200);

        // فلترة الأصناف المنخفضة
        ObservableList<Item> lowStockItems = FXCollections.observableArrayList(
            items.stream().filter(item -> item.getCurrentQuantity() < 10).collect(java.util.stream.Collectors.toList())
        );
        lowStockTable.setItems(lowStockItems);

        // الأعمدة
        TableColumn<Item, String> nameCol = new TableColumn<>("اسم الصنف");
        nameCol.setCellValueFactory(new PropertyValueFactory<>("name"));
        nameCol.setPrefWidth(200);

        TableColumn<Item, Double> quantityCol = new TableColumn<>("الكمية الحالية");
        quantityCol.setCellValueFactory(new PropertyValueFactory<>("currentQuantity"));
        quantityCol.setPrefWidth(120);
        quantityCol.setCellFactory(col -> new TableCell<Item, Double>() {
            @Override
            protected void updateItem(Double item, boolean empty) {
                super.updateItem(item, empty);
                if (empty || item == null) {
                    setText(null);
                    setStyle("");
                } else {
                    Item rowItem = getTableView().getItems().get(getIndex());
                    setText(decimalFormat.format(item) + " " + rowItem.getUnit().getSymbol());
                    setStyle("-fx-text-fill: #e74c3c; -fx-font-weight: bold;");
                }
            }
        });

        TableColumn<Item, String> warehouseCol = new TableColumn<>("المخزن");
        warehouseCol.setCellValueFactory(new PropertyValueFactory<>("warehouseName"));
        warehouseCol.setPrefWidth(150);

        lowStockTable.getColumns().addAll(nameCol, quantityCol, warehouseCol);

        section.getChildren().addAll(titleLabel, lowStockTable);
        return section;
    }

    /**
     * إنشاء شريط الأدوات (الطريقة القديمة - محفوظة للتوافق)
     */
    private HBox createToolbar() {
        HBox toolbar = new HBox(15);
        toolbar.setAlignment(Pos.CENTER_LEFT);
        toolbar.setPadding(new Insets(10));
        toolbar.getStyleClass().add("toolbar");

        // زر إضافة صنف
        Button addItemBtn = createToolbarButton("إضافة صنف", FontAwesomeIcon.PLUS_CIRCLE, this::showAddItemDialog);

        // زر استلام
        Button receiveBtn = createToolbarButton("استلام", FontAwesomeIcon.ARROW_DOWN, this::showReceiveDialog);

        // زر صرف
        Button issueBtn = createToolbarButton("صرف", FontAwesomeIcon.ARROW_UP, this::showIssueDialog);

        // فاصل
        Separator separator = new Separator();
        separator.setOrientation(javafx.geometry.Orientation.VERTICAL);

        // زر التقارير
        Button reportsBtn = createToolbarButton("التقارير", FontAwesomeIcon.BAR_CHART, this::showReports);

        toolbar.getChildren().addAll(addItemBtn, receiveBtn, issueBtn, separator, reportsBtn);
        return toolbar;
    }

    /**
     * إنشاء زر شريط الأدوات
     */
    private Button createToolbarButton(String text, FontAwesomeIcon icon, Runnable action) {
        Button button = new Button(text);
        FontAwesomeIconView iconView = new FontAwesomeIconView(icon);
        iconView.setSize("16px");
        button.setGraphic(iconView);
        button.getStyleClass().add("toolbar-button");
        button.setOnAction(e -> action.run());
        return button;
    }

    /**
     * إنشاء قسم الفلترة والبحث
     */
    private VBox createFilterSection() {
        VBox filterSection = new VBox(10);
        filterSection.setPadding(new Insets(15));
        filterSection.getStyleClass().add("filter-section");

        Label filterLabel = new Label("🔍 البحث والفلترة:");
        filterLabel.getStyleClass().add("section-title");

        HBox filterBox = new HBox(15);
        filterBox.setAlignment(Pos.CENTER_LEFT);

        // فلتر المخزن
        Label warehouseLabel = new Label("المخزن:");
        warehouseFilter = new ComboBox<>();
        warehouseFilter.getItems().add(null); // خيار "الكل"
        warehouseFilter.getItems().addAll(warehouses);
        warehouseFilter.setPromptText("جميع المخازن");
        warehouseFilter.setPrefWidth(150);
        warehouseFilter.setOnAction(e -> applyFilters());

        // فلتر الفئة
        Label categoryLabel = new Label("الفئة:");
        categoryFilter = new ComboBox<>();
        categoryFilter.getItems().addAll("الكل", "زجاج", "ألومنيوم", "أكسسوارات", "مواد كيميائية");
        categoryFilter.setValue("الكل");
        categoryFilter.setPrefWidth(120);
        categoryFilter.setOnAction(e -> applyFilters());

        // حقل البحث
        Label searchLabel = new Label("البحث:");
        searchField = new TextField();
        searchField.setPromptText("ابحث بالاسم أو الكود");
        searchField.setPrefWidth(200);
        searchField.textProperty().addListener((obs, oldVal, newVal) -> applyFilters());

        // زر مسح الفلاتر
        Button clearBtn = new Button("مسح");
        FontAwesomeIconView clearIcon = new FontAwesomeIconView(FontAwesomeIcon.REFRESH);
        clearIcon.setSize("12px");
        clearBtn.setGraphic(clearIcon);
        clearBtn.getStyleClass().add("clear-button");
        clearBtn.setOnAction(e -> clearFilters());

        filterBox.getChildren().addAll(
            warehouseLabel, warehouseFilter,
            categoryLabel, categoryFilter,
            searchLabel, searchField,
            clearBtn
        );

        filterSection.getChildren().addAll(filterLabel, filterBox);
        return filterSection;
    }

    /**
     * تطبيق المرشحات
     */
    private void applyFilters() {
        filteredItems.setPredicate(item -> {
            // فلتر المخزن
            if (warehouseFilter.getValue() != null) {
                if (!item.getWarehouseId().equals(warehouseFilter.getValue().getWarehouseId())) {
                    return false;
                }
            }

            // فلتر الفئة
            if (categoryFilter.getValue() != null && !categoryFilter.getValue().equals("الكل")) {
                if (!item.getCategory().equals(categoryFilter.getValue())) {
                    return false;
                }
            }

            // البحث النصي
            if (searchField.getText() != null && !searchField.getText().isEmpty()) {
                String searchText = searchField.getText().toLowerCase();
                return item.getName().toLowerCase().contains(searchText) ||
                       item.getItemId().toLowerCase().contains(searchText);
            }

            return true;
        });
    }

    /**
     * مسح المرشحات
     */
    private void clearFilters() {
        warehouseFilter.setValue(null);
        categoryFilter.setValue("الكل");
        searchField.clear();
    }

    /**
     * إنشاء التبويبات الرئيسية (مع تبويب التحليلات البيانية)
     */
    private TabPane createMainTabs() {
        TabPane tabPane = new TabPane();
        tabPane.getStyleClass().add("main-tabs");

        // تبويب الأصناف
        Tab itemsTab = new Tab("الأصناف");
        itemsTab.setContent(createItemsTab());
        itemsTab.setClosable(false);

        // تبويب حركة المخزون
        Tab movementsTab = new Tab("حركة المخزون");
        movementsTab.setContent(createMovementsTab());
        movementsTab.setClosable(false);

        // تبويب المخازن
        Tab warehousesTab = new Tab("المخازن");
        warehousesTab.setContent(createWarehousesTab());
        warehousesTab.setClosable(false);

        // تبويب التحليلات البيانية
        Tab chartsTab = new Tab("تحليلات بيانية");
        chartsTab.setContent(createChartsTab());
        chartsTab.setClosable(false);

        tabPane.getTabs().addAll(itemsTab, movementsTab, warehousesTab, chartsTab);
        return tabPane;
    }

    /**
     * إنشاء تبويب الأصناف
     */
    private VBox createItemsTab() {
        VBox container = new VBox(15);
        container.setPadding(new Insets(15));

        // إحصائيات سريعة
        HBox statsBox = createStatsBox();

        // جدول الأصناف - مع ارتفاع مناسب لعرض 8 صفوف
        itemsTable = createItemsTable();

        // تحديد ارتفاع مناسب لعرض 8 صفوف (40 بكسل لكل صف + 35 للهيدر)
        double tableHeight = (8 * 40) + 35; // 355 بكسل
        itemsTable.setPrefHeight(tableHeight);
        itemsTable.setMinHeight(tableHeight);
        itemsTable.setMaxHeight(Double.MAX_VALUE);
        VBox.setVgrow(itemsTable, Priority.ALWAYS);

        container.getChildren().addAll(statsBox, itemsTable);
        return container;
    }

    /**
     * إنشاء صندوق الإحصائيات
     */
    private HBox createStatsBox() {
        HBox statsBox = new HBox(20);
        statsBox.setAlignment(Pos.CENTER);
        statsBox.setPadding(new Insets(15));
        statsBox.getStyleClass().add("stats-box");

        // إجمالي الأصناف
        VBox totalItemsBox = createStatCard("إجمالي الأصناف", String.valueOf(items.size()), "#3498db");

        // إجمالي القيمة
        double totalValue = items.stream().mapToDouble(Item::getTotalValue).sum();
        VBox totalValueBox = createStatCard("إجمالي القيمة", decimalFormat.format(totalValue) + " ج.م", "#2ecc71");

        // الأصناف المنخفضة
        long lowStockItems = items.stream().filter(item -> item.getCurrentQuantity() < 10).count();
        VBox lowStockBox = createStatCard("أصناف منخفضة", String.valueOf(lowStockItems), "#e74c3c");

        // المخازن النشطة
        VBox activeWarehousesBox = createStatCard("المخازن النشطة", String.valueOf(warehouses.size()), "#f39c12");

        statsBox.getChildren().addAll(totalItemsBox, totalValueBox, lowStockBox, activeWarehousesBox);
        return statsBox;
    }

    /**
     * إنشاء بطاقة إحصائية
     */
    private VBox createStatCard(String title, String value, String color) {
        VBox card = new VBox(5);
        card.setAlignment(Pos.CENTER);
        card.setPadding(new Insets(15));
        card.getStyleClass().add("stat-card");
        card.setStyle("-fx-border-color: " + color + "; -fx-border-width: 2;");

        Label titleLabel = new Label(title);
        titleLabel.getStyleClass().add("stat-title");

        Label valueLabel = new Label(value);
        valueLabel.getStyleClass().add("stat-value");
        valueLabel.setStyle("-fx-text-fill: " + color + ";");

        card.getChildren().addAll(titleLabel, valueLabel);
        return card;
    }

    /**
     * إنشاء جدول الأصناف
     */
    private TableView<Item> createItemsTable() {
        TableView<Item> table = new TableView<>();
        table.setItems(filteredItems);

        // تحديد ارتفاع الصفوف
        table.setRowFactory(tv -> {
            TableRow<Item> row = new TableRow<>();
            row.setPrefHeight(40); // ارتفاع كل صف 40 بكسل
            return row;
        });

        table.getStyleClass().add("data-table");

        // العمود: كود الصنف
        TableColumn<Item, String> idCol = new TableColumn<>("كود الصنف");
        idCol.setPrefWidth(100);
        idCol.setCellValueFactory(new PropertyValueFactory<>("itemId"));

        // العمود: اسم الصنف
        TableColumn<Item, String> nameCol = new TableColumn<>("اسم الصنف");
        nameCol.setPrefWidth(200);
        nameCol.setCellValueFactory(new PropertyValueFactory<>("name"));

        // العمود: الفئة
        TableColumn<Item, String> categoryCol = new TableColumn<>("الفئة");
        categoryCol.setPrefWidth(100);
        categoryCol.setCellValueFactory(new PropertyValueFactory<>("category"));

        // العمود: الوحدة
        TableColumn<Item, Unit> unitCol = new TableColumn<>("الوحدة");
        unitCol.setPrefWidth(100);
        unitCol.setCellValueFactory(new PropertyValueFactory<>("unit"));
        unitCol.setCellFactory(col -> new TableCell<Item, Unit>() {
            @Override
            protected void updateItem(Unit item, boolean empty) {
                super.updateItem(item, empty);
                if (empty || item == null) {
                    setText(null);
                } else {
                    setText(item.getArabicName());
                }
            }
        });

        // العمود: نوع الصنف
        TableColumn<Item, String> typeCol = new TableColumn<>("النوع");
        typeCol.setPrefWidth(80);
        typeCol.setCellFactory(col -> new TableCell<Item, String>() {
            @Override
            protected void updateItem(String item, boolean empty) {
                super.updateItem(item, empty);
                if (empty) {
                    setText(null);
                    setStyle("");
                } else {
                    Item rowItem = getTableView().getItems().get(getIndex());
                    if (rowItem.hasDimensions()) {
                        setText("أبعاد");
                        setStyle("-fx-text-fill: #007bff; -fx-font-weight: bold;");
                    } else {
                        setText("عادي");
                        setStyle("-fx-text-fill: #6c757d;");
                    }
                }
            }
        });

        // العمود: الكمية الحالية
        TableColumn<Item, Double> quantityCol = new TableColumn<>("الكمية الحالية");
        quantityCol.setPrefWidth(120);
        quantityCol.setCellValueFactory(new PropertyValueFactory<>("currentQuantity"));
        quantityCol.setCellFactory(col -> new TableCell<Item, Double>() {
            @Override
            protected void updateItem(Double item, boolean empty) {
                super.updateItem(item, empty);
                if (empty || item == null) {
                    setText(null);
                } else {
                    Item rowItem = getTableView().getItems().get(getIndex());
                    String displayText;

                    if (rowItem.hasDimensions()) {
                        // للأصناف ذات الأبعاد: عرض المساحة + تفاصيل الأبعاد
                        displayText = decimalFormat.format(item) + " " + rowItem.getUnit().getSymbol();
                        if (rowItem.getLength() != null && rowItem.getWidth() != null && rowItem.getPieces() != null) {
                            displayText += String.format("\n(%dx%d×%d)",
                                rowItem.getLength().intValue(),
                                rowItem.getWidth().intValue(),
                                rowItem.getPieces());
                        }
                    } else {
                        // للأصناف العادية
                        displayText = decimalFormat.format(item) + " " + rowItem.getUnit().getSymbol();
                    }

                    setText(displayText);

                    // تلوين الكميات المنخفضة
                    if (item < 10) {
                        setStyle("-fx-text-fill: #e74c3c; -fx-font-weight: bold;");
                    } else {
                        setStyle("");
                    }
                }
            }
        });

        // العمود: سعر الوحدة
        TableColumn<Item, Double> priceCol = new TableColumn<>("سعر الوحدة");
        priceCol.setPrefWidth(100);
        priceCol.setCellValueFactory(new PropertyValueFactory<>("unitPrice"));
        priceCol.setCellFactory(col -> new TableCell<Item, Double>() {
            @Override
            protected void updateItem(Double item, boolean empty) {
                super.updateItem(item, empty);
                if (empty || item == null) {
                    setText(null);
                } else {
                    setText(decimalFormat.format(item) + " ج.م");
                }
            }
        });

        // العمود: القيمة الإجمالية
        TableColumn<Item, Double> totalValueCol = new TableColumn<>("القيمة الإجمالية");
        totalValueCol.setPrefWidth(120);
        totalValueCol.setCellValueFactory(new PropertyValueFactory<>("totalValue"));
        totalValueCol.setCellFactory(col -> new TableCell<Item, Double>() {
            @Override
            protected void updateItem(Double item, boolean empty) {
                super.updateItem(item, empty);
                getStyleClass().add("calculated-cell");
                if (empty || item == null) {
                    setText(null);
                } else {
                    setText(decimalFormat.format(item) + " ج.م");
                }
            }
        });

        // العمود: المخزن
        TableColumn<Item, String> warehouseCol = new TableColumn<>("المخزن");
        warehouseCol.setPrefWidth(120);
        warehouseCol.setCellValueFactory(new PropertyValueFactory<>("warehouseName"));

        table.getColumns().addAll(idCol, nameCol, categoryCol, typeCol, unitCol, quantityCol, priceCol, totalValueCol, warehouseCol);
        return table;
    }

    /**
     * إنشاء تبويب حركة المخزون
     */
    private VBox createMovementsTab() {
        VBox container = new VBox(15);
        container.setPadding(new Insets(15));

        // عنوان القسم
        Label titleLabel = new Label("📊 سجل حركة المخزون");
        titleLabel.setStyle("-fx-font-size: 16px; -fx-font-weight: bold; -fx-text-fill: #2c3e50;");

        // جدول حركة المخزون - مع ارتفاع مناسب لعرض 8 صفوف
        TableView<StockMovement> movementsTable = createMovementsTable();

        // تحديد ارتفاع مناسب لعرض 8 صفوف (40 بكسل لكل صف + 35 للهيدر)
        double tableHeight = (8 * 40) + 35; // 355 بكسل
        movementsTable.setPrefHeight(tableHeight);
        movementsTable.setMinHeight(tableHeight);
        movementsTable.setMaxHeight(Double.MAX_VALUE);
        VBox.setVgrow(movementsTable, Priority.ALWAYS);

        container.getChildren().addAll(titleLabel, movementsTable);
        return container;
    }

    /**
     * إنشاء جدول حركة المخزون
     */
    private TableView<StockMovement> createMovementsTable() {
        TableView<StockMovement> table = new TableView<>();
        table.setItems(filteredMovements);

        // تحديد ارتفاع الصفوف
        table.setRowFactory(tv -> {
            TableRow<StockMovement> row = new TableRow<>();
            row.setPrefHeight(40); // ارتفاع كل صف 40 بكسل
            return row;
        });

        table.getStyleClass().add("data-table");

        // العمود: رقم الحركة
        TableColumn<StockMovement, String> idCol = new TableColumn<>("رقم الحركة");
        idCol.setPrefWidth(100);
        idCol.setCellValueFactory(new PropertyValueFactory<>("movementId"));

        // العمود: نوع الحركة
        TableColumn<StockMovement, StockMovement.MovementType> typeCol = new TableColumn<>("نوع الحركة");
        typeCol.setPrefWidth(100);
        typeCol.setCellValueFactory(new PropertyValueFactory<>("movementType"));
        typeCol.setCellFactory(col -> new TableCell<StockMovement, StockMovement.MovementType>() {
            @Override
            protected void updateItem(StockMovement.MovementType item, boolean empty) {
                super.updateItem(item, empty);
                if (empty || item == null) {
                    setText(null);
                    setStyle("");
                } else {
                    setText(item.getArabicName());
                    // تلوين حسب نوع الحركة
                    switch (item) {
                        case IN:
                            setStyle("-fx-text-fill: #27ae60; -fx-font-weight: bold;");
                            break;
                        case OUT:
                            setStyle("-fx-text-fill: #e74c3c; -fx-font-weight: bold;");
                            break;
                        default:
                            setStyle("-fx-text-fill: #f39c12; -fx-font-weight: bold;");
                    }
                }
            }
        });

        // العمود: اسم الصنف
        TableColumn<StockMovement, String> itemCol = new TableColumn<>("الصنف");
        itemCol.setPrefWidth(150);
        itemCol.setCellValueFactory(new PropertyValueFactory<>("itemName"));

        // العمود: الكمية
        TableColumn<StockMovement, Double> quantityCol = new TableColumn<>("الكمية");
        quantityCol.setPrefWidth(100);
        quantityCol.setCellValueFactory(new PropertyValueFactory<>("quantity"));
        quantityCol.setCellFactory(col -> new TableCell<StockMovement, Double>() {
            @Override
            protected void updateItem(Double item, boolean empty) {
                super.updateItem(item, empty);
                if (empty || item == null) {
                    setText(null);
                } else {
                    setText(decimalFormat.format(item));
                }
            }
        });

        // العمود: سعر الوحدة
        TableColumn<StockMovement, Double> priceCol = new TableColumn<>("سعر الوحدة");
        priceCol.setPrefWidth(100);
        priceCol.setCellValueFactory(new PropertyValueFactory<>("unitPrice"));
        priceCol.setCellFactory(col -> new TableCell<StockMovement, Double>() {
            @Override
            protected void updateItem(Double item, boolean empty) {
                super.updateItem(item, empty);
                if (empty || item == null) {
                    setText(null);
                } else {
                    setText(decimalFormat.format(item) + " ج.م");
                }
            }
        });

        // العمود: القيمة الإجمالية
        TableColumn<StockMovement, Double> totalCol = new TableColumn<>("القيمة الإجمالية");
        totalCol.setPrefWidth(120);
        totalCol.setCellValueFactory(new PropertyValueFactory<>("totalValue"));
        totalCol.setCellFactory(col -> new TableCell<StockMovement, Double>() {
            @Override
            protected void updateItem(Double item, boolean empty) {
                super.updateItem(item, empty);
                if (empty || item == null) {
                    setText(null);
                } else {
                    setText(decimalFormat.format(item) + " ج.م");
                }
            }
        });

        // العمود: التاريخ والوقت
        TableColumn<StockMovement, LocalDateTime> dateCol = new TableColumn<>("التاريخ والوقت");
        dateCol.setPrefWidth(120);
        dateCol.setCellValueFactory(new PropertyValueFactory<>("dateTime"));
        dateCol.setCellFactory(col -> new TableCell<StockMovement, LocalDateTime>() {
            @Override
            protected void updateItem(LocalDateTime item, boolean empty) {
                super.updateItem(item, empty);
                if (empty || item == null) {
                    setText(null);
                } else {
                    setText(item.format(dateTimeFormatter));
                }
            }
        });

        // العمود: المستخدم
        TableColumn<StockMovement, String> userCol = new TableColumn<>("المستخدم");
        userCol.setPrefWidth(100);
        userCol.setCellValueFactory(new PropertyValueFactory<>("user"));

        table.getColumns().addAll(idCol, typeCol, itemCol, quantityCol, priceCol, totalCol, dateCol, userCol);
        return table;
    }

    /**
     * إنشاء تبويب المخازن
     */
    private VBox createWarehousesTab() {
        VBox container = new VBox(15);
        container.setPadding(new Insets(15));

        // عنوان القسم
        Label titleLabel = new Label("🏢 قائمة المخازن");
        titleLabel.setStyle("-fx-font-size: 16px; -fx-font-weight: bold; -fx-text-fill: #2c3e50;");

        // جدول المخازن - مع ارتفاع مناسب لعرض 8 صفوف
        TableView<Warehouse> warehousesTable = createWarehousesTable();

        // تحديد ارتفاع مناسب لعرض 8 صفوف (40 بكسل لكل صف + 35 للهيدر)
        double tableHeight = (8 * 40) + 35; // 355 بكسل
        warehousesTable.setPrefHeight(tableHeight);
        warehousesTable.setMinHeight(tableHeight);
        warehousesTable.setMaxHeight(Double.MAX_VALUE);
        VBox.setVgrow(warehousesTable, Priority.ALWAYS);

        container.getChildren().addAll(titleLabel, warehousesTable);
        return container;
    }

    /**
     * إنشاء جدول المخازن
     */
    private TableView<Warehouse> createWarehousesTable() {
        TableView<Warehouse> table = new TableView<>();
        table.setItems(warehouses);

        // تحديد ارتفاع الصفوف
        table.setRowFactory(tv -> {
            TableRow<Warehouse> row = new TableRow<>();
            row.setPrefHeight(40); // ارتفاع كل صف 40 بكسل
            return row;
        });

        table.getStyleClass().add("data-table");

        // العمود: كود المخزن
        TableColumn<Warehouse, String> idCol = new TableColumn<>("كود المخزن");
        idCol.setPrefWidth(100);
        idCol.setCellValueFactory(new PropertyValueFactory<>("warehouseId"));

        // العمود: اسم المخزن
        TableColumn<Warehouse, String> nameCol = new TableColumn<>("اسم المخزن");
        nameCol.setPrefWidth(200);
        nameCol.setCellValueFactory(new PropertyValueFactory<>("name"));

        // العمود: الموقع
        TableColumn<Warehouse, String> locationCol = new TableColumn<>("الموقع");
        locationCol.setPrefWidth(150);
        locationCol.setCellValueFactory(new PropertyValueFactory<>("location"));

        // العمود: عدد الأصناف
        TableColumn<Warehouse, Integer> itemsCountCol = new TableColumn<>("عدد الأصناف");
        itemsCountCol.setPrefWidth(100);
        itemsCountCol.setCellFactory(col -> new TableCell<Warehouse, Integer>() {
            @Override
            protected void updateItem(Integer item, boolean empty) {
                super.updateItem(item, empty);
                if (empty) {
                    setText(null);
                } else {
                    Warehouse warehouse = getTableView().getItems().get(getIndex());
                    long itemsCount = items.stream()
                        .filter(i -> i.getWarehouseId().equals(warehouse.getWarehouseId()))
                        .count();
                    setText(String.valueOf(itemsCount));
                    setStyle("-fx-text-fill: #007bff; -fx-font-weight: bold;");
                }
            }
        });

        // العمود: إجمالي القيمة
        TableColumn<Warehouse, Double> totalValueCol = new TableColumn<>("إجمالي القيمة");
        totalValueCol.setPrefWidth(120);
        totalValueCol.setCellFactory(col -> new TableCell<Warehouse, Double>() {
            @Override
            protected void updateItem(Double item, boolean empty) {
                super.updateItem(item, empty);
                if (empty) {
                    setText(null);
                } else {
                    Warehouse warehouse = getTableView().getItems().get(getIndex());
                    double totalValue = items.stream()
                        .filter(i -> i.getWarehouseId().equals(warehouse.getWarehouseId()))
                        .mapToDouble(Item::getTotalValue)
                        .sum();
                    setText(decimalFormat.format(totalValue) + " ج.م");
                    setStyle("-fx-text-fill: #28a745; -fx-font-weight: bold;");
                }
            }
        });

        // العمود: الوصف
        TableColumn<Warehouse, String> descriptionCol = new TableColumn<>("الوصف");
        descriptionCol.setPrefWidth(200);
        descriptionCol.setCellValueFactory(new PropertyValueFactory<>("description"));

        table.getColumns().addAll(idCol, nameCol, locationCol, itemsCountCol, totalValueCol, descriptionCol);
        return table;
    }

    /**
     * إنشاء تبويب التحليلات البيانية
     */
    private VBox createChartsTab() {
        VBox container = new VBox(15);
        container.setPadding(new Insets(15));

        Label titleLabel = new Label("📈 تحليلات بيانية للمخزون");
        titleLabel.setStyle("-fx-font-size: 16px; -fx-font-weight: bold; -fx-text-fill: #2c3e50;");

        // رسم بياني: توزيع الأصناف حسب الفئة
        javafx.scene.chart.PieChart categoryPieChart = new javafx.scene.chart.PieChart();
        categoryPieChart.setTitle("توزيع الأصناف حسب الفئة");
        java.util.Map<String, Long> categoryCounts = items.stream().collect(java.util.stream.Collectors.groupingBy(Item::getCategory, java.util.stream.Collectors.counting()));
        for (var entry : categoryCounts.entrySet()) {
            categoryPieChart.getData().add(new javafx.scene.chart.PieChart.Data(entry.getKey(), entry.getValue()));
        }

        // رسم بياني: الكميات حسب المخزن
        javafx.scene.chart.BarChart<String, Number> warehouseBarChart = new javafx.scene.chart.BarChart<>(
            new javafx.scene.chart.CategoryAxis(), new javafx.scene.chart.NumberAxis()
        );
        warehouseBarChart.setTitle("إجمالي الكميات حسب المخزن");
        javafx.scene.chart.XYChart.Series<String, Number> series = new javafx.scene.chart.XYChart.Series<>();
        for (Warehouse wh : warehouses) {
            double totalQty = items.stream().filter(i -> i.getWarehouseId().equals(wh.getWarehouseId())).mapToDouble(Item::getCurrentQuantity).sum();
            series.getData().add(new javafx.scene.chart.XYChart.Data<>(wh.getName(), totalQty));
        }
        warehouseBarChart.getData().add(series);

        // جعل الرسوم البيانية قابلة للسحب والتكبير
        ScrollPane chartScroll = new ScrollPane(new VBox(20, categoryPieChart, warehouseBarChart));
        chartScroll.setFitToWidth(true);
        chartScroll.setPrefHeight(500);

        container.getChildren().addAll(titleLabel, chartScroll);
        return container;
    }

    // وظائف الأزرار - النوافذ الفعلية
    /**
     * إظهار نافذة إضافة صنف جديد
     */
    private void showAddItemDialog() {
        InventoryDialogs dialogs = new InventoryDialogs();
        dialogs.showItemDialog(null, warehouses, items);

        // تحديث الجدول بعد الإضافة
        if (itemsTable != null) {
            itemsTable.refresh();
        }
    }

    /**
     * إظهار نافذة استلام أصناف
     */
    private void showReceiveDialog() {
        showMovementDialog(StockMovement.MovementType.IN);
    }

    /**
     * إظهار نافذة صرف أصناف
     */
    private void showIssueDialog() {
        showMovementDialog(StockMovement.MovementType.OUT);
    }

    /**
     * نافذة حركة المخزون (استلام/صرف)
     */
    private void showMovementDialog(StockMovement.MovementType movementType) {
        Stage dialog = new Stage();
        dialog.setTitle(movementType.getArabicName() + " أصناف");
        dialog.initModality(Modality.APPLICATION_MODAL);
        dialog.setWidth(600);
        dialog.setHeight(500);

        VBox mainLayout = new VBox(20);
        mainLayout.setPadding(new Insets(20));

        // العنوان
        Label titleLabel = new Label(movementType.getArabicName() + " أصناف");
        titleLabel.setStyle("-fx-font-size: 18px; -fx-font-weight: bold; -fx-text-fill: #2c3e50;");

        // نموذج البيانات
        GridPane form = new GridPane();
        form.setHgap(15);
        form.setVgap(15);

        // اختيار المخزن
        Label warehouseLabel = new Label("المخزن:");
        ComboBox<Warehouse> warehouseCombo = new ComboBox<>();
        warehouseCombo.setItems(warehouses);
        warehouseCombo.setPrefWidth(200);
        warehouseCombo.setPromptText("اختر المخزن");

        // اختيار الصنف
        Label itemLabel = new Label("الصنف:");
        ComboBox<Item> itemCombo = new ComboBox<>();
        itemCombo.setPrefWidth(200);
        itemCombo.setPromptText("اختر المخزن أولاً");
        itemCombo.setDisable(true);

        // تحديث قائمة الأصناف عند اختيار المخزن
        warehouseCombo.setOnAction(e -> {
            if (warehouseCombo.getValue() != null) {
                itemCombo.getItems().clear();
                items.stream()
                    .filter(item -> item.getWarehouseId().equals(warehouseCombo.getValue().getWarehouseId()))
                    .forEach(item -> itemCombo.getItems().add(item));
                itemCombo.setDisable(false);
                itemCombo.setPromptText("اختر الصنف");
            }
        });

        // الكمية
        Label quantityLabel = new Label("الكمية:");
        TextField quantityField = new TextField();
        quantityField.setPromptText("0.00");

        // سعر الوحدة
        Label priceLabel = new Label("سعر الوحدة:");
        TextField priceField = new TextField();
        priceField.setPromptText("0.00");

        // تحديث سعر الوحدة عند اختيار الصنف
        itemCombo.setOnAction(e -> {
            if (itemCombo.getValue() != null) {
                priceField.setText(String.valueOf(itemCombo.getValue().getUnitPrice()));
            }
        });

        // المرجع
        Label referenceLabel = new Label("المرجع:");
        TextField referenceField = new TextField();
        referenceField.setPromptText("رقم الفاتورة أو أمر العمل");

        // المستخدم
        Label userLabel = new Label("المستخدم:");
        TextField userField = new TextField();
        userField.setText("المستخدم الحالي");

        // الملاحظات
        Label notesLabel = new Label("الملاحظات:");
        TextArea notesArea = new TextArea();
        notesArea.setPromptText("ملاحظات اختيارية");
        notesArea.setPrefRowCount(3);

        // ترتيب الحقول
        form.add(warehouseLabel, 0, 0);
        form.add(warehouseCombo, 1, 0);
        form.add(itemLabel, 0, 1);
        form.add(itemCombo, 1, 1);
        form.add(quantityLabel, 0, 2);
        form.add(quantityField, 1, 2);
        form.add(priceLabel, 0, 3);
        form.add(priceField, 1, 3);
        form.add(referenceLabel, 0, 4);
        form.add(referenceField, 1, 4);
        form.add(userLabel, 0, 5);
        form.add(userField, 1, 5);
        form.add(notesLabel, 0, 6);
        form.add(notesArea, 1, 6);

        // أزرار الحوار
        HBox buttonBox = new HBox(10);
        buttonBox.setAlignment(Pos.CENTER_RIGHT);

        Button saveButton = new Button("تنفيذ " + movementType.getArabicName());
        saveButton.setStyle("-fx-background-color: #28a745; -fx-text-fill: white; -fx-font-weight: bold;");
        saveButton.setOnAction(e -> {
            if (validateMovementForm(warehouseCombo, itemCombo, quantityField, priceField)) {
                executeMovement(movementType, warehouseCombo.getValue(), itemCombo.getValue(),
                              quantityField, priceField, referenceField, userField, notesArea);
                dialog.close();
            }
        });

        Button cancelButton = new Button("إلغاء");
        cancelButton.setOnAction(e -> dialog.close());

        buttonBox.getChildren().addAll(saveButton, cancelButton);

        mainLayout.getChildren().addAll(titleLabel, form, buttonBox);

        Scene scene = new Scene(mainLayout);
        dialog.setScene(scene);
        dialog.showAndWait();
    }

    /**
     * التحقق من صحة نموذج الحركة
     */
    private boolean validateMovementForm(ComboBox<Warehouse> warehouseCombo, ComboBox<Item> itemCombo,
                                       TextField quantityField, TextField priceField) {
        if (warehouseCombo.getValue() == null) {
            showErrorAlert("يرجى اختيار المخزن");
            return false;
        }

        if (itemCombo.getValue() == null) {
            showErrorAlert("يرجى اختيار الصنف");
            return false;
        }

        if (quantityField.getText().trim().isEmpty()) {
            showErrorAlert("يرجى إدخال الكمية");
            return false;
        }

        if (priceField.getText().trim().isEmpty()) {
            showErrorAlert("يرجى إدخال سعر الوحدة");
            return false;
        }

        try {
            double quantity = Double.parseDouble(quantityField.getText().trim());
            double price = Double.parseDouble(priceField.getText().trim());

            if (quantity <= 0) {
                showErrorAlert("يرجى إدخال كمية أكبر من صفر");
                return false;
            }

            if (price < 0) {
                showErrorAlert("يرجى إدخال سعر صحيح");
                return false;
            }
        } catch (NumberFormatException e) {
            showErrorAlert("يرجى إدخال قيم صحيحة للأرقام");
            return false;
        }

        return true;
    }

    /**
     * تنفيذ حركة المخزون
     */
    private void executeMovement(StockMovement.MovementType movementType, Warehouse warehouse, Item item,
                               TextField quantityField, TextField priceField, TextField referenceField,
                               TextField userField, TextArea notesArea) {
        try {
            double quantity = Double.parseDouble(quantityField.getText().trim());
            double price = Double.parseDouble(priceField.getText().trim());

            // التحقق من الكمية المتاحة في حالة الصرف
            if (movementType == StockMovement.MovementType.OUT) {
                if (quantity > item.getCurrentQuantity()) {
                    showErrorAlert("الكمية المطلوبة (" + decimalFormat.format(quantity) +
                                 ") أكبر من الكمية المتاحة (" + decimalFormat.format(item.getCurrentQuantity()) + ")");
                    return;
                }
            }

            // إنشاء حركة جديدة
            String movementId = "MOV" + String.format("%03d", stockMovements.size() + 1);
            StockMovement movement = new StockMovement(
                movementId,
                item.getItemId(),
                item.getName(),
                warehouse.getWarehouseId(),
                movementType,
                quantity,
                price
            );

            movement.setWarehouseName(warehouse.getName());
            movement.setUser(userField.getText().trim());
            movement.setReference(referenceField.getText().trim());
            movement.setNotes(notesArea.getText().trim());

            // تحديث كمية الصنف
            double newQuantity;
            if (movementType == StockMovement.MovementType.IN) {
                newQuantity = item.getCurrentQuantity() + quantity;
            } else {
                newQuantity = item.getCurrentQuantity() - quantity;
            }

            item.setCurrentQuantity(newQuantity);
            movement.setBalanceAfter(newQuantity);

            // تحديث متوسط السعر (في حالة الاستلام)
            if (movementType == StockMovement.MovementType.IN) {
                double totalValue = (item.getCurrentQuantity() - quantity) * item.getUnitPrice() + quantity * price;
                if (item.getCurrentQuantity() > 0) {
                    item.setUnitPrice(totalValue / item.getCurrentQuantity());
                }
            }

            stockMovements.add(movement);

            showSuccessAlert("تم تنفيذ " + movementType.getArabicName() + " بنجاح\n" +
                           "الصنف: " + item.getName() + "\n" +
                           "الكمية: " + decimalFormat.format(quantity) + " " + item.getUnit().getSymbol() + "\n" +
                           "الرصيد الجديد: " + decimalFormat.format(newQuantity) + " " + item.getUnit().getSymbol());

            // تحديث الجداول
            refreshTables();

        } catch (NumberFormatException e) {
            showErrorAlert("خطأ في البيانات المدخلة");
        }
    }

    /**
     * تحديث جميع الجداول
     */
    private void refreshTables() {
        if (itemsTable != null) {
            itemsTable.refresh();
        }
    }

    /**
     * إظهار نافذة التقارير
     */
    private void showReports() {
        showInventoryReportsDialog();
    }

    /**
     * نافذة تقارير المخزون
     */
    private void showInventoryReportsDialog() {
        Stage dialog = new Stage();
        dialog.setTitle("تقارير المخزون");
        dialog.initModality(Modality.APPLICATION_MODAL);
        dialog.setWidth(700);
        dialog.setHeight(600);

        VBox mainLayout = new VBox(20);
        mainLayout.setPadding(new Insets(20));

        // العنوان
        Label titleLabel = new Label("📊 تقارير المخزون");
        titleLabel.setStyle("-fx-font-size: 18px; -fx-font-weight: bold; -fx-text-fill: #2c3e50;");

        // قائمة التقارير
        VBox reportsBox = new VBox(15);

        // تقرير الأصناف
        Button itemsReportBtn = createReportButton("تقرير الأصناف", "عرض جميع الأصناف مع الكميات والقيم",
                                                  FontAwesomeIcon.CUBE, this::showItemsReport);

        // تقرير حركة المخزون
        Button movementsReportBtn = createReportButton("تقرير حركة المخزون", "عرض جميع حركات الاستلام والصرف",
                                                      FontAwesomeIcon.EXCHANGE, this::showMovementsReport);

        // تقرير المخازن
        Button warehousesReportBtn = createReportButton("تقرير المخازن", "عرض تفاصيل جميع المخازن",
                                                       FontAwesomeIcon.BUILDING, this::showWarehousesReport);

        // تقرير الأصناف المنخفضة
        Button lowStockReportBtn = createReportButton("تقرير الأصناف المنخفضة", "عرض الأصناف التي تحتاج إعادة تموين",
                                                     FontAwesomeIcon.EXCLAMATION_TRIANGLE, this::showLowStockReport);

        // تقرير تقييم المخزون
        Button valuationReportBtn = createReportButton("تقرير تقييم المخزون", "عرض القيمة الإجمالية للمخزون",
                                                      FontAwesomeIcon.CALCULATOR, this::showValuationReport);

        reportsBox.getChildren().addAll(itemsReportBtn, movementsReportBtn, warehousesReportBtn,
                                       lowStockReportBtn, valuationReportBtn);

        // زر الإغلاق
        Button closeButton = new Button("إغلاق");
        closeButton.setStyle("-fx-background-color: #6c757d; -fx-text-fill: white; -fx-font-weight: bold;");
        closeButton.setOnAction(e -> dialog.close());

        HBox buttonBox = new HBox();
        buttonBox.setAlignment(Pos.CENTER);
        buttonBox.getChildren().add(closeButton);

        mainLayout.getChildren().addAll(titleLabel, reportsBox, buttonBox);

        Scene scene = new Scene(mainLayout);
        dialog.setScene(scene);
        dialog.showAndWait();
    }

    /**
     * إنشاء زر تقرير
     */
    private Button createReportButton(String title, String description, FontAwesomeIcon icon, Runnable action) {
        VBox buttonContent = new VBox(5);
        buttonContent.setAlignment(Pos.CENTER_LEFT);
        buttonContent.setPadding(new Insets(15));

        HBox titleBox = new HBox(10);
        titleBox.setAlignment(Pos.CENTER_LEFT);

        FontAwesomeIconView iconView = new FontAwesomeIconView(icon);
        iconView.setSize("20px");
        iconView.setStyle("-fx-fill: #007bff;");

        Label titleLabel = new Label(title);
        titleLabel.setStyle("-fx-font-size: 14px; -fx-font-weight: bold; -fx-text-fill: #2c3e50;");

        titleBox.getChildren().addAll(iconView, titleLabel);

        Label descLabel = new Label(description);
        descLabel.setStyle("-fx-font-size: 12px; -fx-text-fill: #6c757d;");

        buttonContent.getChildren().addAll(titleBox, descLabel);

        Button button = new Button();
        button.setGraphic(buttonContent);
        button.setPrefWidth(600);
        button.setStyle("-fx-background-color: white; -fx-border-color: #dee2e6; -fx-border-width: 1; " +
                       "-fx-border-radius: 8; -fx-background-radius: 8; -fx-cursor: hand;");

        button.setOnMouseEntered(e -> button.setStyle("-fx-background-color: #f8f9fa; -fx-border-color: #007bff; " +
                                                     "-fx-border-width: 2; -fx-border-radius: 8; -fx-background-radius: 8; -fx-cursor: hand;"));
        button.setOnMouseExited(e -> button.setStyle("-fx-background-color: white; -fx-border-color: #dee2e6; " +
                                                    "-fx-border-width: 1; -fx-border-radius: 8; -fx-background-radius: 8; -fx-cursor: hand;"));

        button.setOnAction(e -> action.run());
        return button;
    }

    // وظائف التقارير
    private void showItemsReport() {
        showPlaceholderDialog("تقرير الأصناف", "سيتم عرض تقرير مفصل بجميع الأصناف مع الكميات والقيم");
    }

    private void showMovementsReport() {
        showPlaceholderDialog("تقرير حركة المخزون", "سيتم عرض تقرير مفصل بجميع حركات الاستلام والصرف");
    }

    private void showWarehousesReport() {
        showPlaceholderDialog("تقرير المخازن", "سيتم عرض تقرير مفصل بجميع المخازن وأصنافها");
    }

    private void showLowStockReport() {
        long lowStockCount = items.stream().filter(item -> item.getCurrentQuantity() < 10).count();
        showPlaceholderDialog("تقرير الأصناف المنخفضة",
                            "عدد الأصناف المنخفضة: " + lowStockCount + "\n\nسيتم عرض تقرير مفصل بالأصناف التي تحتاج إعادة تموين");
    }

    private void showValuationReport() {
        double totalValue = items.stream().mapToDouble(Item::getTotalValue).sum();
        showPlaceholderDialog("تقرير تقييم المخزون",
                            "إجمالي قيمة المخزون: " + decimalFormat.format(totalValue) + " ج.م\n\nسيتم عرض تقرير مفصل بتقييم المخزون");
    }

    // رسائل التنبيه
    private void showSuccessAlert(String message) {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle("نجح");
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }

    private void showErrorAlert(String message) {
        Alert alert = new Alert(Alert.AlertType.ERROR);
        alert.setTitle("خطأ");
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }

    private void showPlaceholderDialog(String title, String message) {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle(title);
        alert.setHeaderText(null);
        alert.setContentText(message + "\n\nسيتم تطوير هذه الميزة قريباً...");
        alert.showAndWait();
    }
}
