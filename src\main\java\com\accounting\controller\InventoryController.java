package com.accounting.controller;

import com.accounting.model.*;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.collections.transformation.FilteredList;
import javafx.geometry.Insets;
import javafx.geometry.Pos;
import javafx.scene.Scene;
import javafx.scene.control.*;
import javafx.scene.control.cell.PropertyValueFactory;
import javafx.scene.layout.*;
import javafx.stage.Modality;
import javafx.stage.Stage;
import de.jensd.fx.glyphs.fontawesome.FontAwesomeIcon;
import de.jensd.fx.glyphs.fontawesome.FontAwesomeIconView;

import java.text.DecimalFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;

/**
 * متحكم موديول المخازن
 * Inventory Module Controller
 */
public class InventoryController {
    
    private ObservableList<Warehouse> warehouses = FXCollections.observableArrayList();
    private ObservableList<Item> items = FXCollections.observableArrayList();
    private ObservableList<StockMovement> stockMovements = FXCollections.observableArrayList();
    
    private FilteredList<Item> filteredItems;
    private FilteredList<StockMovement> filteredMovements;
    private DecimalFormat decimalFormat = new DecimalFormat("#,##0.00");
    private DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("dd/MM/yyyy");
    private DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm");
    
    // مراجع للواجهة
    private ComboBox<Warehouse> warehouseFilter;
    private ComboBox<String> categoryFilter;
    private TextField searchField;
    private TableView<Item> itemsTable;
    
    public InventoryController() {
        initializeData();
        setupFilters();
    }
    
    /**
     * تهيئة البيانات الافتراضية
     */
    private void initializeData() {
        // إضافة مخازن افتراضية
        warehouses.addAll(
            new Warehouse("WH001", "مخزن الزجاج", "الطابق الأول - قسم أ", "مخزن خاص بجميع أنواع الزجاج"),
            new Warehouse("WH002", "مخزن الألومنيوم", "الطابق الأول - قسم ب", "مخزن خاص بالألومنيوم والمعادن"),
            new Warehouse("WH003", "مخزن الأكسسوارات", "الطابق الثاني", "مخزن المقابض والأكسسوارات"),
            new Warehouse("WH004", "مخزن المواد الخام", "المستودع الخارجي", "مخزن المواد الخام والكيماويات")
        );
        
        // إضافة أصناف افتراضية
        addSampleItems();
        
        // إضافة حركات افتراضية
        addSampleMovements();
    }
    
    /**
     * إضافة أصناف تجريبية
     */
    private void addSampleItems() {
        // زجاج شفاف (له أبعاد)
        Item glass1 = new Item("GL001", "زجاج شفاف 6مم", "زجاج", Unit.SQUARE_METER, "WH001");
        glass1.setWarehouseName("مخزن الزجاج");
        glass1.setHasDimensions(true);
        glass1.setLength(2000.0); // 2 متر
        glass1.setWidth(1000.0); // 1 متر
        glass1.setPieces(75); // 75 قطعة
        glass1.setThickness(6.0);
        glass1.setColor("شفاف");
        glass1.setType("عادي");
        glass1.setCurrentQuantity(glass1.calculateArea()); // 150 م² محسوبة
        glass1.setUnitPrice(45.0);
        glass1.setSupplier("شركة الزجاج المصري");
        glass1.setNotes("زجاج عالي الجودة - له أبعاد");
        
        // ألومنيوم (بدون أبعاد)
        Item aluminum1 = new Item("AL001", "ألومنيوم أبيض 50×50", "ألومنيوم", Unit.LINEAR_METER, "WH002");
        aluminum1.setWarehouseName("مخزن الألومنيوم");
        aluminum1.setHasDimensions(false); // بدون أبعاد
        aluminum1.setCurrentQuantity(500.0);
        aluminum1.setUnitPrice(25.0);
        aluminum1.setSupplier("مصنع الألومنيوم الحديث");
        aluminum1.setColor("أبيض");
        aluminum1.setType("مربع");
        aluminum1.setNotes("ألومنيوم عادي - بدون أبعاد");
        
        // مقابض (بدون أبعاد)
        Item handle1 = new Item("HD001", "مقبض ألومنيوم فضي", "أكسسوارات", Unit.PIECE, "WH003");
        handle1.setWarehouseName("مخزن الأكسسوارات");
        handle1.setHasDimensions(false); // بدون أبعاد
        handle1.setCurrentQuantity(200.0);
        handle1.setUnitPrice(15.0);
        handle1.setSupplier("شركة الأكسسوارات المتقدمة");
        handle1.setColor("فضي");
        handle1.setNotes("مقابض عادية - بدون أبعاد");
        
        // مادة لاصقة (بدون أبعاد)
        Item adhesive1 = new Item("CH001", "سيليكون شفاف", "مواد كيميائية", Unit.LITER, "WH004");
        adhesive1.setWarehouseName("مخزن المواد الخام");
        adhesive1.setHasDimensions(false); // بدون أبعاد
        adhesive1.setCurrentQuantity(50.0);
        adhesive1.setUnitPrice(35.0);
        adhesive1.setSupplier("شركة الكيماويات الصناعية");
        adhesive1.setColor("شفاف");
        adhesive1.setNotes("مواد كيميائية - بدون أبعاد");

        // إضافة صنف زجاج آخر له أبعاد
        Item glass2 = new Item("GL002", "زجاج ملون أزرق 8مم", "زجاج", Unit.SQUARE_METER, "WH001");
        glass2.setWarehouseName("مخزن الزجاج");
        glass2.setHasDimensions(true);
        glass2.setLength(1500.0); // 1.5 متر
        glass2.setWidth(800.0);   // 0.8 متر
        glass2.setPieces(20);     // 20 قطعة
        glass2.setThickness(8.0);
        glass2.setColor("أزرق");
        glass2.setType("ملون");
        glass2.setCurrentQuantity(glass2.calculateArea()); // 24 م² محسوبة
        glass2.setUnitPrice(65.0);
        glass2.setSupplier("شركة الزجاج الملون");
        glass2.setNotes("زجاج ملون عالي الجودة - له أبعاد");

        items.addAll(glass1, aluminum1, handle1, adhesive1, glass2);
    }
    
    /**
     * إضافة حركات تجريبية
     */
    private void addSampleMovements() {
        // حركة استلام زجاج
        StockMovement movement1 = new StockMovement("MOV001", "GL001", "زجاج شفاف 6مم", "WH001",
                StockMovement.MovementType.IN, 100.0, 45.0);
        movement1.setWarehouseName("مخزن الزجاج");
        movement1.setBalanceAfter(100.0);
        movement1.setUser("أحمد محمد");
        movement1.setReference("فاتورة شراء رقم 2024001");
        movement1.setNotes("استلام دفعة جديدة من الزجاج");
        
        // حركة صرف ألومنيوم
        StockMovement movement2 = new StockMovement("MOV002", "AL001", "ألومنيوم أبيض 50×50", "WH002",
                StockMovement.MovementType.OUT, 50.0, 25.0);
        movement2.setWarehouseName("مخزن الألومنيوم");
        movement2.setBalanceAfter(450.0);
        movement2.setUser("فاطمة علي");
        movement2.setReference("أمر إنتاج رقم ********");
        movement2.setNotes("صرف للإنتاج");
        
        stockMovements.addAll(movement1, movement2);
    }
    
    /**
     * إعداد المرشحات
     */
    private void setupFilters() {
        filteredItems = new FilteredList<>(items, p -> true);
    }
    
    /**
     * إنشاء واجهة موديول المخازن
     */
    public VBox createInventoryModule() {
        VBox mainContainer = new VBox(20);
        mainContainer.setPadding(new Insets(20));
        mainContainer.getStyleClass().add("inventory-module");
        
        // العنوان الرئيسي
        Label titleLabel = new Label("📦 موديول إدارة المخازن");
        titleLabel.getStyleClass().add("module-title");
        
        // شريط الأدوات
        HBox toolbar = createToolbar();
        
        // منطقة الفلترة والبحث
        VBox filterSection = createFilterSection();
        
        // منطقة المحتوى الرئيسية
        TabPane tabPane = createMainTabs();
        
        mainContainer.getChildren().addAll(titleLabel, toolbar, filterSection, tabPane);
        return mainContainer;
    }
    
    /**
     * إنشاء شريط الأدوات
     */
    private HBox createToolbar() {
        HBox toolbar = new HBox(15);
        toolbar.setAlignment(Pos.CENTER_LEFT);
        toolbar.setPadding(new Insets(10));
        toolbar.getStyleClass().add("toolbar");
        
        // زر إدارة المخازن
        Button warehousesBtn = createToolbarButton("إدارة المخازن", FontAwesomeIcon.BUILDING, this::showWarehouseManagement);
        
        // زر إضافة صنف
        Button addItemBtn = createToolbarButton("إضافة صنف", FontAwesomeIcon.PLUS_CIRCLE, this::showAddItemDialog);
        
        // زر استلام
        Button receiveBtn = createToolbarButton("استلام", FontAwesomeIcon.ARROW_DOWN, this::showReceiveDialog);
        
        // زر صرف
        Button issueBtn = createToolbarButton("صرف", FontAwesomeIcon.ARROW_UP, this::showIssueDialog);
        
        // فاصل
        Separator separator = new Separator();
        separator.setOrientation(javafx.geometry.Orientation.VERTICAL);
        
        // زر تقييم المخزون
        Button valuationBtn = createToolbarButton("تقييم المخزون", FontAwesomeIcon.CALCULATOR, this::showValuationReport);
        
        // زر التقارير
        Button reportsBtn = createToolbarButton("التقارير", FontAwesomeIcon.BAR_CHART, this::showReports);
        
        toolbar.getChildren().addAll(warehousesBtn, addItemBtn, receiveBtn, issueBtn, separator, valuationBtn, reportsBtn);
        return toolbar;
    }
    
    /**
     * إنشاء زر شريط الأدوات
     */
    private Button createToolbarButton(String text, FontAwesomeIcon icon, Runnable action) {
        Button button = new Button(text);
        FontAwesomeIconView iconView = new FontAwesomeIconView(icon);
        iconView.setSize("16px");
        button.setGraphic(iconView);
        button.getStyleClass().add("toolbar-button");
        button.setOnAction(e -> action.run());
        return button;
    }
    
    /**
     * إنشاء قسم الفلترة والبحث
     */
    private VBox createFilterSection() {
        VBox filterSection = new VBox(10);
        filterSection.setPadding(new Insets(15));
        filterSection.getStyleClass().add("filter-section");
        
        Label filterLabel = new Label("🔍 البحث والفلترة:");
        filterLabel.getStyleClass().add("section-title");
        
        HBox filterBox = new HBox(15);
        filterBox.setAlignment(Pos.CENTER_LEFT);
        
        // فلتر المخزن
        Label warehouseLabel = new Label("المخزن:");
        warehouseFilter = new ComboBox<>();
        warehouseFilter.getItems().add(null); // خيار "الكل"
        warehouseFilter.getItems().addAll(warehouses);
        warehouseFilter.setPromptText("جميع المخازن");
        warehouseFilter.setPrefWidth(150);
        warehouseFilter.setOnAction(e -> applyFilters());
        
        // فلتر الفئة
        Label categoryLabel = new Label("الفئة:");
        categoryFilter = new ComboBox<>();
        categoryFilter.getItems().addAll("الكل", "زجاج", "ألومنيوم", "أكسسوارات", "مواد كيميائية");
        categoryFilter.setValue("الكل");
        categoryFilter.setPrefWidth(120);
        categoryFilter.setOnAction(e -> applyFilters());
        
        // حقل البحث
        Label searchLabel = new Label("البحث:");
        searchField = new TextField();
        searchField.setPromptText("ابحث بالاسم أو الكود");
        searchField.setPrefWidth(200);
        searchField.textProperty().addListener((obs, oldVal, newVal) -> applyFilters());
        
        // زر مسح الفلاتر
        Button clearBtn = new Button("مسح");
        FontAwesomeIconView clearIcon = new FontAwesomeIconView(FontAwesomeIcon.REFRESH);
        clearIcon.setSize("12px");
        clearBtn.setGraphic(clearIcon);
        clearBtn.getStyleClass().add("clear-button");
        clearBtn.setOnAction(e -> clearFilters());
        
        filterBox.getChildren().addAll(
            warehouseLabel, warehouseFilter,
            categoryLabel, categoryFilter,
            searchLabel, searchField,
            clearBtn
        );
        
        filterSection.getChildren().addAll(filterLabel, filterBox);
        return filterSection;
    }

    /**
     * تطبيق المرشحات
     */
    private void applyFilters() {
        filteredItems.setPredicate(item -> {
            // فلتر المخزن
            if (warehouseFilter.getValue() != null) {
                if (!item.getWarehouseId().equals(warehouseFilter.getValue().getWarehouseId())) {
                    return false;
                }
            }

            // فلتر الفئة
            if (categoryFilter.getValue() != null && !categoryFilter.getValue().equals("الكل")) {
                if (!item.getCategory().equals(categoryFilter.getValue())) {
                    return false;
                }
            }

            // البحث النصي
            if (searchField.getText() != null && !searchField.getText().isEmpty()) {
                String searchText = searchField.getText().toLowerCase();
                return item.getName().toLowerCase().contains(searchText) ||
                       item.getItemId().toLowerCase().contains(searchText);
            }

            return true;
        });
    }

    /**
     * مسح المرشحات
     */
    private void clearFilters() {
        warehouseFilter.setValue(null);
        categoryFilter.setValue("الكل");
        searchField.clear();
    }

    /**
     * إنشاء التبويبات الرئيسية
     */
    private TabPane createMainTabs() {
        TabPane tabPane = new TabPane();
        tabPane.getStyleClass().add("main-tabs");

        // تبويب الأصناف
        Tab itemsTab = new Tab("الأصناف");
        itemsTab.setContent(createItemsTab());
        itemsTab.setClosable(false);

        // تبويب حركة المخزون
        Tab movementsTab = new Tab("حركة المخزون");
        movementsTab.setContent(createMovementsTab());
        movementsTab.setClosable(false);

        // تبويب المخازن
        Tab warehousesTab = new Tab("المخازن");
        warehousesTab.setContent(createWarehousesTab());
        warehousesTab.setClosable(false);

        tabPane.getTabs().addAll(itemsTab, movementsTab, warehousesTab);
        return tabPane;
    }

    /**
     * إنشاء تبويب الأصناف
     */
    private VBox createItemsTab() {
        VBox container = new VBox(15);
        container.setPadding(new Insets(15));

        // إحصائيات سريعة
        HBox statsBox = createStatsBox();

        // جدول الأصناف
        itemsTable = createItemsTable();

        // وضع الجدول في ScrollPane
        ScrollPane itemsScrollPane = new ScrollPane(itemsTable);
        itemsScrollPane.setFitToWidth(true);
        itemsScrollPane.setVbarPolicy(ScrollPane.ScrollBarPolicy.AS_NEEDED);
        itemsScrollPane.setHbarPolicy(ScrollPane.ScrollBarPolicy.AS_NEEDED);
        itemsScrollPane.setPrefHeight(400);
        VBox.setVgrow(itemsScrollPane, Priority.ALWAYS);

        container.getChildren().addAll(statsBox, itemsScrollPane);
        return container;
    }

    /**
     * إنشاء صندوق الإحصائيات
     */
    private HBox createStatsBox() {
        HBox statsBox = new HBox(20);
        statsBox.setAlignment(Pos.CENTER);
        statsBox.setPadding(new Insets(15));
        statsBox.getStyleClass().add("stats-box");

        // إجمالي الأصناف
        VBox totalItemsBox = createStatCard("إجمالي الأصناف", String.valueOf(items.size()), "#3498db");

        // إجمالي القيمة
        double totalValue = items.stream().mapToDouble(Item::getTotalValue).sum();
        VBox totalValueBox = createStatCard("إجمالي القيمة", decimalFormat.format(totalValue) + " ج.م", "#2ecc71");

        // الأصناف المنخفضة
        long lowStockItems = items.stream().filter(item -> item.getCurrentQuantity() < 10).count();
        VBox lowStockBox = createStatCard("أصناف منخفضة", String.valueOf(lowStockItems), "#e74c3c");

        // المخازن النشطة
        VBox activeWarehousesBox = createStatCard("المخازن النشطة", String.valueOf(warehouses.size()), "#f39c12");

        statsBox.getChildren().addAll(totalItemsBox, totalValueBox, lowStockBox, activeWarehousesBox);
        return statsBox;
    }

    /**
     * إنشاء بطاقة إحصائية
     */
    private VBox createStatCard(String title, String value, String color) {
        VBox card = new VBox(5);
        card.setAlignment(Pos.CENTER);
        card.setPadding(new Insets(15));
        card.getStyleClass().add("stat-card");
        card.setStyle("-fx-border-color: " + color + "; -fx-border-width: 2;");

        Label titleLabel = new Label(title);
        titleLabel.getStyleClass().add("stat-title");

        Label valueLabel = new Label(value);
        valueLabel.getStyleClass().add("stat-value");
        valueLabel.setStyle("-fx-text-fill: " + color + ";");

        card.getChildren().addAll(titleLabel, valueLabel);
        return card;
    }

    /**
     * إنشاء جدول الأصناف
     */
    private TableView<Item> createItemsTable() {
        TableView<Item> table = new TableView<>();
        table.setItems(filteredItems);
        // إزالة قيد الارتفاع ليتوسع الجدول تلقائياً
        table.setMinHeight(300);
        VBox.setVgrow(table, Priority.ALWAYS);
        table.getStyleClass().add("data-table");

        // العمود: كود الصنف
        TableColumn<Item, String> idCol = new TableColumn<>("كود الصنف");
        idCol.setPrefWidth(100);
        idCol.setCellValueFactory(new PropertyValueFactory<>("itemId"));

        // العمود: اسم الصنف
        TableColumn<Item, String> nameCol = new TableColumn<>("اسم الصنف");
        nameCol.setPrefWidth(200);
        nameCol.setCellValueFactory(new PropertyValueFactory<>("name"));

        // العمود: الفئة
        TableColumn<Item, String> categoryCol = new TableColumn<>("الفئة");
        categoryCol.setPrefWidth(100);
        categoryCol.setCellValueFactory(new PropertyValueFactory<>("category"));

        // العمود: الوحدة
        TableColumn<Item, Unit> unitCol = new TableColumn<>("الوحدة");
        unitCol.setPrefWidth(100);
        unitCol.setCellValueFactory(new PropertyValueFactory<>("unit"));
        unitCol.setCellFactory(col -> new TableCell<Item, Unit>() {
            @Override
            protected void updateItem(Unit item, boolean empty) {
                super.updateItem(item, empty);
                if (empty || item == null) {
                    setText(null);
                } else {
                    setText(item.getArabicName());
                }
            }
        });

        // العمود: نوع الصنف
        TableColumn<Item, String> typeCol = new TableColumn<>("النوع");
        typeCol.setPrefWidth(80);
        typeCol.setCellFactory(col -> new TableCell<Item, String>() {
            @Override
            protected void updateItem(String item, boolean empty) {
                super.updateItem(item, empty);
                if (empty) {
                    setText(null);
                    setStyle("");
                } else {
                    Item rowItem = getTableView().getItems().get(getIndex());
                    if (rowItem.hasDimensions()) {
                        setText("أبعاد");
                        setStyle("-fx-text-fill: #007bff; -fx-font-weight: bold;");
                    } else {
                        setText("عادي");
                        setStyle("-fx-text-fill: #6c757d;");
                    }
                }
            }
        });

        // العمود: الكمية الحالية
        TableColumn<Item, Double> quantityCol = new TableColumn<>("الكمية الحالية");
        quantityCol.setPrefWidth(120);
        quantityCol.setCellValueFactory(new PropertyValueFactory<>("currentQuantity"));
        quantityCol.setCellFactory(col -> new TableCell<Item, Double>() {
            @Override
            protected void updateItem(Double item, boolean empty) {
                super.updateItem(item, empty);
                if (empty || item == null) {
                    setText(null);
                } else {
                    Item rowItem = getTableView().getItems().get(getIndex());
                    String displayText;

                    if (rowItem.hasDimensions()) {
                        // للأصناف ذات الأبعاد: عرض المساحة + تفاصيل الأبعاد
                        displayText = decimalFormat.format(item) + " " + rowItem.getUnit().getSymbol();
                        if (rowItem.getLength() != null && rowItem.getWidth() != null && rowItem.getPieces() != null) {
                            displayText += String.format("\n(%dx%d×%d)",
                                rowItem.getLength().intValue(),
                                rowItem.getWidth().intValue(),
                                rowItem.getPieces());
                        }
                    } else {
                        // للأصناف العادية
                        displayText = decimalFormat.format(item) + " " + rowItem.getUnit().getSymbol();
                    }

                    setText(displayText);

                    // تلوين الكميات المنخفضة
                    if (item < 10) {
                        setStyle("-fx-text-fill: #e74c3c; -fx-font-weight: bold;");
                    } else {
                        setStyle("");
                    }
                }
            }
        });

        // العمود: سعر الوحدة
        TableColumn<Item, Double> priceCol = new TableColumn<>("سعر الوحدة");
        priceCol.setPrefWidth(100);
        priceCol.setCellValueFactory(new PropertyValueFactory<>("unitPrice"));
        priceCol.setCellFactory(col -> new TableCell<Item, Double>() {
            @Override
            protected void updateItem(Double item, boolean empty) {
                super.updateItem(item, empty);
                if (empty || item == null) {
                    setText(null);
                } else {
                    setText(decimalFormat.format(item) + " ج.م");
                }
            }
        });

        // العمود: القيمة الإجمالية
        TableColumn<Item, Double> totalValueCol = new TableColumn<>("القيمة الإجمالية");
        totalValueCol.setPrefWidth(120);
        totalValueCol.setCellValueFactory(new PropertyValueFactory<>("totalValue"));
        totalValueCol.setCellFactory(col -> new TableCell<Item, Double>() {
            @Override
            protected void updateItem(Double item, boolean empty) {
                super.updateItem(item, empty);
                getStyleClass().add("calculated-cell");
                if (empty || item == null) {
                    setText(null);
                } else {
                    setText(decimalFormat.format(item) + " ج.م");
                }
            }
        });

        // العمود: المخزن
        TableColumn<Item, String> warehouseCol = new TableColumn<>("المخزن");
        warehouseCol.setPrefWidth(120);
        warehouseCol.setCellValueFactory(new PropertyValueFactory<>("warehouseName"));

        // العمود: الإجراءات
        TableColumn<Item, Void> actionsCol = new TableColumn<>("الإجراءات");
        actionsCol.setPrefWidth(200);
        actionsCol.setCellFactory(col -> new TableCell<Item, Void>() {
            private final HBox actionBox = new HBox(5);
            private final Button editBtn = new Button("تعديل");
            private final Button receiveBtn = new Button("استلام");
            private final Button issueBtn = new Button("صرف");
            private final Button historyBtn = new Button("السجل");

            {
                // زر التعديل
                FontAwesomeIconView editIcon = new FontAwesomeIconView(FontAwesomeIcon.EDIT);
                editIcon.setSize("10px");
                editBtn.setGraphic(editIcon);
                editBtn.getStyleClass().add("edit-button");
                editBtn.setOnAction(e -> {
                    Item item = getTableView().getItems().get(getIndex());
                    showEditItemDialog(item);
                });

                // زر الاستلام
                FontAwesomeIconView receiveIcon = new FontAwesomeIconView(FontAwesomeIcon.ARROW_DOWN);
                receiveIcon.setSize("10px");
                receiveBtn.setGraphic(receiveIcon);
                receiveBtn.getStyleClass().add("receive-button");
                receiveBtn.setOnAction(e -> {
                    Item item = getTableView().getItems().get(getIndex());
                    showReceiveDialog(item);
                });

                // زر الصرف
                FontAwesomeIconView issueIcon = new FontAwesomeIconView(FontAwesomeIcon.ARROW_UP);
                issueIcon.setSize("10px");
                issueBtn.setGraphic(issueIcon);
                issueBtn.getStyleClass().add("issue-button");
                issueBtn.setOnAction(e -> {
                    Item item = getTableView().getItems().get(getIndex());
                    showIssueDialog(item);
                });

                // زر السجل
                FontAwesomeIconView historyIcon = new FontAwesomeIconView(FontAwesomeIcon.HISTORY);
                historyIcon.setSize("10px");
                historyBtn.setGraphic(historyIcon);
                historyBtn.getStyleClass().add("history-button");
                historyBtn.setOnAction(e -> {
                    Item item = getTableView().getItems().get(getIndex());
                    showItemHistory(item);
                });

                actionBox.setAlignment(Pos.CENTER);
                actionBox.getChildren().addAll(editBtn, receiveBtn, issueBtn, historyBtn);
            }

            @Override
            protected void updateItem(Void item, boolean empty) {
                super.updateItem(item, empty);
                if (empty) {
                    setGraphic(null);
                } else {
                    setGraphic(actionBox);
                }
            }
        });

        table.getColumns().addAll(idCol, nameCol, categoryCol, typeCol, unitCol, quantityCol, priceCol, totalValueCol, warehouseCol, actionsCol);
        return table;
    }

    /**
     * إنشاء تبويب حركة المخزون
     */
    private VBox createMovementsTab() {
        VBox container = new VBox(15);
        container.setPadding(new Insets(15));

        // شريط الأدوات
        HBox toolbar = new HBox(10);
        toolbar.setAlignment(Pos.CENTER_LEFT);

        Button addMovementBtn = new Button("إضافة حركة");
        FontAwesomeIconView addIcon = new FontAwesomeIconView(FontAwesomeIcon.PLUS);
        addIcon.setSize("14px");
        addMovementBtn.setGraphic(addIcon);
        addMovementBtn.getStyleClass().add("add-button");
        addMovementBtn.setOnAction(e -> showAddMovementDialog());

        Button receiveBtn = new Button("استلام");
        FontAwesomeIconView receiveIcon = new FontAwesomeIconView(FontAwesomeIcon.ARROW_DOWN);
        receiveIcon.setSize("14px");
        receiveBtn.setGraphic(receiveIcon);
        receiveBtn.getStyleClass().add("receive-button");
        receiveBtn.setOnAction(e -> showMovementDialog(null, StockMovement.MovementType.IN));

        Button issueBtn = new Button("صرف");
        FontAwesomeIconView issueIcon = new FontAwesomeIconView(FontAwesomeIcon.ARROW_UP);
        issueIcon.setSize("14px");
        issueBtn.setGraphic(issueIcon);
        issueBtn.getStyleClass().add("issue-button");
        issueBtn.setOnAction(e -> showMovementDialog(null, StockMovement.MovementType.OUT));

        toolbar.getChildren().addAll(addMovementBtn, receiveBtn, issueBtn);

        // فلاتر حركة المخزون
        VBox movementFiltersBox = createMovementFiltersSection();

        // جدول حركة المخزون - بدون ScrollPane داخلي
        TableView<StockMovement> movementsTable = createMovementsTable();

        // إزالة أي قيود على الارتفاع وجعل الجدول يتوسع على كامل الصفحة
        movementsTable.setMinHeight(Region.USE_COMPUTED_SIZE);
        movementsTable.setPrefHeight(Region.USE_COMPUTED_SIZE);
        movementsTable.setMaxHeight(Double.MAX_VALUE);
        VBox.setVgrow(movementsTable, Priority.ALWAYS);

        container.getChildren().addAll(toolbar, movementFiltersBox, movementsTable);
        return container;
    }

    /**
     * إنشاء قسم فلاتر حركة المخزون
     */
    private VBox createMovementFiltersSection() {
        VBox filtersSection = new VBox(10);
        filtersSection.setPadding(new Insets(15));
        filtersSection.setStyle("-fx-background-color: #f8f9fa; -fx-border-color: #dee2e6; -fx-border-width: 1; -fx-border-radius: 5;");

        Label filtersLabel = new Label("🔍 فلاتر حركة المخزون:");
        filtersLabel.getStyleClass().add("section-title");

        HBox filtersRow = new HBox(15);
        filtersRow.setAlignment(Pos.CENTER_LEFT);

        // فلتر نوع الحركة
        ComboBox<StockMovement.MovementType> movementTypeFilter = new ComboBox<>();
        movementTypeFilter.getItems().add(null); // خيار "الكل"
        movementTypeFilter.getItems().addAll(StockMovement.MovementType.values());
        movementTypeFilter.setPromptText("جميع الأنواع");
        movementTypeFilter.setPrefWidth(120);

        // فلتر الصنف
        ComboBox<String> itemNameFilter = new ComboBox<>();
        itemNameFilter.getItems().add("جميع الأصناف");
        items.forEach(item -> itemNameFilter.getItems().add(item.getName()));
        itemNameFilter.setValue("جميع الأصناف");
        itemNameFilter.setPrefWidth(150);

        // فلتر المخزن
        ComboBox<String> warehouseNameFilter = new ComboBox<>();
        warehouseNameFilter.getItems().add("جميع المخازن");
        warehouses.forEach(warehouse -> warehouseNameFilter.getItems().add(warehouse.getName()));
        warehouseNameFilter.setValue("جميع المخازن");
        warehouseNameFilter.setPrefWidth(120);

        // فلتر التاريخ من
        DatePicker fromDateFilter = new DatePicker();
        fromDateFilter.setPromptText("من تاريخ");
        fromDateFilter.setPrefWidth(120);

        // فلتر التاريخ إلى
        DatePicker toDateFilter = new DatePicker();
        toDateFilter.setPromptText("إلى تاريخ");
        toDateFilter.setPrefWidth(120);

        // أزرار التحكم
        Button applyFiltersBtn = new Button("تطبيق");
        FontAwesomeIconView applyIcon = new FontAwesomeIconView(FontAwesomeIcon.FILTER);
        applyIcon.setSize("12px");
        applyFiltersBtn.setGraphic(applyIcon);
        applyFiltersBtn.getStyleClass().add("filter-button");
        applyFiltersBtn.setOnAction(e -> applyMovementFilters(movementTypeFilter, itemNameFilter, warehouseNameFilter, fromDateFilter, toDateFilter));

        Button resetFiltersBtn = new Button("إعادة تعيين");
        FontAwesomeIconView resetIcon = new FontAwesomeIconView(FontAwesomeIcon.REFRESH);
        resetIcon.setSize("12px");
        resetFiltersBtn.setGraphic(resetIcon);
        resetFiltersBtn.getStyleClass().add("reset-button");
        resetFiltersBtn.setOnAction(e -> resetMovementFilters(movementTypeFilter, itemNameFilter, warehouseNameFilter, fromDateFilter, toDateFilter));

        filtersRow.getChildren().addAll(
            new Label("نوع الحركة:"), movementTypeFilter,
            new Label("الصنف:"), itemNameFilter,
            new Label("المخزن:"), warehouseNameFilter,
            new Label("من:"), fromDateFilter,
            new Label("إلى:"), toDateFilter,
            applyFiltersBtn, resetFiltersBtn
        );

        filtersSection.getChildren().addAll(filtersLabel, filtersRow);
        return filtersSection;
    }

    /**
     * تطبيق فلاتر حركة المخزون
     */
    private void applyMovementFilters(ComboBox<StockMovement.MovementType> movementTypeFilter,
                                    ComboBox<String> itemNameFilter,
                                    ComboBox<String> warehouseNameFilter,
                                    DatePicker fromDateFilter,
                                    DatePicker toDateFilter) {

        filteredMovements.setPredicate(movement -> {
            // فلتر نوع الحركة
            if (movementTypeFilter.getValue() != null) {
                if (!movement.getMovementType().equals(movementTypeFilter.getValue())) {
                    return false;
                }
            }

            // فلتر الصنف
            if (itemNameFilter.getValue() != null && !itemNameFilter.getValue().equals("جميع الأصناف")) {
                if (!movement.getItemName().equals(itemNameFilter.getValue())) {
                    return false;
                }
            }

            // فلتر المخزن
            if (warehouseNameFilter.getValue() != null && !warehouseNameFilter.getValue().equals("جميع المخازن")) {
                if (!movement.getWarehouseName().equals(warehouseNameFilter.getValue())) {
                    return false;
                }
            }

            // فلتر التاريخ من
            if (fromDateFilter.getValue() != null) {
                if (movement.getDateTime().toLocalDate().isBefore(fromDateFilter.getValue())) {
                    return false;
                }
            }

            // فلتر التاريخ إلى
            if (toDateFilter.getValue() != null) {
                if (movement.getDateTime().toLocalDate().isAfter(toDateFilter.getValue())) {
                    return false;
                }
            }

            return true;
        });

        showPlaceholderDialog("تطبيق الفلاتر", "تم تطبيق فلاتر حركة المخزون بنجاح");
    }

    /**
     * إعادة تعيين فلاتر حركة المخزون
     */
    private void resetMovementFilters(ComboBox<StockMovement.MovementType> movementTypeFilter,
                                    ComboBox<String> itemNameFilter,
                                    ComboBox<String> warehouseNameFilter,
                                    DatePicker fromDateFilter,
                                    DatePicker toDateFilter) {

        movementTypeFilter.setValue(null);
        itemNameFilter.setValue("جميع الأصناف");
        warehouseNameFilter.setValue("جميع المخازن");
        fromDateFilter.setValue(null);
        toDateFilter.setValue(null);

        // إعادة تعيين الفلتر لعرض جميع البيانات
        filteredMovements.setPredicate(p -> true);

        showPlaceholderDialog("إعادة تعيين الفلاتر", "تم إعادة تعيين جميع فلاتر حركة المخزون");
    }

    /**
     * إنشاء جدول حركة المخزون
     */
    private TableView<StockMovement> createMovementsTable() {
        TableView<StockMovement> table = new TableView<>();

        // إنشاء FilteredList للفلترة
        filteredMovements = new FilteredList<>(stockMovements, p -> true);
        table.setItems(filteredMovements);

        // إزالة جميع قيود الارتفاع ليتوسع الجدول على كامل الصفحة
        table.setMinHeight(Region.USE_COMPUTED_SIZE);
        table.setPrefHeight(Region.USE_COMPUTED_SIZE);
        table.setMaxHeight(Double.MAX_VALUE);
        VBox.setVgrow(table, Priority.ALWAYS);
        table.getStyleClass().add("data-table");

        // العمود: رقم الحركة
        TableColumn<StockMovement, String> idCol = new TableColumn<>("رقم الحركة");
        idCol.setPrefWidth(100);
        idCol.setCellValueFactory(new PropertyValueFactory<>("movementId"));

        // العمود: نوع الحركة
        TableColumn<StockMovement, StockMovement.MovementType> typeCol = new TableColumn<>("نوع الحركة");
        typeCol.setPrefWidth(100);
        typeCol.setCellValueFactory(new PropertyValueFactory<>("movementType"));
        typeCol.setCellFactory(col -> new TableCell<StockMovement, StockMovement.MovementType>() {
            @Override
            protected void updateItem(StockMovement.MovementType item, boolean empty) {
                super.updateItem(item, empty);
                if (empty || item == null) {
                    setText(null);
                    setStyle("");
                } else {
                    setText(item.getArabicName());
                    // تلوين حسب نوع الحركة
                    switch (item) {
                        case IN:
                            setStyle("-fx-text-fill: #27ae60; -fx-font-weight: bold;");
                            break;
                        case OUT:
                            setStyle("-fx-text-fill: #e74c3c; -fx-font-weight: bold;");
                            break;
                        default:
                            setStyle("-fx-text-fill: #f39c12; -fx-font-weight: bold;");
                    }
                }
            }
        });

        // العمود: اسم الصنف
        TableColumn<StockMovement, String> itemCol = new TableColumn<>("الصنف");
        itemCol.setPrefWidth(150);
        itemCol.setCellValueFactory(new PropertyValueFactory<>("itemName"));

        // العمود: الكمية
        TableColumn<StockMovement, Double> quantityCol = new TableColumn<>("الكمية");
        quantityCol.setPrefWidth(150);
        quantityCol.setCellValueFactory(new PropertyValueFactory<>("quantity"));
        quantityCol.setCellFactory(col -> new TableCell<StockMovement, Double>() {
            @Override
            protected void updateItem(Double item, boolean empty) {
                super.updateItem(item, empty);
                if (empty || item == null) {
                    setText(null);
                } else {
                    StockMovement movement = getTableView().getItems().get(getIndex());
                    String displayText = decimalFormat.format(item);

                    // إضافة تفاصيل الأبعاد إذا كانت متوفرة
                    if (movement.hasDimensions() && movement.getLength() != null &&
                        movement.getWidth() != null && movement.getPieces() != null) {
                        displayText += String.format(" م²\n(%dx%d×%d)",
                            movement.getLength().intValue(),
                            movement.getWidth().intValue(),
                            movement.getPieces());
                    }

                    setText(displayText);
                }
            }
        });

        // العمود: سعر الوحدة
        TableColumn<StockMovement, Double> priceCol = new TableColumn<>("سعر الوحدة");
        priceCol.setPrefWidth(100);
        priceCol.setCellValueFactory(new PropertyValueFactory<>("unitPrice"));
        priceCol.setCellFactory(col -> new TableCell<StockMovement, Double>() {
            @Override
            protected void updateItem(Double item, boolean empty) {
                super.updateItem(item, empty);
                if (empty || item == null) {
                    setText(null);
                } else {
                    setText(decimalFormat.format(item) + " ج.م");
                }
            }
        });

        // العمود: القيمة الإجمالية
        TableColumn<StockMovement, Double> totalCol = new TableColumn<>("القيمة الإجمالية");
        totalCol.setPrefWidth(120);
        totalCol.setCellValueFactory(new PropertyValueFactory<>("totalValue"));
        totalCol.setCellFactory(col -> new TableCell<StockMovement, Double>() {
            @Override
            protected void updateItem(Double item, boolean empty) {
                super.updateItem(item, empty);
                if (empty || item == null) {
                    setText(null);
                } else {
                    setText(decimalFormat.format(item) + " ج.م");
                }
            }
        });

        // العمود: الرصيد بعد الحركة
        TableColumn<StockMovement, Double> balanceCol = new TableColumn<>("الرصيد بعد الحركة");
        balanceCol.setPrefWidth(120);
        balanceCol.setCellValueFactory(new PropertyValueFactory<>("balanceAfter"));
        balanceCol.setCellFactory(col -> new TableCell<StockMovement, Double>() {
            @Override
            protected void updateItem(Double item, boolean empty) {
                super.updateItem(item, empty);
                getStyleClass().add("calculated-cell");
                if (empty || item == null) {
                    setText(null);
                } else {
                    setText(decimalFormat.format(item));
                }
            }
        });

        // العمود: التاريخ والوقت
        TableColumn<StockMovement, LocalDateTime> dateCol = new TableColumn<>("التاريخ والوقت");
        dateCol.setPrefWidth(120);
        dateCol.setCellValueFactory(new PropertyValueFactory<>("dateTime"));
        dateCol.setCellFactory(col -> new TableCell<StockMovement, LocalDateTime>() {
            @Override
            protected void updateItem(LocalDateTime item, boolean empty) {
                super.updateItem(item, empty);
                if (empty || item == null) {
                    setText(null);
                } else {
                    setText(item.format(dateTimeFormatter));
                }
            }
        });

        // العمود: المستخدم
        TableColumn<StockMovement, String> userCol = new TableColumn<>("المستخدم");
        userCol.setPrefWidth(100);
        userCol.setCellValueFactory(new PropertyValueFactory<>("user"));

        table.getColumns().addAll(idCol, typeCol, itemCol, quantityCol, priceCol, totalCol, balanceCol, dateCol, userCol);
        return table;
    }

    /**
     * إنشاء تبويب المخازن
     */
    private VBox createWarehousesTab() {
        VBox container = new VBox(15);
        container.setPadding(new Insets(15));

        // شريط الأدوات
        HBox toolbar = new HBox(10);
        toolbar.setAlignment(Pos.CENTER_LEFT);

        Button addWarehouseBtn = new Button("إضافة مخزن");
        FontAwesomeIconView addIcon = new FontAwesomeIconView(FontAwesomeIcon.PLUS);
        addIcon.setSize("14px");
        addWarehouseBtn.setGraphic(addIcon);
        addWarehouseBtn.getStyleClass().add("add-button");
        addWarehouseBtn.setOnAction(e -> showAddWarehouseDialog());

        Button editWarehouseBtn = new Button("تعديل مخزن");
        FontAwesomeIconView editIcon = new FontAwesomeIconView(FontAwesomeIcon.EDIT);
        editIcon.setSize("14px");
        editWarehouseBtn.setGraphic(editIcon);
        editWarehouseBtn.getStyleClass().add("edit-button");
        editWarehouseBtn.setOnAction(e -> showPlaceholderDialog("تعديل مخزن", "نافذة تعديل المخزن"));

        toolbar.getChildren().addAll(addWarehouseBtn, editWarehouseBtn);

        // فلاتر المخازن
        VBox warehouseFiltersBox = createWarehouseFiltersSection();

        // جدول المخازن - بدون ScrollPane داخلي
        TableView<Warehouse> warehousesTable = createWarehousesTable();

        // إزالة أي قيود على الارتفاع وجعل الجدول يتوسع على كامل الصفحة
        warehousesTable.setMinHeight(Region.USE_COMPUTED_SIZE);
        warehousesTable.setPrefHeight(Region.USE_COMPUTED_SIZE);
        warehousesTable.setMaxHeight(Double.MAX_VALUE);
        VBox.setVgrow(warehousesTable, Priority.ALWAYS);

        container.getChildren().addAll(toolbar, warehouseFiltersBox, warehousesTable);
        return container;
    }

    /**
     * إنشاء قسم فلاتر المخازن
     */
    private VBox createWarehouseFiltersSection() {
        VBox filtersSection = new VBox(10);
        filtersSection.setPadding(new Insets(15));
        filtersSection.setStyle("-fx-background-color: #f8f9fa; -fx-border-color: #dee2e6; -fx-border-width: 1; -fx-border-radius: 5;");

        Label filtersLabel = new Label("🔍 فلاتر المخازن:");
        filtersLabel.getStyleClass().add("section-title");

        HBox filtersRow = new HBox(15);
        filtersRow.setAlignment(Pos.CENTER_LEFT);

        // فلتر البحث بالاسم
        TextField warehouseSearchField = new TextField();
        warehouseSearchField.setPromptText("البحث بالاسم أو الكود");
        warehouseSearchField.setPrefWidth(150);

        // فلتر الموقع
        ComboBox<String> locationFilter = new ComboBox<>();
        locationFilter.getItems().add("جميع المواقع");
        // إضافة المواقع المختلفة من المخازن الموجودة
        warehouses.stream()
            .map(Warehouse::getLocation)
            .distinct()
            .forEach(location -> locationFilter.getItems().add(location));
        locationFilter.setValue("جميع المواقع");
        locationFilter.setPrefWidth(150);

        // فلتر حسب عدد الأصناف
        ComboBox<String> itemsCountFilter = new ComboBox<>();
        itemsCountFilter.getItems().addAll("الكل", "فارغة", "أقل من 5 أصناف", "5-10 أصناف", "أكثر من 10 أصناف");
        itemsCountFilter.setValue("الكل");
        itemsCountFilter.setPrefWidth(120);

        // أزرار التحكم
        Button applyWarehouseFiltersBtn = new Button("تطبيق");
        FontAwesomeIconView applyIcon = new FontAwesomeIconView(FontAwesomeIcon.FILTER);
        applyIcon.setSize("12px");
        applyWarehouseFiltersBtn.setGraphic(applyIcon);
        applyWarehouseFiltersBtn.getStyleClass().add("filter-button");
        applyWarehouseFiltersBtn.setOnAction(e -> applyWarehouseFilters(warehouseSearchField, locationFilter, itemsCountFilter));

        Button resetWarehouseFiltersBtn = new Button("إعادة تعيين");
        FontAwesomeIconView resetIcon = new FontAwesomeIconView(FontAwesomeIcon.REFRESH);
        resetIcon.setSize("12px");
        resetWarehouseFiltersBtn.setGraphic(resetIcon);
        resetWarehouseFiltersBtn.getStyleClass().add("reset-button");
        resetWarehouseFiltersBtn.setOnAction(e -> resetWarehouseFilters(warehouseSearchField, locationFilter, itemsCountFilter));

        filtersRow.getChildren().addAll(
            new Label("البحث:"), warehouseSearchField,
            new Label("الموقع:"), locationFilter,
            new Label("عدد الأصناف:"), itemsCountFilter,
            applyWarehouseFiltersBtn, resetWarehouseFiltersBtn
        );

        filtersSection.getChildren().addAll(filtersLabel, filtersRow);
        return filtersSection;
    }

    /**
     * إنشاء جدول المخازن
     */
    private TableView<Warehouse> createWarehousesTable() {
        TableView<Warehouse> table = new TableView<>();
        table.setItems(warehouses);
        // إزالة قيد الارتفاع ليتوسع الجدول تلقائياً
        table.setMinHeight(300);
        VBox.setVgrow(table, Priority.ALWAYS);
        table.getStyleClass().add("data-table");

        // العمود: كود المخزن
        TableColumn<Warehouse, String> idCol = new TableColumn<>("كود المخزن");
        idCol.setPrefWidth(100);
        idCol.setCellValueFactory(new PropertyValueFactory<>("warehouseId"));

        // العمود: اسم المخزن
        TableColumn<Warehouse, String> nameCol = new TableColumn<>("اسم المخزن");
        nameCol.setPrefWidth(150);
        nameCol.setCellValueFactory(new PropertyValueFactory<>("name"));

        // العمود: الموقع
        TableColumn<Warehouse, String> locationCol = new TableColumn<>("الموقع");
        locationCol.setPrefWidth(200);
        locationCol.setCellValueFactory(new PropertyValueFactory<>("location"));

        // العمود: الوصف
        TableColumn<Warehouse, String> descriptionCol = new TableColumn<>("الوصف");
        descriptionCol.setPrefWidth(250);
        descriptionCol.setCellValueFactory(new PropertyValueFactory<>("description"));

        // العمود: عدد الأصناف
        TableColumn<Warehouse, Integer> itemsCountCol = new TableColumn<>("عدد الأصناف");
        itemsCountCol.setPrefWidth(100);
        itemsCountCol.setCellFactory(col -> new TableCell<Warehouse, Integer>() {
            @Override
            protected void updateItem(Integer item, boolean empty) {
                super.updateItem(item, empty);
                if (empty) {
                    setText(null);
                } else {
                    Warehouse warehouse = getTableView().getItems().get(getIndex());
                    long count = items.stream().filter(i -> i.getWarehouseId().equals(warehouse.getWarehouseId())).count();
                    setText(String.valueOf(count));
                }
            }
        });

        // العمود: الإجراءات
        TableColumn<Warehouse, Void> actionsCol = new TableColumn<>("الإجراءات");
        actionsCol.setPrefWidth(150);
        actionsCol.setCellFactory(col -> new TableCell<Warehouse, Void>() {
            private final HBox actionBox = new HBox(5);
            private final Button editBtn = new Button("تعديل");
            private final Button viewBtn = new Button("عرض");
            private final Button deleteBtn = new Button("حذف");

            {
                // زر التعديل
                FontAwesomeIconView editIcon = new FontAwesomeIconView(FontAwesomeIcon.EDIT);
                editIcon.setSize("10px");
                editBtn.setGraphic(editIcon);
                editBtn.getStyleClass().add("edit-button");
                editBtn.setOnAction(e -> {
                    Warehouse warehouse = getTableView().getItems().get(getIndex());
                    showEditWarehouseDialog(warehouse);
                });

                // زر العرض
                FontAwesomeIconView viewIcon = new FontAwesomeIconView(FontAwesomeIcon.EYE);
                viewIcon.setSize("10px");
                viewBtn.setGraphic(viewIcon);
                viewBtn.getStyleClass().add("view-button");
                viewBtn.setOnAction(e -> {
                    Warehouse warehouse = getTableView().getItems().get(getIndex());
                    showWarehouseDetails(warehouse);
                });

                // زر الحذف
                FontAwesomeIconView deleteIcon = new FontAwesomeIconView(FontAwesomeIcon.TRASH);
                deleteIcon.setSize("10px");
                deleteBtn.setGraphic(deleteIcon);
                deleteBtn.getStyleClass().add("delete-button");
                deleteBtn.setOnAction(e -> {
                    Warehouse warehouse = getTableView().getItems().get(getIndex());
                    deleteWarehouse(warehouse);
                });

                actionBox.setAlignment(Pos.CENTER);
                actionBox.getChildren().addAll(editBtn, viewBtn, deleteBtn);
            }

            @Override
            protected void updateItem(Void item, boolean empty) {
                super.updateItem(item, empty);
                if (empty) {
                    setGraphic(null);
                } else {
                    setGraphic(actionBox);
                }
            }
        });

        table.getColumns().addAll(idCol, nameCol, locationCol, descriptionCol, itemsCountCol, actionsCol);
        return table;
    }

    // أحداث الأزرار الرئيسية
    private void showWarehouseManagement() {
        showWarehouseManagementPage();
    }

    private void showAddItemDialog() {
        showAddItemPage();
    }

    private void showReceiveDialog() {
        showReceivePage();
    }

    private void showIssueDialog() {
        showIssuePage();
    }

    private void showValuationReport() {
        showInventoryValuationPage();
    }

    private void showReports() {
        showInventoryReportsPage();
    }

    // وظائف الأصناف
    private void showEditItemDialog(Item item) {
        showItemDialog(item);
    }

    private void showReceiveDialog(Item item) {
        showMovementDialog(item, StockMovement.MovementType.IN);
    }

    private void showIssueDialog(Item item) {
        showMovementDialog(item, StockMovement.MovementType.OUT);
    }

    private void showItemHistory(Item item) {
        showPlaceholderDialog("سجل الصنف", "سيتم عرض سجل حركات الصنف: " + item.getName());
    }

    // وظائف المخازن
    private void showAddWarehouseDialog() {
        showWarehouseDialog(null);
    }

    private void showEditWarehouseDialog(Warehouse warehouse) {
        showWarehouseDialog(warehouse);
    }

    private void showWarehouseDetails(Warehouse warehouse) {
        showPlaceholderDialog("تفاصيل المخزن", "تفاصيل المخزن: " + warehouse.getName());
    }

    private void deleteWarehouse(Warehouse warehouse) {
        Alert alert = new Alert(Alert.AlertType.CONFIRMATION);
        alert.setTitle("تأكيد الحذف");
        alert.setHeaderText("حذف المخزن");
        alert.setContentText("هل أنت متأكد من حذف المخزن: " + warehouse.getName() + "؟");

        alert.showAndWait().ifPresent(response -> {
            if (response == ButtonType.OK) {
                warehouses.remove(warehouse);
                showPlaceholderDialog("نجح", "تم حذف المخزن بنجاح");
            }
        });
    }

    // وظائف الحركات
    private void showAddMovementDialog() {
        showPlaceholderDialog("إضافة حركة", "سيتم تطوير نافذة إضافة الحركات قريباً");
    }

    private void showPlaceholderDialog(String title, String message) {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle(title);
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }

    /**
     * نافذة إضافة/تعديل صنف
     */
    private void showItemDialog(Item item) {
        InventoryDialogs dialogs = new InventoryDialogs();
        dialogs.showItemDialog(item, warehouses, items);
    }

    /**
     * نافذة حركة المخزون (استلام/صرف)
     */
    private void showMovementDialog(Item item, StockMovement.MovementType movementType) {
        Stage dialog = new Stage();
        dialog.setTitle(movementType.getArabicName() + " - " + (item != null ? item.getName() : "اختر صنف"));
        dialog.initModality(Modality.APPLICATION_MODAL);
        dialog.setWidth(500);
        dialog.setHeight(400);

        VBox mainLayout = new VBox(20);
        mainLayout.setPadding(new Insets(20));

        // العنوان
        Label titleLabel = new Label(movementType.getArabicName() + " صنف");
        titleLabel.getStyleClass().add("dialog-title");

        // نموذج البيانات
        GridPane form = new GridPane();
        form.setHgap(15);
        form.setVgap(15);

        // اختيار المخزن أولاً
        Label warehouseLabel = new Label("المخزن:");
        ComboBox<Warehouse> warehouseCombo = new ComboBox<>();
        warehouseCombo.setItems(warehouses);
        warehouseCombo.setPrefWidth(200);
        warehouseCombo.setPromptText("اختر المخزن أولاً");

        // اختيار الصنف (يتم تحديثه حسب المخزن المختار)
        Label itemLabel = new Label("الصنف:");
        ComboBox<Item> itemCombo = new ComboBox<>();
        itemCombo.setPrefWidth(200);
        itemCombo.setPromptText("اختر المخزن أولاً");
        itemCombo.setDisable(true); // معطل حتى يتم اختيار المخزن

        // إذا تم تمرير صنف محدد مسبقاً
        if (item != null) {
            // اختيار المخزن الخاص بالصنف تلقائياً
            Warehouse itemWarehouse = warehouses.stream()
                .filter(w -> w.getWarehouseId().equals(item.getWarehouseId()))
                .findFirst().orElse(null);
            if (itemWarehouse != null) {
                warehouseCombo.setValue(itemWarehouse);
                // تحديث قائمة الأصناف
                updateItemsList(warehouseCombo, itemCombo);
                itemCombo.setValue(item);
                itemCombo.setDisable(true);
            }
        }

        // الكمية (للأصناف العادية)
        Label quantityLabel = new Label("الكمية:");
        TextField quantityField = new TextField();
        quantityField.setPromptText("0.00");

        // خصائص الأبعاد (للأصناف ذات الأبعاد)
        VBox dimensionsBox = new VBox(10);
        dimensionsBox.setVisible(false);

        Label dimensionsLabel = new Label("الأبعاد:");
        dimensionsLabel.getStyleClass().add("section-title");

        GridPane dimensionsForm = new GridPane();
        dimensionsForm.setHgap(10);
        dimensionsForm.setVgap(10);

        Label lengthLabel = new Label("الطول (مم):");
        TextField lengthField = new TextField();
        lengthField.setPromptText("1000");

        Label widthLabel = new Label("العرض (مم):");
        TextField widthField = new TextField();
        widthField.setPromptText("500");

        Label piecesLabel = new Label("العدد:");
        TextField piecesField = new TextField();
        piecesField.setPromptText("1");

        Label calculatedAreaLabel = new Label("المساحة المحسوبة (م²):");
        TextField calculatedAreaField = new TextField();
        calculatedAreaField.setPromptText("0.00");
        calculatedAreaField.setDisable(true);
        calculatedAreaField.getStyleClass().add("calculated-field");

        // حساب المساحة تلقائياً
        Runnable calculateArea = () -> {
            try {
                double length = lengthField.getText().isEmpty() ? 0 : Double.parseDouble(lengthField.getText());
                double width = widthField.getText().isEmpty() ? 0 : Double.parseDouble(widthField.getText());
                int pieces = piecesField.getText().isEmpty() ? 1 : Integer.parseInt(piecesField.getText());

                double area = (length / 1000.0) * (width / 1000.0) * pieces;
                calculatedAreaField.setText(decimalFormat.format(area));
            } catch (NumberFormatException e) {
                calculatedAreaField.setText("0.00");
            }
        };

        lengthField.textProperty().addListener((obs, oldVal, newVal) -> calculateArea.run());
        widthField.textProperty().addListener((obs, oldVal, newVal) -> calculateArea.run());
        piecesField.textProperty().addListener((obs, oldVal, newVal) -> calculateArea.run());

        dimensionsForm.add(lengthLabel, 0, 0);
        dimensionsForm.add(lengthField, 1, 0);
        dimensionsForm.add(widthLabel, 2, 0);
        dimensionsForm.add(widthField, 3, 0);
        dimensionsForm.add(piecesLabel, 0, 1);
        dimensionsForm.add(piecesField, 1, 1);
        dimensionsForm.add(calculatedAreaLabel, 2, 1);
        dimensionsForm.add(calculatedAreaField, 3, 1);

        dimensionsBox.getChildren().addAll(dimensionsLabel, dimensionsForm);

        // تحديث قائمة الأصناف عند اختيار المخزن
        warehouseCombo.setOnAction(e -> {
            updateItemsList(warehouseCombo, itemCombo);
        });

        // سعر الوحدة
        Label priceLabel = new Label("سعر الوحدة:");
        TextField priceField = new TextField();
        priceField.setPromptText("0.00");

        // المرجع
        Label referenceLabel = new Label("المرجع:");
        TextField referenceField = new TextField();
        referenceField.setPromptText("رقم الفاتورة أو أمر العمل");

        // المستخدم
        Label userLabel = new Label("المستخدم:");
        TextField userField = new TextField();
        userField.setPromptText("اسم المستخدم");
        userField.setText("المستخدم الحالي");

        // الملاحظات
        Label notesLabel = new Label("الملاحظات:");
        TextArea notesArea = new TextArea();
        notesArea.setPromptText("ملاحظات اختيارية");
        notesArea.setPrefRowCount(3);

        // تحديث الواجهة عند اختيار الصنف
        itemCombo.setOnAction(e -> {
            Item selectedItem = itemCombo.getValue();
            if (selectedItem != null) {
                priceField.setText(String.valueOf(selectedItem.getUnitPrice()));

                // إظهار/إخفاء الحقول حسب نوع الصنف
                boolean hasDimensions = selectedItem.hasDimensions();
                dimensionsBox.setVisible(hasDimensions);
                quantityField.setDisable(hasDimensions);

                if (hasDimensions) {
                    quantityField.setPromptText("محسوبة تلقائياً من الأبعاد");
                    // ملء القيم الافتراضية إذا كانت متوفرة
                    if (selectedItem.getLength() != null) {
                        lengthField.setText(String.valueOf(selectedItem.getLength()));
                    }
                    if (selectedItem.getWidth() != null) {
                        widthField.setText(String.valueOf(selectedItem.getWidth()));
                    }
                    piecesField.setText("1"); // قيمة افتراضية
                } else {
                    quantityField.setPromptText("0.00");
                    quantityField.setDisable(false);
                }
            }
        });

        // ترتيب الحقول
        form.add(warehouseLabel, 0, 0);
        form.add(warehouseCombo, 1, 0);
        form.add(itemLabel, 0, 1);
        form.add(itemCombo, 1, 1);
        form.add(quantityLabel, 0, 2);
        form.add(quantityField, 1, 2);
        form.add(priceLabel, 0, 3);
        form.add(priceField, 1, 3);
        form.add(referenceLabel, 0, 4);
        form.add(referenceField, 1, 4);
        form.add(userLabel, 0, 5);
        form.add(userField, 1, 5);
        form.add(notesLabel, 0, 6);
        form.add(notesArea, 1, 6);

        // إضافة صندوق الأبعاد
        form.add(dimensionsBox, 0, 7);
        GridPane.setColumnSpan(dimensionsBox, 2);

        // أزرار الحوار
        HBox buttonBox = new HBox(10);
        buttonBox.setAlignment(Pos.CENTER_RIGHT);

        Button saveButton = new Button("تنفيذ " + movementType.getArabicName());
        saveButton.getStyleClass().add("add-button");
        saveButton.setOnAction(e -> {
            Item selectedItem = itemCombo.getValue();
            if (selectedItem == null) {
                showPlaceholderDialog("تحذير", "يرجى اختيار الصنف");
                return;
            }

            try {
                double quantity;
                double price = Double.parseDouble(priceField.getText().trim());

                // حساب الكمية حسب نوع الصنف
                if (selectedItem.hasDimensions()) {
                    // للأصناف ذات الأبعاد
                    if (!validateDimensionsForm(lengthField, widthField, piecesField)) {
                        return;
                    }

                    double length = Double.parseDouble(lengthField.getText().trim());
                    double width = Double.parseDouble(widthField.getText().trim());
                    int pieces = Integer.parseInt(piecesField.getText().trim());

                    quantity = (length / 1000.0) * (width / 1000.0) * pieces;
                } else {
                    // للأصناف العادية
                    if (quantityField.getText().trim().isEmpty()) {
                        showPlaceholderDialog("تحذير", "يرجى إدخال الكمية");
                        return;
                    }
                    quantity = Double.parseDouble(quantityField.getText().trim());
                }

                if (quantity <= 0) {
                    showPlaceholderDialog("تحذير", "يرجى إدخال كمية أكبر من صفر");
                    return;
                }

                // التحقق من الكمية المتاحة في حالة الصرف
                if (movementType == StockMovement.MovementType.OUT) {
                    if (quantity > selectedItem.getCurrentQuantity()) {
                        showPlaceholderDialog("تحذير", "الكمية المطلوبة أكبر من الكمية المتاحة");
                        return;
                    }
                }

                    // إنشاء حركة جديدة
                    String movementId = "MOV" + String.format("%03d", stockMovements.size() + 1);
                    StockMovement movement = new StockMovement(
                        movementId,
                        selectedItem.getItemId(),
                        selectedItem.getName(),
                        selectedItem.getWarehouseId(),
                        movementType,
                        quantity,
                        price
                    );

                    movement.setWarehouseName(selectedItem.getWarehouseName());
                    movement.setUser(userField.getText().trim());
                    movement.setReference(referenceField.getText().trim());
                    movement.setNotes(notesArea.getText().trim());

                    // حفظ بيانات الأبعاد إذا كان الصنف له أبعاد
                    if (selectedItem.hasDimensions()) {
                        movement.setHasDimensions(true);
                        movement.setLength(Double.parseDouble(lengthField.getText().trim()));
                        movement.setWidth(Double.parseDouble(widthField.getText().trim()));
                        movement.setPieces(Integer.parseInt(piecesField.getText().trim()));
                        movement.setCalculatedArea(quantity);
                    }

                    // تحديث كمية الصنف
                    double newQuantity;
                    if (movementType == StockMovement.MovementType.IN) {
                        newQuantity = selectedItem.getCurrentQuantity() + quantity;
                    } else {
                        newQuantity = selectedItem.getCurrentQuantity() - quantity;
                    }

                    selectedItem.setCurrentQuantity(newQuantity);
                    selectedItem.setLastUpdated(LocalDate.now());
                    movement.setBalanceAfter(newQuantity);

                    // تحديث متوسط السعر (في حالة الاستلام)
                    if (movementType == StockMovement.MovementType.IN) {
                        double totalValue = (selectedItem.getCurrentQuantity() - quantity) * selectedItem.getUnitPrice() + quantity * price;
                        selectedItem.setUnitPrice(totalValue / selectedItem.getCurrentQuantity());
                    }

                    stockMovements.add(movement);

                    showPlaceholderDialog("نجح", "تم تنفيذ " + movementType.getArabicName() + " بنجاح");
                    dialog.close();

                    // تحديث الجدول
                    if (itemsTable != null) {
                        itemsTable.refresh();
                    }

                } catch (NumberFormatException ex) {
                    showPlaceholderDialog("خطأ", "يرجى إدخال قيم صحيحة للأرقام");
                }
        });

        Button cancelButton = new Button("إلغاء");
        cancelButton.setOnAction(e -> dialog.close());

        buttonBox.getChildren().addAll(saveButton, cancelButton);

        mainLayout.getChildren().addAll(titleLabel, form, buttonBox);

        Scene scene = new Scene(mainLayout);
        scene.getStylesheets().add(getClass().getResource("/css/styles.css").toExternalForm());
        dialog.setScene(scene);
        dialog.showAndWait();
    }

    /**
     * التحقق من صحة نموذج الأبعاد
     */
    private boolean validateDimensionsForm(TextField lengthField, TextField widthField, TextField piecesField) {
        if (lengthField.getText().trim().isEmpty()) {
            showPlaceholderDialog("تحذير", "يرجى إدخال الطول");
            return false;
        }

        if (widthField.getText().trim().isEmpty()) {
            showPlaceholderDialog("تحذير", "يرجى إدخال العرض");
            return false;
        }

        if (piecesField.getText().trim().isEmpty()) {
            showPlaceholderDialog("تحذير", "يرجى إدخال العدد");
            return false;
        }

        try {
            double length = Double.parseDouble(lengthField.getText().trim());
            double width = Double.parseDouble(widthField.getText().trim());
            int pieces = Integer.parseInt(piecesField.getText().trim());

            if (length <= 0) {
                showPlaceholderDialog("تحذير", "يرجى إدخال طول أكبر من صفر");
                return false;
            }

            if (width <= 0) {
                showPlaceholderDialog("تحذير", "يرجى إدخال عرض أكبر من صفر");
                return false;
            }

            if (pieces <= 0) {
                showPlaceholderDialog("تحذير", "يرجى إدخال عدد أكبر من صفر");
                return false;
            }
        } catch (NumberFormatException e) {
            showPlaceholderDialog("تحذير", "يرجى إدخال قيم صحيحة للأبعاد");
            return false;
        }

        return true;
    }

    /**
     * نافذة إضافة/تعديل مخزن
     */
    private void showWarehouseDialog(Warehouse warehouse) {
        boolean isEdit = (warehouse != null);

        Stage dialog = new Stage();
        dialog.setTitle(isEdit ? "تعديل مخزن" : "إضافة مخزن جديد");
        dialog.initModality(Modality.APPLICATION_MODAL);
        dialog.setWidth(450);
        dialog.setHeight(350);

        VBox mainLayout = new VBox(20);
        mainLayout.setPadding(new Insets(20));

        // العنوان
        Label titleLabel = new Label(isEdit ? "تعديل بيانات المخزن" : "إضافة مخزن جديد");
        titleLabel.getStyleClass().add("dialog-title");

        // نموذج البيانات
        GridPane form = new GridPane();
        form.setHgap(15);
        form.setVgap(15);

        // كود المخزن
        Label idLabel = new Label("كود المخزن:");
        TextField idField = new TextField();
        idField.setPromptText("WH001");
        if (isEdit) {
            idField.setText(warehouse.getWarehouseId());
            idField.setDisable(true);
        }

        // اسم المخزن
        Label nameLabel = new Label("اسم المخزن:");
        TextField nameField = new TextField();
        nameField.setPromptText("أدخل اسم المخزن");
        if (isEdit) nameField.setText(warehouse.getName());

        // الموقع
        Label locationLabel = new Label("الموقع:");
        TextField locationField = new TextField();
        locationField.setPromptText("أدخل موقع المخزن");
        if (isEdit) locationField.setText(warehouse.getLocation());

        // الوصف
        Label descriptionLabel = new Label("الوصف:");
        TextArea descriptionArea = new TextArea();
        descriptionArea.setPromptText("وصف المخزن");
        descriptionArea.setPrefRowCount(3);
        if (isEdit) descriptionArea.setText(warehouse.getDescription());

        // ترتيب الحقول
        form.add(idLabel, 0, 0);
        form.add(idField, 1, 0);
        form.add(nameLabel, 0, 1);
        form.add(nameField, 1, 1);
        form.add(locationLabel, 0, 2);
        form.add(locationField, 1, 2);
        form.add(descriptionLabel, 0, 3);
        form.add(descriptionArea, 1, 3);

        // أزرار الحوار
        HBox buttonBox = new HBox(10);
        buttonBox.setAlignment(Pos.CENTER_RIGHT);

        Button saveButton = new Button(isEdit ? "حفظ التعديلات" : "إضافة المخزن");
        saveButton.getStyleClass().add("add-button");
        saveButton.setOnAction(e -> {
            if (validateWarehouseForm(nameField, locationField)) {
                if (isEdit) {
                    // تحديث المخزن الموجود
                    warehouse.setName(nameField.getText().trim());
                    warehouse.setLocation(locationField.getText().trim());
                    warehouse.setDescription(descriptionArea.getText().trim());

                    showPlaceholderDialog("نجح", "تم تحديث بيانات المخزن بنجاح");
                } else {
                    // إضافة مخزن جديد
                    String warehouseId = idField.getText().trim();
                    if (warehouseId.isEmpty()) {
                        warehouseId = "WH" + String.format("%03d", warehouses.size() + 1);
                    }

                    Warehouse newWarehouse = new Warehouse(
                        warehouseId,
                        nameField.getText().trim(),
                        locationField.getText().trim(),
                        descriptionArea.getText().trim()
                    );

                    warehouses.add(newWarehouse);
                    showPlaceholderDialog("نجح", "تم إضافة المخزن بنجاح");
                }

                dialog.close();
            }
        });

        Button cancelButton = new Button("إلغاء");
        cancelButton.setOnAction(e -> dialog.close());

        buttonBox.getChildren().addAll(saveButton, cancelButton);

        mainLayout.getChildren().addAll(titleLabel, form, buttonBox);

        Scene scene = new Scene(mainLayout);
        scene.getStylesheets().add(getClass().getResource("/css/styles.css").toExternalForm());
        dialog.setScene(scene);
        dialog.showAndWait();
    }

    /**
     * التحقق من صحة نموذج المخزن
     */
    private boolean validateWarehouseForm(TextField nameField, TextField locationField) {
        if (nameField.getText().trim().isEmpty()) {
            showPlaceholderDialog("تحذير", "يرجى إدخال اسم المخزن");
            return false;
        }

        if (locationField.getText().trim().isEmpty()) {
            showPlaceholderDialog("تحذير", "يرجى إدخال موقع المخزن");
            return false;
        }

        return true;
    }

    /**
     * تحديث قائمة الأصناف حسب المخزن المختار
     */
    private void updateItemsList(ComboBox<Warehouse> warehouseCombo, ComboBox<Item> itemCombo) {
        Warehouse selectedWarehouse = warehouseCombo.getValue();
        if (selectedWarehouse != null) {
            // فلترة الأصناف حسب المخزن المختار
            ObservableList<Item> warehouseItems = FXCollections.observableArrayList();
            for (Item item : items) {
                if (item.getWarehouseId().equals(selectedWarehouse.getWarehouseId())) {
                    warehouseItems.add(item);
                }
            }

            itemCombo.setItems(warehouseItems);
            itemCombo.setDisable(false);
            itemCombo.setPromptText("اختر الصنف من " + selectedWarehouse.getName());
            itemCombo.setValue(null); // مسح الاختيار السابق
        } else {
            // إذا لم يتم اختيار مخزن
            itemCombo.setItems(FXCollections.observableArrayList());
            itemCombo.setDisable(true);
            itemCombo.setPromptText("اختر المخزن أولاً");
            itemCombo.setValue(null);
        }
    }

    /**
     * عرض تقارير المخازن
     */
    private void showInventoryReportsDialog() {
        Stage dialog = new Stage();
        dialog.setTitle("📊 تقارير المخازن");
        dialog.initModality(Modality.APPLICATION_MODAL);
        dialog.setWidth(900);
        dialog.setHeight(700);

        VBox mainLayout = new VBox(20);
        mainLayout.setPadding(new Insets(20));

        // العنوان
        Label titleLabel = new Label("📊 تقارير وتحليلات المخازن");
        titleLabel.getStyleClass().add("dialog-title");

        // شريط أدوات التقارير
        HBox reportsToolbar = new HBox(15);
        reportsToolbar.setAlignment(Pos.CENTER_LEFT);
        reportsToolbar.setPadding(new Insets(10));

        Button stockReportBtn = new Button("تقرير المخزون");
        FontAwesomeIconView stockIcon = new FontAwesomeIconView(FontAwesomeIcon.CUBES);
        stockIcon.setSize("14px");
        stockReportBtn.setGraphic(stockIcon);
        stockReportBtn.getStyleClass().add("report-button");

        Button movementReportBtn = new Button("تقرير الحركات");
        FontAwesomeIconView movementIcon = new FontAwesomeIconView(FontAwesomeIcon.EXCHANGE);
        movementIcon.setSize("14px");
        movementReportBtn.setGraphic(movementIcon);
        movementReportBtn.getStyleClass().add("report-button");

        Button valuationReportBtn = new Button("تقرير التقييم");
        FontAwesomeIconView valuationIcon = new FontAwesomeIconView(FontAwesomeIcon.CALCULATOR);
        valuationIcon.setSize("14px");
        valuationReportBtn.setGraphic(valuationIcon);
        valuationReportBtn.getStyleClass().add("report-button");

        Button chartBtn = new Button("الرسوم البيانية");
        FontAwesomeIconView chartIcon = new FontAwesomeIconView(FontAwesomeIcon.PIE_CHART);
        chartIcon.setSize("14px");
        chartBtn.setGraphic(chartIcon);
        chartBtn.getStyleClass().add("chart-button");
        chartBtn.setOnAction(e -> showInventoryChartsPage());

        reportsToolbar.getChildren().addAll(stockReportBtn, movementReportBtn, valuationReportBtn, chartBtn);

        // منطقة عرض التقارير
        TabPane reportsTabPane = new TabPane();

        // تبويب الإحصائيات السريعة
        Tab statsTab = new Tab("الإحصائيات");
        VBox statsContent = createInventoryStatsContent();
        statsTab.setContent(statsContent);
        statsTab.setClosable(false);

        // تبويب تقرير المخزون
        Tab stockTab = new Tab("تقرير المخزون");
        VBox stockContent = createStockReportContent();
        stockTab.setContent(stockContent);
        stockTab.setClosable(false);

        // تبويب الرسوم البيانية
        Tab chartsTab = new Tab("الرسوم البيانية");
        VBox chartsContent = createInventoryChartsContent();
        chartsTab.setContent(chartsContent);
        chartsTab.setClosable(false);

        reportsTabPane.getTabs().addAll(statsTab, stockTab, chartsTab);

        // زر الإغلاق
        Button closeButton = new Button("إغلاق");
        closeButton.setOnAction(e -> dialog.close());

        HBox buttonBox = new HBox();
        buttonBox.setAlignment(Pos.CENTER_RIGHT);
        buttonBox.getChildren().add(closeButton);

        mainLayout.getChildren().addAll(titleLabel, reportsToolbar, reportsTabPane, buttonBox);

        Scene scene = new Scene(mainLayout);
        scene.getStylesheets().add(getClass().getResource("/css/styles.css").toExternalForm());
        dialog.setScene(scene);
        dialog.showAndWait();
    }

    /**
     * إنشاء محتوى إحصائيات المخازن
     */
    private VBox createInventoryStatsContent() {
        VBox content = new VBox(20);
        content.setPadding(new Insets(20));

        // إحصائيات سريعة (نفس الموجودة في الصفحة الرئيسية)
        HBox statsBox = createStatsBox();

        // جدول أفضل الأصناف
        Label topItemsLabel = new Label("أعلى الأصناف قيمة:");
        topItemsLabel.getStyleClass().add("section-title");

        TableView<Item> topItemsTable = new TableView<>();
        topItemsTable.setItems(items.sorted((a, b) -> Double.compare(b.getTotalValue(), a.getTotalValue())));
        topItemsTable.setMinHeight(200);
        VBox.setVgrow(topItemsTable, Priority.ALWAYS);

        TableColumn<Item, String> itemNameCol = new TableColumn<>("اسم الصنف");
        itemNameCol.setPrefWidth(200);
        itemNameCol.setCellValueFactory(new PropertyValueFactory<>("name"));

        TableColumn<Item, Double> quantityCol = new TableColumn<>("الكمية");
        quantityCol.setPrefWidth(100);
        quantityCol.setCellValueFactory(new PropertyValueFactory<>("currentQuantity"));
        quantityCol.setCellFactory(col -> new TableCell<Item, Double>() {
            @Override
            protected void updateItem(Double item, boolean empty) {
                super.updateItem(item, empty);
                if (empty || item == null) {
                    setText(null);
                } else {
                    Item rowItem = getTableView().getItems().get(getIndex());
                    setText(decimalFormat.format(item) + " " + rowItem.getUnit().getSymbol());
                }
            }
        });

        TableColumn<Item, Double> valueCol = new TableColumn<>("القيمة الإجمالية");
        valueCol.setPrefWidth(150);
        valueCol.setCellValueFactory(new PropertyValueFactory<>("totalValue"));
        valueCol.setCellFactory(col -> new TableCell<Item, Double>() {
            @Override
            protected void updateItem(Double item, boolean empty) {
                super.updateItem(item, empty);
                if (empty || item == null) {
                    setText(null);
                } else {
                    setText(decimalFormat.format(item) + " ج.م");
                }
            }
        });

        topItemsTable.getColumns().addAll(itemNameCol, quantityCol, valueCol);

        content.getChildren().addAll(statsBox, topItemsLabel, topItemsTable);
        return content;
    }

    /**
     * إنشاء محتوى تقرير المخزون
     */
    private VBox createStockReportContent() {
        VBox content = new VBox(15);
        content.setPadding(new Insets(20));

        Label titleLabel = new Label("تقرير المخزون التفصيلي:");
        titleLabel.getStyleClass().add("section-title");

        // شريط الفلترة
        HBox filterBox = new HBox(10);
        filterBox.setAlignment(Pos.CENTER_LEFT);

        ComboBox<Warehouse> warehouseFilterCombo = new ComboBox<>();
        warehouseFilterCombo.getItems().add(null);
        warehouseFilterCombo.getItems().addAll(warehouses);
        warehouseFilterCombo.setPromptText("جميع المخازن");
        warehouseFilterCombo.setPrefWidth(150);

        ComboBox<String> categoryFilterCombo = new ComboBox<>();
        categoryFilterCombo.getItems().addAll("الكل", "زجاج", "ألومنيوم", "أكسسوارات", "مواد كيميائية");
        categoryFilterCombo.setValue("الكل");
        categoryFilterCombo.setPrefWidth(120);

        // فلتر التاريخ
        DatePicker fromDatePicker = new DatePicker();
        fromDatePicker.setPromptText("من تاريخ");
        fromDatePicker.setPrefWidth(120);

        DatePicker toDatePicker = new DatePicker();
        toDatePicker.setPromptText("إلى تاريخ");
        toDatePicker.setPrefWidth(120);

        Button printBtn = new Button("طباعة");
        FontAwesomeIconView printIcon = new FontAwesomeIconView(FontAwesomeIcon.PRINT);
        printIcon.setSize("12px");
        printBtn.setGraphic(printIcon);
        printBtn.getStyleClass().add("print-button");
        printBtn.setOnAction(e -> printInventoryReport());

        Button exportBtn = new Button("تصدير Excel");
        FontAwesomeIconView exportIcon = new FontAwesomeIconView(FontAwesomeIcon.FILE_EXCEL_ALT);
        exportIcon.setSize("12px");
        exportBtn.setGraphic(exportIcon);
        exportBtn.getStyleClass().add("export-button");
        exportBtn.setOnAction(e -> exportInventoryReport());

        Button applyFiltersBtn = new Button("تطبيق الفلاتر");
        FontAwesomeIconView filterIcon = new FontAwesomeIconView(FontAwesomeIcon.FILTER);
        filterIcon.setSize("12px");
        applyFiltersBtn.setGraphic(filterIcon);
        applyFiltersBtn.getStyleClass().add("filter-button");
        applyFiltersBtn.setOnAction(e -> applyInventoryReportFilters());

        filterBox.getChildren().addAll(
            new Label("المخزن:"), warehouseFilterCombo,
            new Label("الفئة:"), categoryFilterCombo,
            new Label("من:"), fromDatePicker,
            new Label("إلى:"), toDatePicker,
            applyFiltersBtn, printBtn, exportBtn
        );

        // جدول التقرير (نفس جدول الأصناف)
        TableView<Item> reportTable = createItemsTable();

        content.getChildren().addAll(titleLabel, filterBox, reportTable);
        return content;
    }

    /**
     * إنشاء محتوى الرسوم البيانية للمخازن
     */
    private VBox createInventoryChartsContent() {
        VBox content = new VBox(20);
        content.setPadding(new Insets(20));

        Label titleLabel = new Label("الرسوم البيانية والتحليلات:");
        titleLabel.getStyleClass().add("section-title");

        // منطقة الرسوم البيانية
        VBox chartsArea = new VBox(15);
        chartsArea.setAlignment(Pos.CENTER);
        chartsArea.setPadding(new Insets(30));
        chartsArea.setStyle("-fx-background-color: #f8f9fa; -fx-border-color: #dee2e6; -fx-border-width: 1; -fx-border-radius: 5;");

        FontAwesomeIconView chartIcon = new FontAwesomeIconView(FontAwesomeIcon.PIE_CHART);
        chartIcon.setSize("48px");
        chartIcon.setStyle("-fx-fill: #6c757d;");

        Label chartLabel = new Label("الرسوم البيانية للمخازن");
        chartLabel.setStyle("-fx-font-size: 18px; -fx-font-weight: bold; -fx-text-fill: #495057;");

        Label chartDesc = new Label("سيتم عرض الرسوم البيانية للمخازن هنا\nبما في ذلك توزيع المخزون والحركات");
        chartDesc.setStyle("-fx-text-alignment: center; -fx-text-fill: #6c757d;");

        chartsArea.getChildren().addAll(chartIcon, chartLabel, chartDesc);

        content.getChildren().addAll(titleLabel, chartsArea);
        return content;
    }

    // متغيرات للصفحات الجديدة
    private String currentPage = "MAIN";
    private VBox currentPageContent = new VBox();

    /**
     * عرض صفحة تقييم المخزون
     */
    private void showInventoryValuationPage() {
        currentPage = "VALUATION";
        // سيتم تطوير هذه الوظيفة لاحقاً مع باقي التحديثات
        showValuationDialog();
    }

    /**
     * عرض نافذة تقييم المخزون (مؤقت)
     */
    private void showValuationDialog() {
        Stage dialog = new Stage();
        dialog.setTitle("📊 تقييم المخزون");
        dialog.initModality(Modality.APPLICATION_MODAL);
        dialog.setWidth(1000);
        dialog.setHeight(700);

        VBox mainLayout = new VBox(20);
        mainLayout.setPadding(new Insets(20));

        // العنوان
        Label titleLabel = new Label("📊 تقييم المخزون");
        titleLabel.getStyleClass().add("dialog-title");

        // معلومات التقييم
        Label infoLabel = new Label("تاريخ التقييم: " + java.time.LocalDateTime.now().format(java.time.format.DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm")));
        infoLabel.setStyle("-fx-font-size: 12px; -fx-text-fill: #666;");

        // فلاتر التقييم
        VBox filtersBox = createValuationFilters();

        // جدول التقييم
        TableView<Item> valuationTable = createValuationTable();

        // وضع الجدول في ScrollPane
        ScrollPane scrollPane = new ScrollPane(valuationTable);
        scrollPane.setFitToWidth(true);
        scrollPane.setVbarPolicy(ScrollPane.ScrollBarPolicy.AS_NEEDED);
        scrollPane.setHbarPolicy(ScrollPane.ScrollBarPolicy.AS_NEEDED);
        scrollPane.setPrefHeight(400);
        VBox.setVgrow(scrollPane, Priority.ALWAYS);

        // إجماليات التقييم
        HBox totalsBox = createValuationTotals();

        // أزرار الإجراءات
        HBox actionsBox = createValuationActions();

        mainLayout.getChildren().addAll(titleLabel, infoLabel, filtersBox, scrollPane, totalsBox, actionsBox);

        Scene scene = new Scene(mainLayout);
        scene.getStylesheets().add(getClass().getResource("/css/styles.css").toExternalForm());
        dialog.setScene(scene);
        dialog.showAndWait();
    }

    /**
     * إنشاء فلاتر التقييم
     */
    private VBox createValuationFilters() {
        VBox filtersBox = new VBox(10);
        filtersBox.setPadding(new Insets(15));
        filtersBox.setStyle("-fx-background-color: #f8f9fa; -fx-border-color: #dee2e6; -fx-border-width: 1; -fx-border-radius: 8;");

        Label filtersLabel = new Label("🔍 فلاتر التقييم");
        filtersLabel.setStyle("-fx-font-weight: bold; -fx-font-size: 14px;");

        HBox filtersRow = new HBox(15);
        filtersRow.setAlignment(Pos.CENTER_LEFT);

        // فلتر الصنف
        ComboBox<String> itemFilter = new ComboBox<>();
        itemFilter.getItems().add("جميع الأصناف");
        itemFilter.getItems().addAll(items.stream().map(Item::getName).distinct().sorted().toArray(String[]::new));
        itemFilter.setValue("جميع الأصناف");
        itemFilter.setPrefWidth(150);

        // فلتر المخزن
        ComboBox<String> warehouseFilter = new ComboBox<>();
        warehouseFilter.getItems().add("جميع المخازن");
        warehouseFilter.getItems().addAll(warehouses.stream().map(Warehouse::getName).toArray(String[]::new));
        warehouseFilter.setValue("جميع المخازن");
        warehouseFilter.setPrefWidth(150);

        // فلتر الفئة
        ComboBox<String> categoryFilter = new ComboBox<>();
        categoryFilter.getItems().add("جميع الفئات");
        categoryFilter.getItems().addAll(items.stream().map(Item::getCategory).distinct().sorted().toArray(String[]::new));
        categoryFilter.setValue("جميع الفئات");
        categoryFilter.setPrefWidth(150);

        Button applyFiltersBtn = new Button("تطبيق");
        applyFiltersBtn.getStyleClass().add("filter-button");
        applyFiltersBtn.setOnAction(e -> applyValuationFilters(itemFilter, warehouseFilter, categoryFilter));

        Button resetFiltersBtn = new Button("إعادة تعيين");
        resetFiltersBtn.setOnAction(e -> {
            itemFilter.setValue("جميع الأصناف");
            warehouseFilter.setValue("جميع المخازن");
            categoryFilter.setValue("جميع الفئات");
            applyValuationFilters(itemFilter, warehouseFilter, categoryFilter);
        });

        filtersRow.getChildren().addAll(
            new Label("الصنف:"), itemFilter,
            new Label("المخزن:"), warehouseFilter,
            new Label("الفئة:"), categoryFilter,
            applyFiltersBtn, resetFiltersBtn
        );

        filtersBox.getChildren().addAll(filtersLabel, filtersRow);
        return filtersBox;
    }

    /**
     * إنشاء جدول التقييم
     */
    private TableView<Item> createValuationTable() {
        TableView<Item> table = new TableView<>();
        table.getStyleClass().add("valuation-table");

        // عمود اسم الصنف
        TableColumn<Item, String> nameCol = new TableColumn<>("اسم الصنف");
        nameCol.setCellValueFactory(cellData -> new javafx.beans.property.SimpleStringProperty(cellData.getValue().getName()));
        nameCol.setPrefWidth(200);

        // عمود الكمية المتوفرة
        TableColumn<Item, String> quantityCol = new TableColumn<>("الكمية المتوفرة");
        quantityCol.setCellValueFactory(cellData -> {
            double quantity = cellData.getValue().getCurrentQuantity();
            return new javafx.beans.property.SimpleStringProperty(decimalFormat.format(quantity));
        });
        quantityCol.setPrefWidth(120);

        // عمود وحدة القياس
        TableColumn<Item, String> unitCol = new TableColumn<>("وحدة القياس");
        unitCol.setCellValueFactory(cellData -> new javafx.beans.property.SimpleStringProperty(cellData.getValue().getUnit().toString()));
        unitCol.setPrefWidth(100);

        // عمود سعر الوحدة
        TableColumn<Item, String> priceCol = new TableColumn<>("سعر الوحدة (متوسط متحرك)");
        priceCol.setCellValueFactory(cellData -> {
            double price = cellData.getValue().getUnitPrice();
            return new javafx.beans.property.SimpleStringProperty(decimalFormat.format(price));
        });
        priceCol.setPrefWidth(150);

        // عمود إجمالي القيمة
        TableColumn<Item, String> totalValueCol = new TableColumn<>("إجمالي القيمة");
        totalValueCol.setCellValueFactory(cellData -> {
            Item item = cellData.getValue();
            double totalValue = item.getCurrentQuantity() * item.getUnitPrice();
            return new javafx.beans.property.SimpleStringProperty(decimalFormat.format(totalValue));
        });
        totalValueCol.setPrefWidth(120);

        // عمود اسم المخزن
        TableColumn<Item, String> warehouseCol = new TableColumn<>("اسم المخزن");
        warehouseCol.setCellValueFactory(cellData -> new javafx.beans.property.SimpleStringProperty(cellData.getValue().getWarehouseName()));
        warehouseCol.setPrefWidth(150);

        // عمود الفئة
        TableColumn<Item, String> categoryCol = new TableColumn<>("الفئة");
        categoryCol.setCellValueFactory(cellData -> new javafx.beans.property.SimpleStringProperty(cellData.getValue().getCategory()));
        categoryCol.setPrefWidth(120);

        table.getColumns().addAll(nameCol, quantityCol, unitCol, priceCol, totalValueCol, warehouseCol, categoryCol);

        // تحميل البيانات
        table.getItems().addAll(items.stream().filter(item -> item.getCurrentQuantity() > 0).toArray(Item[]::new));

        return table;
    }

    /**
     * إنشاء إجماليات التقييم
     */
    private HBox createValuationTotals() {
        HBox totalsBox = new HBox(30);
        totalsBox.setAlignment(Pos.CENTER);
        totalsBox.setPadding(new Insets(15));
        totalsBox.setStyle("-fx-background-color: #e9ecef; -fx-border-radius: 8; -fx-background-radius: 8;");

        // إجمالي الأصناف
        VBox totalItemsBox = new VBox(5);
        totalItemsBox.setAlignment(Pos.CENTER);
        Label totalItemsLabel = new Label("إجمالي الأصناف");
        totalItemsLabel.setStyle("-fx-font-weight: bold;");
        Label totalItemsValue = new Label(String.valueOf(items.stream().filter(item -> item.getCurrentQuantity() > 0).count()));
        totalItemsValue.setStyle("-fx-font-size: 18px; -fx-font-weight: bold; -fx-text-fill: #007bff;");
        totalItemsBox.getChildren().addAll(totalItemsLabel, totalItemsValue);

        // إجمالي القيمة
        VBox totalValueBox = new VBox(5);
        totalValueBox.setAlignment(Pos.CENTER);
        Label totalValueLabel = new Label("إجمالي القيمة");
        totalValueLabel.setStyle("-fx-font-weight: bold;");
        double totalValue = items.stream()
            .filter(item -> item.getCurrentQuantity() > 0)
            .mapToDouble(item -> item.getCurrentQuantity() * item.getUnitPrice())
            .sum();
        Label totalValueValue = new Label(decimalFormat.format(totalValue) + " ريال");
        totalValueValue.setStyle("-fx-font-size: 18px; -fx-font-weight: bold; -fx-text-fill: #28a745;");
        totalValueBox.getChildren().addAll(totalValueLabel, totalValueValue);

        // إجمالي المخازن
        VBox totalWarehousesBox = new VBox(5);
        totalWarehousesBox.setAlignment(Pos.CENTER);
        Label totalWarehousesLabel = new Label("عدد المخازن");
        totalWarehousesLabel.setStyle("-fx-font-weight: bold;");
        Label totalWarehousesValue = new Label(String.valueOf(warehouses.size()));
        totalWarehousesValue.setStyle("-fx-font-size: 18px; -fx-font-weight: bold; -fx-text-fill: #6f42c1;");
        totalWarehousesBox.getChildren().addAll(totalWarehousesLabel, totalWarehousesValue);

        totalsBox.getChildren().addAll(totalItemsBox, totalValueBox, totalWarehousesBox);
        return totalsBox;
    }

    /**
     * إنشاء أزرار الإجراءات
     */
    private HBox createValuationActions() {
        HBox actionsBox = new HBox(15);
        actionsBox.setAlignment(Pos.CENTER);
        actionsBox.setPadding(new Insets(15, 0, 0, 0));

        Button printBtn = new Button("طباعة التقرير");
        FontAwesomeIconView printIcon = new FontAwesomeIconView(FontAwesomeIcon.PRINT);
        printIcon.setSize("12px");
        printBtn.setGraphic(printIcon);
        printBtn.getStyleClass().add("print-button");
        printBtn.setOnAction(e -> printValuationReport());

        Button exportBtn = new Button("تصدير PDF");
        FontAwesomeIconView exportIcon = new FontAwesomeIconView(FontAwesomeIcon.FILE_PDF_ALT);
        exportIcon.setSize("12px");
        exportBtn.setGraphic(exportIcon);
        exportBtn.getStyleClass().add("export-button");
        exportBtn.setOnAction(e -> exportValuationReport());

        Button refreshBtn = new Button("تحديث");
        FontAwesomeIconView refreshIcon = new FontAwesomeIconView(FontAwesomeIcon.REFRESH);
        refreshIcon.setSize("12px");
        refreshBtn.setGraphic(refreshIcon);
        refreshBtn.getStyleClass().add("refresh-button");
        refreshBtn.setOnAction(e -> refreshValuationData());

        actionsBox.getChildren().addAll(printBtn, exportBtn, refreshBtn);
        return actionsBox;
    }

    /**
     * تطبيق فلاتر التقييم
     */
    private void applyValuationFilters(ComboBox<String> itemFilter, ComboBox<String> warehouseFilter, ComboBox<String> categoryFilter) {
        // سيتم تطبيق الفلاتر هنا
        showPlaceholderDialog("معلومات", "تم تطبيق الفلاتر بنجاح");
    }

    /**
     * طباعة تقرير التقييم
     */
    private void printValuationReport() {
        showPlaceholderDialog("معلومات", "سيتم طباعة تقرير التقييم");
    }

    /**
     * تصدير تقرير التقييم
     */
    private void exportValuationReport() {
        showPlaceholderDialog("معلومات", "سيتم تصدير تقرير التقييم إلى PDF");
    }

    /**
     * تحديث بيانات التقييم
     */
    private void refreshValuationData() {
        showPlaceholderDialog("معلومات", "تم تحديث بيانات التقييم");
    }

    /**
     * عرض صفحة إدارة المخازن
     */
    private void showWarehouseManagementPage() {
        currentPage = "WAREHOUSE_MANAGEMENT";
        showPlaceholderDialog("إدارة المخازن", "يمكنك إدارة المخازن من تبويب المخازن أعلاه");
    }

    /**
     * عرض صفحة إضافة صنف
     */
    private void showAddItemPage() {
        currentPage = "ADD_ITEM";
        showItemDialog(null);
    }

    /**
     * عرض صفحة الاستلام
     */
    private void showReceivePage() {
        currentPage = "RECEIVE";
        showMovementDialog(null, StockMovement.MovementType.IN);
    }

    /**
     * عرض صفحة الصرف
     */
    private void showIssuePage() {
        currentPage = "ISSUE";
        showMovementDialog(null, StockMovement.MovementType.OUT);
    }

    /**
     * عرض صفحة التقارير
     */
    private void showInventoryReportsPage() {
        currentPage = "REPORTS";
        showInventoryReportsDialog();
    }

    /**
     * عرض صفحة الرسوم البيانية
     */
    private void showInventoryChartsPage() {
        currentPage = "CHARTS";
        showInventoryChartsDialog();
    }

    /**
     * عرض نافذة الرسوم البيانية
     */
    private void showInventoryChartsDialog() {
        Stage dialog = new Stage();
        dialog.setTitle("📊 الرسوم البيانية للمخزون");
        dialog.initModality(Modality.APPLICATION_MODAL);
        dialog.setWidth(1000);
        dialog.setHeight(700);

        VBox mainLayout = new VBox(20);
        mainLayout.setPadding(new Insets(20));

        // العنوان
        Label titleLabel = new Label("📊 الرسوم البيانية والتحليلات");
        titleLabel.getStyleClass().add("dialog-title");

        // تبويبات الرسوم البيانية
        TabPane chartsTabPane = new TabPane();

        // تبويب الأصناف الأكثر تداولاً
        Tab mostActiveTab = new Tab("الأصناف الأكثر تداولاً");
        mostActiveTab.setContent(createMostActiveItemsChart());
        mostActiveTab.setClosable(false);

        // تبويب حركة الأصناف
        Tab movementTab = new Tab("حركة الأصناف");
        movementTab.setContent(createItemMovementChart());
        movementTab.setClosable(false);

        // تبويب قيمة المخزون حسب الفئة
        Tab categoryValueTab = new Tab("قيمة المخزون حسب الفئة");
        categoryValueTab.setContent(createCategoryValueChart());
        categoryValueTab.setClosable(false);

        // تبويب قيمة المخزون حسب المخزن
        Tab warehouseValueTab = new Tab("قيمة المخزون حسب المخزن");
        warehouseValueTab.setContent(createWarehouseValueChart());
        warehouseValueTab.setClosable(false);

        chartsTabPane.getTabs().addAll(mostActiveTab, movementTab, categoryValueTab, warehouseValueTab);

        // زر الإغلاق
        Button closeButton = new Button("إغلاق");
        closeButton.setOnAction(e -> dialog.close());

        HBox buttonBox = new HBox();
        buttonBox.setAlignment(Pos.CENTER_RIGHT);
        buttonBox.getChildren().add(closeButton);

        mainLayout.getChildren().addAll(titleLabel, chartsTabPane, buttonBox);

        Scene scene = new Scene(mainLayout);
        scene.getStylesheets().add(getClass().getResource("/css/styles.css").toExternalForm());
        dialog.setScene(scene);
        dialog.showAndWait();
    }

    /**
     * إنشاء رسم بياني للأصناف الأكثر تداولاً
     */
    private VBox createMostActiveItemsChart() {
        VBox content = new VBox(20);
        content.setPadding(new Insets(20));

        Label titleLabel = new Label("📈 الأصناف الأكثر تداولاً");
        titleLabel.setStyle("-fx-font-size: 16px; -fx-font-weight: bold;");

        // إنشاء جدول بدلاً من الرسم البياني (مؤقت)
        TableView<Item> mostActiveTable = new TableView<>();

        TableColumn<Item, String> nameCol = new TableColumn<>("اسم الصنف");
        nameCol.setCellValueFactory(cellData -> new javafx.beans.property.SimpleStringProperty(cellData.getValue().getName()));
        nameCol.setPrefWidth(200);

        TableColumn<Item, String> movementsCol = new TableColumn<>("عدد الحركات");
        movementsCol.setCellValueFactory(cellData -> {
            // حساب عدد الحركات للصنف
            long movementCount = stockMovements.stream()
                .filter(movement -> movement.getItemName().equals(cellData.getValue().getName()))
                .count();
            return new javafx.beans.property.SimpleStringProperty(String.valueOf(movementCount));
        });
        movementsCol.setPrefWidth(120);

        TableColumn<Item, String> totalQuantityCol = new TableColumn<>("إجمالي الكمية المتحركة");
        totalQuantityCol.setCellValueFactory(cellData -> {
            // حساب إجمالي الكمية المتحركة
            double totalQuantity = stockMovements.stream()
                .filter(movement -> movement.getItemName().equals(cellData.getValue().getName()))
                .mapToDouble(StockMovement::getQuantity)
                .sum();
            return new javafx.beans.property.SimpleStringProperty(decimalFormat.format(totalQuantity));
        });
        totalQuantityCol.setPrefWidth(150);

        mostActiveTable.getColumns().addAll(nameCol, movementsCol, totalQuantityCol);

        // ترتيب الأصناف حسب عدد الحركات
        java.util.List<Item> sortedItems = items.stream()
            .sorted((item1, item2) -> {
                long count1 = stockMovements.stream().filter(m -> m.getItemName().equals(item1.getName())).count();
                long count2 = stockMovements.stream().filter(m -> m.getItemName().equals(item2.getName())).count();
                return Long.compare(count2, count1); // ترتيب تنازلي
            })
            .limit(10) // أفضل 10 أصناف
            .collect(java.util.stream.Collectors.toList());

        mostActiveTable.getItems().addAll(sortedItems);

        ScrollPane scrollPane = new ScrollPane(mostActiveTable);
        scrollPane.setFitToWidth(true);
        scrollPane.setPrefHeight(400);
        VBox.setVgrow(scrollPane, Priority.ALWAYS);

        content.getChildren().addAll(titleLabel, scrollPane);
        return content;
    }

    /**
     * إنشاء رسم بياني لحركة الأصناف
     */
    private VBox createItemMovementChart() {
        VBox content = new VBox(20);
        content.setPadding(new Insets(20));

        Label titleLabel = new Label("📊 حركة الأصناف (آخر 30 يوم)");
        titleLabel.setStyle("-fx-font-size: 16px; -fx-font-weight: bold;");

        // إنشاء جدول للحركات الأخيرة
        TableView<StockMovement> movementTable = new TableView<>();

        TableColumn<StockMovement, String> dateCol = new TableColumn<>("التاريخ");
        dateCol.setCellValueFactory(cellData -> new javafx.beans.property.SimpleStringProperty(
            cellData.getValue().getDateTime().format(java.time.format.DateTimeFormatter.ofPattern("dd/MM/yyyy"))));
        dateCol.setPrefWidth(100);

        TableColumn<StockMovement, String> itemCol = new TableColumn<>("الصنف");
        itemCol.setCellValueFactory(cellData -> new javafx.beans.property.SimpleStringProperty(cellData.getValue().getItemName()));
        itemCol.setPrefWidth(200);

        TableColumn<StockMovement, String> typeCol = new TableColumn<>("نوع الحركة");
        typeCol.setCellValueFactory(cellData -> new javafx.beans.property.SimpleStringProperty(cellData.getValue().getMovementType().getArabicName()));
        typeCol.setPrefWidth(100);

        TableColumn<StockMovement, String> quantityCol = new TableColumn<>("الكمية");
        quantityCol.setCellValueFactory(cellData -> new javafx.beans.property.SimpleStringProperty(
            decimalFormat.format(cellData.getValue().getQuantity())));
        quantityCol.setPrefWidth(100);

        movementTable.getColumns().addAll(dateCol, itemCol, typeCol, quantityCol);

        // عرض الحركات الأخيرة (آخر 30 يوم)
        java.time.LocalDateTime thirtyDaysAgo = java.time.LocalDateTime.now().minusDays(30);
        java.util.List<StockMovement> recentMovements = stockMovements.stream()
            .filter(movement -> movement.getDateTime().isAfter(thirtyDaysAgo))
            .sorted((m1, m2) -> m2.getDateTime().compareTo(m1.getDateTime()))
            .collect(java.util.stream.Collectors.toList());

        movementTable.getItems().addAll(recentMovements);

        ScrollPane scrollPane = new ScrollPane(movementTable);
        scrollPane.setFitToWidth(true);
        scrollPane.setPrefHeight(400);
        VBox.setVgrow(scrollPane, Priority.ALWAYS);

        content.getChildren().addAll(titleLabel, scrollPane);
        return content;
    }

    /**
     * إنشاء رسم بياني لقيمة المخزون حسب الفئة
     */
    private VBox createCategoryValueChart() {
        VBox content = new VBox(20);
        content.setPadding(new Insets(20));

        Label titleLabel = new Label("💰 قيمة المخزون حسب الفئة");
        titleLabel.setStyle("-fx-font-size: 16px; -fx-font-weight: bold;");

        // إنشاء جدول للفئات وقيمها
        TableView<String> categoryTable = new TableView<>();

        TableColumn<String, String> categoryCol = new TableColumn<>("الفئة");
        categoryCol.setCellValueFactory(cellData -> new javafx.beans.property.SimpleStringProperty(cellData.getValue()));
        categoryCol.setPrefWidth(200);

        TableColumn<String, String> itemsCountCol = new TableColumn<>("عدد الأصناف");
        itemsCountCol.setCellValueFactory(cellData -> {
            long count = items.stream().filter(item -> item.getCategory().equals(cellData.getValue())).count();
            return new javafx.beans.property.SimpleStringProperty(String.valueOf(count));
        });
        itemsCountCol.setPrefWidth(120);

        TableColumn<String, String> totalValueCol = new TableColumn<>("إجمالي القيمة");
        totalValueCol.setCellValueFactory(cellData -> {
            double totalValue = items.stream()
                .filter(item -> item.getCategory().equals(cellData.getValue()))
                .mapToDouble(item -> item.getCurrentQuantity() * item.getUnitPrice())
                .sum();
            return new javafx.beans.property.SimpleStringProperty(decimalFormat.format(totalValue) + " ريال");
        });
        totalValueCol.setPrefWidth(150);

        categoryTable.getColumns().addAll(categoryCol, itemsCountCol, totalValueCol);

        // الحصول على الفئات المختلفة
        java.util.List<String> categories = items.stream()
            .map(Item::getCategory)
            .distinct()
            .sorted()
            .collect(java.util.stream.Collectors.toList());

        categoryTable.getItems().addAll(categories);

        ScrollPane scrollPane = new ScrollPane(categoryTable);
        scrollPane.setFitToWidth(true);
        scrollPane.setPrefHeight(400);
        VBox.setVgrow(scrollPane, Priority.ALWAYS);

        content.getChildren().addAll(titleLabel, scrollPane);
        return content;
    }

    /**
     * إنشاء رسم بياني لقيمة المخزون حسب المخزن
     */
    private VBox createWarehouseValueChart() {
        VBox content = new VBox(20);
        content.setPadding(new Insets(20));

        Label titleLabel = new Label("🏪 قيمة المخزون حسب المخزن");
        titleLabel.setStyle("-fx-font-size: 16px; -fx-font-weight: bold;");

        // إنشاء جدول للمخازن وقيمها
        TableView<Warehouse> warehouseTable = new TableView<>();

        TableColumn<Warehouse, String> warehouseCol = new TableColumn<>("المخزن");
        warehouseCol.setCellValueFactory(cellData -> new javafx.beans.property.SimpleStringProperty(cellData.getValue().getName()));
        warehouseCol.setPrefWidth(200);

        TableColumn<Warehouse, String> itemsCountCol = new TableColumn<>("عدد الأصناف");
        itemsCountCol.setCellValueFactory(cellData -> {
            long count = items.stream().filter(item -> item.getWarehouseName().equals(cellData.getValue().getName())).count();
            return new javafx.beans.property.SimpleStringProperty(String.valueOf(count));
        });
        itemsCountCol.setPrefWidth(120);

        TableColumn<Warehouse, String> totalValueCol = new TableColumn<>("إجمالي القيمة");
        totalValueCol.setCellValueFactory(cellData -> {
            double totalValue = items.stream()
                .filter(item -> item.getWarehouseName().equals(cellData.getValue().getName()))
                .mapToDouble(item -> item.getCurrentQuantity() * item.getUnitPrice())
                .sum();
            return new javafx.beans.property.SimpleStringProperty(decimalFormat.format(totalValue) + " ريال");
        });
        totalValueCol.setPrefWidth(150);

        warehouseTable.getColumns().addAll(warehouseCol, itemsCountCol, totalValueCol);
        warehouseTable.getItems().addAll(warehouses);

        ScrollPane scrollPane = new ScrollPane(warehouseTable);
        scrollPane.setFitToWidth(true);
        scrollPane.setPrefHeight(400);
        VBox.setVgrow(scrollPane, Priority.ALWAYS);

        content.getChildren().addAll(titleLabel, scrollPane);
        return content;
    }

    /**
     * تطبيق فلاتر تقرير المخزون
     */
    private void applyInventoryReportFilters() {
        showPlaceholderDialog("معلومات", "تم تطبيق فلاتر التقرير بنجاح");
    }

    /**
     * طباعة تقرير المخزون
     */
    private void printInventoryReport() {
        showPlaceholderDialog("معلومات", "سيتم طباعة تقرير المخزون");
    }

    /**
     * تصدير تقرير المخزون
     */
    private void exportInventoryReport() {
        showPlaceholderDialog("معلومات", "سيتم تصدير تقرير المخزون إلى Excel");
    }
}
