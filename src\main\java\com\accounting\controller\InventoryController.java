package com.accounting.controller;

import com.accounting.model.*;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.collections.transformation.FilteredList;
import javafx.geometry.Insets;
import javafx.geometry.Pos;
import javafx.scene.Scene;
import javafx.scene.control.*;
import javafx.scene.control.cell.PropertyValueFactory;
import javafx.scene.layout.*;
import javafx.stage.Modality;
import javafx.stage.Stage;
import de.jensd.fx.glyphs.fontawesome.FontAwesomeIcon;
import de.jensd.fx.glyphs.fontawesome.FontAwesomeIconView;

import java.text.DecimalFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;

/**
 * متحكم موديول المخازن
 * Inventory Module Controller
 */
public class InventoryController {
    
    private ObservableList<Warehouse> warehouses = FXCollections.observableArrayList();
    private ObservableList<Item> items = FXCollections.observableArrayList();
    private ObservableList<StockMovement> stockMovements = FXCollections.observableArrayList();
    
    private FilteredList<Item> filteredItems;
    private FilteredList<StockMovement> filteredMovements;
    private DecimalFormat decimalFormat = new DecimalFormat("#,##0.00");
    private DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("dd/MM/yyyy");
    private DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm");
    
    // مراجع للواجهة
    private ComboBox<Warehouse> warehouseFilter;
    private ComboBox<String> categoryFilter;
    private TextField searchField;
    private TableView<Item> itemsTable;
    
    public InventoryController() {
        initializeData();
        setupFilters();
    }
    
    /**
     * تهيئة البيانات الافتراضية
     */
    private void initializeData() {
        // إضافة مخازن افتراضية
        warehouses.addAll(
            new Warehouse("WH001", "مخزن الزجاج", "الطابق الأول - قسم أ", "مخزن خاص بجميع أنواع الزجاج"),
            new Warehouse("WH002", "مخزن الألومنيوم", "الطابق الأول - قسم ب", "مخزن خاص بالألومنيوم والمعادن"),
            new Warehouse("WH003", "مخزن الأكسسوارات", "الطابق الثاني", "مخزن المقابض والأكسسوارات"),
            new Warehouse("WH004", "مخزن المواد الخام", "المستودع الخارجي", "مخزن المواد الخام والكيماويات"),
            new Warehouse("WH005", "مخزن المنتجات النهائية", "الطابق الثالث", "مخزن المنتجات الجاهزة للتسليم"),
            new Warehouse("WH006", "مخزن قطع الغيار", "الطابق الأول - قسم ج", "مخزن قطع الغيار والصيانة"),
            new Warehouse("WH007", "مخزن الأدوات", "ورشة العمل", "مخزن الأدوات والمعدات"),
            new Warehouse("WH008", "مخزن التالف", "المستودع الخلفي", "مخزن الأصناف التالفة والمرتجعة"),
            new Warehouse("WH009", "مخزن الحجر الصحي", "منطقة منفصلة", "مخزن مؤقت للفحص والحجر الصحي"),
            new Warehouse("WH010", "مخزن الطوارئ", "الطابق السفلي", "مخزن احتياطي للحالات الطارئة")
        );
        
        // إضافة أصناف افتراضية
        addSampleItems();
        
        // إضافة حركات افتراضية
        addSampleMovements();
    }
    
    /**
     * إضافة أصناف تجريبية
     */
    private void addSampleItems() {
        // زجاج شفاف (له أبعاد)
        Item glass1 = new Item("GL001", "زجاج شفاف 6مم", "زجاج", Unit.SQUARE_METER, "WH001");
        glass1.setWarehouseName("مخزن الزجاج");
        glass1.setHasDimensions(true);
        glass1.setLength(2000.0); // 2 متر
        glass1.setWidth(1000.0); // 1 متر
        glass1.setPieces(75); // 75 قطعة
        glass1.setThickness(6.0);
        glass1.setColor("شفاف");
        glass1.setType("عادي");
        glass1.setCurrentQuantity(glass1.calculateArea()); // 150 م² محسوبة
        glass1.setUnitPrice(45.0);
        glass1.setSupplier("شركة الزجاج المصري");
        glass1.setNotes("زجاج عالي الجودة - له أبعاد");
        
        // ألومنيوم (بدون أبعاد)
        Item aluminum1 = new Item("AL001", "ألومنيوم أبيض 50×50", "ألومنيوم", Unit.LINEAR_METER, "WH002");
        aluminum1.setWarehouseName("مخزن الألومنيوم");
        aluminum1.setHasDimensions(false); // بدون أبعاد
        aluminum1.setCurrentQuantity(500.0);
        aluminum1.setUnitPrice(25.0);
        aluminum1.setSupplier("مصنع الألومنيوم الحديث");
        aluminum1.setColor("أبيض");
        aluminum1.setType("مربع");
        aluminum1.setNotes("ألومنيوم عادي - بدون أبعاد");
        
        // مقابض (بدون أبعاد)
        Item handle1 = new Item("HD001", "مقبض ألومنيوم فضي", "أكسسوارات", Unit.PIECE, "WH003");
        handle1.setWarehouseName("مخزن الأكسسوارات");
        handle1.setHasDimensions(false); // بدون أبعاد
        handle1.setCurrentQuantity(200.0);
        handle1.setUnitPrice(15.0);
        handle1.setSupplier("شركة الأكسسوارات المتقدمة");
        handle1.setColor("فضي");
        handle1.setNotes("مقابض عادية - بدون أبعاد");
        
        // مادة لاصقة (بدون أبعاد)
        Item adhesive1 = new Item("CH001", "سيليكون شفاف", "مواد كيميائية", Unit.LITER, "WH004");
        adhesive1.setWarehouseName("مخزن المواد الخام");
        adhesive1.setHasDimensions(false); // بدون أبعاد
        adhesive1.setCurrentQuantity(50.0);
        adhesive1.setUnitPrice(35.0);
        adhesive1.setSupplier("شركة الكيماويات الصناعية");
        adhesive1.setColor("شفاف");
        adhesive1.setNotes("مواد كيميائية - بدون أبعاد");

        // إضافة صنف زجاج آخر له أبعاد
        Item glass2 = new Item("GL002", "زجاج ملون أزرق 8مم", "زجاج", Unit.SQUARE_METER, "WH001");
        glass2.setWarehouseName("مخزن الزجاج");
        glass2.setHasDimensions(true);
        glass2.setLength(1500.0); // 1.5 متر
        glass2.setWidth(800.0);   // 0.8 متر
        glass2.setPieces(20);     // 20 قطعة
        glass2.setThickness(8.0);
        glass2.setColor("أزرق");
        glass2.setType("ملون");
        glass2.setCurrentQuantity(glass2.calculateArea()); // 24 م² محسوبة
        glass2.setUnitPrice(65.0);
        glass2.setSupplier("شركة الزجاج الملون");
        glass2.setNotes("زجاج ملون عالي الجودة - له أبعاد");

        items.addAll(glass1, aluminum1, handle1, adhesive1, glass2);
    }
    
    /**
     * إضافة حركات تجريبية
     */
    private void addSampleMovements() {
        // حركة استلام زجاج
        StockMovement movement1 = new StockMovement("MOV001", "GL001", "زجاج شفاف 6مم", "WH001",
                StockMovement.MovementType.IN, 100.0, 45.0);
        movement1.setWarehouseName("مخزن الزجاج");
        movement1.setBalanceAfter(100.0);
        movement1.setUser("أحمد محمد");
        movement1.setReference("فاتورة شراء رقم 2024001");
        movement1.setNotes("استلام دفعة جديدة من الزجاج");
        
        // حركة صرف ألومنيوم
        StockMovement movement2 = new StockMovement("MOV002", "AL001", "ألومنيوم أبيض 50×50", "WH002",
                StockMovement.MovementType.OUT, 50.0, 25.0);
        movement2.setWarehouseName("مخزن الألومنيوم");
        movement2.setBalanceAfter(450.0);
        movement2.setUser("فاطمة علي");
        movement2.setReference("أمر إنتاج رقم P2024001");
        movement2.setNotes("صرف للإنتاج");
        
        // حركة استلام مقابض
        StockMovement movement3 = new StockMovement("MOV003", "HD001", "مقبض ألومنيوم فضي", "WH003",
                StockMovement.MovementType.IN, 100.0, 15.0);
        movement3.setWarehouseName("مخزن الأكسسوارات");
        movement3.setBalanceAfter(300.0);
        movement3.setUser("محمد أحمد");
        movement3.setReference("فاتورة شراء رقم 2024002");
        movement3.setNotes("استلام مقابض جديدة");
        
        // حركة صرف سيليكون
        StockMovement movement4 = new StockMovement("MOV004", "CH001", "سيليكون شفاف", "WH004",
                StockMovement.MovementType.OUT, 10.0, 35.0);
        movement4.setWarehouseName("مخزن المواد الخام");
        movement4.setBalanceAfter(40.0);
        movement4.setUser("سارة محمود");
        movement4.setReference("أمر إنتاج رقم P2024002");
        movement4.setNotes("صرف للإنتاج");
        
        // حركة استلام زجاج ملون
        StockMovement movement5 = new StockMovement("MOV005", "GL002", "زجاج ملون أزرق 8مم", "WH001",
                StockMovement.MovementType.IN, 50.0, 65.0);
        movement5.setWarehouseName("مخزن الزجاج");
        movement5.setBalanceAfter(74.0);
        movement5.setUser("علي حسن");
        movement5.setReference("فاتورة شراء رقم 2024003");
        movement5.setNotes("استلام زجاج ملون عالي الجودة");
        
        // حركة صرف ألومنيوم
        StockMovement movement6 = new StockMovement("MOV006", "AL001", "ألومنيوم أبيض 50×50", "WH002",
                StockMovement.MovementType.OUT, 25.0, 25.0);
        movement6.setWarehouseName("مخزن الألومنيوم");
        movement6.setBalanceAfter(425.0);
        movement6.setUser("نور الدين");
        movement6.setReference("أمر إنتاج رقم P2024003");
        movement6.setNotes("صرف للعميل الجديد");
        
        // حركة استلام مقابض إضافية
        StockMovement movement7 = new StockMovement("MOV007", "HD001", "مقبض ألومنيوم فضي", "WH003",
                StockMovement.MovementType.IN, 50.0, 15.0);
        movement7.setWarehouseName("مخزن الأكسسوارات");
        movement7.setBalanceAfter(350.0);
        movement7.setUser("ليلى أحمد");
        movement7.setReference("فاتورة شراء رقم 2024004");
        movement7.setNotes("تعزيز المخزون");
        
        // حركة صرف زجاج شفاف
        StockMovement movement8 = new StockMovement("MOV008", "GL001", "زجاج شفاف 6مم", "WH001",
                StockMovement.MovementType.OUT, 30.0, 45.0);
        movement8.setWarehouseName("مخزن الزجاج");
        movement8.setBalanceAfter(120.0);
        movement8.setUser("خالد محمد");
        movement8.setReference("أمر إنتاج رقم P2024004");
        movement8.setNotes("صرف لمشروع خاص");
        
        // حركة تعديل مخزون
        StockMovement movement9 = new StockMovement("MOV009", "CH001", "سيليكون شفاف", "WH004",
                StockMovement.MovementType.ADJUSTMENT, 5.0, 35.0);
        movement9.setWarehouseName("مخزن المواد الخام");
        movement9.setBalanceAfter(45.0);
        movement9.setUser("مدير المخزن");
        movement9.setReference("تعديل جرد");
        movement9.setNotes("تعديل بعد الجرد الدوري");
        
        // حركة نقل بين المخازن
        StockMovement movement10 = new StockMovement("MOV010", "HD001", "مقبض ألومنيوم فضي", "WH003",
                StockMovement.MovementType.TRANSFER, 20.0, 15.0);
        movement10.setWarehouseName("مخزن الأكسسوارات");
        movement10.setBalanceAfter(330.0);
        movement10.setUser("مشرف النقل");
        movement10.setReference("أمر نقل رقم ********");
        movement10.setNotes("نقل إلى مخزن فرعي");
        
        stockMovements.addAll(movement1, movement2, movement3, movement4, movement5, 
                             movement6, movement7, movement8, movement9, movement10);
    }
    
    /**
     * إعداد المرشحات
     */
    private void setupFilters() {
        filteredItems = new FilteredList<>(items, p -> true);
        filteredMovements = new FilteredList<>(stockMovements, p -> true);
    }

    /**
     * إنشاء واجهة موديول المخازن البسيطة والعملية
     */
    public VBox createInventoryModule() {
        VBox mainContainer = new VBox(15);
        mainContainer.setPadding(new Insets(15));
        mainContainer.getStyleClass().add("inventory-module");

        // العنوان الرئيسي
        Label titleLabel = new Label("📦 موديول إدارة المخازن");
        titleLabel.getStyleClass().add("module-title");
        titleLabel.setStyle("-fx-font-size: 18px; -fx-font-weight: bold; -fx-text-fill: #2c3e50;");

        // شريط الأدوات
        HBox toolbar = createToolbar();

        // منطقة الفلترة والبحث
        VBox filterSection = createFilterSection();

        // منطقة المحتوى الرئيسية
        TabPane tabPane = createMainTabs();
        VBox.setVgrow(tabPane, Priority.ALWAYS);

        mainContainer.getChildren().addAll(titleLabel, toolbar, filterSection, tabPane);
        return mainContainer;
    }

    /**
     * إنشاء شريط الأدوات
     */
    private HBox createToolbar() {
        HBox toolbar = new HBox(15);
        toolbar.setAlignment(Pos.CENTER_LEFT);
        toolbar.setPadding(new Insets(10));
        toolbar.getStyleClass().add("toolbar");

        // زر إدارة المخازن
        Button warehousesBtn = createToolbarButton("إدارة المخازن", FontAwesomeIcon.BUILDING, this::showWarehouseManagement);

        // زر إضافة صنف
        Button addItemBtn = createToolbarButton("إضافة صنف", FontAwesomeIcon.PLUS_CIRCLE, this::showAddItemDialog);

        // زر استلام
        Button receiveBtn = createToolbarButton("استلام", FontAwesomeIcon.ARROW_DOWN, this::showReceiveDialog);

        // زر صرف
        Button issueBtn = createToolbarButton("صرف", FontAwesomeIcon.ARROW_UP, this::showIssueDialog);

        // فاصل
        Separator separator = new Separator();
        separator.setOrientation(javafx.geometry.Orientation.VERTICAL);

        // زر تقييم المخزون
        Button valuationBtn = createToolbarButton("تقييم المخزون", FontAwesomeIcon.CALCULATOR, this::showValuationReport);

        // زر التقارير
        Button reportsBtn = createToolbarButton("التقارير", FontAwesomeIcon.BAR_CHART, this::showReports);

        toolbar.getChildren().addAll(warehousesBtn, addItemBtn, receiveBtn, issueBtn, separator, valuationBtn, reportsBtn);
        return toolbar;
    }

    /**
     * إنشاء زر شريط الأدوات
     */
    private Button createToolbarButton(String text, FontAwesomeIcon icon, Runnable action) {
        Button button = new Button(text);
        FontAwesomeIconView iconView = new FontAwesomeIconView(icon);
        iconView.setSize("16px");
        button.setGraphic(iconView);
        button.getStyleClass().add("toolbar-button");
        button.setOnAction(e -> action.run());
        return button;
    }

    /**
     * إنشاء قسم الفلترة والبحث
     */
    private VBox createFilterSection() {
        VBox filterSection = new VBox(10);
        filterSection.setPadding(new Insets(15));
        filterSection.getStyleClass().add("filter-section");

        Label filterLabel = new Label("🔍 البحث والفلترة:");
        filterLabel.getStyleClass().add("section-title");

        HBox filterBox = new HBox(15);
        filterBox.setAlignment(Pos.CENTER_LEFT);

        // فلتر المخزن
        Label warehouseLabel = new Label("المخزن:");
        warehouseFilter = new ComboBox<>();
        warehouseFilter.getItems().add(null); // خيار "الكل"
        warehouseFilter.getItems().addAll(warehouses);
        warehouseFilter.setPromptText("جميع المخازن");
        warehouseFilter.setPrefWidth(150);
        warehouseFilter.setOnAction(e -> applyFilters());

        // فلتر الفئة
        Label categoryLabel = new Label("الفئة:");
        categoryFilter = new ComboBox<>();
        categoryFilter.getItems().addAll("الكل", "زجاج", "ألومنيوم", "أكسسوارات", "مواد كيميائية");
        categoryFilter.setValue("الكل");
        categoryFilter.setPrefWidth(120);
        categoryFilter.setOnAction(e -> applyFilters());

        // حقل البحث
        Label searchLabel = new Label("البحث:");
        searchField = new TextField();
        searchField.setPromptText("ابحث بالاسم أو الكود");
        searchField.setPrefWidth(200);
        searchField.textProperty().addListener((obs, oldVal, newVal) -> applyFilters());

        // زر مسح الفلاتر
        Button clearBtn = new Button("مسح");
        FontAwesomeIconView clearIcon = new FontAwesomeIconView(FontAwesomeIcon.REFRESH);
        clearIcon.setSize("12px");
        clearBtn.setGraphic(clearIcon);
        clearBtn.getStyleClass().add("clear-button");
        clearBtn.setOnAction(e -> clearFilters());

        filterBox.getChildren().addAll(
            warehouseLabel, warehouseFilter,
            categoryLabel, categoryFilter,
            searchLabel, searchField,
            clearBtn
        );

        filterSection.getChildren().addAll(filterLabel, filterBox);
        return filterSection;
    }

    /**
     * تطبيق المرشحات
     */
    private void applyFilters() {
        filteredItems.setPredicate(item -> {
            // فلتر المخزن
            if (warehouseFilter.getValue() != null) {
                if (!item.getWarehouseId().equals(warehouseFilter.getValue().getWarehouseId())) {
                    return false;
                }
            }

            // فلتر الفئة
            if (categoryFilter.getValue() != null && !categoryFilter.getValue().equals("الكل")) {
                if (!item.getCategory().equals(categoryFilter.getValue())) {
                    return false;
                }
            }

            // البحث النصي
            if (searchField.getText() != null && !searchField.getText().isEmpty()) {
                String searchText = searchField.getText().toLowerCase();
                return item.getName().toLowerCase().contains(searchText) ||
                       item.getItemId().toLowerCase().contains(searchText);
            }

            return true;
        });
    }

    /**
     * مسح المرشحات
     */
    private void clearFilters() {
        warehouseFilter.setValue(null);
        categoryFilter.setValue("الكل");
        searchField.clear();
    }

    /**
     * إنشاء التبويبات الرئيسية
     */
    private TabPane createMainTabs() {
        TabPane tabPane = new TabPane();
        tabPane.getStyleClass().add("main-tabs");

        // تبويب الأصناف
        Tab itemsTab = new Tab("الأصناف");
        itemsTab.setContent(createItemsTab());
        itemsTab.setClosable(false);

        // تبويب حركة المخزون
        Tab movementsTab = new Tab("حركة المخزون");
        movementsTab.setContent(createMovementsTab());
        movementsTab.setClosable(false);

        // تبويب المخازن
        Tab warehousesTab = new Tab("المخازن");
        warehousesTab.setContent(createWarehousesTab());
        warehousesTab.setClosable(false);

        tabPane.getTabs().addAll(itemsTab, movementsTab, warehousesTab);
        return tabPane;
    }

    /**
     * إنشاء تبويب الأصناف
     */
    private VBox createItemsTab() {
        VBox container = new VBox(15);
        container.setPadding(new Insets(15));

        // إحصائيات سريعة
        HBox statsBox = createStatsBox();

        // جدول الأصناف - مع ارتفاع مناسب لعرض 8 صفوف
        itemsTable = createItemsTable();

        // تحديد ارتفاع مناسب لعرض 8 صفوف (40 بكسل لكل صف + 35 للهيدر)
        double tableHeight = (8 * 40) + 35; // 355 بكسل
        itemsTable.setPrefHeight(tableHeight);
        itemsTable.setMinHeight(tableHeight);
        itemsTable.setMaxHeight(Double.MAX_VALUE);
        VBox.setVgrow(itemsTable, Priority.ALWAYS);

        container.getChildren().addAll(statsBox, itemsTable);
        return container;
    }

    /**
     * إنشاء صندوق الإحصائيات
     */
    private HBox createStatsBox() {
        HBox statsBox = new HBox(20);
        statsBox.setAlignment(Pos.CENTER);
        statsBox.setPadding(new Insets(15));
        statsBox.getStyleClass().add("stats-box");

        // إجمالي الأصناف
        VBox totalItemsBox = createStatCard("إجمالي الأصناف", String.valueOf(items.size()), "#3498db");

        // إجمالي القيمة
        double totalValue = items.stream().mapToDouble(Item::getTotalValue).sum();
        VBox totalValueBox = createStatCard("إجمالي القيمة", decimalFormat.format(totalValue) + " ج.م", "#2ecc71");

        // الأصناف المنخفضة
        long lowStockItems = items.stream().filter(item -> item.getCurrentQuantity() < 10).count();
        VBox lowStockBox = createStatCard("أصناف منخفضة", String.valueOf(lowStockItems), "#e74c3c");

        // المخازن النشطة
        VBox activeWarehousesBox = createStatCard("المخازن النشطة", String.valueOf(warehouses.size()), "#f39c12");

        statsBox.getChildren().addAll(totalItemsBox, totalValueBox, lowStockBox, activeWarehousesBox);
        return statsBox;
    }

    /**
     * إنشاء بطاقة إحصائية
     */
    private VBox createStatCard(String title, String value, String color) {
        VBox card = new VBox(5);
        card.setAlignment(Pos.CENTER);
        card.setPadding(new Insets(15));
        card.getStyleClass().add("stat-card");
        card.setStyle("-fx-border-color: " + color + "; -fx-border-width: 2;");

        Label titleLabel = new Label(title);
        titleLabel.getStyleClass().add("stat-title");

        Label valueLabel = new Label(value);
        valueLabel.getStyleClass().add("stat-value");
        valueLabel.setStyle("-fx-text-fill: " + color + ";");

        card.getChildren().addAll(titleLabel, valueLabel);
        return card;
    }

    /**
     * إنشاء جدول الأصناف
     */
    private TableView<Item> createItemsTable() {
        TableView<Item> table = new TableView<>();
        table.setItems(filteredItems);

        // تحديد ارتفاع الصفوف
        table.setRowFactory(tv -> {
            TableRow<Item> row = new TableRow<>();
            row.setPrefHeight(40); // ارتفاع كل صف 40 بكسل
            return row;
        });

        table.getStyleClass().add("data-table");

        // العمود: كود الصنف
        TableColumn<Item, String> idCol = new TableColumn<>("كود الصنف");
        idCol.setPrefWidth(100);
        idCol.setCellValueFactory(new PropertyValueFactory<>("itemId"));

        // العمود: اسم الصنف
        TableColumn<Item, String> nameCol = new TableColumn<>("اسم الصنف");
        nameCol.setPrefWidth(200);
        nameCol.setCellValueFactory(new PropertyValueFactory<>("name"));

        // العمود: الفئة
        TableColumn<Item, String> categoryCol = new TableColumn<>("الفئة");
        categoryCol.setPrefWidth(100);
        categoryCol.setCellValueFactory(new PropertyValueFactory<>("category"));

        // العمود: الوحدة
        TableColumn<Item, Unit> unitCol = new TableColumn<>("الوحدة");
        unitCol.setPrefWidth(100);
        unitCol.setCellValueFactory(new PropertyValueFactory<>("unit"));
        unitCol.setCellFactory(col -> new TableCell<Item, Unit>() {
            @Override
            protected void updateItem(Unit item, boolean empty) {
                super.updateItem(item, empty);
                if (empty || item == null) {
                    setText(null);
                } else {
                    setText(item.getArabicName());
                }
            }
        });

        // العمود: نوع الصنف
        TableColumn<Item, String> typeCol = new TableColumn<>("النوع");
        typeCol.setPrefWidth(80);
        typeCol.setCellFactory(col -> new TableCell<Item, String>() {
            @Override
            protected void updateItem(String item, boolean empty) {
                super.updateItem(item, empty);
                if (empty) {
                    setText(null);
                    setStyle("");
                } else {
                    Item rowItem = getTableView().getItems().get(getIndex());
                    if (rowItem.hasDimensions()) {
                        setText("أبعاد");
                        setStyle("-fx-text-fill: #007bff; -fx-font-weight: bold;");
                    } else {
                        setText("عادي");
                        setStyle("-fx-text-fill: #6c757d;");
                    }
                }
            }
        });

        // العمود: الكمية الحالية
        TableColumn<Item, Double> quantityCol = new TableColumn<>("الكمية الحالية");
        quantityCol.setPrefWidth(120);
        quantityCol.setCellValueFactory(new PropertyValueFactory<>("currentQuantity"));
        quantityCol.setCellFactory(col -> new TableCell<Item, Double>() {
            @Override
            protected void updateItem(Double item, boolean empty) {
                super.updateItem(item, empty);
                if (empty || item == null) {
                    setText(null);
                } else {
                    Item rowItem = getTableView().getItems().get(getIndex());
                    String displayText;

                    if (rowItem.hasDimensions()) {
                        // للأصناف ذات الأبعاد: عرض المساحة + تفاصيل الأبعاد
                        displayText = decimalFormat.format(item) + " " + rowItem.getUnit().getSymbol();
                        if (rowItem.getLength() != null && rowItem.getWidth() != null && rowItem.getPieces() != null) {
                            displayText += String.format("\n(%dx%d×%d)",
                                rowItem.getLength().intValue(),
                                rowItem.getWidth().intValue(),
                                rowItem.getPieces());
                        }
                    } else {
                        // للأصناف العادية
                        displayText = decimalFormat.format(item) + " " + rowItem.getUnit().getSymbol();
                    }

                    setText(displayText);

                    // تلوين الكميات المنخفضة
                    if (item < 10) {
                        setStyle("-fx-text-fill: #e74c3c; -fx-font-weight: bold;");
                    } else {
                        setStyle("");
                    }
                }
            }
        });

        // العمود: سعر الوحدة
        TableColumn<Item, Double> priceCol = new TableColumn<>("سعر الوحدة");
        priceCol.setPrefWidth(100);
        priceCol.setCellValueFactory(new PropertyValueFactory<>("unitPrice"));
        priceCol.setCellFactory(col -> new TableCell<Item, Double>() {
            @Override
            protected void updateItem(Double item, boolean empty) {
                super.updateItem(item, empty);
                if (empty || item == null) {
                    setText(null);
                } else {
                    setText(decimalFormat.format(item) + " ج.م");
                }
            }
        });

        // العمود: القيمة الإجمالية
        TableColumn<Item, Double> totalValueCol = new TableColumn<>("القيمة الإجمالية");
        totalValueCol.setPrefWidth(120);
        totalValueCol.setCellValueFactory(new PropertyValueFactory<>("totalValue"));
        totalValueCol.setCellFactory(col -> new TableCell<Item, Double>() {
            @Override
            protected void updateItem(Double item, boolean empty) {
                super.updateItem(item, empty);
                getStyleClass().add("calculated-cell");
                if (empty || item == null) {
                    setText(null);
                } else {
                    setText(decimalFormat.format(item) + " ج.م");
                }
            }
        });

        // العمود: المخزن
        TableColumn<Item, String> warehouseCol = new TableColumn<>("المخزن");
        warehouseCol.setPrefWidth(120);
        warehouseCol.setCellValueFactory(new PropertyValueFactory<>("warehouseName"));

        table.getColumns().addAll(idCol, nameCol, categoryCol, typeCol, unitCol, quantityCol, priceCol, totalValueCol, warehouseCol);
        return table;
    }

    /**
     * إنشاء تبويب حركة المخزون
     */
    private VBox createMovementsTab() {
        VBox container = new VBox(15);
        container.setPadding(new Insets(15));

        // عنوان القسم
        Label titleLabel = new Label("📊 سجل حركة المخزون");
        titleLabel.setStyle("-fx-font-size: 16px; -fx-font-weight: bold; -fx-text-fill: #2c3e50;");

        // جدول حركة المخزون - مع ارتفاع مناسب لعرض 8 صفوف
        TableView<StockMovement> movementsTable = createMovementsTable();

        // تحديد ارتفاع مناسب لعرض 8 صفوف (40 بكسل لكل صف + 35 للهيدر)
        double tableHeight = (8 * 40) + 35; // 355 بكسل
        movementsTable.setPrefHeight(tableHeight);
        movementsTable.setMinHeight(tableHeight);
        movementsTable.setMaxHeight(Double.MAX_VALUE);
        VBox.setVgrow(movementsTable, Priority.ALWAYS);

        container.getChildren().addAll(titleLabel, movementsTable);
        return container;
    }

    /**
     * إنشاء جدول حركة المخزون
     */
    private TableView<StockMovement> createMovementsTable() {
        TableView<StockMovement> table = new TableView<>();
        table.setItems(filteredMovements);

        // تحديد ارتفاع الصفوف
        table.setRowFactory(tv -> {
            TableRow<StockMovement> row = new TableRow<>();
            row.setPrefHeight(40); // ارتفاع كل صف 40 بكسل
            return row;
        });

        table.getStyleClass().add("data-table");

        // العمود: رقم الحركة
        TableColumn<StockMovement, String> idCol = new TableColumn<>("رقم الحركة");
        idCol.setPrefWidth(100);
        idCol.setCellValueFactory(new PropertyValueFactory<>("movementId"));

        // العمود: نوع الحركة
        TableColumn<StockMovement, StockMovement.MovementType> typeCol = new TableColumn<>("نوع الحركة");
        typeCol.setPrefWidth(100);
        typeCol.setCellValueFactory(new PropertyValueFactory<>("movementType"));
        typeCol.setCellFactory(col -> new TableCell<StockMovement, StockMovement.MovementType>() {
            @Override
            protected void updateItem(StockMovement.MovementType item, boolean empty) {
                super.updateItem(item, empty);
                if (empty || item == null) {
                    setText(null);
                    setStyle("");
                } else {
                    setText(item.getArabicName());
                    // تلوين حسب نوع الحركة
                    switch (item) {
                        case IN:
                            setStyle("-fx-text-fill: #27ae60; -fx-font-weight: bold;");
                            break;
                        case OUT:
                            setStyle("-fx-text-fill: #e74c3c; -fx-font-weight: bold;");
                            break;
                        default:
                            setStyle("-fx-text-fill: #f39c12; -fx-font-weight: bold;");
                    }
                }
            }
        });

        // العمود: اسم الصنف
        TableColumn<StockMovement, String> itemCol = new TableColumn<>("الصنف");
        itemCol.setPrefWidth(150);
        itemCol.setCellValueFactory(new PropertyValueFactory<>("itemName"));

        // العمود: الكمية
        TableColumn<StockMovement, Double> quantityCol = new TableColumn<>("الكمية");
        quantityCol.setPrefWidth(100);
        quantityCol.setCellValueFactory(new PropertyValueFactory<>("quantity"));
        quantityCol.setCellFactory(col -> new TableCell<StockMovement, Double>() {
            @Override
            protected void updateItem(Double item, boolean empty) {
                super.updateItem(item, empty);
                if (empty || item == null) {
                    setText(null);
                } else {
                    setText(decimalFormat.format(item));
                }
            }
        });

        // العمود: سعر الوحدة
        TableColumn<StockMovement, Double> priceCol = new TableColumn<>("سعر الوحدة");
        priceCol.setPrefWidth(100);
        priceCol.setCellValueFactory(new PropertyValueFactory<>("unitPrice"));
        priceCol.setCellFactory(col -> new TableCell<StockMovement, Double>() {
            @Override
            protected void updateItem(Double item, boolean empty) {
                super.updateItem(item, empty);
                if (empty || item == null) {
                    setText(null);
                } else {
                    setText(decimalFormat.format(item) + " ج.م");
                }
            }
        });

        // العمود: القيمة الإجمالية
        TableColumn<StockMovement, Double> totalCol = new TableColumn<>("القيمة الإجمالية");
        totalCol.setPrefWidth(120);
        totalCol.setCellValueFactory(new PropertyValueFactory<>("totalValue"));
        totalCol.setCellFactory(col -> new TableCell<StockMovement, Double>() {
            @Override
            protected void updateItem(Double item, boolean empty) {
                super.updateItem(item, empty);
                if (empty || item == null) {
                    setText(null);
                } else {
                    setText(decimalFormat.format(item) + " ج.م");
                }
            }
        });

        // العمود: التاريخ والوقت
        TableColumn<StockMovement, LocalDateTime> dateCol = new TableColumn<>("التاريخ والوقت");
        dateCol.setPrefWidth(120);
        dateCol.setCellValueFactory(new PropertyValueFactory<>("dateTime"));
        dateCol.setCellFactory(col -> new TableCell<StockMovement, LocalDateTime>() {
            @Override
            protected void updateItem(LocalDateTime item, boolean empty) {
                super.updateItem(item, empty);
                if (empty || item == null) {
                    setText(null);
                } else {
                    setText(item.format(dateTimeFormatter));
                }
            }
        });

        // العمود: المستخدم
        TableColumn<StockMovement, String> userCol = new TableColumn<>("المستخدم");
        userCol.setPrefWidth(100);
        userCol.setCellValueFactory(new PropertyValueFactory<>("user"));

        table.getColumns().addAll(idCol, typeCol, itemCol, quantityCol, priceCol, totalCol, dateCol, userCol);
        return table;
    }

    /**
     * إنشاء تبويب المخازن
     */
    private VBox createWarehousesTab() {
        VBox container = new VBox(15);
        container.setPadding(new Insets(15));

        // عنوان القسم
        Label titleLabel = new Label("🏢 قائمة المخازن");
        titleLabel.setStyle("-fx-font-size: 16px; -fx-font-weight: bold; -fx-text-fill: #2c3e50;");

        // جدول المخازن - مع ارتفاع مناسب لعرض 8 صفوف
        TableView<Warehouse> warehousesTable = createWarehousesTable();

        // تحديد ارتفاع مناسب لعرض 8 صفوف (40 بكسل لكل صف + 35 للهيدر)
        double tableHeight = (8 * 40) + 35; // 355 بكسل
        warehousesTable.setPrefHeight(tableHeight);
        warehousesTable.setMinHeight(tableHeight);
        warehousesTable.setMaxHeight(Double.MAX_VALUE);
        VBox.setVgrow(warehousesTable, Priority.ALWAYS);

        container.getChildren().addAll(titleLabel, warehousesTable);
        return container;
    }

    /**
     * إنشاء جدول المخازن
     */
    private TableView<Warehouse> createWarehousesTable() {
        TableView<Warehouse> table = new TableView<>();
        table.setItems(warehouses);

        // تحديد ارتفاع الصفوف
        table.setRowFactory(tv -> {
            TableRow<Warehouse> row = new TableRow<>();
            row.setPrefHeight(40); // ارتفاع كل صف 40 بكسل
            return row;
        });

        table.getStyleClass().add("data-table");

        // العمود: كود المخزن
        TableColumn<Warehouse, String> idCol = new TableColumn<>("كود المخزن");
        idCol.setPrefWidth(100);
        idCol.setCellValueFactory(new PropertyValueFactory<>("warehouseId"));

        // العمود: اسم المخزن
        TableColumn<Warehouse, String> nameCol = new TableColumn<>("اسم المخزن");
        nameCol.setPrefWidth(200);
        nameCol.setCellValueFactory(new PropertyValueFactory<>("name"));

        // العمود: الموقع
        TableColumn<Warehouse, String> locationCol = new TableColumn<>("الموقع");
        locationCol.setPrefWidth(150);
        locationCol.setCellValueFactory(new PropertyValueFactory<>("location"));

        // العمود: عدد الأصناف
        TableColumn<Warehouse, Integer> itemsCountCol = new TableColumn<>("عدد الأصناف");
        itemsCountCol.setPrefWidth(100);
        itemsCountCol.setCellFactory(col -> new TableCell<Warehouse, Integer>() {
            @Override
            protected void updateItem(Integer item, boolean empty) {
                super.updateItem(item, empty);
                if (empty) {
                    setText(null);
                } else {
                    Warehouse warehouse = getTableView().getItems().get(getIndex());
                    long itemsCount = items.stream()
                        .filter(i -> i.getWarehouseId().equals(warehouse.getWarehouseId()))
                        .count();
                    setText(String.valueOf(itemsCount));
                    setStyle("-fx-text-fill: #007bff; -fx-font-weight: bold;");
                }
            }
        });

        // العمود: إجمالي القيمة
        TableColumn<Warehouse, Double> totalValueCol = new TableColumn<>("إجمالي القيمة");
        totalValueCol.setPrefWidth(120);
        totalValueCol.setCellFactory(col -> new TableCell<Warehouse, Double>() {
            @Override
            protected void updateItem(Double item, boolean empty) {
                super.updateItem(item, empty);
                if (empty) {
                    setText(null);
                } else {
                    Warehouse warehouse = getTableView().getItems().get(getIndex());
                    double totalValue = items.stream()
                        .filter(i -> i.getWarehouseId().equals(warehouse.getWarehouseId()))
                        .mapToDouble(Item::getTotalValue)
                        .sum();
                    setText(decimalFormat.format(totalValue) + " ج.م");
                    setStyle("-fx-text-fill: #28a745; -fx-font-weight: bold;");
                }
            }
        });

        // العمود: الوصف
        TableColumn<Warehouse, String> descriptionCol = new TableColumn<>("الوصف");
        descriptionCol.setPrefWidth(200);
        descriptionCol.setCellValueFactory(new PropertyValueFactory<>("description"));

        table.getColumns().addAll(idCol, nameCol, locationCol, itemsCountCol, totalValueCol, descriptionCol);
        return table;
    }

    // وظائف الأزرار - نوافذ مؤقتة
    private void showWarehouseManagement() {
        showPlaceholderDialog("إدارة المخازن", "نافذة إدارة المخازن");
    }

    private void showAddItemDialog() {
        showPlaceholderDialog("إضافة صنف", "نافذة إضافة صنف جديد");
    }

    private void showReceiveDialog() {
        showPlaceholderDialog("استلام", "نافذة استلام أصناف");
    }

    private void showIssueDialog() {
        showPlaceholderDialog("صرف", "نافذة صرف أصناف");
    }

    private void showValuationReport() {
        showPlaceholderDialog("تقييم المخزون", "تقرير تقييم المخزون");
    }

    private void showReports() {
        showPlaceholderDialog("التقارير", "نافذة التقارير");
    }

    private void showInventoryChartsPage() {
        showPlaceholderDialog("الرسوم البيانية", "صفحة الرسوم البيانية للمخزون");
    }

    private void showPlaceholderDialog(String title, String message) {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle(title);
        alert.setHeaderText(null);
        alert.setContentText(message + "\n\nسيتم تطوير هذه الميزة قريباً...");
        alert.showAndWait();
    }
}
