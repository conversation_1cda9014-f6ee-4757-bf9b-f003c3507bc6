package com.accounting.controller;

import com.accounting.model.*;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.collections.transformation.FilteredList;
import javafx.geometry.Insets;
import javafx.geometry.Pos;
import javafx.scene.Scene;
import javafx.scene.control.*;
import javafx.scene.control.cell.PropertyValueFactory;
import javafx.scene.layout.*;
import javafx.stage.Modality;
import javafx.stage.Stage;
import de.jensd.fx.glyphs.fontawesome.FontAwesomeIcon;
import de.jensd.fx.glyphs.fontawesome.FontAwesomeIconView;

import java.text.DecimalFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;

/**
 * متحكم موديول المخازن
 * Inventory Module Controller
 */
public class InventoryController {
    
    private ObservableList<Warehouse> warehouses = FXCollections.observableArrayList();
    private ObservableList<Item> items = FXCollections.observableArrayList();
    private ObservableList<StockMovement> stockMovements = FXCollections.observableArrayList();
    
    private FilteredList<Item> filteredItems;
    private DecimalFormat decimalFormat = new DecimalFormat("#,##0.00");
    private DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("dd/MM/yyyy");
    private DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm");
    
    // مراجع للواجهة
    private ComboBox<Warehouse> warehouseFilter;
    private ComboBox<String> categoryFilter;
    private TextField searchField;
    private TableView<Item> itemsTable;
    
    public InventoryController() {
        initializeData();
        setupFilters();
    }
    
    /**
     * تهيئة البيانات الافتراضية
     */
    private void initializeData() {
        // إضافة مخازن افتراضية
        warehouses.addAll(
            new Warehouse("WH001", "مخزن الزجاج", "الطابق الأول - قسم أ", "مخزن خاص بجميع أنواع الزجاج"),
            new Warehouse("WH002", "مخزن الألومنيوم", "الطابق الأول - قسم ب", "مخزن خاص بالألومنيوم والمعادن"),
            new Warehouse("WH003", "مخزن الأكسسوارات", "الطابق الثاني", "مخزن المقابض والأكسسوارات"),
            new Warehouse("WH004", "مخزن المواد الخام", "المستودع الخارجي", "مخزن المواد الخام والكيماويات")
        );
        
        // إضافة أصناف افتراضية
        addSampleItems();
        
        // إضافة حركات افتراضية
        addSampleMovements();
    }
    
    /**
     * إضافة أصناف تجريبية
     */
    private void addSampleItems() {
        // زجاج شفاف (له أبعاد)
        Item glass1 = new Item("GL001", "زجاج شفاف 6مم", "زجاج", Unit.SQUARE_METER, "WH001");
        glass1.setWarehouseName("مخزن الزجاج");
        glass1.setHasDimensions(true);
        glass1.setLength(2000.0); // 2 متر
        glass1.setWidth(1000.0); // 1 متر
        glass1.setPieces(75); // 75 قطعة
        glass1.setThickness(6.0);
        glass1.setColor("شفاف");
        glass1.setType("عادي");
        glass1.setCurrentQuantity(glass1.calculateArea()); // 150 م² محسوبة
        glass1.setUnitPrice(45.0);
        glass1.setSupplier("شركة الزجاج المصري");
        glass1.setNotes("زجاج عالي الجودة - له أبعاد");
        
        // ألومنيوم (بدون أبعاد)
        Item aluminum1 = new Item("AL001", "ألومنيوم أبيض 50×50", "ألومنيوم", Unit.LINEAR_METER, "WH002");
        aluminum1.setWarehouseName("مخزن الألومنيوم");
        aluminum1.setHasDimensions(false); // بدون أبعاد
        aluminum1.setCurrentQuantity(500.0);
        aluminum1.setUnitPrice(25.0);
        aluminum1.setSupplier("مصنع الألومنيوم الحديث");
        aluminum1.setColor("أبيض");
        aluminum1.setType("مربع");
        aluminum1.setNotes("ألومنيوم عادي - بدون أبعاد");
        
        // مقابض (بدون أبعاد)
        Item handle1 = new Item("HD001", "مقبض ألومنيوم فضي", "أكسسوارات", Unit.PIECE, "WH003");
        handle1.setWarehouseName("مخزن الأكسسوارات");
        handle1.setHasDimensions(false); // بدون أبعاد
        handle1.setCurrentQuantity(200.0);
        handle1.setUnitPrice(15.0);
        handle1.setSupplier("شركة الأكسسوارات المتقدمة");
        handle1.setColor("فضي");
        handle1.setNotes("مقابض عادية - بدون أبعاد");
        
        // مادة لاصقة (بدون أبعاد)
        Item adhesive1 = new Item("CH001", "سيليكون شفاف", "مواد كيميائية", Unit.LITER, "WH004");
        adhesive1.setWarehouseName("مخزن المواد الخام");
        adhesive1.setHasDimensions(false); // بدون أبعاد
        adhesive1.setCurrentQuantity(50.0);
        adhesive1.setUnitPrice(35.0);
        adhesive1.setSupplier("شركة الكيماويات الصناعية");
        adhesive1.setColor("شفاف");
        adhesive1.setNotes("مواد كيميائية - بدون أبعاد");

        // إضافة صنف زجاج آخر له أبعاد
        Item glass2 = new Item("GL002", "زجاج ملون أزرق 8مم", "زجاج", Unit.SQUARE_METER, "WH001");
        glass2.setWarehouseName("مخزن الزجاج");
        glass2.setHasDimensions(true);
        glass2.setLength(1500.0); // 1.5 متر
        glass2.setWidth(800.0);   // 0.8 متر
        glass2.setPieces(20);     // 20 قطعة
        glass2.setThickness(8.0);
        glass2.setColor("أزرق");
        glass2.setType("ملون");
        glass2.setCurrentQuantity(glass2.calculateArea()); // 24 م² محسوبة
        glass2.setUnitPrice(65.0);
        glass2.setSupplier("شركة الزجاج الملون");
        glass2.setNotes("زجاج ملون عالي الجودة - له أبعاد");

        items.addAll(glass1, aluminum1, handle1, adhesive1, glass2);
    }
    
    /**
     * إضافة حركات تجريبية
     */
    private void addSampleMovements() {
        // حركة استلام زجاج
        StockMovement movement1 = new StockMovement("MOV001", "GL001", "زجاج شفاف 6مم", "WH001",
                StockMovement.MovementType.IN, 100.0, 45.0);
        movement1.setWarehouseName("مخزن الزجاج");
        movement1.setBalanceAfter(100.0);
        movement1.setUser("أحمد محمد");
        movement1.setReference("فاتورة شراء رقم 2024001");
        movement1.setNotes("استلام دفعة جديدة من الزجاج");
        
        // حركة صرف ألومنيوم
        StockMovement movement2 = new StockMovement("MOV002", "AL001", "ألومنيوم أبيض 50×50", "WH002",
                StockMovement.MovementType.OUT, 50.0, 25.0);
        movement2.setWarehouseName("مخزن الألومنيوم");
        movement2.setBalanceAfter(450.0);
        movement2.setUser("فاطمة علي");
        movement2.setReference("أمر إنتاج رقم ********");
        movement2.setNotes("صرف للإنتاج");
        
        stockMovements.addAll(movement1, movement2);
    }
    
    /**
     * إعداد المرشحات
     */
    private void setupFilters() {
        filteredItems = new FilteredList<>(items, p -> true);
    }
    
    /**
     * إنشاء واجهة موديول المخازن
     */
    public VBox createInventoryModule() {
        VBox mainContainer = new VBox(20);
        mainContainer.setPadding(new Insets(20));
        mainContainer.getStyleClass().add("inventory-module");
        
        // العنوان الرئيسي
        Label titleLabel = new Label("📦 موديول إدارة المخازن");
        titleLabel.getStyleClass().add("module-title");
        
        // شريط الأدوات
        HBox toolbar = createToolbar();
        
        // منطقة الفلترة والبحث
        VBox filterSection = createFilterSection();
        
        // منطقة المحتوى الرئيسية
        TabPane tabPane = createMainTabs();
        
        mainContainer.getChildren().addAll(titleLabel, toolbar, filterSection, tabPane);
        return mainContainer;
    }
    
    /**
     * إنشاء شريط الأدوات
     */
    private HBox createToolbar() {
        HBox toolbar = new HBox(15);
        toolbar.setAlignment(Pos.CENTER_LEFT);
        toolbar.setPadding(new Insets(10));
        toolbar.getStyleClass().add("toolbar");
        
        // زر إدارة المخازن
        Button warehousesBtn = createToolbarButton("إدارة المخازن", FontAwesomeIcon.BUILDING, this::showWarehouseManagement);
        
        // زر إضافة صنف
        Button addItemBtn = createToolbarButton("إضافة صنف", FontAwesomeIcon.PLUS_CIRCLE, this::showAddItemDialog);
        
        // زر استلام
        Button receiveBtn = createToolbarButton("استلام", FontAwesomeIcon.ARROW_DOWN, this::showReceiveDialog);
        
        // زر صرف
        Button issueBtn = createToolbarButton("صرف", FontAwesomeIcon.ARROW_UP, this::showIssueDialog);
        
        // فاصل
        Separator separator = new Separator();
        separator.setOrientation(javafx.geometry.Orientation.VERTICAL);
        
        // زر تقييم المخزون
        Button valuationBtn = createToolbarButton("تقييم المخزون", FontAwesomeIcon.CALCULATOR, this::showValuationReport);
        
        // زر التقارير
        Button reportsBtn = createToolbarButton("التقارير", FontAwesomeIcon.BAR_CHART, this::showReports);
        
        toolbar.getChildren().addAll(warehousesBtn, addItemBtn, receiveBtn, issueBtn, separator, valuationBtn, reportsBtn);
        return toolbar;
    }
    
    /**
     * إنشاء زر شريط الأدوات
     */
    private Button createToolbarButton(String text, FontAwesomeIcon icon, Runnable action) {
        Button button = new Button(text);
        FontAwesomeIconView iconView = new FontAwesomeIconView(icon);
        iconView.setSize("16px");
        button.setGraphic(iconView);
        button.getStyleClass().add("toolbar-button");
        button.setOnAction(e -> action.run());
        return button;
    }
    
    /**
     * إنشاء قسم الفلترة والبحث
     */
    private VBox createFilterSection() {
        VBox filterSection = new VBox(10);
        filterSection.setPadding(new Insets(15));
        filterSection.getStyleClass().add("filter-section");
        
        Label filterLabel = new Label("🔍 البحث والفلترة:");
        filterLabel.getStyleClass().add("section-title");
        
        HBox filterBox = new HBox(15);
        filterBox.setAlignment(Pos.CENTER_LEFT);
        
        // فلتر المخزن
        Label warehouseLabel = new Label("المخزن:");
        warehouseFilter = new ComboBox<>();
        warehouseFilter.getItems().add(null); // خيار "الكل"
        warehouseFilter.getItems().addAll(warehouses);
        warehouseFilter.setPromptText("جميع المخازن");
        warehouseFilter.setPrefWidth(150);
        warehouseFilter.setOnAction(e -> applyFilters());
        
        // فلتر الفئة
        Label categoryLabel = new Label("الفئة:");
        categoryFilter = new ComboBox<>();
        categoryFilter.getItems().addAll("الكل", "زجاج", "ألومنيوم", "أكسسوارات", "مواد كيميائية");
        categoryFilter.setValue("الكل");
        categoryFilter.setPrefWidth(120);
        categoryFilter.setOnAction(e -> applyFilters());
        
        // حقل البحث
        Label searchLabel = new Label("البحث:");
        searchField = new TextField();
        searchField.setPromptText("ابحث بالاسم أو الكود");
        searchField.setPrefWidth(200);
        searchField.textProperty().addListener((obs, oldVal, newVal) -> applyFilters());
        
        // زر مسح الفلاتر
        Button clearBtn = new Button("مسح");
        FontAwesomeIconView clearIcon = new FontAwesomeIconView(FontAwesomeIcon.REFRESH);
        clearIcon.setSize("12px");
        clearBtn.setGraphic(clearIcon);
        clearBtn.getStyleClass().add("clear-button");
        clearBtn.setOnAction(e -> clearFilters());
        
        filterBox.getChildren().addAll(
            warehouseLabel, warehouseFilter,
            categoryLabel, categoryFilter,
            searchLabel, searchField,
            clearBtn
        );
        
        filterSection.getChildren().addAll(filterLabel, filterBox);
        return filterSection;
    }

    /**
     * تطبيق المرشحات
     */
    private void applyFilters() {
        filteredItems.setPredicate(item -> {
            // فلتر المخزن
            if (warehouseFilter.getValue() != null) {
                if (!item.getWarehouseId().equals(warehouseFilter.getValue().getWarehouseId())) {
                    return false;
                }
            }

            // فلتر الفئة
            if (categoryFilter.getValue() != null && !categoryFilter.getValue().equals("الكل")) {
                if (!item.getCategory().equals(categoryFilter.getValue())) {
                    return false;
                }
            }

            // البحث النصي
            if (searchField.getText() != null && !searchField.getText().isEmpty()) {
                String searchText = searchField.getText().toLowerCase();
                return item.getName().toLowerCase().contains(searchText) ||
                       item.getItemId().toLowerCase().contains(searchText);
            }

            return true;
        });
    }

    /**
     * مسح المرشحات
     */
    private void clearFilters() {
        warehouseFilter.setValue(null);
        categoryFilter.setValue("الكل");
        searchField.clear();
    }

    /**
     * إنشاء التبويبات الرئيسية
     */
    private TabPane createMainTabs() {
        TabPane tabPane = new TabPane();
        tabPane.getStyleClass().add("main-tabs");

        // تبويب الأصناف
        Tab itemsTab = new Tab("الأصناف");
        itemsTab.setContent(createItemsTab());
        itemsTab.setClosable(false);

        // تبويب حركة المخزون
        Tab movementsTab = new Tab("حركة المخزون");
        movementsTab.setContent(createMovementsTab());
        movementsTab.setClosable(false);

        // تبويب المخازن
        Tab warehousesTab = new Tab("المخازن");
        warehousesTab.setContent(createWarehousesTab());
        warehousesTab.setClosable(false);

        tabPane.getTabs().addAll(itemsTab, movementsTab, warehousesTab);
        return tabPane;
    }

    /**
     * إنشاء تبويب الأصناف
     */
    private VBox createItemsTab() {
        VBox container = new VBox(15);
        container.setPadding(new Insets(15));

        // إحصائيات سريعة
        HBox statsBox = createStatsBox();

        // جدول الأصناف
        itemsTable = createItemsTable();

        container.getChildren().addAll(statsBox, itemsTable);
        return container;
    }

    /**
     * إنشاء صندوق الإحصائيات
     */
    private HBox createStatsBox() {
        HBox statsBox = new HBox(20);
        statsBox.setAlignment(Pos.CENTER);
        statsBox.setPadding(new Insets(15));
        statsBox.getStyleClass().add("stats-box");

        // إجمالي الأصناف
        VBox totalItemsBox = createStatCard("إجمالي الأصناف", String.valueOf(items.size()), "#3498db");

        // إجمالي القيمة
        double totalValue = items.stream().mapToDouble(Item::getTotalValue).sum();
        VBox totalValueBox = createStatCard("إجمالي القيمة", decimalFormat.format(totalValue) + " ج.م", "#2ecc71");

        // الأصناف المنخفضة
        long lowStockItems = items.stream().filter(item -> item.getCurrentQuantity() < 10).count();
        VBox lowStockBox = createStatCard("أصناف منخفضة", String.valueOf(lowStockItems), "#e74c3c");

        // المخازن النشطة
        VBox activeWarehousesBox = createStatCard("المخازن النشطة", String.valueOf(warehouses.size()), "#f39c12");

        statsBox.getChildren().addAll(totalItemsBox, totalValueBox, lowStockBox, activeWarehousesBox);
        return statsBox;
    }

    /**
     * إنشاء بطاقة إحصائية
     */
    private VBox createStatCard(String title, String value, String color) {
        VBox card = new VBox(5);
        card.setAlignment(Pos.CENTER);
        card.setPadding(new Insets(15));
        card.getStyleClass().add("stat-card");
        card.setStyle("-fx-border-color: " + color + "; -fx-border-width: 2;");

        Label titleLabel = new Label(title);
        titleLabel.getStyleClass().add("stat-title");

        Label valueLabel = new Label(value);
        valueLabel.getStyleClass().add("stat-value");
        valueLabel.setStyle("-fx-text-fill: " + color + ";");

        card.getChildren().addAll(titleLabel, valueLabel);
        return card;
    }

    /**
     * إنشاء جدول الأصناف
     */
    private TableView<Item> createItemsTable() {
        TableView<Item> table = new TableView<>();
        table.setItems(filteredItems);
        table.setPrefHeight(400);
        table.getStyleClass().add("data-table");

        // العمود: كود الصنف
        TableColumn<Item, String> idCol = new TableColumn<>("كود الصنف");
        idCol.setPrefWidth(100);
        idCol.setCellValueFactory(new PropertyValueFactory<>("itemId"));

        // العمود: اسم الصنف
        TableColumn<Item, String> nameCol = new TableColumn<>("اسم الصنف");
        nameCol.setPrefWidth(200);
        nameCol.setCellValueFactory(new PropertyValueFactory<>("name"));

        // العمود: الفئة
        TableColumn<Item, String> categoryCol = new TableColumn<>("الفئة");
        categoryCol.setPrefWidth(100);
        categoryCol.setCellValueFactory(new PropertyValueFactory<>("category"));

        // العمود: الوحدة
        TableColumn<Item, Unit> unitCol = new TableColumn<>("الوحدة");
        unitCol.setPrefWidth(100);
        unitCol.setCellValueFactory(new PropertyValueFactory<>("unit"));
        unitCol.setCellFactory(col -> new TableCell<Item, Unit>() {
            @Override
            protected void updateItem(Unit item, boolean empty) {
                super.updateItem(item, empty);
                if (empty || item == null) {
                    setText(null);
                } else {
                    setText(item.getArabicName());
                }
            }
        });

        // العمود: نوع الصنف
        TableColumn<Item, String> typeCol = new TableColumn<>("النوع");
        typeCol.setPrefWidth(80);
        typeCol.setCellFactory(col -> new TableCell<Item, String>() {
            @Override
            protected void updateItem(String item, boolean empty) {
                super.updateItem(item, empty);
                if (empty) {
                    setText(null);
                    setStyle("");
                } else {
                    Item rowItem = getTableView().getItems().get(getIndex());
                    if (rowItem.hasDimensions()) {
                        setText("أبعاد");
                        setStyle("-fx-text-fill: #007bff; -fx-font-weight: bold;");
                    } else {
                        setText("عادي");
                        setStyle("-fx-text-fill: #6c757d;");
                    }
                }
            }
        });

        // العمود: الكمية الحالية
        TableColumn<Item, Double> quantityCol = new TableColumn<>("الكمية الحالية");
        quantityCol.setPrefWidth(120);
        quantityCol.setCellValueFactory(new PropertyValueFactory<>("currentQuantity"));
        quantityCol.setCellFactory(col -> new TableCell<Item, Double>() {
            @Override
            protected void updateItem(Double item, boolean empty) {
                super.updateItem(item, empty);
                if (empty || item == null) {
                    setText(null);
                } else {
                    Item rowItem = getTableView().getItems().get(getIndex());
                    String displayText;

                    if (rowItem.hasDimensions()) {
                        // للأصناف ذات الأبعاد: عرض المساحة + تفاصيل الأبعاد
                        displayText = decimalFormat.format(item) + " " + rowItem.getUnit().getSymbol();
                        if (rowItem.getLength() != null && rowItem.getWidth() != null && rowItem.getPieces() != null) {
                            displayText += String.format("\n(%dx%d×%d)",
                                rowItem.getLength().intValue(),
                                rowItem.getWidth().intValue(),
                                rowItem.getPieces());
                        }
                    } else {
                        // للأصناف العادية
                        displayText = decimalFormat.format(item) + " " + rowItem.getUnit().getSymbol();
                    }

                    setText(displayText);

                    // تلوين الكميات المنخفضة
                    if (item < 10) {
                        setStyle("-fx-text-fill: #e74c3c; -fx-font-weight: bold;");
                    } else {
                        setStyle("");
                    }
                }
            }
        });

        // العمود: سعر الوحدة
        TableColumn<Item, Double> priceCol = new TableColumn<>("سعر الوحدة");
        priceCol.setPrefWidth(100);
        priceCol.setCellValueFactory(new PropertyValueFactory<>("unitPrice"));
        priceCol.setCellFactory(col -> new TableCell<Item, Double>() {
            @Override
            protected void updateItem(Double item, boolean empty) {
                super.updateItem(item, empty);
                if (empty || item == null) {
                    setText(null);
                } else {
                    setText(decimalFormat.format(item) + " ج.م");
                }
            }
        });

        // العمود: القيمة الإجمالية
        TableColumn<Item, Double> totalValueCol = new TableColumn<>("القيمة الإجمالية");
        totalValueCol.setPrefWidth(120);
        totalValueCol.setCellValueFactory(new PropertyValueFactory<>("totalValue"));
        totalValueCol.setCellFactory(col -> new TableCell<Item, Double>() {
            @Override
            protected void updateItem(Double item, boolean empty) {
                super.updateItem(item, empty);
                getStyleClass().add("calculated-cell");
                if (empty || item == null) {
                    setText(null);
                } else {
                    setText(decimalFormat.format(item) + " ج.م");
                }
            }
        });

        // العمود: المخزن
        TableColumn<Item, String> warehouseCol = new TableColumn<>("المخزن");
        warehouseCol.setPrefWidth(120);
        warehouseCol.setCellValueFactory(new PropertyValueFactory<>("warehouseName"));

        // العمود: الإجراءات
        TableColumn<Item, Void> actionsCol = new TableColumn<>("الإجراءات");
        actionsCol.setPrefWidth(200);
        actionsCol.setCellFactory(col -> new TableCell<Item, Void>() {
            private final HBox actionBox = new HBox(5);
            private final Button editBtn = new Button("تعديل");
            private final Button receiveBtn = new Button("استلام");
            private final Button issueBtn = new Button("صرف");
            private final Button historyBtn = new Button("السجل");

            {
                // زر التعديل
                FontAwesomeIconView editIcon = new FontAwesomeIconView(FontAwesomeIcon.EDIT);
                editIcon.setSize("10px");
                editBtn.setGraphic(editIcon);
                editBtn.getStyleClass().add("edit-button");
                editBtn.setOnAction(e -> {
                    Item item = getTableView().getItems().get(getIndex());
                    showEditItemDialog(item);
                });

                // زر الاستلام
                FontAwesomeIconView receiveIcon = new FontAwesomeIconView(FontAwesomeIcon.ARROW_DOWN);
                receiveIcon.setSize("10px");
                receiveBtn.setGraphic(receiveIcon);
                receiveBtn.getStyleClass().add("receive-button");
                receiveBtn.setOnAction(e -> {
                    Item item = getTableView().getItems().get(getIndex());
                    showReceiveDialog(item);
                });

                // زر الصرف
                FontAwesomeIconView issueIcon = new FontAwesomeIconView(FontAwesomeIcon.ARROW_UP);
                issueIcon.setSize("10px");
                issueBtn.setGraphic(issueIcon);
                issueBtn.getStyleClass().add("issue-button");
                issueBtn.setOnAction(e -> {
                    Item item = getTableView().getItems().get(getIndex());
                    showIssueDialog(item);
                });

                // زر السجل
                FontAwesomeIconView historyIcon = new FontAwesomeIconView(FontAwesomeIcon.HISTORY);
                historyIcon.setSize("10px");
                historyBtn.setGraphic(historyIcon);
                historyBtn.getStyleClass().add("history-button");
                historyBtn.setOnAction(e -> {
                    Item item = getTableView().getItems().get(getIndex());
                    showItemHistory(item);
                });

                actionBox.setAlignment(Pos.CENTER);
                actionBox.getChildren().addAll(editBtn, receiveBtn, issueBtn, historyBtn);
            }

            @Override
            protected void updateItem(Void item, boolean empty) {
                super.updateItem(item, empty);
                if (empty) {
                    setGraphic(null);
                } else {
                    setGraphic(actionBox);
                }
            }
        });

        table.getColumns().addAll(idCol, nameCol, categoryCol, typeCol, unitCol, quantityCol, priceCol, totalValueCol, warehouseCol, actionsCol);
        return table;
    }

    /**
     * إنشاء تبويب حركة المخزون
     */
    private VBox createMovementsTab() {
        VBox container = new VBox(15);
        container.setPadding(new Insets(15));

        // شريط الأدوات
        HBox toolbar = new HBox(10);
        toolbar.setAlignment(Pos.CENTER_LEFT);

        Button addMovementBtn = new Button("إضافة حركة");
        FontAwesomeIconView addIcon = new FontAwesomeIconView(FontAwesomeIcon.PLUS);
        addIcon.setSize("14px");
        addMovementBtn.setGraphic(addIcon);
        addMovementBtn.getStyleClass().add("add-button");
        addMovementBtn.setOnAction(e -> showAddMovementDialog());

        toolbar.getChildren().add(addMovementBtn);

        // جدول حركة المخزون
        TableView<StockMovement> movementsTable = createMovementsTable();

        container.getChildren().addAll(toolbar, movementsTable);
        return container;
    }

    /**
     * إنشاء جدول حركة المخزون
     */
    private TableView<StockMovement> createMovementsTable() {
        TableView<StockMovement> table = new TableView<>();
        table.setItems(stockMovements);
        table.setPrefHeight(400);
        table.getStyleClass().add("data-table");

        // العمود: رقم الحركة
        TableColumn<StockMovement, String> idCol = new TableColumn<>("رقم الحركة");
        idCol.setPrefWidth(100);
        idCol.setCellValueFactory(new PropertyValueFactory<>("movementId"));

        // العمود: نوع الحركة
        TableColumn<StockMovement, StockMovement.MovementType> typeCol = new TableColumn<>("نوع الحركة");
        typeCol.setPrefWidth(100);
        typeCol.setCellValueFactory(new PropertyValueFactory<>("movementType"));
        typeCol.setCellFactory(col -> new TableCell<StockMovement, StockMovement.MovementType>() {
            @Override
            protected void updateItem(StockMovement.MovementType item, boolean empty) {
                super.updateItem(item, empty);
                if (empty || item == null) {
                    setText(null);
                    setStyle("");
                } else {
                    setText(item.getArabicName());
                    // تلوين حسب نوع الحركة
                    switch (item) {
                        case IN:
                            setStyle("-fx-text-fill: #27ae60; -fx-font-weight: bold;");
                            break;
                        case OUT:
                            setStyle("-fx-text-fill: #e74c3c; -fx-font-weight: bold;");
                            break;
                        default:
                            setStyle("-fx-text-fill: #f39c12; -fx-font-weight: bold;");
                    }
                }
            }
        });

        // العمود: اسم الصنف
        TableColumn<StockMovement, String> itemCol = new TableColumn<>("الصنف");
        itemCol.setPrefWidth(150);
        itemCol.setCellValueFactory(new PropertyValueFactory<>("itemName"));

        // العمود: الكمية
        TableColumn<StockMovement, Double> quantityCol = new TableColumn<>("الكمية");
        quantityCol.setPrefWidth(150);
        quantityCol.setCellValueFactory(new PropertyValueFactory<>("quantity"));
        quantityCol.setCellFactory(col -> new TableCell<StockMovement, Double>() {
            @Override
            protected void updateItem(Double item, boolean empty) {
                super.updateItem(item, empty);
                if (empty || item == null) {
                    setText(null);
                } else {
                    StockMovement movement = getTableView().getItems().get(getIndex());
                    String displayText = decimalFormat.format(item);

                    // إضافة تفاصيل الأبعاد إذا كانت متوفرة
                    if (movement.hasDimensions() && movement.getLength() != null &&
                        movement.getWidth() != null && movement.getPieces() != null) {
                        displayText += String.format(" م²\n(%dx%d×%d)",
                            movement.getLength().intValue(),
                            movement.getWidth().intValue(),
                            movement.getPieces());
                    }

                    setText(displayText);
                }
            }
        });

        // العمود: سعر الوحدة
        TableColumn<StockMovement, Double> priceCol = new TableColumn<>("سعر الوحدة");
        priceCol.setPrefWidth(100);
        priceCol.setCellValueFactory(new PropertyValueFactory<>("unitPrice"));
        priceCol.setCellFactory(col -> new TableCell<StockMovement, Double>() {
            @Override
            protected void updateItem(Double item, boolean empty) {
                super.updateItem(item, empty);
                if (empty || item == null) {
                    setText(null);
                } else {
                    setText(decimalFormat.format(item) + " ج.م");
                }
            }
        });

        // العمود: القيمة الإجمالية
        TableColumn<StockMovement, Double> totalCol = new TableColumn<>("القيمة الإجمالية");
        totalCol.setPrefWidth(120);
        totalCol.setCellValueFactory(new PropertyValueFactory<>("totalValue"));
        totalCol.setCellFactory(col -> new TableCell<StockMovement, Double>() {
            @Override
            protected void updateItem(Double item, boolean empty) {
                super.updateItem(item, empty);
                if (empty || item == null) {
                    setText(null);
                } else {
                    setText(decimalFormat.format(item) + " ج.م");
                }
            }
        });

        // العمود: الرصيد بعد الحركة
        TableColumn<StockMovement, Double> balanceCol = new TableColumn<>("الرصيد بعد الحركة");
        balanceCol.setPrefWidth(120);
        balanceCol.setCellValueFactory(new PropertyValueFactory<>("balanceAfter"));
        balanceCol.setCellFactory(col -> new TableCell<StockMovement, Double>() {
            @Override
            protected void updateItem(Double item, boolean empty) {
                super.updateItem(item, empty);
                getStyleClass().add("calculated-cell");
                if (empty || item == null) {
                    setText(null);
                } else {
                    setText(decimalFormat.format(item));
                }
            }
        });

        // العمود: التاريخ والوقت
        TableColumn<StockMovement, LocalDateTime> dateCol = new TableColumn<>("التاريخ والوقت");
        dateCol.setPrefWidth(120);
        dateCol.setCellValueFactory(new PropertyValueFactory<>("dateTime"));
        dateCol.setCellFactory(col -> new TableCell<StockMovement, LocalDateTime>() {
            @Override
            protected void updateItem(LocalDateTime item, boolean empty) {
                super.updateItem(item, empty);
                if (empty || item == null) {
                    setText(null);
                } else {
                    setText(item.format(dateTimeFormatter));
                }
            }
        });

        // العمود: المستخدم
        TableColumn<StockMovement, String> userCol = new TableColumn<>("المستخدم");
        userCol.setPrefWidth(100);
        userCol.setCellValueFactory(new PropertyValueFactory<>("user"));

        table.getColumns().addAll(idCol, typeCol, itemCol, quantityCol, priceCol, totalCol, balanceCol, dateCol, userCol);
        return table;
    }

    /**
     * إنشاء تبويب المخازن
     */
    private VBox createWarehousesTab() {
        VBox container = new VBox(15);
        container.setPadding(new Insets(15));

        // شريط الأدوات
        HBox toolbar = new HBox(10);
        toolbar.setAlignment(Pos.CENTER_LEFT);

        Button addWarehouseBtn = new Button("إضافة مخزن");
        FontAwesomeIconView addIcon = new FontAwesomeIconView(FontAwesomeIcon.PLUS);
        addIcon.setSize("14px");
        addWarehouseBtn.setGraphic(addIcon);
        addWarehouseBtn.getStyleClass().add("add-button");
        addWarehouseBtn.setOnAction(e -> showAddWarehouseDialog());

        toolbar.getChildren().add(addWarehouseBtn);

        // جدول المخازن
        TableView<Warehouse> warehousesTable = createWarehousesTable();

        container.getChildren().addAll(toolbar, warehousesTable);
        return container;
    }

    /**
     * إنشاء جدول المخازن
     */
    private TableView<Warehouse> createWarehousesTable() {
        TableView<Warehouse> table = new TableView<>();
        table.setItems(warehouses);
        table.setPrefHeight(400);
        table.getStyleClass().add("data-table");

        // العمود: كود المخزن
        TableColumn<Warehouse, String> idCol = new TableColumn<>("كود المخزن");
        idCol.setPrefWidth(100);
        idCol.setCellValueFactory(new PropertyValueFactory<>("warehouseId"));

        // العمود: اسم المخزن
        TableColumn<Warehouse, String> nameCol = new TableColumn<>("اسم المخزن");
        nameCol.setPrefWidth(150);
        nameCol.setCellValueFactory(new PropertyValueFactory<>("name"));

        // العمود: الموقع
        TableColumn<Warehouse, String> locationCol = new TableColumn<>("الموقع");
        locationCol.setPrefWidth(200);
        locationCol.setCellValueFactory(new PropertyValueFactory<>("location"));

        // العمود: الوصف
        TableColumn<Warehouse, String> descriptionCol = new TableColumn<>("الوصف");
        descriptionCol.setPrefWidth(250);
        descriptionCol.setCellValueFactory(new PropertyValueFactory<>("description"));

        // العمود: عدد الأصناف
        TableColumn<Warehouse, Integer> itemsCountCol = new TableColumn<>("عدد الأصناف");
        itemsCountCol.setPrefWidth(100);
        itemsCountCol.setCellFactory(col -> new TableCell<Warehouse, Integer>() {
            @Override
            protected void updateItem(Integer item, boolean empty) {
                super.updateItem(item, empty);
                if (empty) {
                    setText(null);
                } else {
                    Warehouse warehouse = getTableView().getItems().get(getIndex());
                    long count = items.stream().filter(i -> i.getWarehouseId().equals(warehouse.getWarehouseId())).count();
                    setText(String.valueOf(count));
                }
            }
        });

        // العمود: الإجراءات
        TableColumn<Warehouse, Void> actionsCol = new TableColumn<>("الإجراءات");
        actionsCol.setPrefWidth(150);
        actionsCol.setCellFactory(col -> new TableCell<Warehouse, Void>() {
            private final HBox actionBox = new HBox(5);
            private final Button editBtn = new Button("تعديل");
            private final Button viewBtn = new Button("عرض");
            private final Button deleteBtn = new Button("حذف");

            {
                // زر التعديل
                FontAwesomeIconView editIcon = new FontAwesomeIconView(FontAwesomeIcon.EDIT);
                editIcon.setSize("10px");
                editBtn.setGraphic(editIcon);
                editBtn.getStyleClass().add("edit-button");
                editBtn.setOnAction(e -> {
                    Warehouse warehouse = getTableView().getItems().get(getIndex());
                    showEditWarehouseDialog(warehouse);
                });

                // زر العرض
                FontAwesomeIconView viewIcon = new FontAwesomeIconView(FontAwesomeIcon.EYE);
                viewIcon.setSize("10px");
                viewBtn.setGraphic(viewIcon);
                viewBtn.getStyleClass().add("view-button");
                viewBtn.setOnAction(e -> {
                    Warehouse warehouse = getTableView().getItems().get(getIndex());
                    showWarehouseDetails(warehouse);
                });

                // زر الحذف
                FontAwesomeIconView deleteIcon = new FontAwesomeIconView(FontAwesomeIcon.TRASH);
                deleteIcon.setSize("10px");
                deleteBtn.setGraphic(deleteIcon);
                deleteBtn.getStyleClass().add("delete-button");
                deleteBtn.setOnAction(e -> {
                    Warehouse warehouse = getTableView().getItems().get(getIndex());
                    deleteWarehouse(warehouse);
                });

                actionBox.setAlignment(Pos.CENTER);
                actionBox.getChildren().addAll(editBtn, viewBtn, deleteBtn);
            }

            @Override
            protected void updateItem(Void item, boolean empty) {
                super.updateItem(item, empty);
                if (empty) {
                    setGraphic(null);
                } else {
                    setGraphic(actionBox);
                }
            }
        });

        table.getColumns().addAll(idCol, nameCol, locationCol, descriptionCol, itemsCountCol, actionsCol);
        return table;
    }

    // أحداث الأزرار الرئيسية
    private void showWarehouseManagement() {
        showPlaceholderDialog("إدارة المخازن", "يمكنك إدارة المخازن من تبويب المخازن أعلاه");
    }

    private void showAddItemDialog() {
        showItemDialog(null);
    }

    private void showReceiveDialog() {
        showReceiveDialog(null);
    }

    private void showIssueDialog() {
        showIssueDialog(null);
    }

    private void showValuationReport() {
        showPlaceholderDialog("تقييم المخزون", "سيتم تطوير تقرير تقييم المخزون قريباً");
    }

    private void showReports() {
        showPlaceholderDialog("التقارير", "سيتم تطوير التقارير المتقدمة قريباً");
    }

    // وظائف الأصناف
    private void showEditItemDialog(Item item) {
        showItemDialog(item);
    }

    private void showReceiveDialog(Item item) {
        showMovementDialog(item, StockMovement.MovementType.IN);
    }

    private void showIssueDialog(Item item) {
        showMovementDialog(item, StockMovement.MovementType.OUT);
    }

    private void showItemHistory(Item item) {
        showPlaceholderDialog("سجل الصنف", "سيتم عرض سجل حركات الصنف: " + item.getName());
    }

    // وظائف المخازن
    private void showAddWarehouseDialog() {
        showWarehouseDialog(null);
    }

    private void showEditWarehouseDialog(Warehouse warehouse) {
        showWarehouseDialog(warehouse);
    }

    private void showWarehouseDetails(Warehouse warehouse) {
        showPlaceholderDialog("تفاصيل المخزن", "تفاصيل المخزن: " + warehouse.getName());
    }

    private void deleteWarehouse(Warehouse warehouse) {
        Alert alert = new Alert(Alert.AlertType.CONFIRMATION);
        alert.setTitle("تأكيد الحذف");
        alert.setHeaderText("حذف المخزن");
        alert.setContentText("هل أنت متأكد من حذف المخزن: " + warehouse.getName() + "؟");

        alert.showAndWait().ifPresent(response -> {
            if (response == ButtonType.OK) {
                warehouses.remove(warehouse);
                showPlaceholderDialog("نجح", "تم حذف المخزن بنجاح");
            }
        });
    }

    // وظائف الحركات
    private void showAddMovementDialog() {
        showPlaceholderDialog("إضافة حركة", "سيتم تطوير نافذة إضافة الحركات قريباً");
    }

    private void showPlaceholderDialog(String title, String message) {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle(title);
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }

    /**
     * نافذة إضافة/تعديل صنف
     */
    private void showItemDialog(Item item) {
        InventoryDialogs dialogs = new InventoryDialogs();
        dialogs.showItemDialog(item, warehouses, items);
    }

    /**
     * نافذة حركة المخزون (استلام/صرف)
     */
    private void showMovementDialog(Item item, StockMovement.MovementType movementType) {
        Stage dialog = new Stage();
        dialog.setTitle(movementType.getArabicName() + " - " + (item != null ? item.getName() : "اختر صنف"));
        dialog.initModality(Modality.APPLICATION_MODAL);
        dialog.setWidth(500);
        dialog.setHeight(400);

        VBox mainLayout = new VBox(20);
        mainLayout.setPadding(new Insets(20));

        // العنوان
        Label titleLabel = new Label(movementType.getArabicName() + " صنف");
        titleLabel.getStyleClass().add("dialog-title");

        // نموذج البيانات
        GridPane form = new GridPane();
        form.setHgap(15);
        form.setVgap(15);

        // اختيار المخزن أولاً
        Label warehouseLabel = new Label("المخزن:");
        ComboBox<Warehouse> warehouseCombo = new ComboBox<>();
        warehouseCombo.setItems(warehouses);
        warehouseCombo.setPrefWidth(200);
        warehouseCombo.setPromptText("اختر المخزن أولاً");

        // اختيار الصنف (يتم تحديثه حسب المخزن المختار)
        Label itemLabel = new Label("الصنف:");
        ComboBox<Item> itemCombo = new ComboBox<>();
        itemCombo.setPrefWidth(200);
        itemCombo.setPromptText("اختر المخزن أولاً");
        itemCombo.setDisable(true); // معطل حتى يتم اختيار المخزن

        // إذا تم تمرير صنف محدد مسبقاً
        if (item != null) {
            // اختيار المخزن الخاص بالصنف تلقائياً
            Warehouse itemWarehouse = warehouses.stream()
                .filter(w -> w.getWarehouseId().equals(item.getWarehouseId()))
                .findFirst().orElse(null);
            if (itemWarehouse != null) {
                warehouseCombo.setValue(itemWarehouse);
                // تحديث قائمة الأصناف
                updateItemsList(warehouseCombo, itemCombo);
                itemCombo.setValue(item);
                itemCombo.setDisable(true);
            }
        }

        // الكمية (للأصناف العادية)
        Label quantityLabel = new Label("الكمية:");
        TextField quantityField = new TextField();
        quantityField.setPromptText("0.00");

        // خصائص الأبعاد (للأصناف ذات الأبعاد)
        VBox dimensionsBox = new VBox(10);
        dimensionsBox.setVisible(false);

        Label dimensionsLabel = new Label("الأبعاد:");
        dimensionsLabel.getStyleClass().add("section-title");

        GridPane dimensionsForm = new GridPane();
        dimensionsForm.setHgap(10);
        dimensionsForm.setVgap(10);

        Label lengthLabel = new Label("الطول (مم):");
        TextField lengthField = new TextField();
        lengthField.setPromptText("1000");

        Label widthLabel = new Label("العرض (مم):");
        TextField widthField = new TextField();
        widthField.setPromptText("500");

        Label piecesLabel = new Label("العدد:");
        TextField piecesField = new TextField();
        piecesField.setPromptText("1");

        Label calculatedAreaLabel = new Label("المساحة المحسوبة (م²):");
        TextField calculatedAreaField = new TextField();
        calculatedAreaField.setPromptText("0.00");
        calculatedAreaField.setDisable(true);
        calculatedAreaField.getStyleClass().add("calculated-field");

        // حساب المساحة تلقائياً
        Runnable calculateArea = () -> {
            try {
                double length = lengthField.getText().isEmpty() ? 0 : Double.parseDouble(lengthField.getText());
                double width = widthField.getText().isEmpty() ? 0 : Double.parseDouble(widthField.getText());
                int pieces = piecesField.getText().isEmpty() ? 1 : Integer.parseInt(piecesField.getText());

                double area = (length / 1000.0) * (width / 1000.0) * pieces;
                calculatedAreaField.setText(decimalFormat.format(area));
            } catch (NumberFormatException e) {
                calculatedAreaField.setText("0.00");
            }
        };

        lengthField.textProperty().addListener((obs, oldVal, newVal) -> calculateArea.run());
        widthField.textProperty().addListener((obs, oldVal, newVal) -> calculateArea.run());
        piecesField.textProperty().addListener((obs, oldVal, newVal) -> calculateArea.run());

        dimensionsForm.add(lengthLabel, 0, 0);
        dimensionsForm.add(lengthField, 1, 0);
        dimensionsForm.add(widthLabel, 2, 0);
        dimensionsForm.add(widthField, 3, 0);
        dimensionsForm.add(piecesLabel, 0, 1);
        dimensionsForm.add(piecesField, 1, 1);
        dimensionsForm.add(calculatedAreaLabel, 2, 1);
        dimensionsForm.add(calculatedAreaField, 3, 1);

        dimensionsBox.getChildren().addAll(dimensionsLabel, dimensionsForm);

        // تحديث قائمة الأصناف عند اختيار المخزن
        warehouseCombo.setOnAction(e -> {
            updateItemsList(warehouseCombo, itemCombo);
        });

        // سعر الوحدة
        Label priceLabel = new Label("سعر الوحدة:");
        TextField priceField = new TextField();
        priceField.setPromptText("0.00");

        // المرجع
        Label referenceLabel = new Label("المرجع:");
        TextField referenceField = new TextField();
        referenceField.setPromptText("رقم الفاتورة أو أمر العمل");

        // المستخدم
        Label userLabel = new Label("المستخدم:");
        TextField userField = new TextField();
        userField.setPromptText("اسم المستخدم");
        userField.setText("المستخدم الحالي");

        // الملاحظات
        Label notesLabel = new Label("الملاحظات:");
        TextArea notesArea = new TextArea();
        notesArea.setPromptText("ملاحظات اختيارية");
        notesArea.setPrefRowCount(3);

        // تحديث الواجهة عند اختيار الصنف
        itemCombo.setOnAction(e -> {
            Item selectedItem = itemCombo.getValue();
            if (selectedItem != null) {
                priceField.setText(String.valueOf(selectedItem.getUnitPrice()));

                // إظهار/إخفاء الحقول حسب نوع الصنف
                boolean hasDimensions = selectedItem.hasDimensions();
                dimensionsBox.setVisible(hasDimensions);
                quantityField.setDisable(hasDimensions);

                if (hasDimensions) {
                    quantityField.setPromptText("محسوبة تلقائياً من الأبعاد");
                    // ملء القيم الافتراضية إذا كانت متوفرة
                    if (selectedItem.getLength() != null) {
                        lengthField.setText(String.valueOf(selectedItem.getLength()));
                    }
                    if (selectedItem.getWidth() != null) {
                        widthField.setText(String.valueOf(selectedItem.getWidth()));
                    }
                    piecesField.setText("1"); // قيمة افتراضية
                } else {
                    quantityField.setPromptText("0.00");
                    quantityField.setDisable(false);
                }
            }
        });

        // ترتيب الحقول
        form.add(warehouseLabel, 0, 0);
        form.add(warehouseCombo, 1, 0);
        form.add(itemLabel, 0, 1);
        form.add(itemCombo, 1, 1);
        form.add(quantityLabel, 0, 2);
        form.add(quantityField, 1, 2);
        form.add(priceLabel, 0, 3);
        form.add(priceField, 1, 3);
        form.add(referenceLabel, 0, 4);
        form.add(referenceField, 1, 4);
        form.add(userLabel, 0, 5);
        form.add(userField, 1, 5);
        form.add(notesLabel, 0, 6);
        form.add(notesArea, 1, 6);

        // إضافة صندوق الأبعاد
        form.add(dimensionsBox, 0, 7);
        GridPane.setColumnSpan(dimensionsBox, 2);

        // أزرار الحوار
        HBox buttonBox = new HBox(10);
        buttonBox.setAlignment(Pos.CENTER_RIGHT);

        Button saveButton = new Button("تنفيذ " + movementType.getArabicName());
        saveButton.getStyleClass().add("add-button");
        saveButton.setOnAction(e -> {
            Item selectedItem = itemCombo.getValue();
            if (selectedItem == null) {
                showPlaceholderDialog("تحذير", "يرجى اختيار الصنف");
                return;
            }

            try {
                double quantity;
                double price = Double.parseDouble(priceField.getText().trim());

                // حساب الكمية حسب نوع الصنف
                if (selectedItem.hasDimensions()) {
                    // للأصناف ذات الأبعاد
                    if (!validateDimensionsForm(lengthField, widthField, piecesField)) {
                        return;
                    }

                    double length = Double.parseDouble(lengthField.getText().trim());
                    double width = Double.parseDouble(widthField.getText().trim());
                    int pieces = Integer.parseInt(piecesField.getText().trim());

                    quantity = (length / 1000.0) * (width / 1000.0) * pieces;
                } else {
                    // للأصناف العادية
                    if (quantityField.getText().trim().isEmpty()) {
                        showPlaceholderDialog("تحذير", "يرجى إدخال الكمية");
                        return;
                    }
                    quantity = Double.parseDouble(quantityField.getText().trim());
                }

                if (quantity <= 0) {
                    showPlaceholderDialog("تحذير", "يرجى إدخال كمية أكبر من صفر");
                    return;
                }

                // التحقق من الكمية المتاحة في حالة الصرف
                if (movementType == StockMovement.MovementType.OUT) {
                    if (quantity > selectedItem.getCurrentQuantity()) {
                        showPlaceholderDialog("تحذير", "الكمية المطلوبة أكبر من الكمية المتاحة");
                        return;
                    }
                }

                    // إنشاء حركة جديدة
                    String movementId = "MOV" + String.format("%03d", stockMovements.size() + 1);
                    StockMovement movement = new StockMovement(
                        movementId,
                        selectedItem.getItemId(),
                        selectedItem.getName(),
                        selectedItem.getWarehouseId(),
                        movementType,
                        quantity,
                        price
                    );

                    movement.setWarehouseName(selectedItem.getWarehouseName());
                    movement.setUser(userField.getText().trim());
                    movement.setReference(referenceField.getText().trim());
                    movement.setNotes(notesArea.getText().trim());

                    // حفظ بيانات الأبعاد إذا كان الصنف له أبعاد
                    if (selectedItem.hasDimensions()) {
                        movement.setHasDimensions(true);
                        movement.setLength(Double.parseDouble(lengthField.getText().trim()));
                        movement.setWidth(Double.parseDouble(widthField.getText().trim()));
                        movement.setPieces(Integer.parseInt(piecesField.getText().trim()));
                        movement.setCalculatedArea(quantity);
                    }

                    // تحديث كمية الصنف
                    double newQuantity;
                    if (movementType == StockMovement.MovementType.IN) {
                        newQuantity = selectedItem.getCurrentQuantity() + quantity;
                    } else {
                        newQuantity = selectedItem.getCurrentQuantity() - quantity;
                    }

                    selectedItem.setCurrentQuantity(newQuantity);
                    selectedItem.setLastUpdated(LocalDate.now());
                    movement.setBalanceAfter(newQuantity);

                    // تحديث متوسط السعر (في حالة الاستلام)
                    if (movementType == StockMovement.MovementType.IN) {
                        double totalValue = (selectedItem.getCurrentQuantity() - quantity) * selectedItem.getUnitPrice() + quantity * price;
                        selectedItem.setUnitPrice(totalValue / selectedItem.getCurrentQuantity());
                    }

                    stockMovements.add(movement);

                    showPlaceholderDialog("نجح", "تم تنفيذ " + movementType.getArabicName() + " بنجاح");
                    dialog.close();

                    // تحديث الجدول
                    if (itemsTable != null) {
                        itemsTable.refresh();
                    }

                } catch (NumberFormatException ex) {
                    showPlaceholderDialog("خطأ", "يرجى إدخال قيم صحيحة للأرقام");
                }
        });

        Button cancelButton = new Button("إلغاء");
        cancelButton.setOnAction(e -> dialog.close());

        buttonBox.getChildren().addAll(saveButton, cancelButton);

        mainLayout.getChildren().addAll(titleLabel, form, buttonBox);

        Scene scene = new Scene(mainLayout);
        scene.getStylesheets().add(getClass().getResource("/css/styles.css").toExternalForm());
        dialog.setScene(scene);
        dialog.showAndWait();
    }

    /**
     * التحقق من صحة نموذج الأبعاد
     */
    private boolean validateDimensionsForm(TextField lengthField, TextField widthField, TextField piecesField) {
        if (lengthField.getText().trim().isEmpty()) {
            showPlaceholderDialog("تحذير", "يرجى إدخال الطول");
            return false;
        }

        if (widthField.getText().trim().isEmpty()) {
            showPlaceholderDialog("تحذير", "يرجى إدخال العرض");
            return false;
        }

        if (piecesField.getText().trim().isEmpty()) {
            showPlaceholderDialog("تحذير", "يرجى إدخال العدد");
            return false;
        }

        try {
            double length = Double.parseDouble(lengthField.getText().trim());
            double width = Double.parseDouble(widthField.getText().trim());
            int pieces = Integer.parseInt(piecesField.getText().trim());

            if (length <= 0) {
                showPlaceholderDialog("تحذير", "يرجى إدخال طول أكبر من صفر");
                return false;
            }

            if (width <= 0) {
                showPlaceholderDialog("تحذير", "يرجى إدخال عرض أكبر من صفر");
                return false;
            }

            if (pieces <= 0) {
                showPlaceholderDialog("تحذير", "يرجى إدخال عدد أكبر من صفر");
                return false;
            }
        } catch (NumberFormatException e) {
            showPlaceholderDialog("تحذير", "يرجى إدخال قيم صحيحة للأبعاد");
            return false;
        }

        return true;
    }

    /**
     * نافذة إضافة/تعديل مخزن
     */
    private void showWarehouseDialog(Warehouse warehouse) {
        boolean isEdit = (warehouse != null);

        Stage dialog = new Stage();
        dialog.setTitle(isEdit ? "تعديل مخزن" : "إضافة مخزن جديد");
        dialog.initModality(Modality.APPLICATION_MODAL);
        dialog.setWidth(450);
        dialog.setHeight(350);

        VBox mainLayout = new VBox(20);
        mainLayout.setPadding(new Insets(20));

        // العنوان
        Label titleLabel = new Label(isEdit ? "تعديل بيانات المخزن" : "إضافة مخزن جديد");
        titleLabel.getStyleClass().add("dialog-title");

        // نموذج البيانات
        GridPane form = new GridPane();
        form.setHgap(15);
        form.setVgap(15);

        // كود المخزن
        Label idLabel = new Label("كود المخزن:");
        TextField idField = new TextField();
        idField.setPromptText("WH001");
        if (isEdit) {
            idField.setText(warehouse.getWarehouseId());
            idField.setDisable(true);
        }

        // اسم المخزن
        Label nameLabel = new Label("اسم المخزن:");
        TextField nameField = new TextField();
        nameField.setPromptText("أدخل اسم المخزن");
        if (isEdit) nameField.setText(warehouse.getName());

        // الموقع
        Label locationLabel = new Label("الموقع:");
        TextField locationField = new TextField();
        locationField.setPromptText("أدخل موقع المخزن");
        if (isEdit) locationField.setText(warehouse.getLocation());

        // الوصف
        Label descriptionLabel = new Label("الوصف:");
        TextArea descriptionArea = new TextArea();
        descriptionArea.setPromptText("وصف المخزن");
        descriptionArea.setPrefRowCount(3);
        if (isEdit) descriptionArea.setText(warehouse.getDescription());

        // ترتيب الحقول
        form.add(idLabel, 0, 0);
        form.add(idField, 1, 0);
        form.add(nameLabel, 0, 1);
        form.add(nameField, 1, 1);
        form.add(locationLabel, 0, 2);
        form.add(locationField, 1, 2);
        form.add(descriptionLabel, 0, 3);
        form.add(descriptionArea, 1, 3);

        // أزرار الحوار
        HBox buttonBox = new HBox(10);
        buttonBox.setAlignment(Pos.CENTER_RIGHT);

        Button saveButton = new Button(isEdit ? "حفظ التعديلات" : "إضافة المخزن");
        saveButton.getStyleClass().add("add-button");
        saveButton.setOnAction(e -> {
            if (validateWarehouseForm(nameField, locationField)) {
                if (isEdit) {
                    // تحديث المخزن الموجود
                    warehouse.setName(nameField.getText().trim());
                    warehouse.setLocation(locationField.getText().trim());
                    warehouse.setDescription(descriptionArea.getText().trim());

                    showPlaceholderDialog("نجح", "تم تحديث بيانات المخزن بنجاح");
                } else {
                    // إضافة مخزن جديد
                    String warehouseId = idField.getText().trim();
                    if (warehouseId.isEmpty()) {
                        warehouseId = "WH" + String.format("%03d", warehouses.size() + 1);
                    }

                    Warehouse newWarehouse = new Warehouse(
                        warehouseId,
                        nameField.getText().trim(),
                        locationField.getText().trim(),
                        descriptionArea.getText().trim()
                    );

                    warehouses.add(newWarehouse);
                    showPlaceholderDialog("نجح", "تم إضافة المخزن بنجاح");
                }

                dialog.close();
            }
        });

        Button cancelButton = new Button("إلغاء");
        cancelButton.setOnAction(e -> dialog.close());

        buttonBox.getChildren().addAll(saveButton, cancelButton);

        mainLayout.getChildren().addAll(titleLabel, form, buttonBox);

        Scene scene = new Scene(mainLayout);
        scene.getStylesheets().add(getClass().getResource("/css/styles.css").toExternalForm());
        dialog.setScene(scene);
        dialog.showAndWait();
    }

    /**
     * التحقق من صحة نموذج المخزن
     */
    private boolean validateWarehouseForm(TextField nameField, TextField locationField) {
        if (nameField.getText().trim().isEmpty()) {
            showPlaceholderDialog("تحذير", "يرجى إدخال اسم المخزن");
            return false;
        }

        if (locationField.getText().trim().isEmpty()) {
            showPlaceholderDialog("تحذير", "يرجى إدخال موقع المخزن");
            return false;
        }

        return true;
    }
}
