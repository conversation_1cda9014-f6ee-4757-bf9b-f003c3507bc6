package com.accounting.model;

import javafx.beans.property.*;

/**
 * نموذج عنصر التسليم
 */
public class DeliveryItem {
    
    // المعلومات الأساسية
    private final StringProperty itemId = new SimpleStringProperty();
    private final StringProperty description = new SimpleStringProperty();
    private final DoubleProperty length = new SimpleDoubleProperty();
    private final DoubleProperty width = new SimpleDoubleProperty();
    private final IntegerProperty count = new SimpleIntegerProperty();
    private final StringProperty notes = new SimpleStringProperty();
    
    // معلومات إضافية
    private final StringProperty sourceMeasurementId = new SimpleStringProperty(); // معرف المقاس المصدر
    private final BooleanProperty isDelivered = new SimpleBooleanProperty(false); // هل تم التسليم
    private final ObjectProperty<DeliveryStatus> deliveryStatus = new SimpleObjectProperty<>(DeliveryStatus.PENDING);
    
    /**
     * حالات التسليم
     */
    public enum DeliveryStatus {
        PENDING("في الانتظار"),
        DELIVERED("تم التسليم"),
        CANCELLED("ملغي");
        
        private final String displayName;
        
        DeliveryStatus(String displayName) {
            this.displayName = displayName;
        }
        
        public String getDisplayName() {
            return displayName;
        }
        
        @Override
        public String toString() {
            return displayName;
        }
    }
    
    /**
     * المنشئ الافتراضي
     */
    public DeliveryItem() {
        this.itemId.set(generateItemId());
        
        // إضافة مستمعات للحسابات التلقائية
        this.length.addListener((obs, oldVal, newVal) -> updateCalculations());
        this.width.addListener((obs, oldVal, newVal) -> updateCalculations());
        this.count.addListener((obs, oldVal, newVal) -> updateCalculations());
    }
    
    /**
     * منشئ مع معلومات أساسية
     */
    public DeliveryItem(String description, double length, double width, int count) {
        this();
        this.description.set(description);
        this.length.set(length);
        this.width.set(width);
        this.count.set(count);
    }
    
    /**
     * توليد معرف العنصر
     */
    private String generateItemId() {
        return "DI" + System.currentTimeMillis() + "_" + (int)(Math.random() * 1000);
    }
    
    /**
     * تحديث الحسابات
     */
    private void updateCalculations() {
        // يمكن إضافة حسابات إضافية هنا إذا لزم الأمر
    }
    
    // Getters and Setters
    
    public String getItemId() {
        return itemId.get();
    }
    
    public StringProperty itemIdProperty() {
        return itemId;
    }
    
    public void setItemId(String itemId) {
        this.itemId.set(itemId);
    }
    
    public String getDescription() {
        return description.get();
    }
    
    public StringProperty descriptionProperty() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description.set(description);
    }
    
    public double getLength() {
        return length.get();
    }
    
    public DoubleProperty lengthProperty() {
        return length;
    }
    
    public void setLength(double length) {
        this.length.set(length);
    }
    
    public double getWidth() {
        return width.get();
    }
    
    public DoubleProperty widthProperty() {
        return width;
    }
    
    public void setWidth(double width) {
        this.width.set(width);
    }
    
    public int getCount() {
        return count.get();
    }
    
    public IntegerProperty countProperty() {
        return count;
    }
    
    public void setCount(int count) {
        this.count.set(count);
    }
    
    public String getNotes() {
        return notes.get();
    }
    
    public StringProperty notesProperty() {
        return notes;
    }
    
    public void setNotes(String notes) {
        this.notes.set(notes);
    }
    
    public String getSourceMeasurementId() {
        return sourceMeasurementId.get();
    }
    
    public StringProperty sourceMeasurementIdProperty() {
        return sourceMeasurementId;
    }
    
    public void setSourceMeasurementId(String sourceMeasurementId) {
        this.sourceMeasurementId.set(sourceMeasurementId);
    }
    
    public boolean isDelivered() {
        return isDelivered.get();
    }
    
    public BooleanProperty isDeliveredProperty() {
        return isDelivered;
    }
    
    public void setDelivered(boolean delivered) {
        this.isDelivered.set(delivered);
        if (delivered) {
            setDeliveryStatus(DeliveryStatus.DELIVERED);
        } else {
            setDeliveryStatus(DeliveryStatus.PENDING);
        }
    }
    
    public DeliveryStatus getDeliveryStatus() {
        return deliveryStatus.get();
    }
    
    public ObjectProperty<DeliveryStatus> deliveryStatusProperty() {
        return deliveryStatus;
    }
    
    public void setDeliveryStatus(DeliveryStatus deliveryStatus) {
        this.deliveryStatus.set(deliveryStatus);
        this.isDelivered.set(deliveryStatus == DeliveryStatus.DELIVERED);
    }
    
    /**
     * حساب المساحة بالمتر المربع
     */
    public double getSquareMeters() {
        return (length.get() * width.get()) / 1_000_000.0; // تحويل من مم² إلى م²
    }
    
    /**
     * حساب إجمالي المساحة
     */
    public double getTotalSquareMeters() {
        return getSquareMeters() * count.get();
    }
    
    /**
     * حساب المحيط بالمتر
     */
    public double getPerimeterMeters() {
        return ((length.get() * 2) + (width.get() * 2)) / 1000.0; // تحويل من مم إلى م
    }
    
    /**
     * حساب إجمالي المحيط
     */
    public double getTotalPerimeterMeters() {
        return getPerimeterMeters() * count.get();
    }
    
    /**
     * التحقق من صحة البيانات
     */
    public boolean isValid() {
        return description.get() != null && !description.get().trim().isEmpty() &&
               length.get() > 0 &&
               width.get() > 0 &&
               count.get() > 0;
    }
    
    /**
     * نسخ البيانات من مقاس مطلوب
     */
    public void copyFromRequiredMeasurement(RequiredMeasurement measurement) {
        setDescription(measurement.getDescription());
        setLength(measurement.getLength());
        setWidth(measurement.getWidth());
        setCount(measurement.getCount());
        setSourceMeasurementId(measurement.getDescription()); // استخدام الوصف كمعرف مؤقت
    }
    
    /**
     * إنشاء نسخة من العنصر
     */
    public DeliveryItem copy() {
        DeliveryItem copy = new DeliveryItem();
        copy.setDescription(this.getDescription());
        copy.setLength(this.getLength());
        copy.setWidth(this.getWidth());
        copy.setCount(this.getCount());
        copy.setNotes(this.getNotes());
        copy.setSourceMeasurementId(this.getSourceMeasurementId());
        copy.setDeliveryStatus(this.getDeliveryStatus());
        return copy;
    }
    
    @Override
    public String toString() {
        return "DeliveryItem{" +
                "description=" + description.get() +
                ", length=" + length.get() +
                ", width=" + width.get() +
                ", count=" + count.get() +
                ", deliveryStatus=" + deliveryStatus.get() +
                '}';
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        
        DeliveryItem that = (DeliveryItem) obj;
        return itemId.get().equals(that.itemId.get());
    }
    
    @Override
    public int hashCode() {
        return itemId.get().hashCode();
    }
}
