package com.accounting.model;

/**
 * نموذج وحدة القياس
 * Unit of Measurement Model
 */
public enum Unit {
    SQUARE_METER("متر مربع", "م²"),
    LINEAR_METER("متر طولي", "م"),
    KILOGRAM("كيلو جرام", "كجم"),
    LITER("لتر", "لتر"),
    PIECE("قطعة", "قطعة"),
    GRAM("جرام", "جم"),
    TON("طن", "طن"),
    CENTIMETER("سنتيمتر", "سم"),
    MILLIMETER("مليمتر", "مم"),
    INCH("بوصة", "بوصة"),
    FOOT("قدم", "قدم"),
    ROLL("لفة", "لفة"),
    SHEET("لوح", "لوح"),
    BOX("صندوق", "صندوق"),
    PACK("عبوة", "عبوة");
    
    private final String arabicName;
    private final String symbol;
    
    Unit(String arabicName, String symbol) {
        this.arabicName = arabicName;
        this.symbol = symbol;
    }
    
    public String getArabicName() {
        return arabicName;
    }
    
    public String getSymbol() {
        return symbol;
    }
    
    @Override
    public String toString() {
        return arabicName + " (" + symbol + ")";
    }
    
    /**
     * التحقق من كون الوحدة تحتاج لحساب المساحة
     */
    public boolean requiresAreaCalculation() {
        return this == SQUARE_METER;
    }
    
    /**
     * التحقق من كون الوحدة تحتاج لأبعاد
     */
    public boolean requiresDimensions() {
        return this == SQUARE_METER;
    }
}
