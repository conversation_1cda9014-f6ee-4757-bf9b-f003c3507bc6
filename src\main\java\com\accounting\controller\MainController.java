package com.accounting.controller;

import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.control.*;
import javafx.scene.layout.BorderPane;
import javafx.scene.layout.VBox;
import javafx.scene.layout.HBox;
import javafx.geometry.Insets;
import javafx.geometry.Pos;
import de.jensd.fx.glyphs.fontawesome.FontAwesomeIcon;
import de.jensd.fx.glyphs.fontawesome.FontAwesomeIconView;
import com.accounting.model.CompanyInfo;
import javafx.stage.Stage;
import javafx.stage.Modality;
import javafx.scene.Scene;

import java.net.URL;
import java.util.ResourceBundle;

/**
 * المتحكم الرئيسي للواجهة
 * Main Controller for the application interface
 */
public class MainController implements Initializable {

    @FXML
    private BorderPane mainBorderPane;

    @FXML
    private MenuBar menuBar;

    @FXML
    private VBox contentArea;

    @FXML
    private Label statusLabel;

    private SalesController salesController;
    private PayrollController payrollController;
    private InventoryController inventoryController;

    @Override
    public void initialize(URL url, ResourceBundle resourceBundle) {
        salesController = new SalesController();
        payrollController = new PayrollController();
        inventoryController = new InventoryController();
        setupMenuBar();
        setupWelcomeScreen();
        updateStatusBar("مرحباً بك في نظام الحسابات المتكامل");
    }

    /**
     * إعداد شريط القوائم
     */
    private void setupMenuBar() {
        menuBar.getMenus().clear();
        
        // قائمة بيانات الشركة
        Menu companyMenu = createMenu("بيانات الشركة", FontAwesomeIcon.BUILDING);
        companyMenu.getItems().addAll(
            createMenuItem("معلومات الشركة", FontAwesomeIcon.INFO_CIRCLE, this::showCompanyInfo),
            createMenuItem("الفروع", FontAwesomeIcon.SITEMAP, this::showBranches),
            new SeparatorMenuItem(),
            createMenuItem("الإعدادات العامة", FontAwesomeIcon.COG, this::showGeneralSettings)
        );

        // قائمة المبيعات
        Menu salesMenu = createMenu("المبيعات", FontAwesomeIcon.SHOPPING_CART);
        salesMenu.getItems().addAll(
            createMenuItem("فاتورة مبيعات", FontAwesomeIcon.FILE_TEXT, this::showSalesInvoice),
            createMenuItem("عروض الأسعار", FontAwesomeIcon.CALCULATOR, this::showQuotations),
            createMenuItem("العملاء", FontAwesomeIcon.USERS, this::showCustomers),
            new SeparatorMenuItem(),
            createMenuItem("تقارير المبيعات", FontAwesomeIcon.BAR_CHART, this::showSalesReports)
        );

        // قائمة المخازن
        Menu inventoryMenu = createMenu("المخازن", FontAwesomeIcon.CUBES);
        inventoryMenu.getItems().addAll(
            createMenuItem("موديول المخازن", FontAwesomeIcon.CUBES, this::showInventoryModule),
            new SeparatorMenuItem(),
            createMenuItem("الأصناف", FontAwesomeIcon.CUBE, this::showItems),
            createMenuItem("حركة المخزن", FontAwesomeIcon.EXCHANGE, this::showInventoryMovement),
            createMenuItem("الجرد", FontAwesomeIcon.LIST_ALT, this::showInventoryCount),
            new SeparatorMenuItem(),
            createMenuItem("تقارير المخازن", FontAwesomeIcon.PIE_CHART, this::showInventoryReports)
        );

        // قائمة الحسابات
        Menu accountingMenu = createMenu("الحسابات", FontAwesomeIcon.MONEY);
        accountingMenu.getItems().addAll(
            createMenuItem("دليل الحسابات", FontAwesomeIcon.BOOK, this::showChartOfAccounts),
            createMenuItem("القيود اليومية", FontAwesomeIcon.EDIT, this::showJournalEntries),
            createMenuItem("الموردين", FontAwesomeIcon.TRUCK, this::showSuppliers),
            new SeparatorMenuItem(),
            createMenuItem("التقارير المالية", FontAwesomeIcon.LINE_CHART, this::showFinancialReports)
        );

        // قائمة الرواتب
        Menu payrollMenu = createMenu("الرواتب والأجور", FontAwesomeIcon.USER);
        payrollMenu.getItems().addAll(
            createMenuItem("موديول الرواتب", FontAwesomeIcon.MONEY, this::showPayrollModule),
            new SeparatorMenuItem(),
            createMenuItem("الموظفين", FontAwesomeIcon.USERS, this::showEmployees),
            createMenuItem("تسجيل سلفة", FontAwesomeIcon.HAND_ALT_LEFT, this::showAdvanceEntry),
            createMenuItem("تسجيل مستحق", FontAwesomeIcon.CALCULATOR, this::showSalaryDueEntry),
            createMenuItem("تسجيل سداد", FontAwesomeIcon.CREDIT_CARD, this::showPaymentEntry),
            new SeparatorMenuItem(),
            createMenuItem("تقارير الرواتب", FontAwesomeIcon.FILE_PDF_ALT, this::showPayrollReports)
        );

        // قائمة التصنيع
        Menu manufacturingMenu = createMenu("التصنيع", FontAwesomeIcon.COG);
        manufacturingMenu.getItems().addAll(
            createMenuItem("أوامر الإنتاج", FontAwesomeIcon.INDUSTRY, this::showProductionOrders),
            createMenuItem("وصفات الإنتاج", FontAwesomeIcon.FLASK, this::showBillOfMaterials),
            createMenuItem("مراكز العمل", FontAwesomeIcon.WRENCH, this::showWorkCenters),
            new SeparatorMenuItem(),
            createMenuItem("تقارير الإنتاج", FontAwesomeIcon.AREA_CHART, this::showProductionReports)
        );

        // قائمة المساعدة
        Menu helpMenu = createMenu("مساعدة", FontAwesomeIcon.QUESTION_CIRCLE);
        helpMenu.getItems().addAll(
            createMenuItem("حول البرنامج", FontAwesomeIcon.INFO, this::showAbout),
            createMenuItem("دليل المستخدم", FontAwesomeIcon.BOOK, this::showUserGuide)
        );

        menuBar.getMenus().addAll(companyMenu, salesMenu, inventoryMenu, 
                                 accountingMenu, payrollMenu, manufacturingMenu, helpMenu);
    }

    /**
     * إنشاء قائمة مع أيقونة
     */
    private Menu createMenu(String text, FontAwesomeIcon icon) {
        Menu menu = new Menu(text);
        FontAwesomeIconView iconView = new FontAwesomeIconView(icon);
        iconView.setSize("16px");
        menu.setGraphic(iconView);
        return menu;
    }

    /**
     * إنشاء عنصر قائمة مع أيقونة وحدث
     */
    private MenuItem createMenuItem(String text, FontAwesomeIcon icon, Runnable action) {
        MenuItem menuItem = new MenuItem(text);
        FontAwesomeIconView iconView = new FontAwesomeIconView(icon);
        iconView.setSize("14px");
        menuItem.setGraphic(iconView);
        menuItem.setOnAction(e -> action.run());
        return menuItem;
    }

    /**
     * إعداد شاشة الترحيب
     */
    private void setupWelcomeScreen() {
        VBox welcomeBox = new VBox(20);
        welcomeBox.setAlignment(Pos.CENTER);
        welcomeBox.setPadding(new Insets(50));

        Label welcomeLabel = new Label("مرحباً بك في نظام الحسابات المتكامل");
        welcomeLabel.getStyleClass().add("welcome-title");

        Label descriptionLabel = new Label("اختر من القوائم أعلاه للبدء في استخدام النظام");
        descriptionLabel.getStyleClass().add("welcome-description");

        // إضافة أزرار سريعة للوصول للموديولات الرئيسية
        HBox quickAccessBox = createQuickAccessButtons();

        welcomeBox.getChildren().addAll(welcomeLabel, descriptionLabel, quickAccessBox);
        contentArea.getChildren().clear();
        contentArea.getChildren().add(welcomeBox);
    }

    /**
     * إنشاء أزرار الوصول السريع
     */
    private HBox createQuickAccessButtons() {
        HBox buttonBox = new HBox(15);
        buttonBox.setAlignment(Pos.CENTER);
        buttonBox.setPadding(new Insets(30, 0, 0, 0));

        Button salesBtn = createQuickAccessButton("المبيعات", FontAwesomeIcon.SHOPPING_CART, this::showSalesInvoice);
        Button inventoryBtn = createQuickAccessButton("المخازن", FontAwesomeIcon.CUBES, this::showInventoryModule);
        Button accountingBtn = createQuickAccessButton("الحسابات", FontAwesomeIcon.MONEY, this::showChartOfAccounts);
        Button payrollBtn = createQuickAccessButton("الرواتب", FontAwesomeIcon.USER, this::showPayrollModule);

        buttonBox.getChildren().addAll(salesBtn, inventoryBtn, accountingBtn, payrollBtn);
        return buttonBox;
    }

    /**
     * إنشاء زر وصول سريع
     */
    private Button createQuickAccessButton(String text, FontAwesomeIcon icon, Runnable action) {
        Button button = new Button(text);
        FontAwesomeIconView iconView = new FontAwesomeIconView(icon);
        iconView.setSize("24px");
        button.setGraphic(iconView);
        button.getStyleClass().add("quick-access-button");
        button.setOnAction(e -> action.run());
        return button;
    }

    /**
     * تحديث شريط الحالة
     */
    private void updateStatusBar(String message) {
        statusLabel.setText(message);
    }

    // أحداث القوائم - ستتم إضافة التفاصيل لاحقاً
    private void showCompanyInfo() { showCompanyInfoDialog(); }
    private void showBranches() { showPlaceholder("الفروع"); }
    private void showGeneralSettings() { showPlaceholder("الإعدادات العامة"); }
    private void showSalesInvoice() { showSalesModule(); }
    private void showQuotations() { showPlaceholder("عروض الأسعار"); }
    private void showCustomers() { showCustomersModule(); }
    private void showSalesReports() { showSalesReportsModule(); }
    private void showInventoryModule() { showInventoryModuleView(); }
    private void showItems() { showPlaceholder("الأصناف"); }
    private void showInventoryMovement() { showPlaceholder("حركة المخزن"); }
    private void showInventoryCount() { showPlaceholder("الجرد"); }
    private void showInventoryReports() { showPlaceholder("تقارير المخازن"); }
    private void showChartOfAccounts() { showPlaceholder("دليل الحسابات"); }
    private void showJournalEntries() { showPlaceholder("القيود اليومية"); }
    private void showSuppliers() { showPlaceholder("الموردين"); }
    private void showFinancialReports() { showPlaceholder("التقارير المالية"); }
    private void showPayrollModule() { showPayrollModuleView(); }
    private void showEmployees() { showPlaceholder("الموظفين"); }
    private void showAdvanceEntry() { showPlaceholder("تسجيل سلفة"); }
    private void showSalaryDueEntry() { showPlaceholder("تسجيل مستحق"); }
    private void showPaymentEntry() { showPlaceholder("تسجيل سداد"); }
    private void showPayrollReports() { showPlaceholder("تقارير الرواتب"); }
    private void showProductionOrders() { showPlaceholder("أوامر الإنتاج"); }
    private void showBillOfMaterials() { showPlaceholder("وصفات الإنتاج"); }
    private void showWorkCenters() { showPlaceholder("مراكز العمل"); }
    private void showProductionReports() { showPlaceholder("تقارير الإنتاج"); }
    private void showAbout() { showPlaceholder("حول البرنامج"); }
    private void showUserGuide() { showPlaceholder("دليل المستخدم"); }

    /**
     * عرض موديول المبيعات
     */
    private void showSalesModule() {
        VBox salesModule = salesController.createSalesModule();

        // إضافة زر العودة
        HBox headerBox = new HBox(10);
        headerBox.setAlignment(Pos.CENTER_LEFT);
        headerBox.setPadding(new Insets(10, 0, 0, 0));

        Button backButton = new Button("العودة للشاشة الرئيسية");
        FontAwesomeIconView backIcon = new FontAwesomeIconView(FontAwesomeIcon.ARROW_LEFT);
        backIcon.setSize("14px");
        backButton.setGraphic(backIcon);
        backButton.getStyleClass().add("back-button");
        backButton.setOnAction(e -> setupWelcomeScreen());

        headerBox.getChildren().add(backButton);

        VBox moduleContainer = new VBox();
        moduleContainer.getChildren().addAll(headerBox, salesModule);

        contentArea.getChildren().clear();
        contentArea.getChildren().add(moduleContainer);

        updateStatusBar("تم فتح موديول: المبيعات");
    }

    /**
     * عرض موديول العملاء
     */
    private void showCustomersModule() {
        showPlaceholder("إدارة العملاء");
    }

    /**
     * عرض موديول تقارير المبيعات
     */
    private void showSalesReportsModule() {
        showPlaceholder("تقارير المبيعات");
    }

    /**
     * عرض موديول الرواتب والأجور
     */
    private void showPayrollModuleView() {
        VBox payrollModule = payrollController.createPayrollModule();

        // إضافة زر العودة
        HBox headerBox = new HBox(10);
        headerBox.setAlignment(Pos.CENTER_LEFT);
        headerBox.setPadding(new Insets(10, 0, 0, 0));

        Button backButton = new Button("العودة للشاشة الرئيسية");
        FontAwesomeIconView backIcon = new FontAwesomeIconView(FontAwesomeIcon.ARROW_LEFT);
        backIcon.setSize("14px");
        backButton.setGraphic(backIcon);
        backButton.getStyleClass().add("back-button");
        backButton.setOnAction(e -> setupWelcomeScreen());

        headerBox.getChildren().add(backButton);

        VBox moduleContainer = new VBox();
        moduleContainer.getChildren().addAll(headerBox, payrollModule);

        contentArea.getChildren().clear();
        contentArea.getChildren().add(moduleContainer);

        updateStatusBar("تم فتح موديول: الرواتب والأجور");
    }

    /**
     * عرض موديول المخازن
     */
    private void showInventoryModuleView() {
        VBox inventoryModule = inventoryController.createInventoryModule();

        // إضافة زر العودة
        HBox headerBox = new HBox(10);
        headerBox.setAlignment(Pos.CENTER_LEFT);
        headerBox.setPadding(new Insets(10, 0, 0, 0));

        Button backButton = new Button("العودة للشاشة الرئيسية");
        FontAwesomeIconView backIcon = new FontAwesomeIconView(FontAwesomeIcon.ARROW_LEFT);
        backIcon.setSize("14px");
        backButton.setGraphic(backIcon);
        backButton.getStyleClass().add("back-button");
        backButton.setOnAction(e -> setupWelcomeScreen());

        headerBox.getChildren().add(backButton);

        VBox moduleContainer = new VBox();
        moduleContainer.getChildren().addAll(headerBox, inventoryModule);

        contentArea.getChildren().clear();
        contentArea.getChildren().add(moduleContainer);

        updateStatusBar("تم فتح موديول: المخازن");
    }

    /**
     * عرض شاشة مؤقتة للموديولات
     */
    private void showPlaceholder(String moduleName) {
        VBox placeholderBox = new VBox(20);
        placeholderBox.setAlignment(Pos.CENTER);
        placeholderBox.setPadding(new Insets(50));

        Label titleLabel = new Label(moduleName);
        titleLabel.getStyleClass().add("module-title");

        Label messageLabel = new Label("هذا الموديول قيد التطوير");
        messageLabel.getStyleClass().add("module-message");

        Button backButton = new Button("العودة للشاشة الرئيسية");
        backButton.getStyleClass().add("back-button");
        backButton.setOnAction(e -> setupWelcomeScreen());

        placeholderBox.getChildren().addAll(titleLabel, messageLabel, backButton);
        contentArea.getChildren().clear();
        contentArea.getChildren().add(placeholderBox);

        updateStatusBar("تم فتح موديول: " + moduleName);
    }

    /**
     * عرض نافذة بيانات الشركة
     */
    private void showCompanyInfoDialog() {
        Stage dialog = new Stage();
        dialog.setTitle("🏢 بيانات الشركة");
        dialog.initModality(Modality.APPLICATION_MODAL);
        dialog.setWidth(600);
        dialog.setHeight(500);

        VBox mainLayout = new VBox(20);
        mainLayout.setPadding(new Insets(30));

        // العنوان
        Label titleLabel = new Label("🏢 معلومات الشركة");
        titleLabel.getStyleClass().add("dialog-title");

        // الحصول على بيانات الشركة
        CompanyInfo companyInfo = CompanyInfo.getInstance();

        // نموذج البيانات
        VBox formBox = new VBox(15);
        formBox.setPadding(new Insets(20));
        formBox.setStyle("-fx-background-color: #f8f9fa; -fx-border-color: #dee2e6; -fx-border-width: 1; -fx-border-radius: 8;");

        // اسم الشركة
        Label companyNameLabel = new Label("اسم الشركة:");
        companyNameLabel.getStyleClass().add("field-label");
        TextField companyNameField = new TextField();
        companyNameField.setText(companyInfo.getCompanyName());
        companyNameField.setPromptText("أدخل اسم الشركة...");

        // النشاط
        Label activityLabel = new Label("نشاط الشركة:");
        activityLabel.getStyleClass().add("field-label");
        TextField activityField = new TextField();
        activityField.setText(companyInfo.getActivity());
        activityField.setPromptText("أدخل نشاط الشركة...");

        // العنوان
        Label addressLabel = new Label("العنوان:");
        addressLabel.getStyleClass().add("field-label");
        TextArea addressArea = new TextArea();
        addressArea.setText(companyInfo.getAddress());
        addressArea.setPromptText("أدخل عنوان الشركة...");
        addressArea.setPrefRowCount(2);
        addressArea.setWrapText(true);

        // رقم الهاتف
        Label phoneLabel = new Label("رقم الهاتف:");
        phoneLabel.getStyleClass().add("field-label");
        TextField phoneField = new TextField();
        phoneField.setText(companyInfo.getPhone());
        phoneField.setPromptText("أدخل رقم الهاتف...");

        // الموقع الإلكتروني
        Label websiteLabel = new Label("الموقع الإلكتروني:");
        websiteLabel.getStyleClass().add("field-label");
        TextField websiteField = new TextField();
        websiteField.setText(companyInfo.getWebsite());
        websiteField.setPromptText("أدخل الموقع الإلكتروني...");

        // البريد الإلكتروني
        Label emailLabel = new Label("البريد الإلكتروني:");
        emailLabel.getStyleClass().add("field-label");
        TextField emailField = new TextField();
        emailField.setText(companyInfo.getEmail());
        emailField.setPromptText("أدخل البريد الإلكتروني...");

        formBox.getChildren().addAll(
            companyNameLabel, companyNameField,
            activityLabel, activityField,
            addressLabel, addressArea,
            phoneLabel, phoneField,
            websiteLabel, websiteField,
            emailLabel, emailField
        );

        // أزرار الحفظ والإلغاء
        HBox buttonBox = new HBox(15);
        buttonBox.setAlignment(Pos.CENTER_RIGHT);

        Button saveButton = new Button("حفظ البيانات");
        FontAwesomeIconView saveIcon = new FontAwesomeIconView(FontAwesomeIcon.SAVE);
        saveIcon.setSize("14px");
        saveButton.setGraphic(saveIcon);
        saveButton.getStyleClass().add("save-button");
        saveButton.setOnAction(e -> {
            // حفظ البيانات
            companyInfo.updateCompanyInfo(
                companyNameField.getText().trim(),
                activityField.getText().trim(),
                addressArea.getText().trim(),
                phoneField.getText().trim(),
                websiteField.getText().trim(),
                emailField.getText().trim()
            );

            // حفظ في الملف
            companyInfo.saveToFile();

            // رسالة تأكيد
            showSuccessDialog("تم حفظ بيانات الشركة بنجاح!");
            dialog.close();
        });

        Button cancelButton = new Button("إلغاء");
        cancelButton.setOnAction(e -> dialog.close());

        buttonBox.getChildren().addAll(saveButton, cancelButton);

        mainLayout.getChildren().addAll(titleLabel, formBox, buttonBox);

        Scene scene = new Scene(mainLayout);
        scene.getStylesheets().add(getClass().getResource("/css/styles.css").toExternalForm());
        dialog.setScene(scene);
        dialog.showAndWait();
    }

    /**
     * عرض رسالة نجاح
     */
    private void showSuccessDialog(String message) {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle("نجح العملية");
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }
}
