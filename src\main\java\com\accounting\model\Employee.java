package com.accounting.model;

import java.time.LocalDate;

/**
 * نموذج الموظف
 * Employee Model
 */
public class Employee {
    private String employeeId;
    private String name;
    private LocalDate startDate;
    private double baseSalary;
    private String position;
    private String department;
    private String phone;
    private String email;
    private String address;
    private boolean isActive;
    
    public Employee() {
        this.isActive = true;
    }
    
    public Employee(String employeeId, String name, LocalDate startDate, double baseSalary) {
        this.employeeId = employeeId;
        this.name = name;
        this.startDate = startDate;
        this.baseSalary = baseSalary;
        this.isActive = true;
    }
    
    public Employee(String employeeId, String name, LocalDate startDate, double baseSalary, 
                   String position, String department, String phone, String email, String address) {
        this.employeeId = employeeId;
        this.name = name;
        this.startDate = startDate;
        this.baseSalary = baseSalary;
        this.position = position;
        this.department = department;
        this.phone = phone;
        this.email = email;
        this.address = address;
        this.isActive = true;
    }
    
    // Getters and Setters
    public String getEmployeeId() { return employeeId; }
    public void setEmployeeId(String employeeId) { this.employeeId = employeeId; }
    
    public String getName() { return name; }
    public void setName(String name) { this.name = name; }
    
    public LocalDate getStartDate() { return startDate; }
    public void setStartDate(LocalDate startDate) { this.startDate = startDate; }
    
    public double getBaseSalary() { return baseSalary; }
    public void setBaseSalary(double baseSalary) { this.baseSalary = baseSalary; }
    
    public String getPosition() { return position; }
    public void setPosition(String position) { this.position = position; }
    
    public String getDepartment() { return department; }
    public void setDepartment(String department) { this.department = department; }
    
    public String getPhone() { return phone; }
    public void setPhone(String phone) { this.phone = phone; }
    
    public String getEmail() { return email; }
    public void setEmail(String email) { this.email = email; }
    
    public String getAddress() { return address; }
    public void setAddress(String address) { this.address = address; }
    
    public boolean isActive() { return isActive; }
    public void setActive(boolean active) { isActive = active; }
    
    @Override
    public String toString() {
        return name + " (" + employeeId + ")";
    }
}
