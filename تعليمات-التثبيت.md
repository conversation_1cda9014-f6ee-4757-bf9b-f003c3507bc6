# تعليمات التثبيت والنشر - نظام المحاسبة المتكامل

## 🎯 نظرة عامة

هذا الدليل يوضح كيفية إنشاء ملف تثبيت احترافي لنظام المحاسبة مع Java مدمجة، بحيث يمكن تثبيته على أي جهاز Windows بدون الحاجة لتثبيت Java منفصل.

## 📋 المتطلبات للبناء

### على جهاز التطوير:
- **Java Development Kit (JDK)**: 17 أو أحدث
- **Maven**: 3.8+ (مضمن في المشروع)
- **Windows**: 10/11 (64-bit)
- **WiX Toolset**: لإنشاء MSI (اختياري)
- **مساحة القرص**: 2 GB للبناء

### تحميل JDK:
```
https://adoptium.net/temurin/releases/
اختر: OpenJDK 17 LTS أو 21 LTS
النوع: JDK (ليس JRE)
النظام: Windows x64
```

## 🚀 خطوات إنشاء ملف التثبيت

### الطريقة الأولى: استخدام الـ Script الجاهز (مُنصح به)

#### 1. تحضير البيئة:
```bash
# تأكد من تثبيت Java
java -version

# يجب أن تظهر رسالة مثل:
# openjdk version "17.0.7" 2023-04-18
```

#### 2. تشغيل script البناء:
```bash
# افتح Command Prompt في مجلد المشروع
cd C:\path\to\Glass

# تشغيل script البناء
build-installer.bat
```

#### 3. انتظار اكتمال العملية:
- ستستغرق العملية 5-15 دقيقة
- ستظهر رسائل تقدم العملية
- في النهاية ستجد ملف التثبيت في `target\installer\`

### الطريقة الثانية: البناء اليدوي خطوة بخطوة

#### 1. تنظيف المشروع:
```bash
mvnw clean
```

#### 2. بناء JAR:
```bash
mvnw package
```

#### 3. إنشاء Runtime Image:
```bash
jlink --module-path "%JAVA_HOME%\jmods" ^
      --add-modules java.base,java.desktop,javafx.controls,javafx.fxml ^
      --output target\java-runtime ^
      --compress=2 ^
      --no-header-files ^
      --no-man-pages
```

#### 4. إنشاء Installer:
```bash
jpackage --type msi ^
         --input target ^
         --dest target\installer ^
         --name "AccountingSystem" ^
         --main-jar accounting-system-1.0.0.jar ^
         --main-class com.accounting.AccountingApplication ^
         --runtime-image target\java-runtime ^
         --app-version 1.0.0 ^
         --vendor "Advanced Glass and Aluminum Company" ^
         --win-dir-chooser ^
         --win-menu ^
         --win-shortcut
```

## 📦 ملفات التثبيت المُنتجة

### ملفات الإخراج:
```
target/
├── installer/
│   ├── AccountingSystem-1.0.0.msi    # ملف التثبيت الرئيسي
│   └── AccountingSystem-1.0.0.exe    # نسخة EXE (إذا فشل MSI)
├── java-runtime/                     # Java Runtime مخصص
└── accounting-system-1.0.0.jar       # JAR الأساسي
```

### خصائص ملف التثبيت:
- **الحجم**: ~80-120 MB (مع Java مدمجة)
- **النوع**: Windows Installer (.msi)
- **التوافق**: Windows 10/11 (64-bit)
- **المتطلبات**: لا يحتاج Java منفصل

## 🔧 تخصيص ملف التثبيت

### تغيير معلومات الشركة:
```bash
# في build-installer.bat، عدّل هذه الأسطر:
--vendor "اسم شركتك" ^
--description "وصف نظامك" ^
--copyright "© 2025 اسم شركتك"
```

### إضافة أيقونة مخصصة:
```bash
# أضف هذا السطر لـ jpackage:
--icon src\main\resources\images\app-icon.ico
```

### تخصيص مجلد التثبيت:
```bash
# أضف هذا السطر:
--install-dir "C:\Program Files\اسم البرنامج"
```

## 🧪 اختبار ملف التثبيت

### اختبار أساسي:
1. **تشغيل الـ installer**: انقر نقراً مزدوجاً على `.msi`
2. **اتباع المعالج**: اختر مجلد التثبيت
3. **التحقق من التثبيت**: ابحث عن البرنامج في قائمة البرامج
4. **تشغيل البرنامج**: انقر على الاختصار
5. **اختبار الوظائف**: تأكد من عمل جميع الموديولات

### اختبار متقدم:
1. **تثبيت على جهاز نظيف**: جهاز بدون Java
2. **اختبار الأداء**: سرعة بدء التشغيل
3. **اختبار إلغاء التثبيت**: من لوحة التحكم
4. **اختبار التحديث**: تثبيت إصدار جديد فوق القديم

## 🔄 النشر والتوزيع

### للاستخدام الداخلي:
1. **نسخ الملف**: انسخ `.msi` إلى مجلد مشترك
2. **إرسال للمستخدمين**: عبر البريد أو الشبكة
3. **تعليمات التثبيت**: أرفق دليل المستخدم

### للتوزيع الخارجي:
1. **رفع على موقع**: رفع الملف على موقع الشركة
2. **إنشاء رابط تحميل**: رابط مباشر للتحميل
3. **دليل التثبيت**: صفحة ويب بالتعليمات

## 🛡️ الأمان والتوقيع

### توقيع ملف التثبيت (اختياري):
```bash
# إذا كان لديك شهادة رقمية:
signtool sign /f certificate.pfx /p password target\installer\AccountingSystem-1.0.0.msi
```

### فوائد التوقيع:
- تقليل تحذيرات Windows Defender
- زيادة ثقة المستخدمين
- منع التلاعب بالملف

## 📊 حل المشاكل الشائعة

### مشكلة: فشل في إنشاء MSI
**الحل**:
```bash
# جرب إنشاء EXE بدلاً من MSI
jpackage --type exe ...
```

### مشكلة: حجم الملف كبير جداً
**الحل**:
```bash
# استخدم jlink لتقليل حجم Java Runtime
jlink --add-modules java.base,java.desktop,javafx.controls,javafx.fxml
```

### مشكلة: البرنامج لا يعمل بعد التثبيت
**الحل**:
```bash
# تأكد من إضافة جميع Java modules المطلوبة
--add-modules java.base,java.desktop,java.logging,javafx.controls,javafx.fxml
```

### مشكلة: مشاكل في الترميز العربي
**الحل**:
```bash
# أضف هذه الخيارات لـ Java:
--java-options "-Dfile.encoding=UTF-8"
--java-options "-Djava.awt.headless=false"
```

## 📋 قائمة التحقق النهائية

### قبل النشر:
- [ ] اختبار التثبيت على جهاز نظيف
- [ ] اختبار جميع الوظائف الأساسية
- [ ] التأكد من عمل الطباعة والتصدير
- [ ] اختبار إلغاء التثبيت
- [ ] فحص الملف بمضاد الفيروسات
- [ ] توثيق أي مشاكل معروفة

### ملفات التوزيع:
- [ ] `AccountingSystem-1.0.0.msi` - ملف التثبيت
- [ ] `دليل-المستخدم.md` - دليل المستخدم
- [ ] `README.md` - معلومات عامة
- [ ] `تعليمات-التثبيت.md` - هذا الملف

## 📞 الدعم التقني

### للمساعدة في التثبيت:
- **البريد الإلكتروني**: <EMAIL>
- **الهاتف**: 123-456-7890
- **ساعات العمل**: الأحد - الخميس، 9:00 ص - 5:00 م

### للإبلاغ عن مشاكل:
1. وصف المشكلة بالتفصيل
2. نوع نظام التشغيل والإصدار
3. خطوات إعادة إنتاج المشكلة
4. لقطات شاشة إذا أمكن

---

**© 2025 شركة الزجاج والألومنيوم المتقدمة**  
**دليل التثبيت والنشر - الإصدار 1.0.0**
