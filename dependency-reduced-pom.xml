<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.accounting</groupId>
  <artifactId>accounting-system</artifactId>
  <name>Accounting System</name>
  <version>1.0.0</version>
  <description>Modern Desktop Accounting System using JavaFX</description>
  <build>
    <plugins>
      <plugin>
        <artifactId>maven-compiler-plugin</artifactId>
        <version>3.11.0</version>
        <configuration>
          <source>17</source>
          <target>17</target>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.openjfx</groupId>
        <artifactId>javafx-maven-plugin</artifactId>
        <version>${javafx.maven.plugin.version}</version>
        <configuration>
          <mainClass>com.accounting.AccountingApplication</mainClass>
        </configuration>
      </plugin>
      <plugin>
        <artifactId>maven-shade-plugin</artifactId>
        <version>3.4.1</version>
        <executions>
          <execution>
            <phase>package</phase>
            <goals>
              <goal>shade</goal>
            </goals>
            <configuration>
              <transformers>
                <transformer>
                  <mainClass>com.accounting.AccountingApplication</mainClass>
                </transformer>
              </transformers>
              <filters>
                <filter>
                  <artifact>*:*</artifact>
                  <excludes>
                    <exclude>META-INF/*.SF</exclude>
                    <exclude>META-INF/*.DSA</exclude>
                    <exclude>META-INF/*.RSA</exclude>
                    <exclude>META-INF/MANIFEST.MF</exclude>
                  </excludes>
                </filter>
              </filters>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.panteleyev</groupId>
        <artifactId>jpackage-maven-plugin</artifactId>
        <version>1.6.0</version>
        <configuration>
          <name>AccountingSystem</name>
          <appVersion>${project.version}</appVersion>
          <vendor>شركة الزجاج والألومنيوم المتقدمة</vendor>
          <destination>target/installer</destination>
          <input>target</input>
          <mainJar>accounting-system-${project.version}-shaded.jar</mainJar>
          <mainClass>com.accounting.AccountingApplication</mainClass>
          <javaOptions>
            <option>-Dfile.encoding=UTF-8</option>
            <option>-Djava.awt.headless=false</option>
          </javaOptions>
          <winDirChooser>true</winDirChooser>
          <winMenu>true</winMenu>
          <winShortcut>true</winShortcut>
          <winUpgradeUuid>************************************</winUpgradeUuid>
          <description>نظام محاسبة متكامل لشركات الزجاج والألومنيوم</description>
          <copyright>© 2025 شركة الزجاج والألومنيوم المتقدمة</copyright>
        </configuration>
      </plugin>
    </plugins>
  </build>
  <dependencies>
    <dependency>
      <groupId>com.itextpdf</groupId>
      <artifactId>itext7-core</artifactId>
      <version>7.2.5</version>
      <type>pom</type>
      <scope>compile</scope>
    </dependency>
  </dependencies>
  <properties>
    <maven.compiler.target>17</maven.compiler.target>
    <maven.compiler.source>17</maven.compiler.source>
    <javafx.maven.plugin.version>0.0.8</javafx.maven.plugin.version>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <javafx.version>21.0.1</javafx.version>
  </properties>
</project>
