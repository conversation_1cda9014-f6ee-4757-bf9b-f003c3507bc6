package com.accounting.controller;

import com.accounting.model.*;
import com.accounting.service.*;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.geometry.Insets;
import javafx.geometry.Pos;
import javafx.scene.Scene;
import javafx.scene.control.*;
import javafx.scene.control.cell.PropertyValueFactory;
import javafx.scene.layout.*;
import javafx.stage.Modality;
import javafx.stage.Stage;
import de.jensd.fx.glyphs.fontawesome.FontAwesomeIcon;
import de.jensd.fx.glyphs.fontawesome.FontAwesomeIconView;

import java.text.DecimalFormat;
import java.time.LocalDate;
import java.util.Optional;

/**
 * كونترولر نموذج إنشاء قيد جديد
 * Journal Entry Form Controller
 */
public class JournalEntryFormController {
    
    private final JournalService journalService;
    private final AccountService accountService;
    private final DecimalFormat decimalFormat = new DecimalFormat("#,##0.00");
    
    // مكونات النموذج
    private TextField entryNumberField;
    private DatePicker entryDatePicker;
    private TextField descriptionField;
    private TextField referenceField;
    private TextArea notesArea;
    private TableView<JournalEntryDetail> detailsTable;
    private ObservableList<JournalEntryDetail> entryDetails;
    private Label totalDebitLabel;
    private Label totalCreditLabel;
    private Label balanceLabel;
    
    // القيد الحالي
    private JournalEntry currentEntry;
    private boolean isEditMode = false;
    
    public JournalEntryFormController() {
        this.journalService = JournalService.getInstance();
        this.accountService = AccountService.getInstance();
        this.entryDetails = FXCollections.observableArrayList();
    }
    
    /**
     * عرض نافذة قيد جديد
     */
    public void showNewJournalEntryDialog() {
        showJournalEntryDialog(null);
    }
    
    /**
     * عرض نافذة تعديل قيد
     */
    public void showEditJournalEntryDialog(JournalEntry entry) {
        showJournalEntryDialog(entry);
    }
    
    /**
     * عرض نافذة القيد
     */
    private void showJournalEntryDialog(JournalEntry entry) {
        this.currentEntry = entry;
        this.isEditMode = (entry != null);
        
        Stage dialog = new Stage();
        dialog.setTitle(isEditMode ? "✏️ تعديل قيد يومية" : "➕ قيد يومية جديد");
        dialog.initModality(Modality.APPLICATION_MODAL);
        
        // تحديد حجم النافذة
        javafx.stage.Screen screen = javafx.stage.Screen.getPrimary();
        double screenHeight = screen.getVisualBounds().getHeight();
        double screenWidth = screen.getVisualBounds().getWidth();
        
        dialog.setWidth(Math.min(1200, screenWidth * 0.9));
        dialog.setHeight(Math.min(800, screenHeight * 0.85));
        dialog.setResizable(true);
        
        VBox mainLayout = createMainLayout();
        
        Scene scene = new Scene(mainLayout);
        scene.getStylesheets().add(getClass().getResource("/css/styles.css").toExternalForm());
        dialog.setScene(scene);
        
        // تحميل البيانات إذا كان في وضع التعديل
        if (isEditMode) {
            loadEntryData();
        } else {
            initializeNewEntry();
        }
        
        dialog.showAndWait();
    }
    
    /**
     * إنشاء التخطيط الرئيسي
     */
    private VBox createMainLayout() {
        VBox mainLayout = new VBox(20);
        mainLayout.setPadding(new Insets(20));
        
        // العنوان
        Label titleLabel = new Label(isEditMode ? "✏️ تعديل قيد يومية" : "➕ قيد يومية جديد");
        titleLabel.getStyleClass().add("dialog-title");
        
        // نموذج البيانات الأساسية
        VBox headerForm = createHeaderForm();
        
        // جدول التفاصيل
        VBox detailsSection = createDetailsSection();
        VBox.setVgrow(detailsSection, Priority.ALWAYS);
        
        // منطقة المجاميع
        HBox totalsSection = createTotalsSection();
        
        // أزرار الإجراءات
        HBox buttonsBox = createButtonsBox();
        
        mainLayout.getChildren().addAll(titleLabel, headerForm, detailsSection, totalsSection, buttonsBox);
        return mainLayout;
    }
    
    /**
     * إنشاء نموذج البيانات الأساسية
     */
    private VBox createHeaderForm() {
        VBox headerForm = new VBox(15);
        headerForm.getStyleClass().add("form-section");
        headerForm.setPadding(new Insets(15));
        
        Label headerTitle = new Label("📋 بيانات القيد الأساسية");
        headerTitle.getStyleClass().add("section-title");
        
        GridPane formGrid = new GridPane();
        formGrid.setHgap(15);
        formGrid.setVgap(15);
        
        // رقم القيد
        Label entryNumberLabel = new Label("رقم القيد:");
        entryNumberField = new TextField();
        entryNumberField.setPromptText("سيتم توليده تلقائياً");
        entryNumberField.setDisable(true);
        
        // تاريخ القيد
        Label entryDateLabel = new Label("تاريخ القيد:");
        entryDatePicker = new DatePicker(LocalDate.now());
        
        // الوصف
        Label descriptionLabel = new Label("الوصف:");
        descriptionField = new TextField();
        descriptionField.setPromptText("وصف القيد...");
        
        // المرجع
        Label referenceLabel = new Label("المرجع:");
        referenceField = new TextField();
        referenceField.setPromptText("رقم المستند أو المرجع...");
        
        // الملاحظات
        Label notesLabel = new Label("الملاحظات:");
        notesArea = new TextArea();
        notesArea.setPromptText("ملاحظات إضافية...");
        notesArea.setPrefRowCount(2);
        
        // ترتيب العناصر في الشبكة
        formGrid.add(entryNumberLabel, 0, 0);
        formGrid.add(entryNumberField, 1, 0);
        formGrid.add(entryDateLabel, 2, 0);
        formGrid.add(entryDatePicker, 3, 0);
        
        formGrid.add(descriptionLabel, 0, 1);
        formGrid.add(descriptionField, 1, 1, 3, 1);
        
        formGrid.add(referenceLabel, 0, 2);
        formGrid.add(referenceField, 1, 2, 3, 1);
        
        formGrid.add(notesLabel, 0, 3);
        formGrid.add(notesArea, 1, 3, 3, 1);
        
        // تحديد عرض الأعمدة
        ColumnConstraints col1 = new ColumnConstraints(100);
        ColumnConstraints col2 = new ColumnConstraints(200);
        ColumnConstraints col3 = new ColumnConstraints(100);
        ColumnConstraints col4 = new ColumnConstraints(200);
        formGrid.getColumnConstraints().addAll(col1, col2, col3, col4);
        
        headerForm.getChildren().addAll(headerTitle, formGrid);
        return headerForm;
    }
    
    /**
     * إنشاء قسم التفاصيل
     */
    private VBox createDetailsSection() {
        VBox detailsSection = new VBox(15);
        detailsSection.getStyleClass().add("form-section");
        detailsSection.setPadding(new Insets(15));
        
        // العنوان وأزرار الإدارة
        HBox headerBox = new HBox(15);
        headerBox.setAlignment(Pos.CENTER_LEFT);
        
        Label detailsTitle = new Label("📊 تفاصيل القيد");
        detailsTitle.getStyleClass().add("section-title");
        
        Region spacer = new Region();
        HBox.setHgrow(spacer, Priority.ALWAYS);
        
        Button addDetailBtn = createActionButton("إضافة سطر", FontAwesomeIcon.PLUS, this::addNewDetail);
        Button editDetailBtn = createActionButton("تعديل", FontAwesomeIcon.EDIT, this::editSelectedDetail);
        Button deleteDetailBtn = createActionButton("حذف", FontAwesomeIcon.TRASH, this::deleteSelectedDetail);
        
        headerBox.getChildren().addAll(detailsTitle, spacer, addDetailBtn, editDetailBtn, deleteDetailBtn);
        
        // جدول التفاصيل
        detailsTable = createDetailsTable();
        VBox.setVgrow(detailsTable, Priority.ALWAYS);
        
        detailsSection.getChildren().addAll(headerBox, detailsTable);
        return detailsSection;
    }
    
    /**
     * إنشاء جدول التفاصيل
     */
    private TableView<JournalEntryDetail> createDetailsTable() {
        TableView<JournalEntryDetail> table = new TableView<>();
        table.setItems(entryDetails);
        table.getStyleClass().add("details-table");
        
        // عمود رقم الحساب
        TableColumn<JournalEntryDetail, String> accountCodeCol = new TableColumn<>("رقم الحساب");
        accountCodeCol.setCellValueFactory(new PropertyValueFactory<>("accountCode"));
        accountCodeCol.setPrefWidth(100);
        
        // عمود اسم الحساب
        TableColumn<JournalEntryDetail, String> accountNameCol = new TableColumn<>("اسم الحساب");
        accountNameCol.setCellValueFactory(new PropertyValueFactory<>("accountName"));
        accountNameCol.setPrefWidth(250);
        
        // عمود البيان
        TableColumn<JournalEntryDetail, String> descriptionCol = new TableColumn<>("البيان");
        descriptionCol.setCellValueFactory(new PropertyValueFactory<>("description"));
        descriptionCol.setPrefWidth(200);
        
        // عمود المدين
        TableColumn<JournalEntryDetail, Double> debitCol = new TableColumn<>("مدين");
        debitCol.setCellValueFactory(new PropertyValueFactory<>("debitAmount"));
        debitCol.setPrefWidth(120);
        debitCol.setCellFactory(col -> new TableCell<JournalEntryDetail, Double>() {
            @Override
            protected void updateItem(Double amount, boolean empty) {
                super.updateItem(amount, empty);
                if (empty || amount == null || amount == 0) {
                    setText("");
                } else {
                    setText(decimalFormat.format(amount));
                    setStyle("-fx-alignment: CENTER-RIGHT;");
                }
            }
        });
        
        // عمود الدائن
        TableColumn<JournalEntryDetail, Double> creditCol = new TableColumn<>("دائن");
        creditCol.setCellValueFactory(new PropertyValueFactory<>("creditAmount"));
        creditCol.setPrefWidth(120);
        creditCol.setCellFactory(col -> new TableCell<JournalEntryDetail, Double>() {
            @Override
            protected void updateItem(Double amount, boolean empty) {
                super.updateItem(amount, empty);
                if (empty || amount == null || amount == 0) {
                    setText("");
                } else {
                    setText(decimalFormat.format(amount));
                    setStyle("-fx-alignment: CENTER-RIGHT;");
                }
            }
        });
        
        table.getColumns().addAll(accountCodeCol, accountNameCol, descriptionCol, debitCol, creditCol);
        
        // إضافة مستمع لتحديث المجاميع
        entryDetails.addListener((javafx.collections.ListChangeListener<JournalEntryDetail>) change -> updateTotals());
        
        return table;
    }
    
    /**
     * إنشاء قسم المجاميع
     */
    private HBox createTotalsSection() {
        HBox totalsSection = new HBox(30);
        totalsSection.setAlignment(Pos.CENTER);
        totalsSection.getStyleClass().add("totals-section");
        totalsSection.setPadding(new Insets(15));
        
        // إجمالي المدين
        VBox debitBox = new VBox(5);
        debitBox.setAlignment(Pos.CENTER);
        Label debitTitleLabel = new Label("إجمالي المدين");
        debitTitleLabel.getStyleClass().add("totals-title");
        totalDebitLabel = new Label("0.00");
        totalDebitLabel.getStyleClass().add("totals-amount");
        debitBox.getChildren().addAll(debitTitleLabel, totalDebitLabel);
        
        // إجمالي الدائن
        VBox creditBox = new VBox(5);
        creditBox.setAlignment(Pos.CENTER);
        Label creditTitleLabel = new Label("إجمالي الدائن");
        creditTitleLabel.getStyleClass().add("totals-title");
        totalCreditLabel = new Label("0.00");
        totalCreditLabel.getStyleClass().add("totals-amount");
        creditBox.getChildren().addAll(creditTitleLabel, totalCreditLabel);
        
        // الرصيد
        VBox balanceBox = new VBox(5);
        balanceBox.setAlignment(Pos.CENTER);
        Label balanceTitleLabel = new Label("الرصيد");
        balanceTitleLabel.getStyleClass().add("totals-title");
        balanceLabel = new Label("0.00");
        balanceLabel.getStyleClass().add("totals-balance");
        balanceBox.getChildren().addAll(balanceTitleLabel, balanceLabel);
        
        totalsSection.getChildren().addAll(debitBox, creditBox, balanceBox);
        return totalsSection;
    }
    
    /**
     * إنشاء صندوق الأزرار
     */
    private HBox createButtonsBox() {
        HBox buttonsBox = new HBox(15);
        buttonsBox.setAlignment(Pos.CENTER);
        buttonsBox.setPadding(new Insets(15));
        
        Button saveBtn = createActionButton("حفظ", FontAwesomeIcon.SAVE, this::saveEntry);
        Button saveDraftBtn = createActionButton("حفظ كمسودة", FontAwesomeIcon.EDIT, this::saveDraft);
        Button cancelBtn = createActionButton("إلغاء", FontAwesomeIcon.TIMES, this::cancelEntry);
        
        buttonsBox.getChildren().addAll(saveBtn, saveDraftBtn, cancelBtn);
        return buttonsBox;
    }
    
    /**
     * إنشاء زر إجراء
     */
    private Button createActionButton(String text, FontAwesomeIcon icon, Runnable action) {
        Button button = new Button(text);
        
        FontAwesomeIconView iconView = new FontAwesomeIconView(icon);
        iconView.setSize("14px");
        button.setGraphic(iconView);
        
        button.getStyleClass().add("action-button");
        button.setOnAction(e -> action.run());
        
        return button;
    }
    
    // وظائف الإجراءات
    private void initializeNewEntry() {
        entryNumberField.setText(journalService.generateEntryNumber());
        entryDatePicker.setValue(LocalDate.now());
        descriptionField.clear();
        referenceField.clear();
        notesArea.clear();
        entryDetails.clear();
        updateTotals();
    }
    
    private void loadEntryData() {
        if (currentEntry != null) {
            entryNumberField.setText(currentEntry.getEntryNumber());
            entryDatePicker.setValue(currentEntry.getEntryDate());
            descriptionField.setText(currentEntry.getDescription());
            referenceField.setText(currentEntry.getReference());
            notesArea.setText(""); // ملاحظات إضافية

            entryDetails.clear();
            entryDetails.addAll(currentEntry.getEntryDetails());
            updateTotals();
        }
    }
    
    private void addNewDetail() {
        showDetailDialog(null);
    }
    
    private void editSelectedDetail() {
        JournalEntryDetail selected = detailsTable.getSelectionModel().getSelectedItem();
        if (selected != null) {
            showDetailDialog(selected);
        } else {
            showWarningAlert("تحديد سطر", "يرجى تحديد السطر المراد تعديله.");
        }
    }
    
    private void deleteSelectedDetail() {
        JournalEntryDetail selected = detailsTable.getSelectionModel().getSelectedItem();
        if (selected != null) {
            Alert confirmAlert = new Alert(Alert.AlertType.CONFIRMATION);
            confirmAlert.setTitle("تأكيد الحذف");
            confirmAlert.setHeaderText(null);
            confirmAlert.setContentText("هل أنت متأكد من حذف هذا السطر؟");
            
            Optional<ButtonType> result = confirmAlert.showAndWait();
            if (result.isPresent() && result.get() == ButtonType.OK) {
                entryDetails.remove(selected);
            }
        } else {
            showWarningAlert("تحديد سطر", "يرجى تحديد السطر المراد حذفه.");
        }
    }
    
    private void showDetailDialog(JournalEntryDetail detail) {
        showInfoAlert("نموذج تفاصيل القيد", "سيتم تطوير نموذج إضافة/تعديل تفاصيل القيد قريباً...");
    }
    
    private void updateTotals() {
        double totalDebit = entryDetails.stream().mapToDouble(JournalEntryDetail::getDebitAmount).sum();
        double totalCredit = entryDetails.stream().mapToDouble(JournalEntryDetail::getCreditAmount).sum();
        double balance = totalDebit - totalCredit;
        
        totalDebitLabel.setText(decimalFormat.format(totalDebit));
        totalCreditLabel.setText(decimalFormat.format(totalCredit));
        balanceLabel.setText(decimalFormat.format(Math.abs(balance)));
        
        // تغيير لون الرصيد حسب التوازن
        if (Math.abs(balance) < 0.01) {
            balanceLabel.getStyleClass().removeAll("error-text", "warning-text");
            balanceLabel.getStyleClass().add("success-text");
        } else {
            balanceLabel.getStyleClass().removeAll("success-text", "warning-text");
            balanceLabel.getStyleClass().add("error-text");
        }
    }
    
    private void saveEntry() {
        if (validateEntry()) {
            showInfoAlert("حفظ القيد", "تم حفظ القيد بنجاح!");
            closeDialog();
        }
    }
    
    private void saveDraft() {
        showInfoAlert("حفظ كمسودة", "تم حفظ القيد كمسودة!");
        closeDialog();
    }
    
    private void cancelEntry() {
        Alert confirmAlert = new Alert(Alert.AlertType.CONFIRMATION);
        confirmAlert.setTitle("تأكيد الإلغاء");
        confirmAlert.setHeaderText(null);
        confirmAlert.setContentText("هل أنت متأكد من إلغاء العملية؟ ستفقد جميع التغييرات غير المحفوظة.");
        
        Optional<ButtonType> result = confirmAlert.showAndWait();
        if (result.isPresent() && result.get() == ButtonType.OK) {
            closeDialog();
        }
    }
    
    private boolean validateEntry() {
        if (descriptionField.getText().trim().isEmpty()) {
            showWarningAlert("بيانات ناقصة", "يرجى إدخال وصف القيد.");
            return false;
        }
        
        if (entryDetails.isEmpty()) {
            showWarningAlert("بيانات ناقصة", "يرجى إضافة تفاصيل القيد.");
            return false;
        }
        
        double totalDebit = entryDetails.stream().mapToDouble(JournalEntryDetail::getDebitAmount).sum();
        double totalCredit = entryDetails.stream().mapToDouble(JournalEntryDetail::getCreditAmount).sum();
        
        if (Math.abs(totalDebit - totalCredit) > 0.01) {
            showWarningAlert("خطأ في التوازن", "إجمالي المدين يجب أن يساوي إجمالي الدائن.");
            return false;
        }
        
        return true;
    }
    
    private void closeDialog() {
        Stage stage = (Stage) entryNumberField.getScene().getWindow();
        stage.close();
    }
    
    private void showInfoAlert(String title, String message) {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle(title);
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }
    
    private void showWarningAlert(String title, String message) {
        Alert alert = new Alert(Alert.AlertType.WARNING);
        alert.setTitle(title);
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }
}
