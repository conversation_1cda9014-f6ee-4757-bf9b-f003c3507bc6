@echo off
setlocal

echo.
echo ========================================
echo Installing WiX Toolset and Creating MSI
echo ========================================
echo.

echo Step 1: Download and install WiX Toolset
echo.
echo Please follow these steps manually:
echo 1. Go to: https://wixtoolset.org/releases/
echo 2. Download WiX v3.11.2 (or latest v3.x)
echo 3. Install it to default location
echo 4. Add WiX to PATH environment variable
echo.
echo After installation, WiX tools should be available:
echo - candle.exe (compiler)
echo - light.exe (linker)
echo.

pause

echo.
echo Step 2: Verify WiX installation
echo.

where candle.exe >nul 2>&1
if %errorlevel%==0 (
    echo ✅ WiX candle.exe found
) else (
    echo ❌ WiX candle.exe not found in PATH
    echo Please install WiX and add it to PATH
    pause
    exit /b 1
)

where light.exe >nul 2>&1
if %errorlevel%==0 (
    echo ✅ WiX light.exe found
) else (
    echo ❌ WiX light.exe not found in PATH
    echo Please install WiX and add it to PATH
    pause
    exit /b 1
)

echo.
echo Step 3: Building MSI installer
echo.

:: Set Java environment
set JAVA_HOME=C:\Program Files\Eclipse Adoptium\jdk-********-hotspot
set PATH=%JAVA_HOME%\bin;%PATH%

:: Build the project first
call mvnw.cmd clean package
if not %errorlevel%==0 (
    echo ERROR: Failed to build project
    pause
    exit /b 1
)

:: Create MSI installer
echo Creating MSI installer with WiX support...

jpackage --type msi --input target --dest target\installer --name "AccountingSystem" --main-jar accounting-system-1.0.0.jar --main-class com.accounting.AccountingApplication --app-version 1.0.0 --vendor "Advanced Glass and Aluminum Company" --description "Integrated accounting system for glass and aluminum companies" --copyright "© 2025 Advanced Glass and Aluminum Company" --win-dir-chooser --win-menu --win-shortcut --win-upgrade-uuid ************************************ --java-options "-Dfile.encoding=UTF-8" --java-options "-Djava.awt.headless=false" --java-options "--add-opens javafx.graphics/javafx.scene=ALL-UNNAMED" --java-options "--add-opens javafx.controls/javafx.scene.control=ALL-UNNAMED" --java-options "--add-opens javafx.base/javafx.beans.property=ALL-UNNAMED"

if %errorlevel%==0 (
    echo.
    echo ✅ SUCCESS: MSI installer created!
    echo.
    echo Location: target\installer\AccountingSystem-1.0.0.msi
    echo.
    dir "target\installer\*.msi"
    echo.
) else (
    echo.
    echo ❌ ERROR: Failed to create MSI installer
    echo.
    echo Alternative: Use the portable version instead
    echo Location: AccountingSystem-Portable\
    echo.
)

pause
