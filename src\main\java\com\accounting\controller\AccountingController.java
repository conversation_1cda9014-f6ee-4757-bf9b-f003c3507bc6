package com.accounting.controller;

import com.accounting.model.*;
import com.accounting.service.*;
import javafx.geometry.Insets;
import javafx.geometry.Pos;
import javafx.scene.control.*;
import javafx.scene.layout.*;
import de.jensd.fx.glyphs.fontawesome.FontAwesomeIcon;
import de.jensd.fx.glyphs.fontawesome.FontAwesomeIconView;
import javafx.stage.Stage;
import javafx.stage.Modality;
import javafx.scene.Scene;
import javafx.collections.ObservableList;
import javafx.collections.FXCollections;
import java.time.LocalDate;
import java.text.DecimalFormat;

/**
 * كنترولر موديول النظام المحاسبي المتكامل
 */
public class AccountingController {
    
    // الخدمات المحاسبية
    private final AccountService accountService;
    private final JournalService journalService;
    private final LedgerService ledgerService;
    private final TrialBalanceService trialBalanceService;
    private final IncomeStatementService incomeStatementService;
    
    // تنسيق الأرقام
    private final DecimalFormat decimalFormat = new DecimalFormat("#,##0.00");
    
    public AccountingController() {
        this.accountService = AccountService.getInstance();
        this.journalService = JournalService.getInstance();
        this.ledgerService = LedgerService.getInstance();
        this.trialBalanceService = TrialBalanceService.getInstance();
        this.incomeStatementService = IncomeStatementService.getInstance();
    }
    
    /**
     * إنشاء واجهة موديول الحسابات الرئيسية
     */
    public VBox createAccountingModule() {
        VBox mainContainer = new VBox(20);
        mainContainer.setPadding(new Insets(20));
        
        // العنوان الرئيسي
        Label titleLabel = new Label("📊 النظام المحاسبي المتكامل");
        titleLabel.getStyleClass().add("module-title");
        
        // وصف الموديول
        Label descriptionLabel = new Label("نظام محاسبي شامل يدعم دليل الحسابات، القيود اليومية، دفتر الأستاذ، وميزان المراجعة");
        descriptionLabel.getStyleClass().add("module-description");
        descriptionLabel.setWrapText(true);
        
        // منطقة الأزرار الرئيسية
        GridPane buttonsGrid = createMainButtonsGrid();
        
        // منطقة الإحصائيات السريعة
        HBox statsBox = createQuickStatsBox();
        
        // منطقة الوصول السريع
        VBox quickAccessBox = createQuickAccessBox();
        
        mainContainer.getChildren().addAll(
            titleLabel, 
            descriptionLabel, 
            new Separator(),
            buttonsGrid,
            new Separator(),
            statsBox,
            new Separator(),
            quickAccessBox
        );
        
        return mainContainer;
    }
    
    /**
     * إنشاء شبكة الأزرار الرئيسية
     */
    private GridPane createMainButtonsGrid() {
        GridPane grid = new GridPane();
        grid.setHgap(20);
        grid.setVgap(20);
        grid.setAlignment(Pos.CENTER);
        
        // الصف الأول
        Button chartOfAccountsBtn = createModuleButton(
            "📋 دليل الحسابات", 
            "إدارة شجرة الحسابات والتفرع الهرمي",
            FontAwesomeIcon.SITEMAP,
            this::showChartOfAccounts
        );
        
        Button journalEntriesBtn = createModuleButton(
            "📝 القيود اليومية", 
            "إدخال وإدارة القيود المحاسبية",
            FontAwesomeIcon.EDIT,
            this::showJournalEntries
        );
        
        Button generalLedgerBtn = createModuleButton(
            "📖 دفتر الأستاذ العام", 
            "عرض حركة الحسابات وأرصدتها",
            FontAwesomeIcon.BOOK,
            this::showGeneralLedger
        );
        
        // الصف الثاني
        Button subLedgerBtn = createModuleButton(
            "📑 دفتر الأستاذ الفرعي", 
            "دفاتر العملاء والموردين التفصيلية",
            FontAwesomeIcon.BOOKMARK,
            this::showSubLedger
        );
        
        Button trialBalanceBtn = createModuleButton(
            "⚖️ ميزان المراجعة", 
            "ميزان المراجعة بالمجاميع والأرصدة",
            FontAwesomeIcon.BALANCE_SCALE,
            this::showTrialBalance
        );
        
        Button balanceSheetBtn = createModuleButton(
            "📊 الميزانية العمومية",
            "الميزانية العمومية والمركز المالي",
            FontAwesomeIcon.PIE_CHART,
            this::showBalanceSheet
        );

        // الصف الثالث
        Button incomeStatementBtn = createModuleButton(
            "💰 قائمة الدخل",
            "قائمة الدخل وتحليل الأداء المالي",
            FontAwesomeIcon.MONEY,
            this::showIncomeStatement
        );
        
        // ترتيب الأزرار في الشبكة
        grid.add(chartOfAccountsBtn, 0, 0);
        grid.add(journalEntriesBtn, 1, 0);
        grid.add(generalLedgerBtn, 2, 0);
        grid.add(subLedgerBtn, 0, 1);
        grid.add(trialBalanceBtn, 1, 1);
        grid.add(balanceSheetBtn, 2, 1);
        grid.add(incomeStatementBtn, 0, 2);
        
        return grid;
    }
    
    /**
     * إنشاء زر موديول
     */
    private Button createModuleButton(String title, String description, FontAwesomeIcon icon, Runnable action) {
        VBox buttonContent = new VBox(10);
        buttonContent.setAlignment(Pos.CENTER);
        buttonContent.setPrefSize(200, 120);
        
        FontAwesomeIconView iconView = new FontAwesomeIconView(icon);
        iconView.setSize("32px");
        iconView.getStyleClass().add("module-icon");
        
        Label titleLabel = new Label(title);
        titleLabel.getStyleClass().add("module-button-title");
        titleLabel.setWrapText(true);
        
        Label descLabel = new Label(description);
        descLabel.getStyleClass().add("module-button-description");
        descLabel.setWrapText(true);
        
        buttonContent.getChildren().addAll(iconView, titleLabel, descLabel);
        
        Button button = new Button();
        button.setGraphic(buttonContent);
        button.getStyleClass().add("module-button");
        button.setOnAction(e -> action.run());
        
        return button;
    }
    
    /**
     * إنشاء صندوق الإحصائيات السريعة
     */
    private HBox createQuickStatsBox() {
        HBox statsBox = new HBox(20);
        statsBox.setAlignment(Pos.CENTER);
        statsBox.setPadding(new Insets(20));
        statsBox.getStyleClass().add("stats-container");
        
        // إحصائيات الحسابات
        VBox accountsStats = createStatCard(
            "الحسابات", 
            String.valueOf(accountService.getAllAccounts().size()),
            FontAwesomeIcon.SITEMAP,
            "#3498db"
        );
        
        // إحصائيات القيود
        VBox entriesStats = createStatCard(
            "القيود", 
            String.valueOf(journalService.getAllJournalEntries().size()),
            FontAwesomeIcon.EDIT,
            "#2ecc71"
        );
        
        // إحصائيات دفتر الأستاذ
        VBox ledgerStats = createStatCard(
            "قيود الأستاذ", 
            String.valueOf(ledgerService.getAllLedgerEntries().size()),
            FontAwesomeIcon.BOOK,
            "#e74c3c"
        );
        
        // الرصيد النقدي
        double cashBalance = getCashBalance();
        VBox cashStats = createStatCard(
            "النقدية", 
            decimalFormat.format(cashBalance) + " ج.م",
            FontAwesomeIcon.MONEY,
            "#f39c12"
        );
        
        statsBox.getChildren().addAll(accountsStats, entriesStats, ledgerStats, cashStats);
        return statsBox;
    }
    
    /**
     * إنشاء بطاقة إحصائية
     */
    private VBox createStatCard(String title, String value, FontAwesomeIcon icon, String color) {
        VBox card = new VBox(10);
        card.setAlignment(Pos.CENTER);
        card.setPrefSize(150, 100);
        card.getStyleClass().add("stat-card");
        
        FontAwesomeIconView iconView = new FontAwesomeIconView(icon);
        iconView.setSize("24px");
        iconView.setStyle("-fx-fill: " + color + ";");
        
        Label valueLabel = new Label(value);
        valueLabel.getStyleClass().add("stat-value");
        valueLabel.setStyle("-fx-text-fill: " + color + ";");
        
        Label titleLabel = new Label(title);
        titleLabel.getStyleClass().add("stat-title");
        
        card.getChildren().addAll(iconView, valueLabel, titleLabel);
        return card;
    }
    
    /**
     * إنشاء صندوق الوصول السريع
     */
    private VBox createQuickAccessBox() {
        VBox quickAccessBox = new VBox(15);
        quickAccessBox.setPadding(new Insets(20));
        quickAccessBox.getStyleClass().add("quick-access-container");
        
        Label titleLabel = new Label("🚀 الوصول السريع");
        titleLabel.getStyleClass().add("section-title");
        
        HBox buttonsBox = new HBox(15);
        buttonsBox.setAlignment(Pos.CENTER);
        
        Button newEntryBtn = createQuickButton("قيد جديد", FontAwesomeIcon.PLUS, this::createNewJournalEntry);
        Button viewBalancesBtn = createQuickButton("الأرصدة", FontAwesomeIcon.EYE, this::viewAccountBalances);
        Button monthlyReportBtn = createQuickButton("تقرير شهري", FontAwesomeIcon.CALENDAR, this::generateMonthlyReport);
        Button backupBtn = createQuickButton("نسخ احتياطي", FontAwesomeIcon.DOWNLOAD, this::createBackup);
        
        buttonsBox.getChildren().addAll(newEntryBtn, viewBalancesBtn, monthlyReportBtn, backupBtn);
        
        quickAccessBox.getChildren().addAll(titleLabel, buttonsBox);
        return quickAccessBox;
    }
    
    /**
     * إنشاء زر وصول سريع
     */
    private Button createQuickButton(String text, FontAwesomeIcon icon, Runnable action) {
        Button button = new Button(text);
        FontAwesomeIconView iconView = new FontAwesomeIconView(icon);
        iconView.setSize("14px");
        button.setGraphic(iconView);
        button.getStyleClass().add("quick-access-button");
        button.setOnAction(e -> action.run());
        return button;
    }
    
    /**
     * الحصول على الرصيد النقدي
     */
    private double getCashBalance() {
        double cashBalance = 0.0;
        
        // رصيد الصندوق
        Account cashAccount = accountService.getAccountByCode("1111");
        if (cashAccount != null) {
            cashBalance += cashAccount.getCurrentBalance();
        }
        
        // رصيد البنك
        Account bankAccount = accountService.getAccountByCode("1112");
        if (bankAccount != null) {
            cashBalance += bankAccount.getCurrentBalance();
        }
        
        return cashBalance;
    }
    
    // وظائف الأزرار الرئيسية
    private void showChartOfAccounts() {
        ChartOfAccountsController controller = new ChartOfAccountsController();
        controller.showChartOfAccountsDialog();
    }

    private void showJournalEntries() {
        JournalEntriesController controller = new JournalEntriesController();
        controller.showJournalEntriesDialog();
    }

    private void showGeneralLedger() {
        showPlaceholder("دفتر الأستاذ العام");
    }

    private void showSubLedger() {
        showPlaceholder("دفتر الأستاذ الفرعي");
    }

    private void showTrialBalance() {
        showPlaceholder("ميزان المراجعة");
    }

    private void showBalanceSheet() {
        showPlaceholder("الميزانية العمومية");
    }

    private void showIncomeStatement() {
        IncomeStatementController controller = new IncomeStatementController();
        controller.showIncomeStatementDialog();
    }
    
    // وظائف الوصول السريع
    private void createNewJournalEntry() {
        JournalEntryFormController formController = new JournalEntryFormController();
        formController.showNewJournalEntryDialog();
    }
    
    private void viewAccountBalances() {
        showQuickBalancesDialog();
    }
    
    private void generateMonthlyReport() {
        showMonthlyReportDialog();
    }
    
    private void createBackup() {
        showBackupDialog();
    }
    
    /**
     * عرض نافذة الأرصدة السريعة
     */
    private void showQuickBalancesDialog() {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle("الأرصدة السريعة");
        alert.setHeaderText("أرصدة الحسابات الرئيسية");
        
        StringBuilder balances = new StringBuilder();
        balances.append("الصندوق: ").append(decimalFormat.format(getAccountBalance("1111"))).append(" ج.م\n");
        balances.append("البنك: ").append(decimalFormat.format(getAccountBalance("1112"))).append(" ج.م\n");
        balances.append("العملاء: ").append(decimalFormat.format(getAccountBalance("112"))).append(" ج.م\n");
        balances.append("الموردون: ").append(decimalFormat.format(getAccountBalance("211"))).append(" ج.م\n");
        balances.append("المخزون: ").append(decimalFormat.format(getAccountBalance("113"))).append(" ج.م");
        
        alert.setContentText(balances.toString());
        alert.showAndWait();
    }
    
    /**
     * الحصول على رصيد حساب
     */
    private double getAccountBalance(String accountCode) {
        Account account = accountService.getAccountByCode(accountCode);
        return account != null ? account.getCurrentBalance() : 0.0;
    }
    
    /**
     * عرض نافذة التقرير الشهري
     */
    private void showMonthlyReportDialog() {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle("التقرير الشهري");
        alert.setHeaderText("تقرير الشهر الحالي");
        alert.setContentText("سيتم إنشاء التقرير الشهري قريباً...");
        alert.showAndWait();
    }
    
    /**
     * عرض نافذة النسخ الاحتياطي
     */
    private void showBackupDialog() {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle("النسخ الاحتياطي");
        alert.setHeaderText("إنشاء نسخة احتياطية");
        alert.setContentText("تم إنشاء نسخة احتياطية من البيانات المحاسبية بنجاح!");
        alert.showAndWait();
    }

    /**
     * عرض شاشة مؤقتة للوظائف قيد التطوير
     */
    private void showPlaceholder(String featureName) {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle(featureName);
        alert.setHeaderText("قيد التطوير");
        alert.setContentText("هذه الوظيفة (" + featureName + ") قيد التطوير وستكون متاحة قريباً.");
        alert.showAndWait();
    }
}
