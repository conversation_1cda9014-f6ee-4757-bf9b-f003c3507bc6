package com.accounting.model;

import java.time.LocalDate;

/**
 * نموذج السلفة
 * Advance Model
 */
public class Advance {
    private String advanceId;
    private String employeeId;
    private String employeeName;
    private double amount;
    private LocalDate date;
    private String managerName;
    private String notes;
    private boolean isActive;
    
    public Advance() {
        this.isActive = true;
        this.date = LocalDate.now();
    }
    
    public Advance(String advanceId, String employeeId, String employeeName, 
                  double amount, LocalDate date, String managerName, String notes) {
        this.advanceId = advanceId;
        this.employeeId = employeeId;
        this.employeeName = employeeName;
        this.amount = amount;
        this.date = date;
        this.managerName = managerName;
        this.notes = notes;
        this.isActive = true;
    }
    
    // Getters and Setters
    public String getAdvanceId() { return advanceId; }
    public void setAdvanceId(String advanceId) { this.advanceId = advanceId; }
    
    public String getEmployeeId() { return employeeId; }
    public void setEmployeeId(String employeeId) { this.employeeId = employeeId; }
    
    public String getEmployeeName() { return employeeName; }
    public void setEmployeeName(String employeeName) { this.employeeName = employeeName; }
    
    public double getAmount() { return amount; }
    public void setAmount(double amount) { this.amount = amount; }
    
    public LocalDate getDate() { return date; }
    public void setDate(LocalDate date) { this.date = date; }
    
    public String getManagerName() { return managerName; }
    public void setManagerName(String managerName) { this.managerName = managerName; }
    
    public String getNotes() { return notes; }
    public void setNotes(String notes) { this.notes = notes; }
    
    public boolean isActive() { return isActive; }
    public void setActive(boolean active) { isActive = active; }
    
    @Override
    public String toString() {
        return "سلفة " + amount + " ج.م - " + date;
    }
}
