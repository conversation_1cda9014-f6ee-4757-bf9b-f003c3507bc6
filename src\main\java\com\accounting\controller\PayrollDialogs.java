package com.accounting.controller;

import com.accounting.model.*;
import javafx.collections.ObservableList;
import javafx.geometry.Insets;
import javafx.geometry.Pos;
import javafx.scene.Scene;
import javafx.scene.control.*;
import javafx.scene.control.cell.PropertyValueFactory;
import javafx.scene.control.cell.TextFieldTableCell;
import javafx.scene.layout.*;
import javafx.stage.Modality;
import javafx.stage.Stage;
import de.jensd.fx.glyphs.fontawesome.FontAwesomeIcon;
import de.jensd.fx.glyphs.fontawesome.FontAwesomeIconView;

import java.text.DecimalFormat;
import java.time.LocalDate;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;

/**
 * نوافذ الحوار لموديول الرواتب
 * Payroll Dialogs
 */
public class PayrollDialogs {
    
    private DecimalFormat decimalFormat = new DecimalFormat("#,##0.00");
    private DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("dd/MM/yyyy");
    
    /**
     * نافذة تسجيل سداد
     */
    public void showPaymentDialog(Employee selectedEmployee, ObservableList<Employee> employees, 
                                 ObservableList<Manager> managers, ObservableList<SalaryPaid> salaryPaids) {
        Stage dialog = new Stage();
        dialog.setTitle("تسجيل سداد راتب");
        dialog.initModality(Modality.APPLICATION_MODAL);
        dialog.setWidth(500);
        dialog.setHeight(500);
        
        VBox mainLayout = new VBox(20);
        mainLayout.setPadding(new Insets(20));
        
        // العنوان
        Label titleLabel = new Label("تسجيل سداد راتب");
        titleLabel.getStyleClass().add("dialog-title");
        
        // نموذج البيانات
        GridPane form = new GridPane();
        form.setHgap(15);
        form.setVgap(15);
        
        // اختيار الموظف
        Label employeeLabel = new Label("الموظف:");
        ComboBox<Employee> employeeCombo = new ComboBox<>();
        employeeCombo.setItems(employees);
        employeeCombo.setPrefWidth(200);
        employeeCombo.setPromptText("اختر الموظف");
        if (selectedEmployee != null) {
            employeeCombo.setValue(selectedEmployee);
        }
        
        // الفترة المسددة
        Label periodLabel = new Label("الفترة المسددة:");
        ComboBox<YearMonth> periodCombo = new ComboBox<>();
        // إضافة الأشهر الحالية والسابقة
        for (int i = 0; i < 12; i++) {
            periodCombo.getItems().add(YearMonth.now().minusMonths(i));
        }
        periodCombo.setValue(YearMonth.now().minusMonths(1));
        periodCombo.setPrefWidth(150);
        
        // مبلغ السداد
        Label amountLabel = new Label("مبلغ السداد:");
        TextField amountField = new TextField();
        amountField.setPromptText("0.00");
        
        // تاريخ السداد
        Label dateLabel = new Label("تاريخ السداد:");
        DatePicker datePicker = new DatePicker();
        datePicker.setValue(LocalDate.now());
        
        // المدير
        Label managerLabel = new Label("المدير:");
        ComboBox<Manager> managerCombo = new ComboBox<>();
        managerCombo.setItems(managers);
        managerCombo.setPrefWidth(200);
        managerCombo.setPromptText("اختر المدير");
        
        // مصدر السداد
        Label sourceLabel = new Label("مصدر السداد:");
        ComboBox<String> sourceCombo = new ComboBox<>();
        sourceCombo.getItems().addAll("خزنة", "بنك", "شيك", "تحويل بنكي", "أخرى");
        sourceCombo.setValue("خزنة");
        sourceCombo.setPrefWidth(150);
        
        // الملاحظات
        Label notesLabel = new Label("الملاحظات:");
        TextArea notesArea = new TextArea();
        notesArea.setPromptText("ملاحظات اختيارية");
        notesArea.setPrefRowCount(3);
        
        // ترتيب الحقول
        form.add(employeeLabel, 0, 0);
        form.add(employeeCombo, 1, 0);
        form.add(periodLabel, 0, 1);
        form.add(periodCombo, 1, 1);
        form.add(amountLabel, 0, 2);
        form.add(amountField, 1, 2);
        form.add(dateLabel, 0, 3);
        form.add(datePicker, 1, 3);
        form.add(managerLabel, 0, 4);
        form.add(managerCombo, 1, 4);
        form.add(sourceLabel, 0, 5);
        form.add(sourceCombo, 1, 5);
        form.add(notesLabel, 0, 6);
        form.add(notesArea, 1, 6);
        
        // أزرار الحوار
        HBox buttonBox = new HBox(10);
        buttonBox.setAlignment(Pos.CENTER_RIGHT);
        
        Button saveButton = new Button("حفظ السداد");
        saveButton.getStyleClass().add("add-button");
        saveButton.setOnAction(e -> {
            if (validatePaymentForm(employeeCombo, amountField, managerCombo)) {
                try {
                    String paymentId = "PAY" + String.format("%03d", salaryPaids.size() + 1);
                    Employee employee = employeeCombo.getValue();
                    Manager manager = managerCombo.getValue();
                    
                    SalaryPaid newPayment = new SalaryPaid(
                        paymentId,
                        employee.getEmployeeId(),
                        employee.getName(),
                        periodCombo.getValue(),
                        Double.parseDouble(amountField.getText().trim()),
                        datePicker.getValue(),
                        manager.getName(),
                        sourceCombo.getValue(),
                        notesArea.getText().trim()
                    );
                    
                    salaryPaids.add(newPayment);
                    showPlaceholderDialog("نجح", "تم تسجيل السداد بنجاح");
                    dialog.close();
                } catch (NumberFormatException ex) {
                    showPlaceholderDialog("خطأ", "يرجى إدخال مبلغ صحيح");
                }
            }
        });
        
        Button cancelButton = new Button("إلغاء");
        cancelButton.setOnAction(e -> dialog.close());
        
        buttonBox.getChildren().addAll(saveButton, cancelButton);
        
        mainLayout.getChildren().addAll(titleLabel, form, buttonBox);
        
        Scene scene = new Scene(mainLayout);
        scene.getStylesheets().add(getClass().getResource("/css/styles.css").toExternalForm());
        dialog.setScene(scene);
        dialog.showAndWait();
    }
    
    /**
     * التحقق من صحة نموذج السداد
     */
    private boolean validatePaymentForm(ComboBox<Employee> employeeCombo, TextField amountField, ComboBox<Manager> managerCombo) {
        if (employeeCombo.getValue() == null) {
            showPlaceholderDialog("تحذير", "يرجى اختيار الموظف");
            return false;
        }
        
        if (amountField.getText().trim().isEmpty()) {
            showPlaceholderDialog("تحذير", "يرجى إدخال مبلغ السداد");
            return false;
        }
        
        try {
            double amount = Double.parseDouble(amountField.getText().trim());
            if (amount <= 0) {
                showPlaceholderDialog("تحذير", "يرجى إدخال مبلغ أكبر من صفر");
                return false;
            }
        } catch (NumberFormatException e) {
            showPlaceholderDialog("تحذير", "يرجى إدخال مبلغ صحيح");
            return false;
        }
        
        if (managerCombo.getValue() == null) {
            showPlaceholderDialog("تحذير", "يرجى اختيار المدير");
            return false;
        }
        
        return true;
    }
    
    /**
     * نافذة إدارة المدراء
     */
    public void showManagersDialog(ObservableList<Manager> managers) {
        Stage dialog = new Stage();
        dialog.setTitle("إدارة المدراء");
        dialog.initModality(Modality.APPLICATION_MODAL);
        dialog.setWidth(700);
        dialog.setHeight(500);
        
        VBox mainLayout = new VBox(15);
        mainLayout.setPadding(new Insets(20));
        
        // العنوان
        Label titleLabel = new Label("إدارة المدراء");
        titleLabel.getStyleClass().add("dialog-title");
        
        // جدول المدراء
        TableView<Manager> managersTable = new TableView<>();
        managersTable.setItems(managers);
        managersTable.setPrefHeight(250);
        managersTable.setEditable(true);
        
        // أعمدة الجدول
        TableColumn<Manager, String> nameCol = new TableColumn<>("الاسم");
        nameCol.setPrefWidth(150);
        nameCol.setCellValueFactory(new PropertyValueFactory<>("name"));
        nameCol.setCellFactory(TextFieldTableCell.forTableColumn());
        nameCol.setOnEditCommit(event -> {
            event.getRowValue().setName(event.getNewValue());
        });
        
        TableColumn<Manager, String> positionCol = new TableColumn<>("المنصب");
        positionCol.setPrefWidth(120);
        positionCol.setCellValueFactory(new PropertyValueFactory<>("position"));
        positionCol.setCellFactory(TextFieldTableCell.forTableColumn());
        positionCol.setOnEditCommit(event -> {
            event.getRowValue().setPosition(event.getNewValue());
        });
        
        TableColumn<Manager, String> departmentCol = new TableColumn<>("القسم");
        departmentCol.setPrefWidth(120);
        departmentCol.setCellValueFactory(new PropertyValueFactory<>("department"));
        departmentCol.setCellFactory(TextFieldTableCell.forTableColumn());
        departmentCol.setOnEditCommit(event -> {
            event.getRowValue().setDepartment(event.getNewValue());
        });
        
        TableColumn<Manager, Void> actionsCol = new TableColumn<>("إجراءات");
        actionsCol.setPrefWidth(80);
        actionsCol.setCellFactory(col -> new TableCell<Manager, Void>() {
            private final Button deleteBtn = new Button();
            
            {
                FontAwesomeIconView deleteIcon = new FontAwesomeIconView(FontAwesomeIcon.TRASH);
                deleteIcon.setSize("12px");
                deleteBtn.setGraphic(deleteIcon);
                deleteBtn.getStyleClass().add("delete-button");
                deleteBtn.setOnAction(e -> {
                    Manager manager = getTableView().getItems().get(getIndex());
                    getTableView().getItems().remove(manager);
                });
            }
            
            @Override
            protected void updateItem(Void item, boolean empty) {
                super.updateItem(item, empty);
                if (empty) {
                    setGraphic(null);
                } else {
                    setGraphic(deleteBtn);
                }
            }
        });
        
        managersTable.getColumns().addAll(nameCol, positionCol, departmentCol, actionsCol);
        
        // نموذج إضافة مدير جديد
        GridPane addManagerForm = new GridPane();
        addManagerForm.setHgap(10);
        addManagerForm.setVgap(10);
        addManagerForm.getStyleClass().add("add-service-form");
        
        Label nameLabel = new Label("الاسم:");
        TextField nameField = new TextField();
        nameField.setPromptText("أدخل اسم المدير");
        
        Label positionLabel = new Label("المنصب:");
        TextField positionField = new TextField();
        positionField.setPromptText("أدخل المنصب");
        
        Label departmentLabel = new Label("القسم:");
        TextField departmentField = new TextField();
        departmentField.setPromptText("أدخل القسم");
        
        Button addButton = new Button("إضافة مدير");
        FontAwesomeIconView addIcon = new FontAwesomeIconView(FontAwesomeIcon.USER_PLUS);
        addIcon.setSize("14px");
        addButton.setGraphic(addIcon);
        addButton.getStyleClass().add("add-button");
        addButton.setOnAction(e -> {
            String name = nameField.getText().trim();
            String position = positionField.getText().trim();
            String department = departmentField.getText().trim();
            
            if (!name.isEmpty()) {
                String id = "MGR" + String.format("%03d", managers.size() + 1);
                Manager newManager = new Manager(id, name, position, department);
                managers.add(newManager);
                
                // مسح الحقول
                nameField.clear();
                positionField.clear();
                departmentField.clear();
            } else {
                showPlaceholderDialog("تحذير", "يرجى إدخال اسم المدير على الأقل");
            }
        });
        
        addManagerForm.add(nameLabel, 0, 0);
        addManagerForm.add(nameField, 1, 0);
        addManagerForm.add(positionLabel, 2, 0);
        addManagerForm.add(positionField, 3, 0);
        addManagerForm.add(departmentLabel, 0, 1);
        addManagerForm.add(departmentField, 1, 1);
        addManagerForm.add(addButton, 2, 1);
        
        // أزرار الحوار
        HBox buttonBox = new HBox(10);
        buttonBox.setAlignment(Pos.CENTER_RIGHT);
        
        Button closeButton = new Button("إغلاق");
        closeButton.setOnAction(e -> dialog.close());
        
        buttonBox.getChildren().add(closeButton);
        
        mainLayout.getChildren().addAll(titleLabel, managersTable, 
                                       new Separator(), addManagerForm, buttonBox);
        
        Scene scene = new Scene(mainLayout);
        scene.getStylesheets().add(getClass().getResource("/css/styles.css").toExternalForm());
        dialog.setScene(scene);
        dialog.showAndWait();
    }
    
    private void showPlaceholderDialog(String title, String message) {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle(title);
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }
}
