package com.accounting.service;

import com.accounting.model.DeliveryOrder;
import com.accounting.model.DeliveryItem;
import com.accounting.model.ManufacturingOrder;
import com.accounting.model.RequiredMeasurement;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * خدمة إدارة أوامر التسليم
 */
public class DeliveryOrderService {
    
    private static DeliveryOrderService instance;
    private final ObservableList<DeliveryOrder> deliveryOrders = FXCollections.observableArrayList();
    private int nextOrderNumber = 1;
    
    private DeliveryOrderService() {
        // منشئ خاص للـ Singleton
    }
    
    /**
     * الحصول على المثيل الوحيد
     */
    public static DeliveryOrderService getInstance() {
        if (instance == null) {
            instance = new DeliveryOrderService();
        }
        return instance;
    }
    
    /**
     * إضافة أمر تسليم جديد
     */
    public boolean addDeliveryOrder(DeliveryOrder deliveryOrder) {
        if (deliveryOrder == null || !deliveryOrder.isValid()) {
            return false;
        }
        
        // تعيين رقم أمر تلقائي إذا لم يكن موجود
        if (deliveryOrder.getDeliveryOrderNumber() == null || deliveryOrder.getDeliveryOrderNumber().isEmpty()) {
            deliveryOrder.setDeliveryOrderNumber(generateDeliveryOrderNumber());
        }
        
        deliveryOrders.add(deliveryOrder);
        return true;
    }
    
    /**
     * تحديث أمر تسليم
     */
    public boolean updateDeliveryOrder(DeliveryOrder deliveryOrder) {
        if (deliveryOrder == null || !deliveryOrder.isValid()) {
            return false;
        }
        
        int index = findDeliveryOrderIndex(deliveryOrder.getDeliveryOrderId());
        if (index >= 0) {
            deliveryOrders.set(index, deliveryOrder);
            return true;
        }
        
        return false;
    }
    
    /**
     * حذف أمر تسليم
     */
    public boolean deleteDeliveryOrder(String deliveryOrderId) {
        return deliveryOrders.removeIf(order -> order.getDeliveryOrderId().equals(deliveryOrderId));
    }
    
    /**
     * البحث عن أمر تسليم بالمعرف
     */
    public DeliveryOrder findDeliveryOrderById(String deliveryOrderId) {
        return deliveryOrders.stream()
                .filter(order -> order.getDeliveryOrderId().equals(deliveryOrderId))
                .findFirst()
                .orElse(null);
    }
    
    /**
     * البحث عن أمر تسليم برقم الأمر
     */
    public DeliveryOrder findDeliveryOrderByNumber(String deliveryOrderNumber) {
        return deliveryOrders.stream()
                .filter(order -> order.getDeliveryOrderNumber().equals(deliveryOrderNumber))
                .findFirst()
                .orElse(null);
    }
    
    /**
     * الحصول على جميع أوامر التسليم
     */
    public ObservableList<DeliveryOrder> getAllDeliveryOrders() {
        return FXCollections.unmodifiableObservableList(deliveryOrders);
    }
    
    /**
     * البحث في أوامر التسليم
     */
    public List<DeliveryOrder> searchDeliveryOrders(String searchText) {
        if (searchText == null || searchText.trim().isEmpty()) {
            return new ArrayList<>(deliveryOrders);
        }
        
        String searchLower = searchText.toLowerCase();
        return deliveryOrders.stream()
                .filter(order -> 
                    order.getDeliveryOrderNumber().toLowerCase().contains(searchLower) ||
                    order.getCustomerName().toLowerCase().contains(searchLower) ||
                    (order.getManufacturingOrderNumber() != null && 
                     order.getManufacturingOrderNumber().toLowerCase().contains(searchLower)) ||
                    (order.getInvoiceNumber() != null && 
                     order.getInvoiceNumber().toLowerCase().contains(searchLower))
                )
                .collect(Collectors.toList());
    }
    
    /**
     * تصفية أوامر التسليم حسب التاريخ
     */
    public List<DeliveryOrder> filterDeliveryOrdersByDate(LocalDate fromDate, LocalDate toDate) {
        return deliveryOrders.stream()
                .filter(order -> {
                    LocalDate orderDate = order.getDeliveryDate();
                    return (fromDate == null || !orderDate.isBefore(fromDate)) &&
                           (toDate == null || !orderDate.isAfter(toDate));
                })
                .collect(Collectors.toList());
    }
    
    /**
     * تصفية أوامر التسليم حسب الحالة
     */
    public List<DeliveryOrder> filterDeliveryOrdersByStatus(DeliveryOrder.DeliveryStatus status) {
        if (status == null) {
            return new ArrayList<>(deliveryOrders);
        }
        
        return deliveryOrders.stream()
                .filter(order -> order.getStatus() == status)
                .collect(Collectors.toList());
    }
    
    /**
     * تصفية أوامر التسليم حسب العميل
     */
    public List<DeliveryOrder> filterDeliveryOrdersByCustomer(String customerName) {
        if (customerName == null || customerName.trim().isEmpty()) {
            return new ArrayList<>(deliveryOrders);
        }
        
        return deliveryOrders.stream()
                .filter(order -> order.getCustomerName().equalsIgnoreCase(customerName.trim()))
                .collect(Collectors.toList());
    }
    
    /**
     * إنشاء أمر تسليم من أمر تصنيع
     */
    public DeliveryOrder createDeliveryOrderFromManufacturing(ManufacturingOrder manufacturingOrder) {
        if (manufacturingOrder == null) {
            return null;
        }
        
        DeliveryOrder deliveryOrder = new DeliveryOrder();
        deliveryOrder.copyFromManufacturingOrder(manufacturingOrder);
        
        return deliveryOrder;
    }
    
    /**
     * تأكيد أمر التسليم
     */
    public boolean confirmDeliveryOrder(String deliveryOrderId) {
        DeliveryOrder order = findDeliveryOrderById(deliveryOrderId);
        if (order != null && order.getStatus() == DeliveryOrder.DeliveryStatus.DRAFT) {
            order.setStatus(DeliveryOrder.DeliveryStatus.CONFIRMED);
            return true;
        }
        return false;
    }
    
    /**
     * تسليم أمر التسليم
     */
    public boolean deliverOrder(String deliveryOrderId) {
        DeliveryOrder order = findDeliveryOrderById(deliveryOrderId);
        if (order != null && order.getStatus() == DeliveryOrder.DeliveryStatus.CONFIRMED) {
            order.setStatus(DeliveryOrder.DeliveryStatus.DELIVERED);
            
            // تحديث حالة جميع العناصر إلى مسلم
            for (DeliveryItem item : order.getDeliveryItems()) {
                item.setDelivered(true);
            }
            
            return true;
        }
        return false;
    }
    
    /**
     * إلغاء أمر التسليم
     */
    public boolean cancelDeliveryOrder(String deliveryOrderId) {
        DeliveryOrder order = findDeliveryOrderById(deliveryOrderId);
        if (order != null && order.getStatus() != DeliveryOrder.DeliveryStatus.DELIVERED) {
            order.setStatus(DeliveryOrder.DeliveryStatus.CANCELLED);
            return true;
        }
        return false;
    }
    
    /**
     * تسليم عنصر محدد
     */
    public boolean deliverItem(String deliveryOrderId, String itemId) {
        DeliveryOrder order = findDeliveryOrderById(deliveryOrderId);
        if (order != null) {
            DeliveryItem item = order.getDeliveryItems().stream()
                    .filter(i -> i.getItemId().equals(itemId))
                    .findFirst()
                    .orElse(null);
            
            if (item != null) {
                item.setDelivered(true);
                
                // التحقق من تسليم جميع العناصر
                boolean allDelivered = order.getDeliveryItems().stream()
                        .allMatch(DeliveryItem::isDelivered);
                
                if (allDelivered && order.getStatus() == DeliveryOrder.DeliveryStatus.CONFIRMED) {
                    order.setStatus(DeliveryOrder.DeliveryStatus.DELIVERED);
                }
                
                return true;
            }
        }
        return false;
    }
    
    /**
     * إلغاء تسليم عنصر محدد
     */
    public boolean undeliverItem(String deliveryOrderId, String itemId) {
        DeliveryOrder order = findDeliveryOrderById(deliveryOrderId);
        if (order != null) {
            DeliveryItem item = order.getDeliveryItems().stream()
                    .filter(i -> i.getItemId().equals(itemId))
                    .findFirst()
                    .orElse(null);
            
            if (item != null) {
                item.setDelivered(false);
                
                // إذا كان الأمر مسلم، تغيير الحالة إلى مؤكد
                if (order.getStatus() == DeliveryOrder.DeliveryStatus.DELIVERED) {
                    order.setStatus(DeliveryOrder.DeliveryStatus.CONFIRMED);
                }
                
                return true;
            }
        }
        return false;
    }
    
    /**
     * الحصول على إحصائيات أوامر التسليم
     */
    public Map<String, Integer> getDeliveryOrderStatistics() {
        Map<String, Integer> stats = new HashMap<>();
        
        stats.put("إجمالي الأوامر", deliveryOrders.size());
        stats.put("أوامر مسودة", (int) deliveryOrders.stream()
                .filter(order -> order.getStatus() == DeliveryOrder.DeliveryStatus.DRAFT)
                .count());
        stats.put("أوامر مؤكدة", (int) deliveryOrders.stream()
                .filter(order -> order.getStatus() == DeliveryOrder.DeliveryStatus.CONFIRMED)
                .count());
        stats.put("أوامر مسلمة", (int) deliveryOrders.stream()
                .filter(order -> order.getStatus() == DeliveryOrder.DeliveryStatus.DELIVERED)
                .count());
        stats.put("أوامر ملغية", (int) deliveryOrders.stream()
                .filter(order -> order.getStatus() == DeliveryOrder.DeliveryStatus.CANCELLED)
                .count());
        
        // إحصائيات الشهر الحالي
        LocalDate startOfMonth = LocalDate.now().withDayOfMonth(1);
        LocalDate endOfMonth = LocalDate.now().withDayOfMonth(LocalDate.now().lengthOfMonth());
        
        stats.put("أوامر الشهر الحالي", (int) deliveryOrders.stream()
                .filter(order -> {
                    LocalDate orderDate = order.getDeliveryDate();
                    return !orderDate.isBefore(startOfMonth) && !orderDate.isAfter(endOfMonth);
                })
                .count());
        
        return stats;
    }
    
    /**
     * تصدير أوامر التسليم إلى نص
     */
    public String exportDeliveryOrdersToText(LocalDate fromDate, LocalDate toDate) {
        List<DeliveryOrder> filteredOrders = filterDeliveryOrdersByDate(fromDate, toDate);
        
        StringBuilder sb = new StringBuilder();
        sb.append("تقرير أوامر التسليم\n");
        sb.append("من تاريخ: ").append(fromDate).append(" إلى تاريخ: ").append(toDate).append("\n");
        sb.append("تاريخ التقرير: ").append(LocalDate.now()).append("\n\n");
        
        for (DeliveryOrder order : filteredOrders) {
            sb.append("رقم أمر التسليم: ").append(order.getDeliveryOrderNumber()).append("\n");
            sb.append("العميل: ").append(order.getCustomerName()).append("\n");
            sb.append("رقم أمر العمل: ").append(order.getManufacturingOrderNumber()).append("\n");
            sb.append("رقم الفاتورة: ").append(order.getInvoiceNumber()).append("\n");
            sb.append("تاريخ التسليم: ").append(order.getDeliveryDate()).append("\n");
            sb.append("الحالة: ").append(order.getStatus().getDisplayName()).append("\n");
            sb.append("عدد العناصر: ").append(order.getDeliveryItems().size()).append("\n");
            sb.append("إجمالي العدد: ").append(order.getTotalCount()).append("\n");
            sb.append("---\n");
        }
        
        return sb.toString();
    }
    
    /**
     * توليد رقم أمر تسليم جديد
     */
    private String generateDeliveryOrderNumber() {
        return "DEL" + String.format("%06d", nextOrderNumber++);
    }
    
    /**
     * البحث عن فهرس أمر التسليم
     */
    private int findDeliveryOrderIndex(String deliveryOrderId) {
        for (int i = 0; i < deliveryOrders.size(); i++) {
            if (deliveryOrders.get(i).getDeliveryOrderId().equals(deliveryOrderId)) {
                return i;
            }
        }
        return -1;
    }
    
    /**
     * مسح جميع البيانات (للاختبار)
     */
    public void clearAllData() {
        deliveryOrders.clear();
        nextOrderNumber = 1;
    }

    /**
     * إنشاء أمر تسليم من أمر تصنيع
     */
    public DeliveryOrder createFromManufacturingOrder(ManufacturingOrder manufacturingOrder) {
        if (manufacturingOrder == null) return null;

        DeliveryOrder deliveryOrder = new DeliveryOrder();
        deliveryOrder.setDeliveryOrderNumber(generateDeliveryOrderNumber());
        deliveryOrder.setCustomerName(manufacturingOrder.getCustomerName());
        deliveryOrder.setManufacturingOrderNumber(manufacturingOrder.getOrderNumber());
        deliveryOrder.setDeliveryDate(LocalDate.now().plusDays(1)); // تسليم غداً افتراضياً
        deliveryOrder.setStatus(DeliveryOrder.DeliveryStatus.DRAFT);
        deliveryOrder.setNotes("أمر تسليم من أمر التصنيع: " + manufacturingOrder.getOrderNumber());

        // إضافة عناصر افتراضية بناءً على أمر التصنيع
        DeliveryItem item = new DeliveryItem();
        item.setDescription("منتجات أمر التصنيع " + manufacturingOrder.getOrderNumber());
        item.setCount(1);
        item.setNotes("تسليم منتجات أمر التصنيع");
        deliveryOrder.addDeliveryItem(item);

        addDeliveryOrder(deliveryOrder);
        return deliveryOrder;
    }
}
