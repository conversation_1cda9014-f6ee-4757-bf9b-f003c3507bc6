@echo off
setlocal

echo.
echo ========================================
echo Updating Hossam for JDK 24
echo ========================================
echo.

:: Update all Hossam installations to use JDK 24
set JDK24_PATH=C:\Program Files\Java\jdk-24

echo Updating Hossam installations to use JDK 24...
echo JDK 24 Path: %JDK24_PATH%
echo.

:: Update main installation
if exist "%USERPROFILE%\Hossam" (
    echo Updating main installation...
    echo @echo off > "%USERPROFILE%\Hossam\Hossam.bat"
    echo setlocal >> "%USERPROFILE%\Hossam\Hossam.bat"
    echo set JAVA_HOME=%JDK24_PATH% >> "%USERPROFILE%\Hossam\Hossam.bat"
    echo set PATH=%%JAVA_HOME%%\bin;%%PATH%% >> "%USERPROFILE%\Hossam\Hossam.bat"
    echo cd /d "%%~dp0" >> "%USERPROFILE%\Hossam\Hossam.bat"
    echo title Hossam Accounting System >> "%USERPROFILE%\Hossam\Hossam.bat"
    echo echo ======================================== >> "%USERPROFILE%\Hossam\Hossam.bat"
    echo echo       Hossam Accounting System >> "%USERPROFILE%\Hossam\Hossam.bat"
    echo echo       Using JDK 24 with JavaFX >> "%USERPROFILE%\Hossam\Hossam.bat"
    echo echo ======================================== >> "%USERPROFILE%\Hossam\Hossam.bat"
    echo echo Starting application... >> "%USERPROFILE%\Hossam\Hossam.bat"
    echo echo. >> "%USERPROFILE%\Hossam\Hossam.bat"
    echo java -Dfile.encoding=UTF-8 -jar accounting-system-1.0.0.jar >> "%USERPROFILE%\Hossam\Hossam.bat"
    echo if %%errorlevel%% neq 0 ^( >> "%USERPROFILE%\Hossam\Hossam.bat"
    echo     echo Application failed to start! >> "%USERPROFILE%\Hossam\Hossam.bat"
    echo     pause >> "%USERPROFILE%\Hossam\Hossam.bat"
    echo ^) >> "%USERPROFILE%\Hossam\Hossam.bat"
    echo ✅ Updated main installation
)

:: Update WithJavaFX version
if exist "Hossam-WithJavaFX" (
    echo Updating WithJavaFX version...
    echo @echo off > "Hossam-WithJavaFX\Hossam.bat"
    echo setlocal >> "Hossam-WithJavaFX\Hossam.bat"
    echo set JAVA_HOME=%JDK24_PATH% >> "Hossam-WithJavaFX\Hossam.bat"
    echo set PATH=%%JAVA_HOME%%\bin;%%PATH%% >> "Hossam-WithJavaFX\Hossam.bat"
    echo cd /d "%%~dp0" >> "Hossam-WithJavaFX\Hossam.bat"
    echo title Hossam Accounting System >> "Hossam-WithJavaFX\Hossam.bat"
    echo echo ======================================== >> "Hossam-WithJavaFX\Hossam.bat"
    echo echo       Hossam Accounting System >> "Hossam-WithJavaFX\Hossam.bat"
    echo echo       Using JDK 24 with JavaFX >> "Hossam-WithJavaFX\Hossam.bat"
    echo echo ======================================== >> "Hossam-WithJavaFX\Hossam.bat"
    echo echo Starting application... >> "Hossam-WithJavaFX\Hossam.bat"
    echo echo. >> "Hossam-WithJavaFX\Hossam.bat"
    echo java -Dfile.encoding=UTF-8 -jar accounting-system-1.0.0.jar >> "Hossam-WithJavaFX\Hossam.bat"
    echo if %%errorlevel%% neq 0 ^( >> "Hossam-WithJavaFX\Hossam.bat"
    echo     echo Application failed to start! >> "Hossam-WithJavaFX\Hossam.bat"
    echo     pause >> "Hossam-WithJavaFX\Hossam.bat"
    echo ^) >> "Hossam-WithJavaFX\Hossam.bat"
    echo ✅ Updated WithJavaFX version
)

:: Create desktop shortcut with JDK 24
echo Creating updated desktop shortcut...
set DESKTOP_SHORTCUT=%USERPROFILE%\Desktop\Hossam.lnk
powershell -command "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%DESKTOP_SHORTCUT%'); $Shortcut.TargetPath = '%USERPROFILE%\Hossam\Hossam.bat'; $Shortcut.WorkingDirectory = '%USERPROFILE%\Hossam'; $Shortcut.Description = 'Hossam Accounting System with JDK 24'; $Shortcut.Save()" 2>nul

if exist "%DESKTOP_SHORTCUT%" (
    echo ✅ Desktop shortcut updated
) else (
    echo ⚠️ Desktop shortcut update failed
)

:: Create quick launcher for current directory
echo Creating quick launcher...
echo @echo off > "Start-Hossam-JDK24.bat"
echo setlocal >> "Start-Hossam-JDK24.bat"
echo set JAVA_HOME=%JDK24_PATH% >> "Start-Hossam-JDK24.bat"
echo set PATH=%%JAVA_HOME%%\bin;%%PATH%% >> "Start-Hossam-JDK24.bat"
echo title Hossam Accounting System >> "Start-Hossam-JDK24.bat"
echo echo ======================================== >> "Start-Hossam-JDK24.bat"
echo echo       Hossam Accounting System >> "Start-Hossam-JDK24.bat"
echo echo       Using JDK 24 with JavaFX >> "Start-Hossam-JDK24.bat"
echo echo ======================================== >> "Start-Hossam-JDK24.bat"
echo echo Starting application... >> "Start-Hossam-JDK24.bat"
echo echo. >> "Start-Hossam-JDK24.bat"
echo if exist "Hossam-WithJavaFX\accounting-system-1.0.0.jar" ^( >> "Start-Hossam-JDK24.bat"
echo     cd /d "Hossam-WithJavaFX" >> "Start-Hossam-JDK24.bat"
echo     java -Dfile.encoding=UTF-8 -jar accounting-system-1.0.0.jar >> "Start-Hossam-JDK24.bat"
echo ^) else if exist "target\accounting-system-1.0.0.jar" ^( >> "Start-Hossam-JDK24.bat"
echo     java -Dfile.encoding=UTF-8 -jar target\accounting-system-1.0.0.jar >> "Start-Hossam-JDK24.bat"
echo ^) else ^( >> "Start-Hossam-JDK24.bat"
echo     echo Application JAR not found! >> "Start-Hossam-JDK24.bat"
echo     pause >> "Start-Hossam-JDK24.bat"
echo ^) >> "Start-Hossam-JDK24.bat"

echo.
echo ========================================
echo Update Completed Successfully!
echo ========================================
echo.
echo ✅ All Hossam installations updated to use JDK 24
echo ✅ Desktop shortcut updated
echo ✅ Quick launcher created: Start-Hossam-JDK24.bat
echo.
echo 🚀 How to run Hossam now:
echo.
echo 1. Double-click "Hossam" on desktop
echo 2. Or run: Start-Hossam-JDK24.bat
echo 3. Or go to: %USERPROFILE%\Hossam\ and run Hossam.bat
echo.

:: Test the application
set /p test_now="Do you want to test Hossam now? (y/n): "
if /i "%test_now%"=="y" (
    echo.
    echo Starting Hossam with JDK 24...
    start "" "Start-Hossam-JDK24.bat"
)

echo.
echo ✅ JDK 24 setup completed! Hossam should work perfectly now.
pause
