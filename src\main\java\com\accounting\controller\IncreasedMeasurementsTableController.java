package com.accounting.controller;

import com.accounting.model.RequiredMeasurement;
import javafx.geometry.Insets;
import javafx.geometry.Pos;
import javafx.scene.control.*;
import javafx.scene.control.cell.PropertyValueFactory;
import javafx.scene.layout.*;
import javafx.collections.ObservableList;
import java.text.DecimalFormat;

/**
 * كنترولر جدول المقاسات مع الزيادة
 */
public class IncreasedMeasurementsTableController {
    
    private final ObservableList<RequiredMeasurement> increasedMeasurements;
    private final DecimalFormat decimalFormat = new DecimalFormat("#,##0.00");
    private TableView<RequiredMeasurement> table;
    private Label totalCountLabel;
    private Label totalSquareMetersLabel;
    private Label totalLinearMetersLabel;
    private Label increaseValueLabel;
    
    public IncreasedMeasurementsTableController(ObservableList<RequiredMeasurement> items) {
        this.increasedMeasurements = items;
    }
    
    /**
     * إنشاء جدول المقاسات مع الزيادة
     */
    public VBox createIncreasedMeasurementsTable(double increaseValue) {
        VBox container = new VBox(15);
        container.setPadding(new Insets(15));
        
        // العنوان مع قيمة الزيادة
        HBox titleBox = new HBox(10);
        titleBox.setAlignment(Pos.CENTER_LEFT);
        
        Label title = new Label("📈 جدول المقاسات مع الزيادة");
        title.getStyleClass().add("section-title");
        
        increaseValueLabel = new Label("(الزيادة: " + decimalFormat.format(increaseValue) + " مم)");
        increaseValueLabel.getStyleClass().add("increase-value-label");
        increaseValueLabel.setStyle("-fx-text-fill: #e74c3c; -fx-font-weight: bold;");
        
        titleBox.getChildren().addAll(title, increaseValueLabel);
        
        // الجدول
        table = createTable();
        
        // وضع الجدول في ScrollPane
        ScrollPane scrollPane = new ScrollPane(table);
        scrollPane.setFitToWidth(true);
        scrollPane.setVbarPolicy(ScrollPane.ScrollBarPolicy.AS_NEEDED);
        scrollPane.setHbarPolicy(ScrollPane.ScrollBarPolicy.NEVER);
        scrollPane.setPrefHeight(300);
        VBox.setVgrow(scrollPane, Priority.ALWAYS);
        
        // الإجماليات
        HBox totalsBox = createTotalsBox();
        
        // ملاحظة
        Label noteLabel = new Label("ملاحظة: هذا الجدول يعرض نفس المقاسات الأصلية مع إضافة قيمة الزيادة للطول والعرض");
        noteLabel.getStyleClass().add("note-label");
        noteLabel.setStyle("-fx-text-fill: #6c757d; -fx-font-style: italic;");
        noteLabel.setWrapText(true);
        
        container.getChildren().addAll(titleBox, scrollPane, totalsBox, noteLabel);
        return container;
    }
    
    /**
     * إنشاء الجدول
     */
    private TableView<RequiredMeasurement> createTable() {
        TableView<RequiredMeasurement> tableView = new TableView<>();
        tableView.setEditable(false); // للقراءة فقط
        tableView.getStyleClass().add("increased-measurements-table");
        
        // عمود الترقيم
        TableColumn<RequiredMeasurement, Integer> indexCol = new TableColumn<>("ت");
        indexCol.setCellValueFactory(cellData -> {
            int index = tableView.getItems().indexOf(cellData.getValue()) + 1;
            return new javafx.beans.property.SimpleIntegerProperty(index).asObject();
        });
        indexCol.setPrefWidth(40);
        indexCol.setSortable(false);
        
        // عمود الوصف
        TableColumn<RequiredMeasurement, String> descCol = new TableColumn<>("الوصف");
        descCol.setCellValueFactory(new PropertyValueFactory<>("description"));
        descCol.setPrefWidth(150);
        
        // عمود الطول مع الزيادة
        TableColumn<RequiredMeasurement, Double> lengthCol = new TableColumn<>("طول مع الزيادة (مم)");
        lengthCol.setCellValueFactory(new PropertyValueFactory<>("length"));
        lengthCol.setCellFactory(column -> new TableCell<RequiredMeasurement, Double>() {
            @Override
            protected void updateItem(Double item, boolean empty) {
                super.updateItem(item, empty);
                if (empty || item == null) {
                    setText(null);
                } else {
                    setText(decimalFormat.format(item));
                    setStyle("-fx-background-color: #fff3cd;"); // تمييز بلون مختلف
                }
            }
        });
        lengthCol.setPrefWidth(120);
        
        // عمود العرض مع الزيادة
        TableColumn<RequiredMeasurement, Double> widthCol = new TableColumn<>("عرض مع الزيادة (مم)");
        widthCol.setCellValueFactory(new PropertyValueFactory<>("width"));
        widthCol.setCellFactory(column -> new TableCell<RequiredMeasurement, Double>() {
            @Override
            protected void updateItem(Double item, boolean empty) {
                super.updateItem(item, empty);
                if (empty || item == null) {
                    setText(null);
                } else {
                    setText(decimalFormat.format(item));
                    setStyle("-fx-background-color: #fff3cd;"); // تمييز بلون مختلف
                }
            }
        });
        widthCol.setPrefWidth(120);
        
        // عمود المتر المربع
        TableColumn<RequiredMeasurement, Double> squareMetersCol = new TableColumn<>("متر مربع");
        squareMetersCol.setCellValueFactory(new PropertyValueFactory<>("squareMeters"));
        squareMetersCol.setCellFactory(column -> new TableCell<RequiredMeasurement, Double>() {
            @Override
            protected void updateItem(Double item, boolean empty) {
                super.updateItem(item, empty);
                if (empty || item == null) {
                    setText(null);
                } else {
                    setText(decimalFormat.format(item));
                }
            }
        });
        squareMetersCol.setPrefWidth(100);
        
        // عمود المتر الطولي
        TableColumn<RequiredMeasurement, Double> linearMetersCol = new TableColumn<>("متر طولي");
        linearMetersCol.setCellValueFactory(new PropertyValueFactory<>("linearMeters"));
        linearMetersCol.setCellFactory(column -> new TableCell<RequiredMeasurement, Double>() {
            @Override
            protected void updateItem(Double item, boolean empty) {
                super.updateItem(item, empty);
                if (empty || item == null) {
                    setText(null);
                } else {
                    setText(decimalFormat.format(item));
                }
            }
        });
        linearMetersCol.setPrefWidth(100);
        
        // عمود العدد
        TableColumn<RequiredMeasurement, Integer> countCol = new TableColumn<>("عدد");
        countCol.setCellValueFactory(new PropertyValueFactory<>("count"));
        countCol.setPrefWidth(80);
        
        // عمود إجمالي المتر المربع
        TableColumn<RequiredMeasurement, Double> totalSquareMetersCol = new TableColumn<>("إجمالي متر مربع");
        totalSquareMetersCol.setCellValueFactory(new PropertyValueFactory<>("totalSquareMeters"));
        totalSquareMetersCol.setCellFactory(column -> new TableCell<RequiredMeasurement, Double>() {
            @Override
            protected void updateItem(Double item, boolean empty) {
                super.updateItem(item, empty);
                if (empty || item == null) {
                    setText(null);
                } else {
                    setText(decimalFormat.format(item));
                }
            }
        });
        totalSquareMetersCol.setPrefWidth(120);
        
        // عمود إجمالي المتر الطولي
        TableColumn<RequiredMeasurement, Double> totalLinearMetersCol = new TableColumn<>("إجمالي متر طولي");
        totalLinearMetersCol.setCellValueFactory(new PropertyValueFactory<>("totalLinearMeters"));
        totalLinearMetersCol.setCellFactory(column -> new TableCell<RequiredMeasurement, Double>() {
            @Override
            protected void updateItem(Double item, boolean empty) {
                super.updateItem(item, empty);
                if (empty || item == null) {
                    setText(null);
                } else {
                    setText(decimalFormat.format(item));
                }
            }
        });
        totalLinearMetersCol.setPrefWidth(120);
        
        // إضافة الأعمدة
        tableView.getColumns().addAll(
            indexCol, descCol, lengthCol, widthCol, 
            squareMetersCol, linearMetersCol, countCol, 
            totalSquareMetersCol, totalLinearMetersCol
        );
        
        // ربط البيانات
        tableView.setItems(increasedMeasurements);
        
        // إضافة مستمع للتحديث التلقائي للترقيم
        increasedMeasurements.addListener((javafx.collections.ListChangeListener<RequiredMeasurement>) change -> {
            tableView.refresh();
            updateTotals();
        });
        
        return tableView;
    }
    
    /**
     * إنشاء صندوق الإجماليات
     */
    private HBox createTotalsBox() {
        HBox totalsBox = new HBox(30);
        totalsBox.setAlignment(Pos.CENTER);
        totalsBox.setPadding(new Insets(15));
        totalsBox.getStyleClass().add("totals-container");
        totalsBox.setStyle("-fx-background-color: #fff3cd; -fx-border-color: #ffeaa7; -fx-border-width: 1; -fx-border-radius: 5;");
        
        // إجمالي العدد
        VBox totalCountBox = new VBox(5);
        totalCountBox.setAlignment(Pos.CENTER);
        Label totalCountTitleLabel = new Label("إجمالي العدد");
        totalCountTitleLabel.getStyleClass().add("totals-title");
        totalCountLabel = new Label("0");
        totalCountLabel.getStyleClass().add("totals-value");
        totalCountLabel.setStyle("-fx-text-fill: #d68910;");
        totalCountBox.getChildren().addAll(totalCountTitleLabel, totalCountLabel);
        
        // إجمالي المتر المربع
        VBox totalSquareMetersBox = new VBox(5);
        totalSquareMetersBox.setAlignment(Pos.CENTER);
        Label totalSquareMetersTitleLabel = new Label("إجمالي المتر المربع");
        totalSquareMetersTitleLabel.getStyleClass().add("totals-title");
        totalSquareMetersLabel = new Label("0.00");
        totalSquareMetersLabel.getStyleClass().add("totals-value");
        totalSquareMetersLabel.setStyle("-fx-text-fill: #d68910;");
        totalSquareMetersBox.getChildren().addAll(totalSquareMetersTitleLabel, totalSquareMetersLabel);
        
        // إجمالي المتر الطولي
        VBox totalLinearMetersBox = new VBox(5);
        totalLinearMetersBox.setAlignment(Pos.CENTER);
        Label totalLinearMetersTitleLabel = new Label("إجمالي المتر الطولي");
        totalLinearMetersTitleLabel.getStyleClass().add("totals-title");
        totalLinearMetersLabel = new Label("0.00");
        totalLinearMetersLabel.getStyleClass().add("totals-value");
        totalLinearMetersLabel.setStyle("-fx-text-fill: #d68910;");
        totalLinearMetersBox.getChildren().addAll(totalLinearMetersTitleLabel, totalLinearMetersLabel);
        
        totalsBox.getChildren().addAll(totalCountBox, totalSquareMetersBox, totalLinearMetersBox);
        return totalsBox;
    }
    
    /**
     * تحديث الإجماليات
     */
    private void updateTotals() {
        int totalCount = increasedMeasurements.stream()
                .mapToInt(RequiredMeasurement::getCount)
                .sum();
        
        double totalSquareMeters = increasedMeasurements.stream()
                .mapToDouble(RequiredMeasurement::getTotalSquareMeters)
                .sum();
        
        double totalLinearMeters = increasedMeasurements.stream()
                .mapToDouble(RequiredMeasurement::getTotalLinearMeters)
                .sum();
        
        totalCountLabel.setText(String.valueOf(totalCount));
        totalSquareMetersLabel.setText(decimalFormat.format(totalSquareMeters) + " م²");
        totalLinearMetersLabel.setText(decimalFormat.format(totalLinearMeters) + " م");
    }
    
    /**
     * تحديث قيمة الزيادة في العنوان
     */
    public void updateIncreaseValue(double increaseValue) {
        if (increaseValueLabel != null) {
            increaseValueLabel.setText("(الزيادة: " + decimalFormat.format(increaseValue) + " مم)");
        }
    }
    
    /**
     * الحصول على الجدول
     */
    public TableView<RequiredMeasurement> getTable() {
        return table;
    }
    
    /**
     * تحديث العرض
     */
    public void refresh() {
        if (table != null) {
            table.refresh();
            updateTotals();
        }
    }
}
