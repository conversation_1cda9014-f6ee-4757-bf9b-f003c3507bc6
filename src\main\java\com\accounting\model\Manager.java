package com.accounting.model;

/**
 * نموذج المدير
 * Manager Model
 */
public class Manager {
    private String managerId;
    private String name;
    private String position;
    private String department;
    private boolean isActive;
    
    public Manager() {
        this.isActive = true;
    }
    
    public Manager(String managerId, String name, String position, String department) {
        this.managerId = managerId;
        this.name = name;
        this.position = position;
        this.department = department;
        this.isActive = true;
    }
    
    // Getters and Setters
    public String getManagerId() { return managerId; }
    public void setManagerId(String managerId) { this.managerId = managerId; }
    
    public String getName() { return name; }
    public void setName(String name) { this.name = name; }
    
    public String getPosition() { return position; }
    public void setPosition(String position) { this.position = position; }
    
    public String getDepartment() { return department; }
    public void setDepartment(String department) { this.department = department; }
    
    public boolean isActive() { return isActive; }
    public void setActive(boolean active) { isActive = active; }
    
    @Override
    public String toString() {
        return name + (position != null && !position.isEmpty() ? " - " + position : "");
    }
}
