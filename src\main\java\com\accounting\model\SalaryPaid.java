package com.accounting.model;

import java.time.LocalDate;
import java.time.YearMonth;

/**
 * نموذج المسدد
 * Salary Paid Model
 */
public class SalaryPaid {
    private String paymentId;
    private String employeeId;
    private String employeeName;
    private YearMonth period;
    private double amount;
    private LocalDate date;
    private String paidBy;
    private String source;
    private String notes;
    private boolean isActive;
    
    public SalaryPaid() {
        this.isActive = true;
        this.date = LocalDate.now();
        this.period = YearMonth.now();
    }
    
    public SalaryPaid(String paymentId, String employeeId, String employeeName, 
                     YearMonth period, double amount, LocalDate date, 
                     String paidBy, String source, String notes) {
        this.paymentId = paymentId;
        this.employeeId = employeeId;
        this.employeeName = employeeName;
        this.period = period;
        this.amount = amount;
        this.date = date;
        this.paidBy = paidBy;
        this.source = source;
        this.notes = notes;
        this.isActive = true;
    }
    
    // Getters and Setters
    public String getPaymentId() { return paymentId; }
    public void setPaymentId(String paymentId) { this.paymentId = paymentId; }
    
    public String getEmployeeId() { return employeeId; }
    public void setEmployeeId(String employeeId) { this.employeeId = employeeId; }
    
    public String getEmployeeName() { return employeeName; }
    public void setEmployeeName(String employeeName) { this.employeeName = employeeName; }
    
    public YearMonth getPeriod() { return period; }
    public void setPeriod(YearMonth period) { this.period = period; }
    
    public double getAmount() { return amount; }
    public void setAmount(double amount) { this.amount = amount; }
    
    public LocalDate getDate() { return date; }
    public void setDate(LocalDate date) { this.date = date; }
    
    public String getPaidBy() { return paidBy; }
    public void setPaidBy(String paidBy) { this.paidBy = paidBy; }
    
    public String getSource() { return source; }
    public void setSource(String source) { this.source = source; }
    
    public String getNotes() { return notes; }
    public void setNotes(String notes) { this.notes = notes; }
    
    public boolean isActive() { return isActive; }
    public void setActive(boolean active) { isActive = active; }
    
    @Override
    public String toString() {
        return "سداد " + amount + " ج.م - " + date;
    }
}
