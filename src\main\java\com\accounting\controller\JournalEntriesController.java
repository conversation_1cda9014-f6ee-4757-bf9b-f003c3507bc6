package com.accounting.controller;

import com.accounting.model.*;
import com.accounting.service.*;
import javafx.geometry.Insets;
import javafx.geometry.Pos;
import javafx.scene.Scene;
import javafx.scene.control.*;
import javafx.scene.control.cell.PropertyValueFactory;
import javafx.scene.layout.*;
import javafx.stage.Modality;
import javafx.stage.Stage;
import de.jensd.fx.glyphs.fontawesome.FontAwesomeIcon;
import de.jensd.fx.glyphs.fontawesome.FontAwesomeIconView;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import java.time.LocalDate;
import java.text.DecimalFormat;
import java.util.Optional;

/**
 * كنترولر القيود اليومية
 */
public class JournalEntriesController {
    
    private final JournalService journalService;
    private final AccountService accountService;
    private final DecimalFormat decimalFormat = new DecimalFormat("#,##0.00");
    private TableView<JournalEntry> entriesTable;
    private TextField searchField;
    private DatePicker fromDatePicker;
    private DatePicker toDatePicker;
    private ComboBox<JournalEntry.EntryStatus> statusFilter;
    
    public JournalEntriesController() {
        this.journalService = JournalService.getInstance();
        this.accountService = AccountService.getInstance();
    }
    
    /**
     * عرض نافذة القيود اليومية
     */
    public void showJournalEntriesDialog() {
        Stage dialog = new Stage();
        dialog.setTitle("📝 القيود اليومية");
        dialog.initModality(Modality.APPLICATION_MODAL);
        
        // تحديد حجم النافذة
        javafx.stage.Screen screen = javafx.stage.Screen.getPrimary();
        double screenHeight = screen.getVisualBounds().getHeight();
        double screenWidth = screen.getVisualBounds().getWidth();
        
        dialog.setWidth(Math.min(1400, screenWidth * 0.95));
        dialog.setHeight(Math.min(900, screenHeight * 0.9));
        dialog.setResizable(true);
        
        VBox mainLayout = new VBox(15);
        mainLayout.setPadding(new Insets(20));
        
        // العنوان والوصف
        Label titleLabel = new Label("📝 القيود اليومية");
        titleLabel.getStyleClass().add("dialog-title");
        
        Label descLabel = new Label("إدارة القيود المحاسبية والترحيل التلقائي");
        descLabel.getStyleClass().add("dialog-description");
        
        // شريط الأدوات والفلاتر
        VBox filtersBox = createFiltersBox();
        
        // جدول القيود
        entriesTable = createEntriesTable();
        
        // تحميل البيانات
        refreshEntriesTable();
        
        // وضع الجدول في ScrollPane
        ScrollPane scrollPane = new ScrollPane(entriesTable);
        scrollPane.setFitToWidth(true);
        scrollPane.setVbarPolicy(ScrollPane.ScrollBarPolicy.AS_NEEDED);
        scrollPane.setHbarPolicy(ScrollPane.ScrollBarPolicy.NEVER);
        scrollPane.getStyleClass().add("entries-scroll-pane");
        VBox.setVgrow(scrollPane, Priority.ALWAYS);
        
        // أزرار الإجراءات
        HBox buttonBox = createButtonBox(dialog);
        
        mainLayout.getChildren().addAll(
            titleLabel, descLabel, filtersBox, scrollPane, buttonBox
        );
        
        Scene scene = new Scene(mainLayout);
        scene.getStylesheets().add(getClass().getResource("/css/styles.css").toExternalForm());
        dialog.setScene(scene);
        dialog.showAndWait();
    }
    
    /**
     * عرض نافذة قيد جديد
     */
    public void showNewJournalEntryDialog() {
        showInfoAlert("نموذج إضافة قيد جديد قيد التطوير.");
    }
    
    /**
     * إنشاء صندوق الفلاتر
     */
    private VBox createFiltersBox() {
        VBox filtersBox = new VBox(10);
        filtersBox.setPadding(new Insets(15));
        filtersBox.getStyleClass().add("filters-container");
        
        Label filtersTitle = new Label("🔍 الفلاتر والبحث");
        filtersTitle.getStyleClass().add("filters-title");
        
        // الصف الأول من الفلاتر
        HBox firstRow = new HBox(15);
        firstRow.setAlignment(Pos.CENTER_LEFT);
        
        // البحث
        Label searchLabel = new Label("البحث:");
        searchField = new TextField();
        searchField.setPromptText("ابحث في القيود...");
        searchField.setPrefWidth(200);
        searchField.textProperty().addListener((obs, oldVal, newVal) -> applyFilters());
        
        // فلتر التاريخ من
        Label fromLabel = new Label("من تاريخ:");
        fromDatePicker = new DatePicker();
        fromDatePicker.setValue(LocalDate.now().withDayOfMonth(1)); // أول الشهر
        fromDatePicker.valueProperty().addListener((obs, oldVal, newVal) -> applyFilters());
        
        // فلتر التاريخ إلى
        Label toLabel = new Label("إلى تاريخ:");
        toDatePicker = new DatePicker();
        toDatePicker.setValue(LocalDate.now()); // اليوم
        toDatePicker.valueProperty().addListener((obs, oldVal, newVal) -> applyFilters());
        
        firstRow.getChildren().addAll(
            searchLabel, searchField,
            new Separator(),
            fromLabel, fromDatePicker,
            toLabel, toDatePicker
        );
        
        // الصف الثاني من الفلاتر
        HBox secondRow = new HBox(15);
        secondRow.setAlignment(Pos.CENTER_LEFT);
        
        // فلتر الحالة
        Label statusLabel = new Label("الحالة:");
        statusFilter = new ComboBox<>();
        statusFilter.getItems().add(null); // جميع الحالات
        statusFilter.getItems().addAll(JournalEntry.EntryStatus.values());
        statusFilter.setPromptText("جميع الحالات");
        statusFilter.valueProperty().addListener((obs, oldVal, newVal) -> applyFilters());
        
        // أزرار الفلاتر
        Button applyBtn = new Button("تطبيق");
        FontAwesomeIconView applyIcon = new FontAwesomeIconView(FontAwesomeIcon.SEARCH);
        applyIcon.setSize("12px");
        applyBtn.setGraphic(applyIcon);
        applyBtn.getStyleClass().add("filter-button");
        applyBtn.setOnAction(e -> applyFilters());
        
        Button resetBtn = new Button("إعادة تعيين");
        FontAwesomeIconView resetIcon = new FontAwesomeIconView(FontAwesomeIcon.REFRESH);
        resetIcon.setSize("12px");
        resetBtn.setGraphic(resetIcon);
        resetBtn.getStyleClass().add("reset-button");
        resetBtn.setOnAction(e -> resetFilters());
        
        secondRow.getChildren().addAll(
            statusLabel, statusFilter,
            new Separator(),
            applyBtn, resetBtn
        );
        
        filtersBox.getChildren().addAll(filtersTitle, firstRow, secondRow);
        return filtersBox;
    }
    
    /**
     * إنشاء جدول القيود
     */
    private TableView<JournalEntry> createEntriesTable() {
        TableView<JournalEntry> table = new TableView<>();
        table.getStyleClass().add("entries-table");
        
        // الأعمدة
        TableColumn<JournalEntry, String> numberCol = new TableColumn<>("رقم القيد");
        numberCol.setCellValueFactory(new PropertyValueFactory<>("entryNumber"));
        numberCol.setPrefWidth(100);
        
        TableColumn<JournalEntry, LocalDate> dateCol = new TableColumn<>("التاريخ");
        dateCol.setCellValueFactory(new PropertyValueFactory<>("entryDate"));
        dateCol.setPrefWidth(100);
        
        TableColumn<JournalEntry, String> descCol = new TableColumn<>("الوصف");
        descCol.setCellValueFactory(new PropertyValueFactory<>("description"));
        descCol.setPrefWidth(250);
        
        TableColumn<JournalEntry, String> typeCol = new TableColumn<>("النوع");
        typeCol.setCellValueFactory(cellData -> 
            cellData.getValue().entryTypeProperty().asString());
        typeCol.setPrefWidth(120);
        
        TableColumn<JournalEntry, String> statusCol = new TableColumn<>("الحالة");
        statusCol.setCellValueFactory(cellData -> 
            cellData.getValue().statusProperty().asString());
        statusCol.setPrefWidth(100);
        
        TableColumn<JournalEntry, String> debitCol = new TableColumn<>("إجمالي المدين");
        debitCol.setCellValueFactory(cellData -> {
            double debit = cellData.getValue().getTotalDebit();
            return new javafx.beans.property.SimpleStringProperty(
                decimalFormat.format(debit) + " ج.م"
            );
        });
        debitCol.setPrefWidth(120);
        
        TableColumn<JournalEntry, String> creditCol = new TableColumn<>("إجمالي الدائن");
        creditCol.setCellValueFactory(cellData -> {
            double credit = cellData.getValue().getTotalCredit();
            return new javafx.beans.property.SimpleStringProperty(
                decimalFormat.format(credit) + " ج.م"
            );
        });
        creditCol.setPrefWidth(120);
        
        TableColumn<JournalEntry, String> balancedCol = new TableColumn<>("متوازن");
        balancedCol.setCellValueFactory(cellData -> {
            boolean balanced = cellData.getValue().isBalanced();
            return new javafx.beans.property.SimpleStringProperty(
                balanced ? "✓" : "✗"
            );
        });
        balancedCol.setPrefWidth(80);
        
        // إضافة الأعمدة
        table.getColumns().addAll(numberCol, dateCol, descCol, typeCol, statusCol, debitCol, creditCol, balancedCol);
        
        // تخصيص عرض الصفوف حسب الحالة
        table.setRowFactory(tv -> {
            TableRow<JournalEntry> row = new TableRow<>();
            row.itemProperty().addListener((obs, oldEntry, newEntry) -> {
                if (newEntry != null) {
                    String style = "";
                    switch (newEntry.getStatus()) {
                        case DRAFT:
                            style = "-fx-background-color: #fff3cd;";
                            break;
                        case APPROVED:
                            style = "-fx-background-color: #d1ecf1;";
                            break;
                        case POSTED:
                            style = "-fx-background-color: #d4edda;";
                            break;
                        case CANCELLED:
                            style = "-fx-background-color: #f8d7da;";
                            break;
                    }
                    row.setStyle(style);
                }
            });
            return row;
        });
        
        return table;
    }
    
    /**
     * إنشاء صندوق الأزرار
     */
    private HBox createButtonBox(Stage dialog) {
        HBox buttonBox = new HBox(15);
        buttonBox.setAlignment(Pos.CENTER_LEFT);
        buttonBox.setPadding(new Insets(10, 0, 0, 0));
        
        // أزرار الإجراءات الأساسية
        Button addBtn = new Button("قيد جديد");
        FontAwesomeIconView addIcon = new FontAwesomeIconView(FontAwesomeIcon.PLUS);
        addIcon.setSize("12px");
        addBtn.setGraphic(addIcon);
        addBtn.getStyleClass().add("add-button");
        addBtn.setOnAction(e -> showNewJournalEntryDialog());
        
        Button editBtn = new Button("تعديل");
        FontAwesomeIconView editIcon = new FontAwesomeIconView(FontAwesomeIcon.EDIT);
        editIcon.setSize("12px");
        editBtn.setGraphic(editIcon);
        editBtn.getStyleClass().add("edit-button");
        editBtn.setOnAction(e -> editSelectedEntry());
        
        Button viewBtn = new Button("عرض");
        FontAwesomeIconView viewIcon = new FontAwesomeIconView(FontAwesomeIcon.EYE);
        viewIcon.setSize("12px");
        viewBtn.setGraphic(viewIcon);
        viewBtn.getStyleClass().add("view-button");
        viewBtn.setOnAction(e -> viewSelectedEntry());
        
        Button deleteBtn = new Button("حذف");
        FontAwesomeIconView deleteIcon = new FontAwesomeIconView(FontAwesomeIcon.TRASH);
        deleteIcon.setSize("12px");
        deleteBtn.setGraphic(deleteIcon);
        deleteBtn.getStyleClass().add("delete-button");
        deleteBtn.setOnAction(e -> deleteSelectedEntry());
        
        // أزرار الترحيل
        Button approveBtn = new Button("اعتماد");
        FontAwesomeIconView approveIcon = new FontAwesomeIconView(FontAwesomeIcon.CHECK);
        approveIcon.setSize("12px");
        approveBtn.setGraphic(approveIcon);
        approveBtn.getStyleClass().add("approve-button");
        approveBtn.setOnAction(e -> approveSelectedEntry());
        
        Button postBtn = new Button("ترحيل");
        FontAwesomeIconView postIcon = new FontAwesomeIconView(FontAwesomeIcon.ARROW_RIGHT);
        postIcon.setSize("12px");
        postBtn.setGraphic(postIcon);
        postBtn.getStyleClass().add("post-button");
        postBtn.setOnAction(e -> postSelectedEntry());
        
        Button unpostBtn = new Button("إلغاء ترحيل");
        FontAwesomeIconView unpostIcon = new FontAwesomeIconView(FontAwesomeIcon.ARROW_LEFT);
        unpostIcon.setSize("12px");
        unpostBtn.setGraphic(unpostIcon);
        unpostBtn.getStyleClass().add("unpost-button");
        unpostBtn.setOnAction(e -> unpostSelectedEntry());
        
        // أزرار أخرى
        Button refreshBtn = new Button("تحديث");
        FontAwesomeIconView refreshIcon = new FontAwesomeIconView(FontAwesomeIcon.REFRESH);
        refreshIcon.setSize("12px");
        refreshBtn.setGraphic(refreshIcon);
        refreshBtn.getStyleClass().add("refresh-button");
        refreshBtn.setOnAction(e -> refreshEntriesTable());
        
        Button closeBtn = new Button("إغلاق");
        closeBtn.setOnAction(e -> dialog.close());
        
        buttonBox.getChildren().addAll(
            addBtn, editBtn, viewBtn, deleteBtn,
            new Separator(),
            approveBtn, postBtn, unpostBtn,
            new Separator(),
            refreshBtn, closeBtn
        );
        
        return buttonBox;
    }
    
    /**
     * تحديث جدول القيود
     */
    private void refreshEntriesTable() {
        ObservableList<JournalEntry> entries = FXCollections.observableArrayList(
            journalService.getAllJournalEntries()
        );
        entriesTable.setItems(entries);
    }
    
    /**
     * تطبيق الفلاتر
     */
    private void applyFilters() {
        ObservableList<JournalEntry> filteredEntries = FXCollections.observableArrayList();
        
        for (JournalEntry entry : journalService.getAllJournalEntries()) {
            boolean matches = true;
            
            // فلتر البحث النصي
            String searchText = searchField.getText();
            if (searchText != null && !searchText.trim().isEmpty()) {
                String searchLower = searchText.toLowerCase();
                boolean textMatch = entry.getEntryNumber().toLowerCase().contains(searchLower) ||
                                  entry.getDescription().toLowerCase().contains(searchLower) ||
                                  (entry.getReference() != null && entry.getReference().toLowerCase().contains(searchLower));
                if (!textMatch) {
                    matches = false;
                }
            }
            
            // فلتر التاريخ
            LocalDate fromDate = fromDatePicker.getValue();
            LocalDate toDate = toDatePicker.getValue();
            if (fromDate != null && entry.getEntryDate().isBefore(fromDate)) {
                matches = false;
            }
            if (toDate != null && entry.getEntryDate().isAfter(toDate)) {
                matches = false;
            }
            
            // فلتر الحالة
            JournalEntry.EntryStatus selectedStatus = statusFilter.getValue();
            if (selectedStatus != null && entry.getStatus() != selectedStatus) {
                matches = false;
            }
            
            if (matches) {
                filteredEntries.add(entry);
            }
        }
        
        entriesTable.setItems(filteredEntries);
    }
    
    /**
     * إعادة تعيين الفلاتر
     */
    private void resetFilters() {
        searchField.clear();
        fromDatePicker.setValue(LocalDate.now().withDayOfMonth(1));
        toDatePicker.setValue(LocalDate.now());
        statusFilter.setValue(null);
        refreshEntriesTable();
    }
    
    // وظائف الإجراءات
    private void editSelectedEntry() {
        JournalEntry selectedEntry = entriesTable.getSelectionModel().getSelectedItem();
        if (selectedEntry == null) {
            showWarningAlert("يرجى اختيار قيد للتعديل.");
            return;
        }
        
        if (!selectedEntry.canBeEdited()) {
            showWarningAlert("لا يمكن تعديل هذا القيد لأنه معتمد أو مرحل.");
            return;
        }
        
        showInfoAlert("نموذج تعديل القيد قيد التطوير.");
    }
    
    private void viewSelectedEntry() {
        JournalEntry selectedEntry = entriesTable.getSelectionModel().getSelectedItem();
        if (selectedEntry == null) {
            showWarningAlert("يرجى اختيار قيد للعرض.");
            return;
        }
        
        showInfoAlert("عرض تفاصيل القيد قيد التطوير.");
    }
    
    private void deleteSelectedEntry() {
        JournalEntry selectedEntry = entriesTable.getSelectionModel().getSelectedItem();
        if (selectedEntry == null) {
            showWarningAlert("يرجى اختيار قيد للحذف.");
            return;
        }
        
        if (!selectedEntry.canBeDeleted()) {
            showErrorAlert("لا يمكن حذف هذا القيد لأنه مرحل.");
            return;
        }
        
        Alert confirmAlert = new Alert(Alert.AlertType.CONFIRMATION);
        confirmAlert.setTitle("تأكيد الحذف");
        confirmAlert.setHeaderText("حذف القيد");
        confirmAlert.setContentText("هل أنت متأكد من حذف القيد: " + selectedEntry.getEntryNumber() + "؟");
        
        Optional<ButtonType> result = confirmAlert.showAndWait();
        if (result.isPresent() && result.get() == ButtonType.OK) {
            if (journalService.deleteJournalEntry(selectedEntry.getEntryId())) {
                refreshEntriesTable();
                showSuccessAlert("تم حذف القيد بنجاح!");
            } else {
                showErrorAlert("فشل في حذف القيد.");
            }
        }
    }
    
    private void approveSelectedEntry() {
        JournalEntry selectedEntry = entriesTable.getSelectionModel().getSelectedItem();
        if (selectedEntry == null) {
            showWarningAlert("يرجى اختيار قيد للاعتماد.");
            return;
        }
        
        if (journalService.approveJournalEntry(selectedEntry.getEntryId(), "المستخدم الحالي")) {
            refreshEntriesTable();
            showSuccessAlert("تم اعتماد القيد بنجاح!");
        } else {
            showErrorAlert("فشل في اعتماد القيد. تأكد من صحة البيانات.");
        }
    }
    
    private void postSelectedEntry() {
        JournalEntry selectedEntry = entriesTable.getSelectionModel().getSelectedItem();
        if (selectedEntry == null) {
            showWarningAlert("يرجى اختيار قيد للترحيل.");
            return;
        }
        
        if (journalService.postJournalEntry(selectedEntry.getEntryId(), "المستخدم الحالي")) {
            refreshEntriesTable();
            showSuccessAlert("تم ترحيل القيد بنجاح!");
        } else {
            showErrorAlert("فشل في ترحيل القيد. تأكد من اعتماد القيد أولاً.");
        }
    }
    
    private void unpostSelectedEntry() {
        JournalEntry selectedEntry = entriesTable.getSelectionModel().getSelectedItem();
        if (selectedEntry == null) {
            showWarningAlert("يرجى اختيار قيد لإلغاء ترحيله.");
            return;
        }
        
        Alert confirmAlert = new Alert(Alert.AlertType.CONFIRMATION);
        confirmAlert.setTitle("تأكيد إلغاء الترحيل");
        confirmAlert.setHeaderText("إلغاء ترحيل القيد");
        confirmAlert.setContentText("هل أنت متأكد من إلغاء ترحيل القيد: " + selectedEntry.getEntryNumber() + "؟");
        
        Optional<ButtonType> result = confirmAlert.showAndWait();
        if (result.isPresent() && result.get() == ButtonType.OK) {
            if (journalService.unpostJournalEntry(selectedEntry.getEntryId(), "المستخدم الحالي")) {
                refreshEntriesTable();
                showSuccessAlert("تم إلغاء ترحيل القيد بنجاح!");
            } else {
                showErrorAlert("فشل في إلغاء ترحيل القيد.");
            }
        }
    }
    
    // رسائل التنبيه
    private void showSuccessAlert(String message) {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle("نجح");
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }
    
    private void showErrorAlert(String message) {
        Alert alert = new Alert(Alert.AlertType.ERROR);
        alert.setTitle("خطأ");
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }
    
    private void showWarningAlert(String message) {
        Alert alert = new Alert(Alert.AlertType.WARNING);
        alert.setTitle("تحذير");
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }

    private void showInfoAlert(String message) {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle("معلومات");
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }
}
