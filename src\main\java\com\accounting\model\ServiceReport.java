package com.accounting.model;

import javafx.beans.property.*;
import java.time.LocalDateTime;

/**
 * نموذج تقرير الخدمات المفصل
 */
public class ServiceReport {
    private final StringProperty invoiceNumber = new SimpleStringProperty();
    private final ObjectProperty<LocalDateTime> date = new SimpleObjectProperty<>();
    private final StringProperty customerName = new SimpleStringProperty();
    private final StringProperty serviceName = new SimpleStringProperty();
    private final IntegerProperty thickness = new SimpleIntegerProperty();
    private final StringProperty description = new SimpleStringProperty();
    private final DoubleProperty totalQuantity = new SimpleDoubleProperty();
    private final DoubleProperty price = new SimpleDoubleProperty();
    private final DoubleProperty totalValue = new SimpleDoubleProperty();
    
    public ServiceReport() {}
    
    public ServiceReport(String invoiceNumber, LocalDateTime date, String customerName, 
                        String serviceName, int thickness, String description,
                        double totalQuantity, double price, double totalValue) {
        setInvoiceNumber(invoiceNumber);
        setDate(date);
        setCustomerName(customerName);
        setServiceName(serviceName);
        setThickness(thickness);
        setDescription(description);
        setTotalQuantity(totalQuantity);
        setPrice(price);
        setTotalValue(totalValue);
    }
    
    // Property getters
    public StringProperty invoiceNumberProperty() { return invoiceNumber; }
    public ObjectProperty<LocalDateTime> dateProperty() { return date; }
    public StringProperty customerNameProperty() { return customerName; }
    public StringProperty serviceNameProperty() { return serviceName; }
    public IntegerProperty thicknessProperty() { return thickness; }
    public StringProperty descriptionProperty() { return description; }
    public DoubleProperty totalQuantityProperty() { return totalQuantity; }
    public DoubleProperty priceProperty() { return price; }
    public DoubleProperty totalValueProperty() { return totalValue; }
    
    // Value getters and setters
    public String getInvoiceNumber() { return invoiceNumber.get(); }
    public void setInvoiceNumber(String invoiceNumber) { this.invoiceNumber.set(invoiceNumber); }
    
    public LocalDateTime getDate() { return date.get(); }
    public void setDate(LocalDateTime date) { this.date.set(date); }
    
    public String getCustomerName() { return customerName.get(); }
    public void setCustomerName(String customerName) { this.customerName.set(customerName); }
    
    public String getServiceName() { return serviceName.get(); }
    public void setServiceName(String serviceName) { this.serviceName.set(serviceName); }
    
    public int getThickness() { return thickness.get(); }
    public void setThickness(int thickness) { this.thickness.set(thickness); }
    
    public String getDescription() { return description.get(); }
    public void setDescription(String description) { this.description.set(description); }
    
    public double getTotalQuantity() { return totalQuantity.get(); }
    public void setTotalQuantity(double totalQuantity) { this.totalQuantity.set(totalQuantity); }
    
    public double getPrice() { return price.get(); }
    public void setPrice(double price) { this.price.set(price); }
    
    public double getTotalValue() { return totalValue.get(); }
    public void setTotalValue(double totalValue) { this.totalValue.set(totalValue); }
    
    /**
     * تنسيق التاريخ للعرض
     */
    public String getFormattedDate() {
        if (date.get() != null) {
            return date.get().format(java.time.format.DateTimeFormatter.ofPattern("dd/MM/yyyy"));
        }
        return "";
    }
    
    /**
     * تنسيق الوقت للعرض
     */
    public String getFormattedTime() {
        if (date.get() != null) {
            return date.get().format(java.time.format.DateTimeFormatter.ofPattern("HH:mm"));
        }
        return "";
    }
    
    /**
     * تنسيق التاريخ والوقت للعرض
     */
    public String getFormattedDateTime() {
        if (date.get() != null) {
            return date.get().format(java.time.format.DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm"));
        }
        return "";
    }
}
