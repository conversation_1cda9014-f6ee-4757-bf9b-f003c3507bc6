@echo off
setlocal

echo.
echo ========================================
echo Creating Quick Standalone Distribution
echo ========================================
echo.

:: Check if JAR exists
if not exist "target\accounting-system-1.0.0.jar" (
    echo Building application first...
    call mvnw.cmd clean package -q
    if not %errorlevel%==0 (
        echo ERROR: Build failed
        pause
        exit /b 1
    )
)

echo ✅ Application JAR ready

:: Create distribution folder
set DIST_NAME=AccountingSystem-Standalone-v1.0
if exist "%DIST_NAME%" rmdir /s /q "%DIST_NAME%"
mkdir "%DIST_NAME%"

echo Creating standalone distribution...

:: Copy application
copy "target\accounting-system-1.0.0.jar" "%DIST_NAME%\"

:: Create launcher that downloads Java if needed
echo Creating smart launcher...
echo @echo off > "%DIST_NAME%\AccountingSystem.bat"
echo setlocal enabledelayedexpansion >> "%DIST_NAME%\AccountingSystem.bat"
echo. >> "%DIST_NAME%\AccountingSystem.bat"
echo echo ======================================== >> "%DIST_NAME%\AccountingSystem.bat"
echo echo نظام المحاسبة المتكامل >> "%DIST_NAME%\AccountingSystem.bat"
echo echo Integrated Accounting System >> "%DIST_NAME%\AccountingSystem.bat"
echo echo ======================================== >> "%DIST_NAME%\AccountingSystem.bat"
echo echo. >> "%DIST_NAME%\AccountingSystem.bat"
echo. >> "%DIST_NAME%\AccountingSystem.bat"
echo :: Check for Java >> "%DIST_NAME%\AccountingSystem.bat"
echo java -version ^>nul 2^>^&1 >> "%DIST_NAME%\AccountingSystem.bat"
echo if %%errorlevel%%==0 ^( >> "%DIST_NAME%\AccountingSystem.bat"
echo     echo ✅ Java found on system >> "%DIST_NAME%\AccountingSystem.bat"
echo     goto :run_app >> "%DIST_NAME%\AccountingSystem.bat"
echo ^) >> "%DIST_NAME%\AccountingSystem.bat"
echo. >> "%DIST_NAME%\AccountingSystem.bat"
echo :: Check for portable Java >> "%DIST_NAME%\AccountingSystem.bat"
echo if exist "java-portable\bin\java.exe" ^( >> "%DIST_NAME%\AccountingSystem.bat"
echo     echo ✅ Using portable Java >> "%DIST_NAME%\AccountingSystem.bat"
echo     set JAVA_HOME=%%~dp0java-portable >> "%DIST_NAME%\AccountingSystem.bat"
echo     set PATH=!JAVA_HOME!\bin;!PATH! >> "%DIST_NAME%\AccountingSystem.bat"
echo     goto :run_app >> "%DIST_NAME%\AccountingSystem.bat"
echo ^) >> "%DIST_NAME%\AccountingSystem.bat"
echo. >> "%DIST_NAME%\AccountingSystem.bat"
echo :: Java not found >> "%DIST_NAME%\AccountingSystem.bat"
echo echo ❌ Java not found >> "%DIST_NAME%\AccountingSystem.bat"
echo echo. >> "%DIST_NAME%\AccountingSystem.bat"
echo echo This application requires Java 17 or newer. >> "%DIST_NAME%\AccountingSystem.bat"
echo echo. >> "%DIST_NAME%\AccountingSystem.bat"
echo echo Options: >> "%DIST_NAME%\AccountingSystem.bat"
echo echo 1. Install Java from: https://adoptium.net/ >> "%DIST_NAME%\AccountingSystem.bat"
echo echo 2. Download portable Java using: download-java.bat >> "%DIST_NAME%\AccountingSystem.bat"
echo echo. >> "%DIST_NAME%\AccountingSystem.bat"
echo set /p choice="Would you like to open Java download page? (y/n): " >> "%DIST_NAME%\AccountingSystem.bat"
echo if /i "!choice!"=="y" start https://adoptium.net/temurin/releases/ >> "%DIST_NAME%\AccountingSystem.bat"
echo pause >> "%DIST_NAME%\AccountingSystem.bat"
echo exit /b 1 >> "%DIST_NAME%\AccountingSystem.bat"
echo. >> "%DIST_NAME%\AccountingSystem.bat"
echo :run_app >> "%DIST_NAME%\AccountingSystem.bat"
echo echo Starting Accounting System... >> "%DIST_NAME%\AccountingSystem.bat"
echo echo. >> "%DIST_NAME%\AccountingSystem.bat"
echo java -Dfile.encoding=UTF-8 -Djava.awt.headless=false --add-opens javafx.graphics/javafx.scene=ALL-UNNAMED --add-opens javafx.controls/javafx.scene.control=ALL-UNNAMED --add-opens javafx.base/javafx.beans.property=ALL-UNNAMED -jar accounting-system-1.0.0.jar >> "%DIST_NAME%\AccountingSystem.bat"
echo. >> "%DIST_NAME%\AccountingSystem.bat"
echo if not %%errorlevel%%==0 ^( >> "%DIST_NAME%\AccountingSystem.bat"
echo     echo. >> "%DIST_NAME%\AccountingSystem.bat"
echo     echo ERROR: Application failed to start >> "%DIST_NAME%\AccountingSystem.bat"
echo     echo. >> "%DIST_NAME%\AccountingSystem.bat"
echo     pause >> "%DIST_NAME%\AccountingSystem.bat"
echo ^) >> "%DIST_NAME%\AccountingSystem.bat"

:: Create Java downloader script
echo Creating Java downloader...
echo @echo off > "%DIST_NAME%\download-java.bat"
echo echo ======================================== >> "%DIST_NAME%\download-java.bat"
echo echo Java Portable Downloader >> "%DIST_NAME%\download-java.bat"
echo echo ======================================== >> "%DIST_NAME%\download-java.bat"
echo echo. >> "%DIST_NAME%\download-java.bat"
echo echo This will download a portable Java runtime. >> "%DIST_NAME%\download-java.bat"
echo echo. >> "%DIST_NAME%\download-java.bat"
echo echo Manual steps: >> "%DIST_NAME%\download-java.bat"
echo echo 1. Go to: https://adoptium.net/temurin/releases/ >> "%DIST_NAME%\download-java.bat"
echo echo 2. Download: OpenJDK 17 LTS >> "%DIST_NAME%\download-java.bat"
echo echo 3. Choose: ZIP archive (not installer) >> "%DIST_NAME%\download-java.bat"
echo echo 4. Extract to this folder as 'java-portable' >> "%DIST_NAME%\download-java.bat"
echo echo. >> "%DIST_NAME%\download-java.bat"
echo echo Opening download page... >> "%DIST_NAME%\download-java.bat"
echo start https://adoptium.net/temurin/releases/ >> "%DIST_NAME%\download-java.bat"
echo echo. >> "%DIST_NAME%\download-java.bat"
echo pause >> "%DIST_NAME%\download-java.bat"

:: Create comprehensive README
echo Creating comprehensive README...
echo # نظام المحاسبة المتكامل - النسخة المستقلة > "%DIST_NAME%\README.txt"
echo ## Integrated Accounting System - Standalone Version >> "%DIST_NAME%\README.txt"
echo. >> "%DIST_NAME%\README.txt"
echo ### 🚀 طريقة التشغيل السريعة: >> "%DIST_NAME%\README.txt"
echo 1. انقر نقراً مزدوجاً على AccountingSystem.bat >> "%DIST_NAME%\README.txt"
echo 2. إذا طلب Java، اتبع التعليمات التي ستظهر >> "%DIST_NAME%\README.txt"
echo. >> "%DIST_NAME%\README.txt"
echo ### 📋 المتطلبات: >> "%DIST_NAME%\README.txt"
echo - Windows 10/11 (64-bit) >> "%DIST_NAME%\README.txt"
echo - Java 17+ (سيتم توجيهك لتحميله إذا لم يكن موجود) >> "%DIST_NAME%\README.txt"
echo - 4 GB RAM كحد أدنى >> "%DIST_NAME%\README.txt"
echo - 200 MB مساحة قرص >> "%DIST_NAME%\README.txt"
echo. >> "%DIST_NAME%\README.txt"
echo ### 🔧 إعداد Java المحمولة (اختياري): >> "%DIST_NAME%\README.txt"
echo 1. شغل download-java.bat >> "%DIST_NAME%\README.txt"
echo 2. حمل Java ZIP من الرابط >> "%DIST_NAME%\README.txt"
echo 3. استخرج الملفات في مجلد java-portable >> "%DIST_NAME%\README.txt"
echo 4. شغل AccountingSystem.bat مرة أخرى >> "%DIST_NAME%\README.txt"
echo. >> "%DIST_NAME%\README.txt"
echo ### 📦 طريقة التوزيع: >> "%DIST_NAME%\README.txt"
echo 1. انسخ هذا المجلد كاملاً >> "%DIST_NAME%\README.txt"
echo 2. أو اضغطه في ملف ZIP >> "%DIST_NAME%\README.txt"
echo 3. المستخدم ينسخ المجلد ويشغل AccountingSystem.bat >> "%DIST_NAME%\README.txt"
echo. >> "%DIST_NAME%\README.txt"
echo ### ✨ المميزات: >> "%DIST_NAME%\README.txt"
echo ✅ تشغيل ذكي مع فحص Java تلقائي >> "%DIST_NAME%\README.txt"
echo ✅ دعم Java المحمولة >> "%DIST_NAME%\README.txt"
echo ✅ رسائل خطأ واضحة ومفيدة >> "%DIST_NAME%\README.txt"
echo ✅ سهل التوزيع والنقل >> "%DIST_NAME%\README.txt"
echo ✅ لا يحتاج صلاحيات مدير >> "%DIST_NAME%\README.txt"
echo. >> "%DIST_NAME%\README.txt"
echo ### 📞 الدعم التقني: >> "%DIST_NAME%\README.txt"
echo البريد الإلكتروني: <EMAIL> >> "%DIST_NAME%\README.txt"
echo الهاتف: 123-456-7890 >> "%DIST_NAME%\README.txt"
echo. >> "%DIST_NAME%\README.txt"
echo © 2025 شركة الزجاج والألومنيوم المتقدمة >> "%DIST_NAME%\README.txt"

:: Copy documentation
copy "دليل-المستخدم.md" "%DIST_NAME%\" 2>nul
copy "README-DEVELOPER.md" "%DIST_NAME%\" 2>nul

:: Create ZIP package
echo Creating ZIP package...
powershell -command "Compress-Archive -Path '%DIST_NAME%\*' -DestinationPath '%DIST_NAME%.zip' -Force" 2>nul

echo.
echo ========================================
echo SUCCESS: Quick Standalone Created!
echo ========================================
echo.
echo Distribution folder: %DIST_NAME%\
if exist "%DIST_NAME%.zip" echo ZIP package: %DIST_NAME%.zip
echo.
echo 📁 Contents:
dir /b "%DIST_NAME%"
echo.

:: Show file sizes
echo 📊 File sizes:
for %%f in ("%DIST_NAME%\*") do (
    if not "%%~nxf"=="." if not "%%~nxf"==".." (
        echo   %%~nxf: %%~zf bytes
    )
)

if exist "%DIST_NAME%.zip" (
    echo.
    for %%f in ("%DIST_NAME%.zip") do echo 📦 ZIP size: %%~zf bytes (~%%~zf MB)
)

echo.
echo ✅ Features:
echo   - Smart Java detection
echo   - Portable Java support
echo   - User-friendly error messages
echo   - Easy distribution
echo   - No admin rights required
echo.
echo 🚀 Ready for distribution!
echo.
echo 📋 Distribution options:
echo   1. Send ZIP file: %DIST_NAME%.zip
echo   2. Copy folder: %DIST_NAME%\
echo   3. Users run: AccountingSystem.bat
echo.
echo 💡 For completely standalone version:
echo   - Add portable Java to java-portable folder
echo   - Total size will be ~150-200 MB
echo   - No Java installation required on target system
echo.

pause
