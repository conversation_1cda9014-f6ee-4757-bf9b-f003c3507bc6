package com.accounting.controller;

import com.accounting.model.ManufacturingOrder;
import com.accounting.service.ManufacturingService;
import javafx.geometry.Insets;
import javafx.geometry.Pos;
import javafx.scene.Scene;
import javafx.scene.control.*;
import javafx.scene.control.cell.PropertyValueFactory;
import javafx.scene.layout.*;
import javafx.stage.Modality;
import javafx.stage.Stage;
import de.jensd.fx.glyphs.fontawesome.FontAwesomeIcon;
import de.jensd.fx.glyphs.fontawesome.FontAwesomeIconView;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import java.time.LocalDate;
import java.text.DecimalFormat;
import java.util.Optional;

/**
 * كنترولر تقارير أوامر التصنيع
 */
public class ManufacturingReportsController {
    
    private final ManufacturingService manufacturingService;
    private final DecimalFormat decimalFormat = new DecimalFormat("#,##0.00");
    private TableView<ManufacturingOrder> reportsTable;
    private TextField searchField;
    private DatePicker fromDatePicker;
    private DatePicker toDatePicker;
    private ComboBox<ManufacturingOrder.OrderStatus> statusFilter;
    private ComboBox<String> customerFilter;
    
    public ManufacturingReportsController() {
        this.manufacturingService = ManufacturingService.getInstance();
    }
    
    /**
     * عرض نافذة تقارير أوامر التصنيع
     */
    public void showManufacturingReportsDialog() {
        Stage dialog = new Stage();
        dialog.setTitle("📊 تقرير أوامر التصنيع");
        dialog.initModality(Modality.APPLICATION_MODAL);
        
        // تحديد حجم النافذة
        javafx.stage.Screen screen = javafx.stage.Screen.getPrimary();
        double screenHeight = screen.getVisualBounds().getHeight();
        double screenWidth = screen.getVisualBounds().getWidth();
        
        dialog.setWidth(Math.min(1400, screenWidth * 0.95));
        dialog.setHeight(Math.min(900, screenHeight * 0.9));
        dialog.setResizable(true);
        
        VBox mainLayout = new VBox(15);
        mainLayout.setPadding(new Insets(20));
        
        // العنوان والوصف
        Label titleLabel = new Label("📊 تقرير أوامر التصنيع");
        titleLabel.getStyleClass().add("dialog-title");
        
        Label descLabel = new Label("تقارير شاملة وإحصائيات أوامر التصنيع مع إمكانية التعديل والحذف");
        descLabel.getStyleClass().add("dialog-description");
        
        // شريط الأدوات والفلاتر
        VBox filtersBox = createFiltersBox();
        
        // الإحصائيات السريعة
        HBox statsBox = createStatsBox();
        
        // جدول التقارير
        reportsTable = createReportsTable();
        
        // تحميل البيانات
        refreshReportsTable();
        
        // وضع الجدول في ScrollPane
        ScrollPane scrollPane = new ScrollPane(reportsTable);
        scrollPane.setFitToWidth(true);
        scrollPane.setVbarPolicy(ScrollPane.ScrollBarPolicy.AS_NEEDED);
        scrollPane.setHbarPolicy(ScrollPane.ScrollBarPolicy.NEVER);
        scrollPane.getStyleClass().add("reports-scroll-pane");
        VBox.setVgrow(scrollPane, Priority.ALWAYS);
        
        // أزرار الإجراءات
        HBox buttonBox = createButtonBox(dialog);
        
        mainLayout.getChildren().addAll(
            titleLabel, descLabel, filtersBox, statsBox, scrollPane, buttonBox
        );
        
        Scene scene = new Scene(mainLayout);
        scene.getStylesheets().add(getClass().getResource("/css/styles.css").toExternalForm());
        dialog.setScene(scene);
        dialog.showAndWait();
    }
    
    /**
     * إنشاء صندوق الفلاتر
     */
    private VBox createFiltersBox() {
        VBox filtersBox = new VBox(10);
        filtersBox.setPadding(new Insets(15));
        filtersBox.getStyleClass().add("filters-container");
        
        Label filtersTitle = new Label("🔍 فلاتر التقرير");
        filtersTitle.getStyleClass().add("filters-title");
        
        // الصف الأول من الفلاتر
        HBox firstRow = new HBox(15);
        firstRow.setAlignment(Pos.CENTER_LEFT);
        
        // البحث
        Label searchLabel = new Label("البحث:");
        searchField = new TextField();
        searchField.setPromptText("ابحث في الأوامر...");
        searchField.setPrefWidth(200);
        searchField.textProperty().addListener((obs, oldVal, newVal) -> applyFilters());
        
        // فلتر التاريخ من
        Label fromLabel = new Label("من تاريخ:");
        fromDatePicker = new DatePicker();
        fromDatePicker.setValue(LocalDate.now().withDayOfMonth(1)); // أول الشهر
        fromDatePicker.valueProperty().addListener((obs, oldVal, newVal) -> applyFilters());
        
        // فلتر التاريخ إلى
        Label toLabel = new Label("إلى تاريخ:");
        toDatePicker = new DatePicker();
        toDatePicker.setValue(LocalDate.now()); // اليوم
        toDatePicker.valueProperty().addListener((obs, oldVal, newVal) -> applyFilters());
        
        firstRow.getChildren().addAll(
            searchLabel, searchField,
            new Separator(),
            fromLabel, fromDatePicker,
            toLabel, toDatePicker
        );
        
        // الصف الثاني من الفلاتر
        HBox secondRow = new HBox(15);
        secondRow.setAlignment(Pos.CENTER_LEFT);
        
        // فلتر الحالة
        Label statusLabel = new Label("الحالة:");
        statusFilter = new ComboBox<>();
        statusFilter.getItems().add(null); // جميع الحالات
        statusFilter.getItems().addAll(ManufacturingOrder.OrderStatus.values());
        statusFilter.setPromptText("جميع الحالات");
        statusFilter.valueProperty().addListener((obs, oldVal, newVal) -> applyFilters());
        
        // فلتر العميل
        Label customerLabel = new Label("العميل:");
        customerFilter = new ComboBox<>();
        customerFilter.getItems().add(null); // جميع العملاء
        loadCustomers();
        customerFilter.setPromptText("جميع العملاء");
        customerFilter.valueProperty().addListener((obs, oldVal, newVal) -> applyFilters());
        
        // أزرار الفلاتر
        Button applyBtn = new Button("تطبيق");
        FontAwesomeIconView applyIcon = new FontAwesomeIconView(FontAwesomeIcon.SEARCH);
        applyIcon.setSize("12px");
        applyBtn.setGraphic(applyIcon);
        applyBtn.getStyleClass().add("filter-button");
        applyBtn.setOnAction(e -> applyFilters());
        
        Button resetBtn = new Button("إعادة تعيين");
        FontAwesomeIconView resetIcon = new FontAwesomeIconView(FontAwesomeIcon.REFRESH);
        resetIcon.setSize("12px");
        resetBtn.setGraphic(resetIcon);
        resetBtn.getStyleClass().add("reset-button");
        resetBtn.setOnAction(e -> resetFilters());
        
        secondRow.getChildren().addAll(
            statusLabel, statusFilter,
            customerLabel, customerFilter,
            new Separator(),
            applyBtn, resetBtn
        );
        
        filtersBox.getChildren().addAll(filtersTitle, firstRow, secondRow);
        return filtersBox;
    }
    
    /**
     * إنشاء صندوق الإحصائيات
     */
    private HBox createStatsBox() {
        HBox statsBox = new HBox(20);
        statsBox.setAlignment(Pos.CENTER);
        statsBox.setPadding(new Insets(15));
        statsBox.getStyleClass().add("stats-container");
        
        var stats = manufacturingService.getManufacturingStatistics();
        
        VBox totalStats = createStatCard(
            "إجمالي الأوامر", 
            String.valueOf(stats.get("إجمالي الأوامر")),
            FontAwesomeIcon.INDUSTRY,
            "#3498db"
        );
        
        VBox completedStats = createStatCard(
            "مكتملة", 
            String.valueOf(stats.get("أوامر مكتمل")),
            FontAwesomeIcon.CHECK_CIRCLE,
            "#2ecc71"
        );
        
        VBox inProgressStats = createStatCard(
            "قيد التنفيذ", 
            String.valueOf(stats.get("أوامر قيد التنفيذ")),
            FontAwesomeIcon.COG,
            "#e74c3c"
        );
        
        VBox thisMonthStats = createStatCard(
            "هذا الشهر", 
            String.valueOf(stats.get("أوامر الشهر الحالي")),
            FontAwesomeIcon.CALENDAR,
            "#f39c12"
        );
        
        statsBox.getChildren().addAll(totalStats, completedStats, inProgressStats, thisMonthStats);
        return statsBox;
    }
    
    /**
     * إنشاء بطاقة إحصائية
     */
    private VBox createStatCard(String title, String value, FontAwesomeIcon icon, String color) {
        VBox card = new VBox(10);
        card.setAlignment(Pos.CENTER);
        card.setPrefSize(150, 80);
        card.getStyleClass().add("stat-card");
        
        FontAwesomeIconView iconView = new FontAwesomeIconView(icon);
        iconView.setSize("20px");
        iconView.setStyle("-fx-fill: " + color + ";");
        
        Label valueLabel = new Label(value);
        valueLabel.getStyleClass().add("stat-value");
        valueLabel.setStyle("-fx-text-fill: " + color + ";");
        
        Label titleLabel = new Label(title);
        titleLabel.getStyleClass().add("stat-title");
        
        card.getChildren().addAll(iconView, valueLabel, titleLabel);
        return card;
    }
    
    /**
     * إنشاء جدول التقارير
     */
    private TableView<ManufacturingOrder> createReportsTable() {
        TableView<ManufacturingOrder> table = new TableView<>();
        table.getStyleClass().add("reports-table");
        
        // الأعمدة
        TableColumn<ManufacturingOrder, String> numberCol = new TableColumn<>("رقم الأمر");
        numberCol.setCellValueFactory(new PropertyValueFactory<>("orderNumber"));
        numberCol.setPrefWidth(100);
        
        TableColumn<ManufacturingOrder, String> customerCol = new TableColumn<>("العميل");
        customerCol.setCellValueFactory(new PropertyValueFactory<>("customerName"));
        customerCol.setPrefWidth(150);
        
        TableColumn<ManufacturingOrder, String> invoiceCol = new TableColumn<>("رقم الفاتورة");
        invoiceCol.setCellValueFactory(new PropertyValueFactory<>("invoiceNumber"));
        invoiceCol.setPrefWidth(120);
        
        TableColumn<ManufacturingOrder, LocalDate> dateCol = new TableColumn<>("التاريخ");
        dateCol.setCellValueFactory(new PropertyValueFactory<>("orderDate"));
        dateCol.setPrefWidth(100);
        
        TableColumn<ManufacturingOrder, String> statusCol = new TableColumn<>("الحالة");
        statusCol.setCellValueFactory(cellData -> 
            cellData.getValue().statusProperty().asString());
        statusCol.setPrefWidth(100);
        
        TableColumn<ManufacturingOrder, String> servicesCol = new TableColumn<>("الخدمات");
        servicesCol.setCellValueFactory(cellData -> {
            ManufacturingOrder order = cellData.getValue();
            StringBuilder services = new StringBuilder();
            if (order.isGlassCuttingSelected()) services.append("قص زجاج، ");
            if (order.isThermalGlassSelected()) services.append("حراري، ");
            if (order.isFilmSelected()) services.append("فيلم، ");
            if (order.isBulletProofFilmSelected()) services.append("فيلم ضد الرصاص، ");
            if (order.isDoubleGlassSelected()) services.append("دبل جلاس، ");
            if (order.isPolishSelected()) services.append("Polish، ");
            if (order.isHoleSelected()) services.append("Hole، ");
            if (order.isCncSelected()) services.append("CNC، ");
            if (order.isDrawSelected()) services.append("Draw، ");
            if (order.isOtherServicesSelected()) services.append("أخرى، ");
            
            String result = services.toString();
            if (result.endsWith("، ")) {
                result = result.substring(0, result.length() - 2);
            }
            return new javafx.beans.property.SimpleStringProperty(result);
        });
        servicesCol.setPrefWidth(200);
        
        TableColumn<ManufacturingOrder, String> createdCol = new TableColumn<>("تاريخ الإنشاء");
        createdCol.setCellValueFactory(cellData -> {
            if (cellData.getValue().getCreatedDate() != null) {
                return new javafx.beans.property.SimpleStringProperty(
                    cellData.getValue().getCreatedDate().toLocalDate().toString()
                );
            }
            return new javafx.beans.property.SimpleStringProperty("");
        });
        createdCol.setPrefWidth(120);
        
        TableColumn<ManufacturingOrder, String> notesCol = new TableColumn<>("ملاحظات");
        notesCol.setCellValueFactory(new PropertyValueFactory<>("notes"));
        notesCol.setPrefWidth(150);
        
        // إضافة الأعمدة
        table.getColumns().addAll(numberCol, customerCol, invoiceCol, dateCol, statusCol, servicesCol, createdCol, notesCol);
        
        // تخصيص عرض الصفوف حسب الحالة
        table.setRowFactory(tv -> {
            TableRow<ManufacturingOrder> row = new TableRow<>();
            row.itemProperty().addListener((obs, oldOrder, newOrder) -> {
                if (newOrder != null) {
                    String style = "";
                    switch (newOrder.getStatus()) {
                        case DRAFT:
                            style = "-fx-background-color: #fff3cd;";
                            break;
                        case CONFIRMED:
                            style = "-fx-background-color: #d1ecf1;";
                            break;
                        case IN_PROGRESS:
                            style = "-fx-background-color: #f8d7da;";
                            break;
                        case COMPLETED:
                            style = "-fx-background-color: #d4edda;";
                            break;
                        case CANCELLED:
                            style = "-fx-background-color: #f5c6cb;";
                            break;
                    }
                    row.setStyle(style);
                }
            });
            return row;
        });
        
        return table;
    }
    
    /**
     * إنشاء صندوق الأزرار
     */
    private HBox createButtonBox(Stage dialog) {
        HBox buttonBox = new HBox(15);
        buttonBox.setAlignment(Pos.CENTER_LEFT);
        buttonBox.setPadding(new Insets(10, 0, 0, 0));
        
        // أزرار الإجراءات
        Button editBtn = new Button("تعديل أمر");
        FontAwesomeIconView editIcon = new FontAwesomeIconView(FontAwesomeIcon.EDIT);
        editIcon.setSize("12px");
        editBtn.setGraphic(editIcon);
        editBtn.getStyleClass().add("edit-button");
        editBtn.setOnAction(e -> editSelectedOrder());
        
        Button deleteBtn = new Button("حذف أمر");
        FontAwesomeIconView deleteIcon = new FontAwesomeIconView(FontAwesomeIcon.TRASH);
        deleteIcon.setSize("12px");
        deleteBtn.setGraphic(deleteIcon);
        deleteBtn.getStyleClass().add("delete-button");
        deleteBtn.setOnAction(e -> deleteSelectedOrder());
        
        Button cancelOrderBtn = new Button("تحديد كملغي");
        FontAwesomeIconView cancelIcon = new FontAwesomeIconView(FontAwesomeIcon.TIMES);
        cancelIcon.setSize("12px");
        cancelOrderBtn.setGraphic(cancelIcon);
        cancelOrderBtn.getStyleClass().add("unpost-button");
        cancelOrderBtn.setOnAction(e -> cancelSelectedOrder());
        
        // أزرار التقارير
        Button exportBtn = new Button("تصدير");
        FontAwesomeIconView exportIcon = new FontAwesomeIconView(FontAwesomeIcon.DOWNLOAD);
        exportIcon.setSize("12px");
        exportBtn.setGraphic(exportIcon);
        exportBtn.getStyleClass().add("export-button");
        exportBtn.setOnAction(e -> exportReport());
        
        Button printBtn = new Button("طباعة");
        FontAwesomeIconView printIcon = new FontAwesomeIconView(FontAwesomeIcon.PRINT);
        printIcon.setSize("12px");
        printBtn.setGraphic(printIcon);
        printBtn.getStyleClass().add("print-button");
        printBtn.setOnAction(e -> printReport());
        
        Button refreshBtn = new Button("تحديث");
        FontAwesomeIconView refreshIcon = new FontAwesomeIconView(FontAwesomeIcon.REFRESH);
        refreshIcon.setSize("12px");
        refreshBtn.setGraphic(refreshIcon);
        refreshBtn.getStyleClass().add("refresh-button");
        refreshBtn.setOnAction(e -> refreshReportsTable());
        
        Button closeBtn = new Button("إغلاق");
        closeBtn.setOnAction(e -> dialog.close());
        
        buttonBox.getChildren().addAll(
            editBtn, deleteBtn, cancelOrderBtn,
            new Separator(),
            exportBtn, printBtn, refreshBtn, closeBtn
        );
        
        return buttonBox;
    }
    
    /**
     * تحميل قائمة العملاء
     */
    private void loadCustomers() {
        customerFilter.getItems().addAll(
            manufacturingService.getAllManufacturingOrders().stream()
                .map(ManufacturingOrder::getCustomerName)
                .distinct()
                .sorted()
                .toArray(String[]::new)
        );
    }
    
    /**
     * تحديث جدول التقارير
     */
    private void refreshReportsTable() {
        ObservableList<ManufacturingOrder> orders = FXCollections.observableArrayList(
            manufacturingService.getAllManufacturingOrders()
        );
        reportsTable.setItems(orders);
        
        // تحديث قائمة العملاء
        customerFilter.getItems().clear();
        customerFilter.getItems().add(null);
        loadCustomers();
    }
    
    /**
     * تطبيق الفلاتر
     */
    private void applyFilters() {
        ObservableList<ManufacturingOrder> filteredOrders = FXCollections.observableArrayList();
        
        for (ManufacturingOrder order : manufacturingService.getAllManufacturingOrders()) {
            boolean matches = true;
            
            // فلتر البحث النصي
            String searchText = searchField.getText();
            if (searchText != null && !searchText.trim().isEmpty()) {
                String searchLower = searchText.toLowerCase();
                boolean textMatch = order.getOrderNumber().toLowerCase().contains(searchLower) ||
                                  order.getCustomerName().toLowerCase().contains(searchLower) ||
                                  (order.getInvoiceNumber() != null && order.getInvoiceNumber().toLowerCase().contains(searchLower)) ||
                                  (order.getNotes() != null && order.getNotes().toLowerCase().contains(searchLower));
                if (!textMatch) {
                    matches = false;
                }
            }
            
            // فلتر التاريخ
            LocalDate fromDate = fromDatePicker.getValue();
            LocalDate toDate = toDatePicker.getValue();
            if (fromDate != null && order.getOrderDate().isBefore(fromDate)) {
                matches = false;
            }
            if (toDate != null && order.getOrderDate().isAfter(toDate)) {
                matches = false;
            }
            
            // فلتر الحالة
            ManufacturingOrder.OrderStatus selectedStatus = statusFilter.getValue();
            if (selectedStatus != null && order.getStatus() != selectedStatus) {
                matches = false;
            }
            
            // فلتر العميل
            String selectedCustomer = customerFilter.getValue();
            if (selectedCustomer != null && !order.getCustomerName().equals(selectedCustomer)) {
                matches = false;
            }
            
            if (matches) {
                filteredOrders.add(order);
            }
        }
        
        reportsTable.setItems(filteredOrders);
    }
    
    /**
     * إعادة تعيين الفلاتر
     */
    private void resetFilters() {
        searchField.clear();
        fromDatePicker.setValue(LocalDate.now().withDayOfMonth(1));
        toDatePicker.setValue(LocalDate.now());
        statusFilter.setValue(null);
        customerFilter.setValue(null);
        refreshReportsTable();
    }
    
    // وظائف الإجراءات
    private void editSelectedOrder() {
        ManufacturingOrder selectedOrder = reportsTable.getSelectionModel().getSelectedItem();
        if (selectedOrder == null) {
            showWarningAlert("يرجى اختيار أمر للتعديل.");
            return;
        }
        
        if (selectedOrder.getStatus() == ManufacturingOrder.OrderStatus.COMPLETED ||
            selectedOrder.getStatus() == ManufacturingOrder.OrderStatus.CANCELLED) {
            showWarningAlert("لا يمكن تعديل أمر مكتمل أو ملغي.");
            return;
        }
        
        ManufacturingOrderFormController formController = new ManufacturingOrderFormController(manufacturingService);
        Optional<ManufacturingOrder> result = formController.showManufacturingOrderForm(selectedOrder);
        
        if (result.isPresent()) {
            refreshReportsTable();
            showSuccessAlert("تم تحديث أمر التصنيع بنجاح!");
        }
    }
    
    private void deleteSelectedOrder() {
        ManufacturingOrder selectedOrder = reportsTable.getSelectionModel().getSelectedItem();
        if (selectedOrder == null) {
            showWarningAlert("يرجى اختيار أمر للحذف.");
            return;
        }
        
        if (selectedOrder.getStatus() == ManufacturingOrder.OrderStatus.IN_PROGRESS ||
            selectedOrder.getStatus() == ManufacturingOrder.OrderStatus.COMPLETED) {
            showErrorAlert("لا يمكن حذف أمر قيد التنفيذ أو مكتمل.");
            return;
        }
        
        Alert confirmAlert = new Alert(Alert.AlertType.CONFIRMATION);
        confirmAlert.setTitle("تأكيد الحذف");
        confirmAlert.setHeaderText("حذف أمر التصنيع");
        confirmAlert.setContentText("هل أنت متأكد من حذف أمر التصنيع: " + selectedOrder.getOrderNumber() + "؟");
        
        Optional<ButtonType> result = confirmAlert.showAndWait();
        if (result.isPresent() && result.get() == ButtonType.OK) {
            if (manufacturingService.deleteManufacturingOrder(selectedOrder.getOrderId())) {
                refreshReportsTable();
                showSuccessAlert("تم حذف أمر التصنيع بنجاح!");
            } else {
                showErrorAlert("فشل في حذف أمر التصنيع.");
            }
        }
    }
    
    private void cancelSelectedOrder() {
        ManufacturingOrder selectedOrder = reportsTable.getSelectionModel().getSelectedItem();
        if (selectedOrder == null) {
            showWarningAlert("يرجى اختيار أمر لتحديده كملغي.");
            return;
        }
        
        if (selectedOrder.getStatus() == ManufacturingOrder.OrderStatus.CANCELLED) {
            showWarningAlert("الأمر ملغي بالفعل.");
            return;
        }
        
        Alert confirmAlert = new Alert(Alert.AlertType.CONFIRMATION);
        confirmAlert.setTitle("تأكيد الإلغاء");
        confirmAlert.setHeaderText("تحديد الأمر كملغي");
        confirmAlert.setContentText("هل أنت متأكد من تحديد أمر التصنيع كملغي: " + selectedOrder.getOrderNumber() + "؟");
        
        Optional<ButtonType> result = confirmAlert.showAndWait();
        if (result.isPresent() && result.get() == ButtonType.OK) {
            if (manufacturingService.cancelManufacturingOrder(selectedOrder.getOrderId())) {
                refreshReportsTable();
                showSuccessAlert("تم تحديد أمر التصنيع كملغي بنجاح!");
            } else {
                showErrorAlert("فشل في إلغاء أمر التصنيع.");
            }
        }
    }
    
    private void exportReport() {
        LocalDate fromDate = fromDatePicker.getValue();
        LocalDate toDate = toDatePicker.getValue();
        
        if (fromDate == null || toDate == null) {
            showWarningAlert("يرجى تحديد فترة التقرير.");
            return;
        }
        
        String exportText = manufacturingService.exportManufacturingOrdersToText(fromDate, toDate);
        
        // عرض النص في نافذة
        Stage exportStage = new Stage();
        exportStage.setTitle("تصدير تقرير أوامر التصنيع");
        exportStage.initModality(Modality.APPLICATION_MODAL);
        
        TextArea textArea = new TextArea(exportText);
        textArea.setEditable(false);
        textArea.setWrapText(true);
        
        VBox layout = new VBox(10);
        layout.setPadding(new Insets(20));
        layout.getChildren().add(textArea);
        VBox.setVgrow(textArea, Priority.ALWAYS);
        
        Scene scene = new Scene(layout, 800, 600);
        exportStage.setScene(scene);
        exportStage.showAndWait();
    }
    
    private void printReport() {
        showInfoAlert("ميزة الطباعة ستكون متاحة قريباً.");
    }
    
    // رسائل التنبيه
    private void showSuccessAlert(String message) {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle("نجح");
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }
    
    private void showErrorAlert(String message) {
        Alert alert = new Alert(Alert.AlertType.ERROR);
        alert.setTitle("خطأ");
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }
    
    private void showWarningAlert(String message) {
        Alert alert = new Alert(Alert.AlertType.WARNING);
        alert.setTitle("تحذير");
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }
    
    private void showInfoAlert(String message) {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle("معلومات");
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }
}
