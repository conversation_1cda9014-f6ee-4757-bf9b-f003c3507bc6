package com.accounting.model;

import javafx.beans.property.*;
import java.util.UUID;

/**
 * نموذج عنصر الاستلام
 */
public class ReceiptItem {
    
    // الخصائص الأساسية
    private final StringProperty itemId = new SimpleStringProperty();
    private final StringProperty description = new SimpleStringProperty();
    private final IntegerProperty requiredQuantity = new SimpleIntegerProperty();
    private final IntegerProperty receivedQuantity = new SimpleIntegerProperty();
    private final StringProperty unit = new SimpleStringProperty();
    private final StringProperty notes = new SimpleStringProperty();
    private final ObjectProperty<ReceiptItemStatus> receiptStatus = new SimpleObjectProperty<>();
    
    // حالات عنصر الاستلام
    public enum ReceiptItemStatus {
        PENDING("في الانتظار"),
        PARTIAL("جزئي"),
        COMPLETE("مكتمل"),
        CANCELLED("ملغي");
        
        private final String displayName;
        
        ReceiptItemStatus(String displayName) {
            this.displayName = displayName;
        }
        
        @Override
        public String toString() {
            return displayName;
        }
    }
    
    /**
     * المنشئ الافتراضي
     */
    public ReceiptItem() {
        this.itemId.set(UUID.randomUUID().toString());
        this.requiredQuantity.set(1);
        this.receivedQuantity.set(0);
        this.unit.set("قطعة");
        this.receiptStatus.set(ReceiptItemStatus.PENDING);
        
        // مستمع لتحديث الحالة تلقائياً
        this.receivedQuantity.addListener((obs, oldVal, newVal) -> updateStatus());
        this.requiredQuantity.addListener((obs, oldVal, newVal) -> updateStatus());
    }
    
    /**
     * منشئ مع المعاملات
     */
    public ReceiptItem(String description, int requiredQuantity, String unit) {
        this();
        this.description.set(description);
        this.requiredQuantity.set(requiredQuantity);
        this.unit.set(unit);
    }
    
    /**
     * تحديث حالة العنصر تلقائياً
     */
    private void updateStatus() {
        int required = requiredQuantity.get();
        int received = receivedQuantity.get();
        
        if (received == 0) {
            receiptStatus.set(ReceiptItemStatus.PENDING);
        } else if (received >= required) {
            receiptStatus.set(ReceiptItemStatus.COMPLETE);
        } else {
            receiptStatus.set(ReceiptItemStatus.PARTIAL);
        }
    }
    
    // Getters and Setters
    public String getItemId() {
        return itemId.get();
    }
    
    public void setItemId(String itemId) {
        this.itemId.set(itemId);
    }
    
    public StringProperty itemIdProperty() {
        return itemId;
    }
    
    public String getDescription() {
        return description.get();
    }
    
    public void setDescription(String description) {
        this.description.set(description);
    }
    
    public StringProperty descriptionProperty() {
        return description;
    }
    
    public int getRequiredQuantity() {
        return requiredQuantity.get();
    }
    
    public void setRequiredQuantity(int requiredQuantity) {
        this.requiredQuantity.set(requiredQuantity);
    }
    
    public IntegerProperty requiredQuantityProperty() {
        return requiredQuantity;
    }
    
    public int getReceivedQuantity() {
        return receivedQuantity.get();
    }
    
    public void setReceivedQuantity(int receivedQuantity) {
        this.receivedQuantity.set(receivedQuantity);
    }
    
    public IntegerProperty receivedQuantityProperty() {
        return receivedQuantity;
    }
    
    public String getUnit() {
        return unit.get();
    }
    
    public void setUnit(String unit) {
        this.unit.set(unit);
    }
    
    public StringProperty unitProperty() {
        return unit;
    }
    
    public String getNotes() {
        return notes.get();
    }
    
    public void setNotes(String notes) {
        this.notes.set(notes);
    }
    
    public StringProperty notesProperty() {
        return notes;
    }
    
    public ReceiptItemStatus getReceiptStatus() {
        return receiptStatus.get();
    }
    
    public void setReceiptStatus(ReceiptItemStatus receiptStatus) {
        this.receiptStatus.set(receiptStatus);
    }
    
    public ObjectProperty<ReceiptItemStatus> receiptStatusProperty() {
        return receiptStatus;
    }
    
    // وظائف مساعدة
    public boolean isFullyReceived() {
        return receivedQuantity.get() >= requiredQuantity.get();
    }
    
    public boolean isPartiallyReceived() {
        return receivedQuantity.get() > 0 && receivedQuantity.get() < requiredQuantity.get();
    }
    
    public int getRemainingQuantity() {
        return Math.max(0, requiredQuantity.get() - receivedQuantity.get());
    }
    
    public double getReceiptPercentage() {
        if (requiredQuantity.get() == 0) return 0.0;
        return (double) receivedQuantity.get() / requiredQuantity.get() * 100.0;
    }
    
    @Override
    public String toString() {
        return description.get() + " (" + receivedQuantity.get() + "/" + requiredQuantity.get() + " " + unit.get() + ")";
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        ReceiptItem that = (ReceiptItem) obj;
        return itemId.get().equals(that.itemId.get());
    }
    
    @Override
    public int hashCode() {
        return itemId.get().hashCode();
    }
}
